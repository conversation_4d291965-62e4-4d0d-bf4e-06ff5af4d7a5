import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { LoginForm, SignUpForm } from "./components/forms";
import { useAuth } from "./hooks/useAuth";

const AuthPage: React.FC = () => {
  const [isSignUp, setIsSignUp] = useState(false);
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  // Redirect to saved URL or dashboard if user is already authenticated
  useEffect(() => {
    if (user && !loading) {
      console.log(
        "[AuthPage] User already authenticated, redirecting to saved URL or dashboard"
      );

      // Redirect to dashboard
      navigate("/");
    }
  }, [user, loading, navigate]);

  const handleAuthSuccess = () => {
    // Redirect to dashboard
    navigate("/");
  };

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
  };

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner text="Checking authentication..." />
      </div>
    );
  }

  // If user is authenticated, we'll redirect in the useEffect
  // This prevents the auth form from flashing before redirect
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner text="Already logged in. Redirecting to dashboard..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="h-12 w-12 bg-eventcost-primary text-white flex items-center justify-center rounded-lg">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {isSignUp ? (
            <SignUpForm
              onSuccess={handleAuthSuccess}
              onToggleMode={toggleMode}
            />
          ) : (
            <LoginForm
              onSuccess={handleAuthSuccess}
              onToggleMode={toggleMode}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthPage;
