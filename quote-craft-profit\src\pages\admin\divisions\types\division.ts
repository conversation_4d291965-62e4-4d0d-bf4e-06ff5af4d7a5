/**
 * Division types for the admin divisions feature
 */

import { Division as GlobalDivision } from "@/types/types";

// Re-export the global Division type
export type Division = GlobalDivision;

/**
 * Form data for creating or updating a division
 */
export interface DivisionFormData {
  name: string;
  code: string;
  description?: string;
}

/**
 * API response for division operations
 */
export interface DivisionApiResponse {
  data: Division | Division[];
  error?: string;
  message?: string;
}
