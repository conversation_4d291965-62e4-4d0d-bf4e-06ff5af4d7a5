import {
  Controller,
  Post,
  Delete,
  Get,
  Put,
  Body,
  Param,
  ParseUUI<PERSON>ipe,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { CalculationItemsService } from './calculation-items.service';
import { AddPackageLineItemDto } from './dto/add-package-line-item.dto';
import { AddCustomLineItemDto } from './dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from './dto/update-line-item.dto';
import { LineItemDto } from './dto/line-item.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import {
  ApiTags,
  ApiBearerAuth,
  ApiParam,
  ApiOkResponse,
  ApiResponse,
  ApiOperation,
} from '@nestjs/swagger';
import { CalculationsService } from '../calculations/calculations.service';
import { ItemIdResponse } from './dto/item-id-response.dto';

@ApiTags('Calculation Items')
@ApiBearerAuth()
@Controller('calculations/:calcId')
@UseGuards(JwtAuthGuard)
export class CalculationItemsController {
  private readonly logger = new Logger(CalculationItemsController.name);

  constructor(
    private readonly calculationItemsService: CalculationItemsService,
    @Inject(forwardRef(() => CalculationsService))
    private readonly calculationsService: CalculationsService,
  ) {}

  private async checkOwnership(calcId: string, user: User): Promise<void> {
    this.logger.debug(
      `Checking ownership for calcId: ${calcId}, userId: ${user.id}`,
    );
    await this.calculationsService.checkCalculationOwnership(calcId, user.id);
    this.logger.debug(`Ownership confirmed for calcId: ${calcId}`);
  }

  @Get('items')
  @ApiOperation({ summary: 'Get all line items for a calculation' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'List of line items', type: [LineItemDto] })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  async getCalculationItems(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @GetCurrentUser() user: User,
  ): Promise<LineItemDto[]> {
    this.logger.log(
      `User ${user.email} fetching line items for calculation ID: ${calcId}`,
    );
    await this.checkOwnership(calcId, user);
    return this.calculationItemsService.getCalculationItems(calcId);
  }

  @Get('items/:itemId')
  @ApiOperation({ summary: 'Get a specific line item by ID' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'itemId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'Line item details', type: LineItemDto })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Line item not found or access denied.',
  })
  async getLineItemById(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @GetCurrentUser() user: User,
  ): Promise<LineItemDto> {
    this.logger.log(
      `User ${user.email} fetching line item ID: ${itemId} for calculation ID: ${calcId}`,
    );
    await this.checkOwnership(calcId, user);
    return this.calculationItemsService.getLineItemById(calcId, itemId);
  }

  @Post('items/package')
  @ApiOperation({ summary: 'Add a package line item to a calculation' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'Package item added', type: ItemIdResponse })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input or package/option not found.',
  })
  async addPackageLineItem(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Body() addDto: AddPackageLineItemDto,
    @GetCurrentUser() user: User,
  ): Promise<ItemIdResponse> {
    this.logger.log(
      `User ${user.email} adding package ${addDto.packageId} to calc ${calcId}`,
    );
    await this.checkOwnership(calcId, user);
    const itemId = await this.calculationItemsService.addPackageLineItem(
      calcId,
      addDto,
      user,
    );
    return itemId;
  }

  @Post('items/custom')
  @ApiOperation({ summary: 'Add a custom line item to a calculation' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'Custom item added', type: ItemIdResponse })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  async addCustomLineItem(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Body() addDto: AddCustomLineItemDto,
    @GetCurrentUser() user: User,
  ): Promise<ItemIdResponse> {
    this.logger.log(
      `User ${user.email} adding custom item '${addDto.itemName}' to calc ${calcId}`,
    );
    await this.checkOwnership(calcId, user);
    const itemId = await this.calculationItemsService.addCustomLineItem(
      calcId,
      addDto,
      user,
    );
    return itemId;
  }

  @Put('items/:itemId')
  @ApiOperation({ summary: 'Update a line item (package or custom)' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'itemId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'Line item updated', type: LineItemDto })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation or item not found or access denied.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data.',
  })
  async updateLineItem(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @Body() updateDto: UpdateLineItemDto,
    @GetCurrentUser() user: User,
  ): Promise<LineItemDto> {
    this.logger.log(
      `User ${user.email} updating line item ${itemId} in calc ${calcId}`,
    );
    await this.checkOwnership(calcId, user);
    return this.calculationItemsService.updateLineItem(
      calcId,
      itemId,
      updateDto,
      user,
    );
  }

  @Delete('items/package/:itemId')
  @ApiOperation({ summary: 'Delete a package line item from a calculation' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'itemId', type: 'string', format: 'uuid' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Package item deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation or item not found or access denied.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePackageLineItem(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @GetCurrentUser() user: User,
  ): Promise<void> {
    this.logger.log(
      `User ${user.email} deleting package item ${itemId} from calc ${calcId}`,
    );
    await this.checkOwnership(calcId, user);
    await this.calculationItemsService.deletePackageLineItem(
      calcId,
      itemId,
      user,
    );
  }

  @Delete('items/custom/:itemId')
  @ApiOperation({ summary: 'Delete a custom line item from a calculation' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'itemId', type: 'string', format: 'uuid' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Custom item deleted.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation or item not found or access denied.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCustomLineItem(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @GetCurrentUser() user: User,
  ): Promise<void> {
    this.logger.log(
      `User ${user.email} deleting custom item ${itemId} from calc ${calcId}`,
    );
    await this.checkOwnership(calcId, user);
    await this.calculationItemsService.deleteCustomLineItem(
      calcId,
      itemId,
      user,
    );
  }
}
