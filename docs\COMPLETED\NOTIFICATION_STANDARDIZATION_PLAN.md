# 🔔 Notification System Standardization Plan

## 📊 Current State Analysis

### **🎯 Notification Systems Found (6 Different Approaches)**

#### **1. ✅ Sonner Toast (RECOMMENDED - Primary)**

```typescript
import { toast } from "sonner";
toast.success("Message");
toast.error("Error");
```

- **Usage**: Direct imports in 15+ files
- **Status**: ✅ **KEEP - This is our target standard**
- **Benefits**: Modern, performant, great UX

#### **2. ❌ useToast Hook (LEGACY - Remove)**

```typescript
const { toast } = useToast();
toast({ title: "Title", description: "...", variant: "destructive" });
```

- **Usage**: Found in 8+ files
- **Status**: ❌ **MIGRATE TO SONNER**
- **Issues**: Legacy shadcn pattern, inconsistent with Sonner

#### **3. ✅ Standardized Notifications (GOOD - Enhance)**

```typescript
import { showSuccess, showError } from "@/lib/notifications";
showSuccess("Message", { description: "Details" });
```

- **Usage**: Available but underused
- **Status**: ✅ **ENHANCE AND PROMOTE**
- **Benefits**: Consistent API, configurable options

#### **4. ⚡ Optimized Notifications (ADVANCED - Keep for specific cases)**

```typescript
import { showOptimizedSuccess } from "@/lib/optimized-notifications";
showOptimizedSuccess("Message", { category: "AUTO_SAVE" });
```

- **Usage**: Used in calculation auto-save
- **Status**: ⚡ **KEEP FOR HIGH-FREQUENCY OPERATIONS**
- **Benefits**: Debouncing, smart filtering

#### **5. 🔔 Notification Center (PERSISTENT - Keep)**

```typescript
const { addNotification } = useNotifications();
addNotification({ type: "info", message: "..." });
```

- **Usage**: Bell icon notifications
- **Status**: 🔔 **KEEP FOR PERSISTENT NOTIFICATIONS**
- **Purpose**: Long-term notifications that need review

#### **6. ⚠️ Alert Dialogs (CONFIRMATIONS - Keep)**

```typescript
<AlertDialog>
  <AlertDialogContent>
    <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
  </AlertDialogContent>
</AlertDialog>
```

- **Usage**: Confirmation dialogs
- **Status**: ⚠️ **KEEP FOR CONFIRMATIONS**
- **Purpose**: Blocking confirmations for destructive actions

---

## 🎯 Target Architecture

### **📋 Notification Decision Matrix**

| Use Case                     | Recommended System  | Example                                                         |
| ---------------------------- | ------------------- | --------------------------------------------------------------- |
| **Simple Success/Error**     | Direct Sonner       | `toast.success("Saved!")`                                       |
| **Rich Notifications**       | Standardized        | `showSuccess("Saved!", { action: {...} })`                      |
| **High-frequency ops**       | Optimized           | `showOptimizedSuccess("Auto-saved", { category: 'AUTO_SAVE' })` |
| **Persistent notifications** | Notification Center | `addNotification({ type: 'info', ... })`                        |
| **Confirmations**            | Alert Dialogs       | `<AlertDialog>`                                                 |

### **✅ Standardized Import Patterns**

```typescript
// ✅ PREFERRED: Simple notifications
import { toast } from "sonner";

// ✅ GOOD: Rich notifications with options
import { showSuccess, showError, promiseToast } from "@/lib/notifications";

// ✅ ADVANCED: High-frequency operations
import { showOptimizedSuccess } from "@/lib/optimized-notifications";

// ✅ PERSISTENT: Long-term notifications
import { useNotifications } from "@/contexts/NotificationContext";

// ✅ CONFIRMATIONS: Blocking dialogs
import { AlertDialog } from "@/components/ui/alert-dialog";
```

---

## 🚀 Migration Plan

### **Phase 1: Foundation Setup (1-2 hours)**

#### **1.1 Remove Duplicate Toast Systems**

- [ ] Remove legacy `useToast` hook from App.tsx
- [ ] Remove `<Toaster />` component (keep only `<Sonner />`)
- [ ] Update imports to use Sonner exclusively

#### **1.2 Enhance Standardized System**

- [ ] Extend `src/lib/notifications.ts` with missing features
- [ ] Add action button support
- [ ] Add promise toast helpers
- [ ] Add batch notification helpers

### **Phase 2: Legacy Migration (2-3 hours)**

#### **2.1 Migrate useToast Hook Usage**

**Files to migrate:**

- [ ] `src/pages/dashboard/components/FrequentPackages.tsx`
- [ ] `src/pages/calculations/components/shared/ExportPopup.tsx`
- [ ] `src/hooks/use-calculation-exports.tsx`
- [ ] `src/pages/clients/components/ClientFormDialog.tsx`
- [ ] `src/pages/clients/components/DeleteClientDialog.tsx`

**Migration Pattern:**

```typescript
// ❌ BEFORE (Legacy)
const { toast } = useToast();
toast({
  title: "Success",
  description: "Operation completed",
  variant: "destructive",
});

// ✅ AFTER (Standardized)
import { showSuccess, showError } from "@/lib/notifications";
showSuccess("Success", {
  description: "Operation completed",
});
// OR for errors:
showError("Error", {
  description: "Operation failed",
});
```

#### **2.2 Standardize Direct Sonner Usage**

**Files to update:**

- [ ] `src/pages/admin/packages/components/form/PackageFormDialog.tsx`
- [ ] `src/pages/auth/components/forms/SignUpForm.tsx`
- [ ] `src/pages/admin/cities/components/list/CityList.tsx`
- [ ] `src/contexts/AuthContext.tsx`
- [ ] `src/pages/admin/templates/TemplatesPage.tsx`

**Migration Pattern:**

```typescript
// ❌ BEFORE (Inconsistent)
import { toast } from "sonner";
toast.success("Message");

// ✅ AFTER (Standardized)
import { showSuccess } from "@/lib/notifications";
showSuccess("Message");
```

### **Phase 3: Enhancement & Optimization (1-2 hours)**

#### **3.1 Implement Advanced Features**

- [ ] Add notification batching for bulk operations
- [ ] Add undo functionality for destructive actions
- [ ] Add notification persistence for critical messages
- [ ] Add notification analytics/tracking

#### **3.2 Update Documentation**

- [ ] Create notification usage guidelines
- [ ] Update component documentation
- [ ] Add examples for each notification type
- [ ] Create troubleshooting guide

---

## 📝 Implementation Details

### **Enhanced Standardized Notifications**

```typescript
// src/lib/notifications.ts - Enhanced version
export const showSuccess = (message: string, options?: NotificationOptions) => {
  return toast.success(message, {
    duration: options?.duration || 5000,
    description: options?.description,
    action: options?.action,
    id: options?.id,
  });
};

// New: Batch notifications
export const showBatchSuccess = (messages: string[]) => {
  const batchId = `batch-${Date.now()}`;
  return toast.success(`${messages.length} operations completed`, {
    description: messages.join(", "),
    id: batchId,
  });
};

// New: Undo notifications
export const showUndoableSuccess = (
  message: string,
  undoAction: () => void,
  options?: NotificationOptions
) => {
  return toast.success(message, {
    ...options,
    action: {
      label: "Undo",
      onClick: undoAction,
    },
  });
};
```

### **Migration Scripts**

#### **Script 1: Remove Legacy useToast**

```bash
# Find all useToast usage
grep -r "useToast" src/ --include="*.tsx" --include="*.ts"

# Find all toast({ usage
grep -r "toast({" src/ --include="*.tsx" --include="*.ts"
```

#### **Script 2: Update Imports**

```bash
# Find direct sonner imports
grep -r "from 'sonner'" src/ --include="*.tsx" --include="*.ts"

# Find toast.success/error usage
grep -r "toast\.(success|error|info|warning)" src/ --include="*.tsx" --include="*.ts"
```

---

## ✅ Success Criteria

### **Consistency Metrics**

- [ ] **100%** of notifications use standardized patterns
- [ ] **0** legacy `useToast` usage remaining
- [ ] **1** primary toast system (Sonner)
- [ ] **Clear** documentation for all notification types

### **Performance Improvements**

- [ ] **Reduced** bundle size (remove duplicate toast systems)
- [ ] **Improved** UX consistency
- [ ] **Better** accessibility support
- [ ] **Enhanced** developer experience

### **Quality Assurance**

- [ ] All notification types tested
- [ ] No console errors or warnings
- [ ] Consistent styling across all notifications
- [ ] Proper TypeScript types for all patterns

---

## 🔧 Testing Strategy

### **Manual Testing Checklist**

- [ ] Success notifications display correctly
- [ ] Error notifications show appropriate styling
- [ ] Action buttons work as expected
- [ ] Notifications dismiss properly
- [ ] Multiple notifications stack correctly
- [ ] Accessibility features work (screen readers)

### **Automated Testing**

- [ ] Unit tests for notification functions
- [ ] Integration tests for notification flows
- [ ] Visual regression tests for notification styling
- [ ] Performance tests for high-frequency notifications

---

## 📚 Documentation Updates

### **Developer Guidelines**

- [ ] When to use each notification type
- [ ] Import patterns and examples
- [ ] Best practices for notification content
- [ ] Accessibility considerations

### **Component Documentation**

- [ ] API reference for all notification functions
- [ ] Migration guide from legacy patterns
- [ ] Troubleshooting common issues
- [ ] Performance optimization tips

---

---

## 📋 Detailed File Migration Checklist

### **🔴 HIGH PRIORITY: Legacy useToast Migrations**

#### **File 1: `src/pages/dashboard/components/FrequentPackages.tsx`**

- **Current**: `const { toast } = useToast();`
- **Target**: `import { showSuccess } from '@/lib/notifications';`
- **Lines**: 49, 57-60
- **Complexity**: Low

#### **File 2: `src/pages/calculations/components/shared/ExportPopup.tsx`**

- **Current**: `const { toast } = useToast();`
- **Target**: `import { showSuccess, showError } from '@/lib/notifications';`
- **Lines**: 26, 90-95, 99-104
- **Complexity**: Medium (multiple toast calls)

#### **File 3: `src/hooks/use-calculation-exports.tsx`**

- **Current**: `const { toast } = useToast();`
- **Target**: `import { showSuccess, showError } from '@/lib/notifications';`
- **Lines**: 31, 161-165, 175-181, 203-206
- **Complexity**: Medium (status change notifications)

#### **File 4: `src/pages/clients/components/ClientFormDialog.tsx`**

- **Current**: `const { toast } = useToast();`
- **Target**: `import { showSuccess, showError } from '@/lib/notifications';`
- **Lines**: 66-69, 81-86
- **Complexity**: Low

#### **File 5: `src/pages/clients/components/DeleteClientDialog.tsx`**

- **Current**: `const { toast } = useToast();`
- **Target**: `import { showSuccess, showError } from '@/lib/notifications';`
- **Lines**: Similar pattern to ClientFormDialog
- **Complexity**: Low

### **🟡 MEDIUM PRIORITY: Direct Sonner Standardization**

#### **File 6: `src/pages/admin/packages/components/form/PackageFormDialog.tsx`**

- **Current**: `import { toast } from 'sonner';`
- **Target**: `import { showSuccess, showError, showLoading } from '@/lib/notifications';`
- **Lines**: 217-218, 226-228
- **Complexity**: Medium (loading states)

#### **File 7: `src/pages/auth/components/forms/SignUpForm.tsx`**

- **Current**: `toast.success()`, `toast.error()`
- **Target**: `showSuccess()`, `showError()`
- **Lines**: 28-30, 34
- **Complexity**: Low

#### **File 8: `src/pages/admin/cities/components/list/CityList.tsx`**

- **Current**: `toast.success()`, `toast.error()`
- **Target**: `showSuccess()`, `showError()`
- **Lines**: 62, 71, 73
- **Complexity**: Low

#### **File 9: `src/contexts/AuthContext.tsx`**

- **Current**: `toast.success()`, `toast.error()`
- **Target**: `showSuccess()`, `showError()`
- **Lines**: 709-711, 715
- **Complexity**: Low

#### **File 10: `src/pages/admin/templates/TemplatesPage.tsx`**

- **Current**: `toast.error()`
- **Target**: `showError()`
- **Lines**: 112
- **Complexity**: Low

### **🟢 LOW PRIORITY: Cleanup & Enhancement**

#### **File 11: `src/App.tsx`**

- **Action**: Remove `<Toaster />` import and component
- **Keep**: `<Sonner />` only
- **Lines**: 1, 59
- **Complexity**: Low

#### **File 12: `src/components/ui/use-toast.ts`**

- **Action**: Mark as deprecated or remove
- **Add**: Migration notice
- **Complexity**: Low

#### **File 13: `src/components/ui/toaster.tsx`**

- **Action**: Mark as deprecated or remove
- **Add**: Migration notice
- **Complexity**: Low

### **⚡ ADVANCED: Optimization Opportunities**

#### **File 14: `src/pages/calculations/hooks/utils/useSmartNotifications.ts`**

- **Current**: Custom hook with direct toast calls
- **Target**: Integrate with optimized notifications
- **Complexity**: High (requires refactoring)

#### **File 15: `src/lib/optimized-notifications.ts`**

- **Action**: Enhance with new categories
- **Add**: Export notifications, batch operations
- **Complexity**: Medium

---

## 🛠️ Implementation Order

### **Phase 1: Foundation (Day 1)**

1. ✅ Enhance `src/lib/notifications.ts`
2. ✅ Remove duplicate toaster from `src/App.tsx`
3. ✅ Add deprecation notices to legacy files

### **Phase 2: High Priority Migrations (Day 1-2)**

4. ✅ Migrate `FrequentPackages.tsx`
5. ✅ Migrate `ExportPopup.tsx`
6. ✅ Migrate `use-calculation-exports.tsx`
7. ✅ Migrate `ClientFormDialog.tsx`
8. ✅ Migrate `DeleteClientDialog.tsx`

### **Phase 3: Medium Priority Standardization (Day 2)**

9. ✅ Standardize `PackageFormDialog.tsx`
10. ✅ Standardize `SignUpForm.tsx`
11. ✅ Standardize `CityList.tsx`
12. ✅ Standardize `AuthContext.tsx`
13. ✅ Standardize `TemplatesPage.tsx`

### **Phase 4: Cleanup & Testing (Day 3)**

14. ✅ Remove legacy components
15. ✅ Update documentation
16. ✅ Run comprehensive testing
17. 🔄 Implement advanced optimizations (In Progress)

---

**Estimated Total Time: 4-7 hours**
**Priority: High (Improves consistency and maintainability)**
**Risk Level: Low (Non-breaking changes with fallbacks)**
