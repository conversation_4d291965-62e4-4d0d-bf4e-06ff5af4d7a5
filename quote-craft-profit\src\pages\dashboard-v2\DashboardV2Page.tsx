import React from "react";
import MainLayout from "@/components/layout/MainLayout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { WizardContainer } from "./components";

const DashboardV2Page: React.FC = () => {
  return (
    <MainLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
              Quick Event Setup
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Find the perfect template for your event in just a few steps
            </p>
          </div>
        </div>

        {/* Main Wizard */}
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-100 dark:border-blue-800">
          <CardHeader>
            <CardTitle className="text-xl text-blue-800 dark:text-blue-300">
              Event Setup Wizard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <WizardContainer />
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default DashboardV2Page;
