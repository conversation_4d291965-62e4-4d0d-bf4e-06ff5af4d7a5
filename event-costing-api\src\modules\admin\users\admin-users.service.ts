import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { AdminUserDto } from './dto/admin-user.dto';
import { RoleDto } from './dto/role.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { UpdateUserStatusDto } from './dto/update-user-status.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class AdminUsersService {
  private readonly logger = new Logger(AdminUsersService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Get all users with their profiles
   * @returns List of users with profile information
   */
  async findAll(): Promise<AdminUserDto[]> {
    this.logger.log('Getting all users with profiles');

    const supabase = this.supabaseService.getClient();

    try {
      // Get all users from auth.admin API
      const { data: authUsers, error: authError } =
        await supabase.auth.admin.listUsers();

      if (authError) {
        this.logger.error(
          `Error fetching auth users: ${authError.message}`,
          authError.stack,
        );
        throw authError;
      }

      if (!authUsers || !authUsers.users || authUsers.users.length === 0) {
        return [];
      }

      // Get all profiles in one query
      const userIds = authUsers.users.map(user => user.id);
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select(
          `
          id,
          username,
          full_name,
          profile_picture_url,
          role_id
        `,
        )
        .in('id', userIds);

      // Get all roles
      const { data: roles, error: rolesError } = await supabase
        .from('roles')
        .select('*');

      if (rolesError) {
        this.logger.error(
          `Error fetching roles: ${rolesError.message}`,
          rolesError.stack,
        );
        throw rolesError;
      }

      // Create a map of roles by ID for quick lookup
      const rolesMap = new Map();
      roles?.forEach(role => {
        rolesMap.set(role.id, role);
      });

      if (profilesError) {
        this.logger.error(
          `Error fetching profiles: ${profilesError.message}`,
          profilesError.stack,
        );
        throw profilesError;
      }

      // Create a map of profiles by user ID for quick lookup
      const profilesMap = new Map();
      profiles?.forEach(profile => {
        profilesMap.set(profile.id, profile);
      });

      // Transform the data to match the DTO
      return authUsers.users.map(user => {
        const profile = profilesMap.get(user.id);
        // Check if user is banned using user metadata or app metadata
        const isBanned =
          (user.user_metadata && user.user_metadata.banned) ||
          (user.app_metadata && user.app_metadata.banned) ||
          false;

        return {
          id: user.id,
          email: user.email || '',
          username: profile?.username || null,
          full_name: profile?.full_name || null,
          role_name: profile?.role_id
            ? rolesMap.get(profile.role_id)?.role_name || null
            : null,
          created_at: user.created_at,
          last_sign_in_at: user.last_sign_in_at || null,
          profile_picture_url: profile?.profile_picture_url || null,
          status: isBanned ? 'INACTIVE' : 'ACTIVE',
        };
      });
    } catch (error) {
      this.logger.error(`Error in findAll: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a user by ID
   * @param id User ID
   * @returns User with profile information
   */
  async findOne(id: string): Promise<AdminUserDto> {
    this.logger.log(`Getting user with ID: ${id}`);

    const supabase = this.supabaseService.getClient();

    try {
      // Get user from auth.admin API
      const { data: userData, error: userError } =
        await supabase.auth.admin.getUserById(id);

      if (userError) {
        this.logger.error(
          `Error fetching auth user: ${userError.message}`,
          userError.stack,
        );
        throw userError;
      }

      if (!userData || !userData.user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Get profile data
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select(
          `
          username,
          full_name,
          profile_picture_url,
          role_id
        `,
        )
        .eq('id', id)
        .single();

      // Get role data if profile has a role_id
      let roleName = null;
      if (profile?.role_id) {
        const { data: role, error: roleError } = await supabase
          .from('roles')
          .select('role_name')
          .eq('id', profile.role_id)
          .single();

        if (roleError && roleError.code !== 'PGRST116') {
          this.logger.error(
            `Error fetching role: ${roleError.message}`,
            roleError.stack,
          );
        } else if (role) {
          roleName = role.role_name;
        }
      }

      if (profileError && profileError.code !== 'PGRST116') {
        // PGRST116 is "no rows returned" error
        this.logger.error(
          `Error fetching profile: ${profileError.message}`,
          profileError.stack,
        );
        throw profileError;
      }

      // Check if user is banned using user metadata or app metadata
      const isBanned =
        (userData.user.user_metadata && userData.user.user_metadata.banned) ||
        (userData.user.app_metadata && userData.user.app_metadata.banned) ||
        false;

      // Transform the data to match the DTO
      return {
        id: userData.user.id,
        email: userData.user.email || '',
        username: profile?.username || null,
        full_name: profile?.full_name || null,
        role_name: roleName,
        created_at: userData.user.created_at,
        last_sign_in_at: userData.user.last_sign_in_at || null,
        profile_picture_url: profile?.profile_picture_url || null,
        status: isBanned ? 'INACTIVE' : 'ACTIVE',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error in findOne: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update a user's role
   * @param id User ID
   * @param updateUserRoleDto DTO with role ID
   */
  async updateRole(
    id: string,
    updateUserRoleDto: UpdateUserRoleDto,
  ): Promise<void> {
    this.logger.log(
      `Updating role for user ${id} to role ID ${updateUserRoleDto.roleId}`,
    );

    const supabase = this.supabaseService.getClient();

    // Update the role_id in the profiles table
    const { error } = await supabase
      .from('profiles')
      .update({ role_id: updateUserRoleDto.roleId })
      .eq('id', id);

    if (error) {
      this.logger.error(
        `Error updating user role: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update a user's status
   * @param id User ID
   * @param updateUserStatusDto DTO with status
   */
  async updateStatus(
    id: string,
    updateUserStatusDto: UpdateUserStatusDto,
  ): Promise<void> {
    this.logger.log(
      `Updating status for user ${id} to ${updateUserStatusDto.status}`,
    );

    const supabase = this.supabaseService.getClient();

    try {
      // If status is INACTIVE, set banned to true in user metadata
      // If status is ACTIVE, set banned to false in user metadata
      const isBanned = updateUserStatusDto.status === 'INACTIVE';

      const { error } = await supabase.auth.admin.updateUserById(id, {
        user_metadata: { banned: isBanned },
      });

      if (error) {
        this.logger.error(
          `Error updating user status: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    } catch (error) {
      this.logger.error(`Error in updateStatus: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all roles
   * @returns List of roles
   */
  async getRoles(): Promise<RoleDto[]> {
    this.logger.log('Getting all roles');

    const supabase = this.supabaseService.getClient();

    // Query roles table
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .order('id');

    if (error) {
      this.logger.error(`Error fetching roles: ${error.message}`, error.stack);
      throw error;
    }

    return data as RoleDto[];
  }

  /**
   * Create a new user
   * @param createUserDto DTO with user data
   * @returns Created user
   */
  async createUser(createUserDto: CreateUserDto): Promise<AdminUserDto> {
    this.logger.log(`Creating new user with email: ${createUserDto.email}`);

    const supabase = this.supabaseService.getClient();

    try {
      // Create user in auth.users
      const { data: authData, error: authError } =
        await supabase.auth.admin.createUser({
          email: createUserDto.email,
          password: createUserDto.password || undefined,
          email_confirm: true,
          user_metadata: {
            full_name: createUserDto.full_name,
            username: createUserDto.username,
          },
        });

      if (authError) {
        this.logger.error(
          `Error creating user in auth: ${authError.message}`,
          authError.stack,
        );
        throw authError;
      }

      if (!authData.user) {
        throw new BadRequestException('Failed to create user');
      }

      // Update role in profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ role_id: createUserDto.role_id })
        .eq('id', authData.user.id);

      if (profileError) {
        this.logger.error(
          `Error updating user role: ${profileError.message}`,
          profileError.stack,
        );
        throw profileError;
      }

      // Return the created user
      return this.findOne(authData.user.id);
    } catch (error) {
      if (error.code === '23505') {
        throw new ConflictException('User with this email already exists');
      }
      throw error;
    }
  }

  /**
   * Update a user
   * @param id User ID
   * @param updateUserDto DTO with user data
   * @returns Updated user
   */
  async updateUser(
    id: string,
    updateUserDto: UpdateUserDto,
  ): Promise<AdminUserDto> {
    this.logger.log(`Updating user with ID: ${id}`);

    const supabase = this.supabaseService.getClient();

    try {
      // Check if user exists
      const user = await this.findOne(id);

      // Update auth user if email or password changed
      if (updateUserDto.email || updateUserDto.password) {
        const { error: authError } = await supabase.auth.admin.updateUserById(
          id,
          {
            email: updateUserDto.email,
            password: updateUserDto.password,
          },
        );

        if (authError) {
          this.logger.error(
            `Error updating user in auth: ${authError.message}`,
            authError.stack,
          );
          throw authError;
        }
      }

      // Update profile data
      const profileData: any = {};

      if (updateUserDto.full_name) {
        profileData.full_name = updateUserDto.full_name;
      }

      if (updateUserDto.username) {
        profileData.username = updateUserDto.username;
      }

      if (updateUserDto.role_id) {
        profileData.role_id = updateUserDto.role_id;
      }

      if (Object.keys(profileData).length > 0) {
        const { error: profileError } = await supabase
          .from('profiles')
          .update(profileData)
          .eq('id', id);

        if (profileError) {
          this.logger.error(
            `Error updating user profile: ${profileError.message}`,
            profileError.stack,
          );
          throw profileError;
        }
      }

      // Return the updated user
      return this.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      if (error.code === '23505') {
        throw new ConflictException('Username or email already exists');
      }

      throw error;
    }
  }
}

