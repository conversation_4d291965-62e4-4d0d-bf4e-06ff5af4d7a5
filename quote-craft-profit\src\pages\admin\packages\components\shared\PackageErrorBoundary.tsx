import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error boundary specifically for package management components
 * Provides graceful error handling and recovery options
 */
export class PackageErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Package Error Boundary caught an error:', error, errorInfo);
    
    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Card className='max-w-md mx-auto mt-8'>
          <CardHeader className='text-center'>
            <div className='mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4'>
              <AlertCircle className='w-6 h-6 text-red-600' />
            </div>
            <CardTitle className='text-red-900'>Something went wrong</CardTitle>
            <CardDescription>
              An error occurred while loading the package management interface.
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            {this.state.error && (
              <details className='text-sm text-gray-600 bg-gray-50 p-3 rounded border'>
                <summary className='cursor-pointer font-medium'>Error Details</summary>
                <pre className='mt-2 whitespace-pre-wrap text-xs'>
                  {this.state.error.message}
                </pre>
              </details>
            )}
            <div className='flex gap-2 justify-center'>
              <Button onClick={this.handleRetry} className='flex items-center gap-2'>
                <RefreshCw className='w-4 h-4' />
                Try Again
              </Button>
              <Button 
                variant='outline' 
                onClick={() => window.location.href = '/admin/packages'}
              >
                Go to Packages
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based error boundary wrapper for functional components
 */
export const withPackageErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  return (props: P) => (
    <PackageErrorBoundary fallback={fallback}>
      <Component {...props} />
    </PackageErrorBoundary>
  );
};
