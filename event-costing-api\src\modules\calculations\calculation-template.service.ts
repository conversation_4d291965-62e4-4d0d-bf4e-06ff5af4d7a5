import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import {
  PackageSelectionItem,
  TemplateData,
} from './interfaces/calculation-internal.interfaces';
import { CreateCalculationFromTemplateDto } from './dto/create-calculation-from-template.dto';
import { CalculationItemsService } from '../calculation-items/calculation-items.service';
import { CalculationStatus } from './enums/calculation-status.enum';

@Injectable()
export class CalculationTemplateService {
  private readonly logger = new Logger(CalculationTemplateService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly calculationItemsService: CalculationItemsService,
  ) {}

  async createFromTemplate(
    templateId: string,
    customization: CreateCalculationFromTemplateDto,
    user: User,
  ): Promise<string> {
    this.logger.log(
      `User ${user.id} starting calculation creation from template ${templateId} with customization: ${JSON.stringify(customization)}`,
    );
    const supabase = this.supabaseService.getClient();

    // 1. Fetch Template Data
    const { data: templateData, error: templateError } = await supabase
      .from('templates')
      .select('*') // Select all needed fields
      .eq('id', templateId)
      .or(`is_public.eq.true,created_by.eq.${user.id}`)
      .maybeSingle<TemplateData>();

    if (templateError) {
      this.logger.error(
        `Error fetching template ${templateId} for user ${user.id}: ${templateError.message}`,
        templateError.stack,
      );
      throw new InternalServerErrorException(
        'Failed to fetch template details.',
      );
    }

    if (!templateData) {
      this.logger.warn(
        `Template ${templateId} not found or user ${user.id} lacks access.`,
      );
      throw new NotFoundException(
        `Template with ID ${templateId} not found or access denied.`,
      );
    }

    // 2. Prepare New Calculation Data
    const packageSelections: PackageSelectionItem[] =
      templateData.package_selections || [];

    const calculationData = {
      name: customization.name, // Use customized name
      currency_id: templateData.currency_id,
      city_id: customization.cityId || templateData.city_id, // Use customized city or template city
      client_id: customization.clientId || null, // Use customized client
      event_id: customization.eventId || null, // Use customized event
      event_start_date: customization.eventStartDate || templateData.template_start_date,
      event_end_date: customization.eventEndDate || templateData.template_end_date,
      attendees: customization.attendees || templateData.attendees,
      event_type: templateData.event_type,
      notes: customization.notes || `Based on template: ${templateData.template_name}`,
      created_by: user.id,
      status: CalculationStatus.DRAFT,
      subtotal: 0,
      taxes: templateData.taxes || null,
      discount: templateData.discount || null,
      total: 0,
      total_cost: 0,
      estimated_profit: 0,
    };

    // 3. Create Calculation History Record
    const { data: newCalc, error: createError } = await supabase
      .from('calculation_history')
      .insert(calculationData)
      .select('id')
      .single<{ id: string }>();

    if (createError || !newCalc) {
      this.logger.error(
        `Failed to create calculation history from template ${templateId}: ${createError?.message}`,
        createError?.stack,
      );
      throw new InternalServerErrorException(
        'Could not create calculation record.',
      );
    }

    const newCalculationId: string = newCalc.id;
    this.logger.log(
      `Created calculation ${newCalculationId} from template ${templateId}`,
    );

    // 4. Handle Venues (from customization or template)
    let venueIds: string[] = [];

    if (customization.venueIds && customization.venueIds.length > 0) {
      // Use venues from customization
      venueIds = customization.venueIds;
      this.logger.log(
        `Using ${venueIds.length} venue IDs from customization for calculation ${newCalculationId}`,
      );
    } else {
      // Fetch venues from template
      const { data: templateVenues, error: venueError } = await supabase
        .from('template_venues')
        .select('venue_id')
        .eq('template_id', templateId);

      if (venueError) {
        this.logger.error(
          `Error fetching venues for template ${templateId}: ${venueError.message}`,
          venueError.stack,
        );
        // Continue without venues if there's an error
      } else if (templateVenues && templateVenues.length > 0) {
        venueIds = templateVenues.map(tv => tv.venue_id);
        this.logger.log(
          `Using ${venueIds.length} venue IDs from template for calculation ${newCalculationId}`,
        );
      }
    }

    // Insert venues into calculation_venues if any
    if (venueIds.length > 0) {
      const venuePayloads = venueIds.map(venueId => ({
        calculation_id: newCalculationId,
        venue_id: venueId,
      }));

      const { error: venueInsertError } = await supabase
        .from('calculation_venues')
        .insert(venuePayloads);

      if (venueInsertError) {
        this.logger.error(
          `Error adding venues to calculation ${newCalculationId}: ${venueInsertError.message}`,
          venueInsertError.stack,
        );
        // Continue anyway, we've already created the calculation
      } else {
        this.logger.log(
          `Successfully added ${venueIds.length} venues to calculation ${newCalculationId}`,
        );
      }
    }

    // 5. Populate Line Items using CalculationItemsService
    try {
      await this.calculationItemsService.populateItemsFromTemplateBlueprint(
        newCalculationId,
        templateData.currency_id,
        packageSelections,
        user,
      );
      this.logger.log(
        `Successfully populated items for calculation ${newCalculationId}.`,
      );
    } catch (itemError: unknown) {
      const errorMessage: string =
        itemError instanceof Error ? itemError.message : String(itemError);
      const errorStack: string | undefined =
        itemError instanceof Error ? itemError.stack : undefined;
      this.logger.error(
        `Failed to populate line items for calculation ${newCalculationId} from template ${templateId}. Error originating from CalculationItemsService: ${errorMessage}`,
        errorStack,
      );
      // Allow creation to succeed but log error. Consider cleanup/status update?
    }

    // 6. Trigger Recalculation (Important after populating items)
    try {
      this.logger.log(
        `Attempting to trigger recalculation for calculation ID: ${newCalculationId}`,
      );
      const { error: rpcError } = await supabase.rpc(
        'recalculate_calculation_totals',
        { p_calculation_id: newCalculationId },
      );

      if (rpcError) {
        this.logger.error(
          `Error calling recalculate_calculation_totals RPC for calculation ${newCalculationId}: ${rpcError.message}`,
          rpcError.stack,
        );
        // Log error, but don't throw, as the main creation succeeded.
      } else {
        this.logger.log(
          `Successfully triggered recalculation for calculation ID: ${newCalculationId}`,
        );
      }
    } catch (recalcError: unknown) {
      const errorMessage: string =
        recalcError instanceof Error
          ? recalcError.message
          : String(recalcError);
      const errorStack: string | undefined =
        recalcError instanceof Error ? recalcError.stack : undefined;
      this.logger.error(
        `Recalculation RPC call failed unexpectedly for calculation ID: ${newCalculationId} - ${errorMessage}`,
        errorStack,
      );
      // Log error, but don't throw.
    }

    return newCalculationId;
  }
}
