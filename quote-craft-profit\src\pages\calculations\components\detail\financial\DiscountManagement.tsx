import React from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { DiscountDisplay } from "./DiscountDisplay";
import { DiscountForm } from "./DiscountForm";
import { Discount } from "../../../utils/calculationUtils";

interface DiscountManagementProps {
  discountObj: Discount;
  discountAmount: number;
  isAddingDiscount: boolean;
  newDiscountAmount: string;
  formatCurrency: (amount: number) => string;
  onDiscountAmountChange: (value: string) => void;
  onAddDiscount: () => void;
  onEditDiscount?: (value: number) => void; // New prop for editing
  onRemoveDiscount: () => void;
  onStartAddingDiscount: () => void;
  onCancelAddingDiscount: () => void;
}

/**
 * Discount management component
 * Orchestrates discount display and discount addition form
 */
export const DiscountManagement: React.FC<DiscountManagementProps> = ({
  discountObj,
  discountAmount,
  isAddingDiscount,
  newDiscountAmount,
  formatCurrency,
  onDiscountAmountChange,
  onAddDiscount,
  onEditDiscount,
  onRemoveDiscount,
  onStartAddingDiscount,
  onCancelAddingDiscount,
}) => {
  return (
    <>
      {/* Discount Display */}
      {discountObj.value > 0 && (
        <DiscountDisplay
          discountAmount={discountAmount}
          discountValue={discountObj.value}
          formatCurrency={formatCurrency}
          onEdit={onEditDiscount}
          onRemove={onRemoveDiscount}
        />
      )}

      {/* Add Discount Form or Button */}
      {discountObj.value === 0 && isAddingDiscount ? (
        <DiscountForm
          discountAmount={newDiscountAmount}
          onDiscountAmountChange={onDiscountAmountChange}
          onAdd={onAddDiscount}
          onCancel={onCancelAddingDiscount}
        />
      ) : (
        discountObj.value === 0 && (
          <div className="pb-2 border-b dark:border-gray-700">
            <Button
              variant="outline"
              size="sm"
              onClick={onStartAddingDiscount}
              className="w-full"
            >
              <Plus size={16} className="mr-1" /> Add Discount
            </Button>
          </div>
        )
      )}
    </>
  );
};
