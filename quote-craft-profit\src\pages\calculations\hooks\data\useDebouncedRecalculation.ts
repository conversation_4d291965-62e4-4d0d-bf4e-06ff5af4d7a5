import { useCallback, useRef, useMemo } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { recalculateTotalsWithSupabase } from "../../../../services/calculations";
import { QUERY_KEYS } from "@/lib/queryKeys";

/**
 * Configuration for debounced recalculation
 */
interface DebouncedRecalculationConfig {
  delay?: number; // Debounce delay in milliseconds
  maxWait?: number; // Maximum wait time before forcing execution
  enabled?: boolean; // Whether debouncing is enabled
}

/**
 * Custom hook for debounced recalculation to reduce server load
 * Batches multiple recalculation requests into a single call
 */
export const useDebouncedRecalculation = (
  calculationId: string,
  config: DebouncedRecalculationConfig = {}
) => {
  const {
    delay = 1000, // 1 second default delay
    maxWait = 5000, // 5 seconds maximum wait
    enabled = true,
  } = config;

  const queryClient = useQueryClient();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const maxWaitTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingRef = useRef(false);
  const lastCallTimeRef = useRef<number>(0);

  /**
   * Clear all pending timeouts
   */
  const clearTimeouts = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (maxWaitTimeoutRef.current) {
      clearTimeout(maxWaitTimeoutRef.current);
      maxWaitTimeoutRef.current = null;
    }
  }, []);

  /**
   * Execute the recalculation
   */
  const executeRecalculation = useCallback(async () => {
    if (!calculationId || pendingRef.current) {
      return;
    }

    try {
      pendingRef.current = true;
      console.log(
        `🔄 Executing debounced recalculation for calculation: ${calculationId}`
      );

      // Perform the recalculation
      await recalculateTotalsWithSupabase(calculationId);

      // Invalidate related queries to refresh UI using new hierarchical structure
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.detail(calculationId),
      });

      console.log(
        `✅ Debounced recalculation completed for calculation: ${calculationId}`
      );
    } catch (error) {
      console.error(
        `❌ Error in debounced recalculation for calculation ${calculationId}:`,
        error
      );
    } finally {
      pendingRef.current = false;
      lastCallTimeRef.current = Date.now();
      clearTimeouts();
    }
  }, [calculationId, queryClient, clearTimeouts]);

  /**
   * Debounced recalculation function
   */
  const debouncedRecalculate = useCallback(() => {
    if (!enabled || !calculationId) {
      console.log(
        `⏭️ Debounced recalculation skipped (enabled: ${enabled}, calculationId: ${calculationId})`
      );
      return;
    }

    const now = Date.now();
    const timeSinceLastCall = now - lastCallTimeRef.current;

    console.log(
      `⏰ Debounced recalculation requested for calculation: ${calculationId} (delay: ${delay}ms)`
    );

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set up new timeout
    timeoutRef.current = setTimeout(() => {
      executeRecalculation();
    }, delay);

    // Set up max wait timeout if not already set and we haven't exceeded max wait time
    if (!maxWaitTimeoutRef.current && timeSinceLastCall < maxWait) {
      const remainingMaxWait = maxWait - timeSinceLastCall;
      console.log(`⏳ Setting max wait timeout: ${remainingMaxWait}ms`);

      maxWaitTimeoutRef.current = setTimeout(() => {
        console.log(
          `⚡ Max wait time reached, forcing recalculation for calculation: ${calculationId}`
        );
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
        executeRecalculation();
      }, remainingMaxWait);
    }
  }, [enabled, calculationId, delay, maxWait, executeRecalculation]);

  /**
   * Force immediate recalculation (bypasses debouncing)
   */
  const recalculateNow = useCallback(async () => {
    console.log(
      `🚀 Forcing immediate recalculation for calculation: ${calculationId}`
    );
    clearTimeouts();
    await executeRecalculation();
  }, [calculationId, executeRecalculation, clearTimeouts]);

  /**
   * Cancel any pending recalculation
   */
  const cancelPendingRecalculation = useCallback(() => {
    console.log(
      `🛑 Cancelling pending recalculation for calculation: ${calculationId}`
    );
    clearTimeouts();
  }, [calculationId, clearTimeouts]);

  /**
   * Get the current state of debounced recalculation
   */
  const getState = useCallback(() => {
    return {
      isPending: !!timeoutRef.current || !!maxWaitTimeoutRef.current,
      isExecuting: pendingRef.current,
      lastExecutionTime: lastCallTimeRef.current,
      timeSinceLastExecution: Date.now() - lastCallTimeRef.current,
    };
  }, []);

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    clearTimeouts();
    pendingRef.current = false;
  }, [clearTimeouts]);

  return {
    // Core functions
    debouncedRecalculate,
    recalculateNow,
    cancelPendingRecalculation,

    // State
    getState,

    // Utilities
    cleanup,

    // Configuration
    config: {
      delay,
      maxWait,
      enabled,
    },
  };
};

/**
 * Hook for managing recalculation across multiple calculations
 * Useful for batch operations or when working with multiple calculations
 *
 * Note: Due to React Hook rules, this hook provides utility functions
 * but the actual hook instances should be managed at the component level.
 */
export const useMultiCalculationRecalculation = (calculationIds: string[]) => {
  // Create a stable reference for the calculation IDs to avoid infinite re-renders
  const stableCalculationIds = useMemo(() => calculationIds, [calculationIds]);

  const queryClient = useQueryClient();

  const recalculateAll = useCallback(async () => {
    console.log(
      `🔄 Recalculating all calculations: ${stableCalculationIds.join(", ")}`
    );

    // Directly call the recalculation service for each calculation
    await Promise.all(
      stableCalculationIds.map(async (id) => {
        try {
          await recalculateTotalsWithSupabase(id);
          queryClient.invalidateQueries({
            queryKey: QUERY_KEYS.calculation(id),
          });
        } catch (error) {
          console.error(`Failed to recalculate calculation ${id}:`, error);
        }
      })
    );
  }, [stableCalculationIds, queryClient]);

  const cancelAll = useCallback(() => {
    console.log(
      `🛑 Cancelling all pending recalculations for calculations: ${stableCalculationIds.join(
        ", "
      )}`
    );
    // This would need to be implemented with a global cancellation mechanism
  }, [stableCalculationIds]);

  return {
    recalculateAll,
    cancelAll,
    calculationIds: stableCalculationIds,
  };
};
