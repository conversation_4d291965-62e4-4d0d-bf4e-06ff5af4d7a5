import React, { memo, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CalculationHeaderProps {
  name: string;
  status: string;
}

const CalculationHeader: React.FC<CalculationHeaderProps> = memo(
  ({ name, status }) => {
    const navigate = useNavigate();

    // Memoize the back navigation handler to prevent function recreation
    const handleBackClick = useCallback(() => {
      navigate("/calculations");
    }, [navigate]);

    // Memoize the status styling to prevent recalculation on every render
    const statusClassName = useMemo(() => {
      const baseClasses = "px-3 py-1 text-sm rounded-full";

      switch (status) {
        case "draft":
          return `${baseClasses} bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300`;
        case "completed":
          return `${baseClasses} bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300`;
        default:
          return `${baseClasses} bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300`;
      }
    }, [status]);

    // Memoize the formatted status text
    const formattedStatus = useMemo(() => {
      return status.charAt(0).toUpperCase() + status.slice(1);
    }, [status]);

    return (
      <div className="mb-6">
        <Button variant="ghost" size="sm" onClick={handleBackClick}>
          <ChevronLeft size={16} />
          <span>Back to Calculations</span>
        </Button>
        <div className="flex justify-between items-center mt-2">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            {name}
          </h1>
          <span className={statusClassName}>{formattedStatus}</span>
        </div>
      </div>
    );
  }
);

// Add display name for better debugging
CalculationHeader.displayName = "CalculationHeader";

export default CalculationHeader;
