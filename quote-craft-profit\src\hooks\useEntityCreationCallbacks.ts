import { useQueryClient } from "@tanstack/react-query";
import { UseFormReturn } from "react-hook-form";
import { Client } from "@/types/types";
import { Event } from "@/types/events";
import { useCacheCoordinator } from "./useCacheCoordinator";
import { QUERY_KEYS } from "@/lib/queryKeys";

interface EntityCreationCallbacksConfig {
  form: UseFormReturn<any>;
  clientFieldName?: string; // 'client_id' or 'clientId'
  eventFieldName?: string; // 'event_id' or 'eventId'
}

/**
 * Centralized hook for handling entity creation callbacks
 * Provides consistent cache invalidation, data refetching, and form updates
 */
export function useEntityCreationCallbacks({
  form,
  clientFieldName = "client_id",
  eventFieldName = "event_id",
}: EntityCreationCallbacksConfig) {
  const queryClient = useQueryClient();
  const { invalidateWithRelationships } = useCacheCoordinator();

  /**
   * CACHE OPTIMIZATION: Replaced manual delays with coordinated cache invalidation
   * Uses CacheCoordinator to prevent race conditions and ensure consistency
   */
  const invalidateAndRefetch = async (domain: string, id: string = "all") => {
    console.log(`🗑️ Invalidating ${domain} cache using coordinator...`);

    // Use cache coordinator for relationship-aware invalidation
    await invalidateWithRelationships(domain, id, "create");

    console.log(`✅ ${domain} cache invalidated successfully`);
  };

  /**
   * Handle client creation callback
   */
  const handleClientCreated = async (newClient: Client) => {
    console.log("🔄 Client created callback triggered:", newClient);

    try {
      // Step 1: Invalidate cache using coordinator (no manual delays needed)
      await invalidateAndRefetch("client", newClient.id);

      // Step 2: Verify the new client is in the updated data using new query keys
      const updatedClientsResult = queryClient.getQueryData(
        QUERY_KEYS.clients.all()
      ) as any;
      console.log("📊 Raw clients data structure:", updatedClientsResult);

      // getAllClients returns PaginatedResult<Client> with structure: { data: Client[], totalCount, page, pageSize, totalPages }
      const updatedClients = updatedClientsResult?.data || [];
      console.log("📋 Extracted clients array:", updatedClients);

      const foundClient = updatedClients.find(
        (client: any) => client.id === newClient.id
      );
      console.log("🔍 Looking for client in updated data:", {
        newClientId: newClient.id,
        foundClient,
        totalClients: updatedClients.length,
        allClientIds: updatedClients.map((c: any) => c.id),
      });

      // Step 3: Set the form value using the provided field name
      console.log(`📝 Setting form value ${clientFieldName} to:`, newClient.id);
      form.setValue(clientFieldName, newClient.id);

      // Step 4: Verify the form value was set
      const currentFormValue = form.getValues(clientFieldName);
      console.log(`✅ Form value ${clientFieldName} set to:`, currentFormValue);

      // Step 5: Force a re-render by triggering form validation
      form.trigger(clientFieldName);
    } catch (error) {
      console.error("❌ Error in client creation callback:", error);
    }
  };

  /**
   * Handle event creation callback
   */
  const handleEventCreated = async (newEvent: Event) => {
    console.log("🔄 Event created callback triggered:", newEvent);

    try {
      // Step 1: Invalidate cache using coordinator (no manual delays needed)
      await invalidateAndRefetch("event", newEvent.id);

      // Step 2: Verify the new event is in the updated data using new query keys
      const updatedEvents = queryClient.getQueryData(
        QUERY_KEYS.events.all()
      ) as any[];
      console.log("📊 Raw events data structure:", updatedEvents);

      // getAllEvents returns Event[] directly (not paginated)
      const foundEvent = updatedEvents?.find(
        (event: any) => event.id === newEvent.id
      );
      console.log("🔍 Looking for event in updated data:", {
        newEventId: newEvent.id,
        foundEvent,
        totalEvents: updatedEvents?.length || 0,
        allEventIds: updatedEvents?.map((e: any) => e.id) || [],
      });

      // Step 3: Set the form value using the provided field name
      console.log(`📝 Setting form value ${eventFieldName} to:`, newEvent.id);
      form.setValue(eventFieldName, newEvent.id);

      // Step 4: Verify the form value was set
      const currentFormValue = form.getValues(eventFieldName);
      console.log(`✅ Form value ${eventFieldName} set to:`, currentFormValue);

      // Step 5: Force a re-render by triggering form validation
      form.trigger(eventFieldName);
    } catch (error) {
      console.error("❌ Error in event creation callback:", error);
    }
  };

  return {
    handleClientCreated,
    handleEventCreated,
  };
}
