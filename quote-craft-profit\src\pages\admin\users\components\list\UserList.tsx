import React from 'react';
import { UserWithProfile } from '@/services/shared/users';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Edit,
  MoreHorizontal,
  Shield,
  ShieldAlert,
  ShieldCheck,
  UserCog,
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import {
  updateUserRoleFromApi,
  updateUserStatusFromApi,
} from '@/services/shared/users';

interface UserListProps {
  users: UserWithProfile[];
  isLoading: boolean;
  onEditUser: (user: UserWithProfile) => void;
  roles: { id: number; role_name: string }[];
  refetchUsers: () => void;
}

export default function UserList({
  users,
  isLoading,
  onEditUser,
  roles,
  refetchUsers,
}: UserListProps) {
  // Helper functions
  const getRoleIcon = (roleName: string) => {
    switch (roleName.toLowerCase()) {
      case 'admin':
        return <ShieldAlert className='h-4 w-4 mr-1' />;
      case 'moderator':
        return <ShieldCheck className='h-4 w-4 mr-1' />;
      default:
        return <Shield className='h-4 w-4 mr-1' />;
    }
  };

  const getInitials = (name: string) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((part) => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const handleRoleChange = async (userId: string, roleId: number) => {
    try {
      await updateUserRoleFromApi(userId, roleId);
      toast.success('User role updated successfully');
      refetchUsers();
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update user role');
    }
  };

  const handleToggleStatus = async (userId: string, currentStatus: string) => {
    try {
      // Toggle the current status
      const newStatus = currentStatus === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';

      await updateUserStatusFromApi(userId, newStatus);
      toast.success(
        `User ${newStatus === 'ACTIVE' ? 'activated' : 'deactivated'} successfully`,
      );
      refetchUsers();
    } catch (error) {
      console.error('Error toggling user status:', error);
      toast.error('Failed to update user status');
    }
  };

  if (isLoading) {
    return (
      <div className='flex justify-center items-center h-48'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900'></div>
      </div>
    );
  }

  if (users.length === 0) {
    return (
      <div className='text-center p-8 text-muted-foreground'>
        No users found matching your search criteria.
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className='w-16'>#</TableHead>
          <TableHead>User</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Role</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Created</TableHead>
          <TableHead>Last Login</TableHead>
          <TableHead className='text-right'>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {users.map((user, index) => (
          <TableRow key={user.id}>
            <TableCell className='text-muted-foreground'>{index + 1}</TableCell>
            <TableCell>
              <div className='flex items-center'>
                <Avatar className='h-8 w-8 mr-2'>
                  <AvatarImage src={user.profile_picture_url ?? undefined} />
                  <AvatarFallback>{getInitials(user.full_name)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className='font-medium'>{user.full_name || 'Unnamed User'}</div>
                  <div className='text-xs text-muted-foreground'>@{user.username}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>{user.email}</TableCell>
            <TableCell>
              <Badge variant='outline' className='flex items-center w-fit'>
                {getRoleIcon(user.role_name)}
                {user.role_name}
              </Badge>
            </TableCell>
            <TableCell>
              <Badge
                variant={user.status === 'ACTIVE' ? 'success' : 'destructive'}
                className='capitalize'
              >
                {user.status.toLowerCase()}
              </Badge>
            </TableCell>
            <TableCell>{format(new Date(user.created_at), 'MMM d, yyyy')}</TableCell>
            <TableCell>
              {user.last_sign_in_at
                ? format(new Date(user.last_sign_in_at), 'MMM d, yyyy')
                : 'Never'}
            </TableCell>
            <TableCell className='text-right'>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                    <MoreHorizontal className='h-4 w-4' />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuItem onClick={() => onEditUser(user)}>
                    <Edit className='mr-2 h-4 w-4' />
                    Edit Details
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleToggleStatus(user.id, user.status)}
                  >
                    <UserCog className='mr-2 h-4 w-4' />
                    {user.status === 'ACTIVE' ? 'Deactivate User' : 'Activate User'}
                  </DropdownMenuItem>

                  {/* Role Change Options */}
                  {roles.map((role) => (
                    <DropdownMenuItem
                      key={role.id}
                      onClick={() => handleRoleChange(user.id, role.id)}
                      disabled={user.role_name === role.role_name}
                    >
                      {getRoleIcon(role.role_name)}
                      Make {role.role_name}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
