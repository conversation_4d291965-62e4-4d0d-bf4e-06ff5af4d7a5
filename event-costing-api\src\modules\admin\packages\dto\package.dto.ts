import {
  ApiProperty,
  ApiPropertyOptional,
  ApiExtraModels,
} from '@nestjs/swagger';

// Temporary enum definition - replace with actual import
enum PackageQuantityBasis {
  PER_EVENT = 'PER_EVENT',
  PER_DAY = 'PER_DAY',
  PER_ATTENDEE = 'PER_ATTENDEE',
  PER_ITEM = 'PER_ITEM',
  PER_ITEM_PER_DAY = 'PER_ITEM_PER_DAY',
  PER_ATTENDEE_PER_DAY = 'PER_ATTENDEE_PER_DAY',
}

@ApiExtraModels()
export class PackageDto {
  @ApiProperty({
    description: 'Unique identifier for the package',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the package variation',
    example: 'Standard Catering',
  })
  name: string;

  @ApiPropertyOptional({ description: 'Detailed description of the package' })
  description?: string | null;

  @ApiPropertyOptional({
    description: 'Category UUID',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  category_id?: string | null;

  @ApiPropertyOptional({
    description: 'Division UUID',
    example: 'e47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  division_id?: string | null;

  @ApiPropertyOptional({
    description: 'Code to group variations',
    example: 'CATER_STD_V1',
  })
  variation_group_code?: string | null;

  @ApiProperty({
    description: 'Unique sequence number (potentially for ordering)',
    example: 101,
  })
  seq_number: number;

  @ApiProperty({
    description: 'Basis for quantity calculation',
    enum: PackageQuantityBasis,
    example: PackageQuantityBasis.PER_ATTENDEE,
  })
  quantity_basis: PackageQuantityBasis;

  @ApiProperty({ description: 'Timestamp when the package was created' })
  created_at: string; // Or Date

  @ApiProperty({ description: 'Timestamp when the package was last updated' })
  updated_at: string; // Or Date

  @ApiProperty({ description: 'User ID who created the package' })
  created_by: string;

  @ApiPropertyOptional({
    description: 'User ID who last updated the package',
    nullable: true,
  })
  updated_by?: string | null;

  @ApiProperty({ description: 'Soft delete flag' })
  is_deleted: boolean;

  // Additional fields for frontend display
  @ApiPropertyOptional({
    description: 'Category name',
    example: 'Catering',
  })
  categoryName?: string;

  @ApiPropertyOptional({
    description: 'Division name',
    example: 'Food & Beverage',
  })
  divisionName?: string;

  @ApiPropertyOptional({
    description: 'Array of city names where this package is available',
    type: [String],
    example: ['Jakarta', 'Bandung'],
  })
  cityNames?: string[];

  @ApiPropertyOptional({
    description: 'Array of venue names where this package is available',
    type: [String],
    example: ['Grand Ballroom', 'Conference Hall'],
  })
  venueNames?: string[];

  @ApiPropertyOptional({
    description: 'Base unit cost as string for display',
    example: '750000',
  })
  unitBaseCost?: string;

  @ApiPropertyOptional({
    description: 'Price as string for display',
    example: '1500000',
  })
  price?: string;

  @ApiPropertyOptional({
    description: 'Currency symbol',
    example: 'Rp',
  })
  currencySymbol?: string;

  @ApiPropertyOptional({
    description: 'Flag indicating if the package has pricing information',
    example: true,
  })
  hasPricing?: boolean;

  @ApiPropertyOptional({
    description: 'Flag indicating if the package has options',
    example: true,
  })
  hasOptions?: boolean;

  @ApiPropertyOptional({
    description: 'Flag indicating if the package has dependencies',
    example: false,
  })
  hasDependencies?: boolean;
}
