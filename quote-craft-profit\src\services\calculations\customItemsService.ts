import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { CustomItem, CustomItemInput } from '@/types/customItems';

const API_BASE = '/calculations';

/**
 * Service for managing custom items in calculations
 */
export class CustomItemsService {
  /**
   * Get all custom items for a calculation
   */
  static async getCustomItems(calculationId: string): Promise<CustomItem[]> {
    try {
      const apiClient = await getAuthenticatedApiClient();
      const response = await apiClient.get(`${API_BASE}/${calculationId}/custom-items`);
      return response.data;
    } catch (error) {
      console.error('Error fetching custom items:', error);
      throw error;
    }
  }

  /**
   * Get a specific custom item by ID
   */
  static async getCustomItemById(calculationId: string, itemId: string): Promise<CustomItem> {
    try {
      const apiClient = await getAuthenticatedApiClient();
      const response = await apiClient.get(`${API_BASE}/${calculationId}/custom-items/${itemId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching custom item:', error);
      throw error;
    }
  }

  /**
   * Add a new custom item to a calculation
   */
  static async addCustomItem(calculationId: string, customItem: CustomItemInput): Promise<{ id: string }> {
    try {
      const apiClient = await getAuthenticatedApiClient();
      const response = await apiClient.post(`${API_BASE}/${calculationId}/custom-items`, {
        itemName: customItem.itemName,
        description: customItem.description || '',
        quantity: customItem.quantity,
        unitPrice: customItem.unitPrice, // Already a number from form
        unitCost: customItem.unitCost || 0,
        itemQuantityBasis: customItem.itemQuantityBasis || 1,
        quantityBasis: customItem.quantityBasis || 'PER_DAY',
        categoryId: customItem.categoryId || null,
        cityId: customItem.cityId || null,
      });
      return response.data;
    } catch (error) {
      console.error('Error adding custom item:', error);
      throw error;
    }
  }

  /**
   * Update a custom item
   */
  static async updateCustomItem(
    calculationId: string,
    itemId: string,
    updates: Partial<CustomItemInput>
  ): Promise<CustomItem> {
    try {
      const apiClient = await getAuthenticatedApiClient();
      const response = await apiClient.put(`${API_BASE}/${calculationId}/custom-items/${itemId}`, {
        itemName: updates.itemName,
        description: updates.description,
        quantity: updates.quantity,
        unitPrice: updates.unitPrice, // Already a number from form
        unitCost: updates.unitCost,
        itemQuantityBasis: updates.itemQuantityBasis,
        quantityBasis: updates.quantityBasis,
        categoryId: updates.categoryId,
      });
      return response.data;
    } catch (error) {
      console.error('Error updating custom item:', error);
      throw error;
    }
  }

  /**
   * Delete a custom item
   */
  static async deleteCustomItem(calculationId: string, itemId: string): Promise<void> {
    try {
      const apiClient = await getAuthenticatedApiClient();
      await apiClient.delete(`${API_BASE}/${calculationId}/custom-items/${itemId}`);
    } catch (error) {
      console.error('Error deleting custom item:', error);
      throw error;
    }
  }

  /**
   * Transform custom item to LineItem format for compatibility
   */
  static transformToLineItem(customItem: CustomItem): any {
    return {
      id: customItem.id,
      calculation_id: customItem.calculation_id,
      package_id: null,
      name: customItem.item_name,
      description: customItem.description,
      quantity: customItem.item_quantity,
      item_quantity_basis: customItem.item_quantity_basis || 1,
      unit_price: customItem.unit_price,
      total_price: customItem.calculated_total,
      category_id: customItem.category_id,
      is_custom: true,
      quantity_basis: customItem.quantity_basis || 'PER_DAY',
      selectedOptions: [],
      created_at: customItem.created_at,
      updated_at: customItem.updated_at,
      createdAt: new Date(customItem.created_at),
      updatedAt: new Date(customItem.updated_at),
    };
  }

  /**
   * Transform LineItem to CustomItem format
   */
  static transformFromLineItem(lineItem: any): CustomItemInput {
    return {
      itemName: lineItem.name,
      description: lineItem.description,
      quantity: lineItem.quantity,
      unitPrice: lineItem.unit_price,
      unitCost: 0,
      itemQuantityBasis: lineItem.item_quantity_basis || 1,
      quantityBasis: lineItem.quantity_basis || 'PER_DAY',
      categoryId: lineItem.category_id,
    };
  }
}
