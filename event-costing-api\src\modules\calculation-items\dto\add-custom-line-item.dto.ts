import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsNumber,
  Min,
  <PERSON>Length,
  IsOptional,
  IsUUID,
} from 'class-validator';

export class AddCustomLineItemDto {
  @ApiProperty({ description: 'Name of the custom item' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  itemName: string;

  @ApiProperty({
    description: 'Optional description for the custom item',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Quantity of the custom item' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0.01) // Allow fractional quantities? Or use IsInt and Min(1)? Assuming decimal for flexibility.
  quantity: number;

  @ApiProperty({ description: 'Unit price for the custom item' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  unitPrice: number;

  @ApiProperty({
    description: 'Unit cost for the custom item (optional, defaults to 0)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  unitCost?: number = 0;

  @ApiProperty({
    description: 'Number of days/units this item applies for (optional, defaults to 1)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  itemQuantityBasis?: number = 1;

  @ApiProperty({
    description: 'Quantity basis for calculation (optional, defaults to PER_DAY)',
    required: false,
    enum: ['PER_EVENT', 'PER_DAY', 'PER_ATTENDEE', 'PER_ITEM', 'PER_ITEM_PER_DAY', 'PER_ATTENDEE_PER_DAY'],
  })
  @IsOptional()
  @IsString()
  quantityBasis?: string = 'PER_DAY';

  // Optional fields for linking to supporting data, if applicable
  @ApiProperty({
    description: 'Optional city ID linkage',
    required: false,
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiProperty({
    description: 'Optional category ID linkage',
    required: false,
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;
}
