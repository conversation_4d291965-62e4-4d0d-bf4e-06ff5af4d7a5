// src/modules/templates/dto/template-summary.dto.ts

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  ValidateNested,
  IsString,
  IsOptional,
  IsInt,
  IsUUID,
  IsDate,
  IsBoolean,
  IsNumber,
} from 'class-validator';
import { Type } from 'class-transformer';

// --- Base DTO ---
export class BaseTemplateDto {
  @ApiProperty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ nullable: true, format: 'uuid' })
  @IsOptional()
  @IsUUID()
  event_type_id?: string;

  @ApiPropertyOptional({ nullable: true, format: 'uuid' })
  @IsOptional()
  @IsUUID()
  city_id?: string;

  @ApiPropertyOptional({ nullable: true, format: 'uuid' })
  @IsOptional()
  @IsUUID()
  currency_id?: string;

  @ApiPropertyOptional({ nullable: true })
  @IsOptional()
  @IsInt()
  attendees?: number;

  @ApiPropertyOptional({ nullable: true, type: Date })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  template_start_date?: Date;

  @ApiPropertyOptional({ nullable: true, type: Date })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  template_end_date?: Date;

  @ApiPropertyOptional({ nullable: true, format: 'uuid' })
  @IsOptional()
  @IsUUID()
  category_id?: string;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  created_at: Date;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  updated_at: Date;

  @ApiProperty({ format: 'uuid' })
  @IsUUID()
  created_by: string;

  @ApiProperty()
  @IsBoolean()
  is_public: boolean;

  @ApiProperty()
  @IsBoolean()
  is_deleted: boolean;

  @ApiPropertyOptional({
    nullable: true,
    description: 'Tax configuration from source calculation',
  })
  @IsOptional()
  taxes?: unknown;

  @ApiPropertyOptional({
    nullable: true,
    description: 'Discount configuration from source calculation',
  })
  @IsOptional()
  discount?: unknown;
}

// --- Summary DTO ---
export class TemplateSummaryDto extends BaseTemplateDto {
  @ApiPropertyOptional({
    description: 'Array of venue IDs associated with the template',
    type: [String],
    format: 'uuid',
  })
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  venue_ids?: string[];
}

// --- Detail DTO ---
class PackageSelectionItemDto {
  @ApiProperty({ format: 'uuid' })
  @IsUUID()
  package_id: string;

  @ApiProperty({ type: [String], format: 'uuid' })
  @IsArray()
  @IsUUID('all', { each: true })
  option_ids: string[];

  @ApiPropertyOptional({ nullable: true })
  @IsOptional()
  @IsInt()
  item_quantity: number | null;

  @ApiPropertyOptional({ nullable: true })
  @IsOptional()
  @IsInt()
  duration_days: number | null;
}

// Enhanced DTO with package and option names for better frontend display
class EnhancedPackageSelectionItemDto {
  @ApiProperty({ format: 'uuid' })
  @IsUUID()
  package_id: string;

  @ApiProperty({ description: 'Package name for display purposes' })
  @IsString()
  package_name: string;

  @ApiProperty({ type: [String], format: 'uuid' })
  @IsArray()
  @IsUUID('all', { each: true })
  option_ids: string[];

  @ApiProperty({
    description:
      'Option names corresponding to option_ids for display purposes',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  option_names: string[];

  @ApiPropertyOptional({ nullable: true })
  @IsOptional()
  @IsInt()
  item_quantity: number | null;

  @ApiPropertyOptional({ nullable: true })
  @IsOptional()
  @IsInt()
  duration_days: number | null;
}

// Custom item DTO for templates
class TemplateCustomItemDto {
  @ApiProperty({ description: 'Custom item name' })
  @IsString()
  item_name: string;

  @ApiPropertyOptional({
    description: 'Custom item description',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Item quantity' })
  @IsInt()
  item_quantity: number;

  @ApiProperty({ description: 'Unit price' })
  @IsNumber()
  unit_price: number;

  @ApiPropertyOptional({ description: 'Unit cost', nullable: true })
  @IsOptional()
  @IsNumber()
  unit_cost?: number;

  @ApiPropertyOptional({
    description: 'Currency ID',
    format: 'uuid',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  currency_id?: string;

  @ApiPropertyOptional({
    description: 'Category ID',
    format: 'uuid',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  category_id?: string;

  @ApiPropertyOptional({
    description: 'City ID',
    format: 'uuid',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  city_id?: string;

  @ApiPropertyOptional({ description: 'Item quantity basis', nullable: true })
  @IsOptional()
  @IsInt()
  item_quantity_basis?: number;

  @ApiPropertyOptional({ description: 'Quantity basis', nullable: true })
  @IsOptional()
  @IsString()
  quantity_basis?: string;
}

// Renamed AdminTemplateDetailDto to TemplateDetailDto as it's used publicly now too
export class TemplateDetailDto extends BaseTemplateDto {
  @ApiProperty({
    description:
      'The blueprint of package and option selections (IDs, quantities).',
    type: [PackageSelectionItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageSelectionItemDto)
  package_selections: PackageSelectionItemDto[];

  @ApiPropertyOptional({
    description: 'Array of venue IDs associated with the template',
    type: [String],
    format: 'uuid',
  })
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  venue_ids?: string[];

  @ApiPropertyOptional({
    description: 'Array of custom items associated with the template',
    type: [TemplateCustomItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateCustomItemDto)
  custom_items?: TemplateCustomItemDto[];
}

// Enhanced template detail DTO with package and option names for better frontend display
export class EnhancedTemplateDetailDto extends BaseTemplateDto {
  @ApiProperty({
    description:
      'The blueprint of package and option selections with names included for display.',
    type: [EnhancedPackageSelectionItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EnhancedPackageSelectionItemDto)
  package_selections: EnhancedPackageSelectionItemDto[];

  @ApiPropertyOptional({
    description: 'Array of venue IDs associated with the template',
    type: [String],
    format: 'uuid',
  })
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  venue_ids?: string[];

  @ApiPropertyOptional({
    description: 'Array of custom items associated with the template',
    type: [TemplateCustomItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateCustomItemDto)
  custom_items?: TemplateCustomItemDto[];
}

// --- Paginated Responses ---

// Paginated response for public templates list (uses Summary)
export class PaginatedTemplatesResponse {
  @ApiProperty({ type: [TemplateSummaryDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateSummaryDto)
  data: TemplateSummaryDto[];

  @ApiProperty({ example: 100 })
  @IsInt()
  count: number;
}

// Paginated response for admin-facing templates list (uses Detail)
export class PaginatedAdminTemplatesResponse {
  @ApiProperty({ type: [TemplateDetailDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateDetailDto)
  data: TemplateDetailDto[];

  @ApiProperty({ example: 50 })
  @IsInt()
  count: number;
}
