import React from 'react';
import { UseFormReturn, FieldPath } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Currency {
  id: string;
  code: string;
  description: string;
}

interface CurrencySelectorProps<T extends Record<string, any>> {
  form: UseFormReturn<T>;
  name: FieldPath<T>;
  currencies: Currency[];
  isLoading?: boolean;
  required?: boolean;
}

/**
 * Reusable currency selector component
 * Automatically handles single vs multiple currency scenarios
 */
export function CurrencySelector<T extends Record<string, any>>({
  form,
  name,
  currencies,
  isLoading = false,
  required = false,
}: CurrencySelectorProps<T>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel className='font-medium'>
            Currency {required && '*'}
          </FormLabel>
          {currencies.length === 1 ? (
            // Show read-only field when only one currency is available
            <FormControl>
              <div className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background'>
                {currencies[0].code} - {currencies[0].description}
              </div>
            </FormControl>
          ) : (
            // Show dropdown when multiple currencies are available
            <Select
              onValueChange={field.onChange}
              value={field.value || ''}
              disabled={isLoading}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder='Select currency' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {currencies.map((currency) => (
                  <SelectItem key={currency.id} value={currency.id}>
                    {currency.code} - {currency.description}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          <FormDescription>
            The currency for this package's price
            {currencies.length === 1 && ' (automatically set)'}
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
