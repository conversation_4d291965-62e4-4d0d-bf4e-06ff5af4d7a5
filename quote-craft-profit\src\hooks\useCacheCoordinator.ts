/**
 * Cache Coordinator Hook
 * 
 * Provides easy access to the cache coordinator for components and hooks.
 * Replaces manual cache invalidation patterns with coordinated approach.
 * 
 * CACHE OPTIMIZATION: Centralized cache coordination to prevent race conditions
 */

import { useQueryClient } from '@tanstack/react-query';
import { useMemo } from 'react';
import { CacheCoordinator, createCacheCoordinator, getCacheCoordinator } from '@/services/cache/cacheCoordinator';

export interface UseCacheCoordinatorReturn {
  coordinator: CacheCoordinator;
  invalidateCalculation: (calculationId: string, options?: any) => Promise<void>;
  invalidateWithRelationships: (domain: string, id: string, action: 'create' | 'update' | 'delete') => Promise<void>;
  getCacheStats: () => { totalQueries: number; staleQueries: number; hitRate: string };
  clearAllCaches: () => Promise<void>;
}

/**
 * Hook to access cache coordinator functionality
 * 
 * @returns Cache coordinator instance and helper methods
 */
export const useCacheCoordinator = (): UseCacheCoordinatorReturn => {
  const queryClient = useQueryClient();
  
  // Create or get existing coordinator instance
  const coordinator = useMemo(() => {
    try {
      return getCacheCoordinator();
    } catch {
      return createCacheCoordinator(queryClient);
    }
  }, [queryClient]);

  // Helper methods for common operations
  const invalidateCalculation = async (calculationId: string, options?: any) => {
    return coordinator.invalidateCalculationData(calculationId, options);
  };

  const invalidateWithRelationships = async (
    domain: string, 
    id: string, 
    action: 'create' | 'update' | 'delete'
  ) => {
    return coordinator.invalidateWithRelationships(domain, id, action);
  };

  const getCacheStats = () => {
    return coordinator.getCacheStats();
  };

  const clearAllCaches = async () => {
    return coordinator.clearAllCaches();
  };

  return {
    coordinator,
    invalidateCalculation,
    invalidateWithRelationships,
    getCacheStats,
    clearAllCaches,
  };
};

/**
 * Hook for calculation-specific cache operations
 * Provides optimized cache invalidation for calculation features
 */
export const useCalculationCache = (calculationId: string) => {
  const { coordinator } = useCacheCoordinator();

  const invalidateLineItems = async () => {
    return coordinator.invalidateCalculationData(calculationId, {
      includeLineItems: true,
      includeFinancials: true,
      includePackages: false,
      includeCustomItems: false,
    });
  };

  const invalidateFinancials = async () => {
    return coordinator.invalidateCalculationData(calculationId, {
      includeLineItems: false,
      includeFinancials: true,
      includePackages: false,
      includeCustomItems: false,
    });
  };

  const invalidatePackages = async () => {
    return coordinator.invalidateCalculationData(calculationId, {
      includeLineItems: false,
      includeFinancials: false,
      includePackages: true,
      includeCustomItems: false,
    });
  };

  const invalidateCustomItems = async () => {
    return coordinator.invalidateCalculationData(calculationId, {
      includeLineItems: false,
      includeFinancials: false,
      includePackages: false,
      includeCustomItems: true,
    });
  };

  const invalidateAll = async () => {
    return coordinator.invalidateCalculationData(calculationId, {
      includeLineItems: true,
      includeFinancials: true,
      includePackages: true,
      includeCustomItems: true,
      includeRelatedCalculations: false,
    });
  };

  return {
    invalidateLineItems,
    invalidateFinancials,
    invalidatePackages,
    invalidateCustomItems,
    invalidateAll,
  };
};

/**
 * Hook for package-specific cache operations
 * Provides optimized cache invalidation for package features
 */
export const usePackageCache = () => {
  const { invalidateWithRelationships } = useCacheCoordinator();

  const invalidatePackage = async (packageId: string, action: 'create' | 'update' | 'delete' = 'update') => {
    return invalidateWithRelationships('package', packageId, action);
  };

  const invalidateCategory = async (categoryId: string, action: 'create' | 'update' | 'delete' = 'update') => {
    return invalidateWithRelationships('category', categoryId, action);
  };

  return {
    invalidatePackage,
    invalidateCategory,
  };
};

/**
 * Hook for client-specific cache operations
 */
export const useClientCache = () => {
  const { invalidateWithRelationships } = useCacheCoordinator();

  const invalidateClient = async (clientId: string, action: 'create' | 'update' | 'delete' = 'update') => {
    return invalidateWithRelationships('client', clientId, action);
  };

  return {
    invalidateClient,
  };
};
