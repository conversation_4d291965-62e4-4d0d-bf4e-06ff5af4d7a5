/**
 * Constants for calculation-related functionality
 */

/**
 * Calculation status options
 * @deprecated Use CalculationStatus enum from @/types/types instead
 */
export const CALCULATION_STATUS = {
  DRAFT: 'draft',
  COMPLETED: 'completed',
  CANCELED: 'canceled',
} as const;

/**
 * Quantity basis options
 * @deprecated Use QuantityBasisEnum from @/types/types instead
 */
export const QUANTITY_BASIS = {
  PER_DAY: 'PER_DAY',
  PER_EVENT: 'PER_EVENT',
  PER_ITEM: 'PER_ITEM',
  PER_ATTENDEE: 'PER_ATTENDEE',
  PER_ITEM_PER_DAY: 'PER_ITEM_PER_DAY',
  PER_ATTENDEE_PER_DAY: 'PER_ATTENDEE_PER_DAY',
} as const;

/**
 * Default values for new calculations
 */
export const DEFAULT_VALUES = {
  ATTENDEES: 0,
  ITEM_QUANTITY: 1,
  ITEM_QUANTITY_BASIS: 1,
} as const;

/**
 * Tax types
 */
export const TAX_TYPES = {
  PERCENTAGE: 'percentage',
  FIXED: 'fixed',
} as const;

/**
 * Currency constants
 */
export const CURRENCY_CONSTANTS = {
  // Default currency ID for IDR (Indonesian Rupiah)
  DEFAULT_CURRENCY_ID: '685860b9-257f-41eb-b223-b3e1fad8f3b9',
  DEFAULT_CURRENCY_CODE: 'IDR',
  DEFAULT_CURRENCY_SYMBOL: 'Rp',
} as const;

/**
 * Financial calculation constants
 */
export const FINANCIAL_CONSTANTS = {
  // Default cost ratio for profit calculation (70% cost, 30% profit)
  DEFAULT_COST_RATIO: 0.7,
  DEFAULT_PROFIT_RATIO: 0.3,
} as const;

/**
 * UI constants
 */
export const UI_CONSTANTS = {
  // Pagination
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,

  // Package list virtualization threshold
  VIRTUALIZATION_THRESHOLD: 10,

  // Search debounce delay
  SEARCH_DEBOUNCE_MS: 300,
} as const;

/**
 * Discount types
 */
export const DISCOUNT_TYPES = {
  PERCENTAGE: 'percentage',
  FIXED: 'fixed',
} as const;
