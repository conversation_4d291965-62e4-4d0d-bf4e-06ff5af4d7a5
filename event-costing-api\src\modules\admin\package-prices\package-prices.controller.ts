import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Logger,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { PackagePricesService } from './package-prices.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { CreatePackagePriceDto } from './dto/create-package-price.dto';
import { UpdatePackagePriceDto } from './dto/update-package-price.dto';
import { PackagePriceDto } from './dto/package-price.dto';
import { AdminRoleGuard } from '../../auth/guards/admin-role.guard';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@ApiTags('Admin - Package Prices')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AdminRoleGuard)
@Controller('admin/packages/:packageId/prices')
export class PackagePricesController {
  private readonly logger = new Logger(PackagePricesController.name);

  constructor(private readonly packagePricesService: PackagePricesService) {}

  // --- Create Package Price --- //
  @Post()
  @ApiOperation({ summary: 'Create a new price for a specific package' })
  @ApiParam({ name: 'packageId', description: 'The ID of the package' })
  @ApiResponse({
    status: 201,
    description: 'Package price created successfully.',
    type: PackagePriceDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., validation error)',
  })
  @ApiResponse({ status: 404, description: 'Package not found' })
  @ApiResponse({
    status: 409,
    description: 'Conflict (e.g., duplicate price for currency/date)',
  })
  create(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Body() createPackagePriceDto: CreatePackagePriceDto,
  ): Promise<PackagePriceDto> {
    this.logger.log(
      `Received request to create price for package ${packageId}`,
    );
    return this.packagePricesService.create(packageId, createPackagePriceDto);
  }

  // --- Find All Prices for a Package --- //
  @Get()
  @ApiOperation({ summary: 'Get all prices for a specific package' })
  @ApiParam({ name: 'packageId', description: 'The ID of the package' })
  @ApiResponse({
    status: 200,
    description: 'List of package prices.',
    type: [PackagePriceDto],
  })
  @ApiResponse({ status: 404, description: 'Package not found' })
  findAllByPackage(
    @Param('packageId', ParseUUIDPipe) packageId: string,
  ): Promise<PackagePriceDto[]> {
    this.logger.log(
      `Received request to find all prices for package ${packageId}`,
    );
    return this.packagePricesService.findAll(packageId);
  }

  // --- Find One Price --- //
  @Get(':packagePriceId')
  @ApiOperation({ summary: 'Get a specific price record for a package' })
  @ApiParam({ name: 'packageId', description: 'The ID of the package' })
  @ApiParam({
    name: 'packagePriceId',
    description: 'The ID of the price record',
  })
  @ApiResponse({
    status: 200,
    description: 'Package price details.',
    type: PackagePriceDto,
  })
  @ApiResponse({ status: 404, description: 'Package or Price not found' })
  findOne(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('packagePriceId', ParseUUIDPipe) packagePriceId: string,
  ): Promise<PackagePriceDto> {
    this.logger.log(
      `Received request to find price ${packagePriceId} for package ${packageId}`,
    );
    return this.packagePricesService.findOne(packageId, packagePriceId);
  }

  // --- Update Package Price --- //
  @Patch(':packagePriceId')
  @ApiOperation({ summary: 'Update a specific price record' })
  @ApiParam({ name: 'packageId', description: 'The ID of the package' })
  @ApiParam({
    name: 'packagePriceId',
    description: 'The ID of the price record to update',
  })
  @ApiResponse({
    status: 200,
    description: 'Package price updated successfully.',
    type: PackagePriceDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., validation error)',
  })
  @ApiResponse({ status: 404, description: 'Package or Price not found' })
  update(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('packagePriceId', ParseUUIDPipe) packagePriceId: string,
    @Body() updatePackagePriceDto: UpdatePackagePriceDto,
  ): Promise<PackagePriceDto> {
    this.logger.log(
      `Received request to update price ${packagePriceId} for package ${packageId}`,
    );
    return this.packagePricesService.update(
      packageId,
      packagePriceId,
      updatePackagePriceDto,
    );
  }

  // --- Remove Package Price --- //
  @Delete(':packagePriceId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a specific price record' })
  @ApiParam({ name: 'packageId', description: 'The ID of the package' })
  @ApiParam({
    name: 'packagePriceId',
    description: 'The ID of the price record to delete',
  })
  @ApiResponse({
    status: 204,
    description: 'Package price deleted successfully.',
  })
  @ApiResponse({ status: 404, description: 'Package or Price not found' })
  remove(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('packagePriceId', ParseUUIDPipe) packagePriceId: string,
  ): Promise<void> {
    this.logger.log(
      `Received request to delete price ${packagePriceId} for package ${packageId}`,
    );
    return this.packagePricesService.remove(packageId, packagePriceId);
  }
}
