/**
 * Timezone Settings Component
 * 
 * Allows users to select and manage their timezone preferences
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, Globe, MapPin } from 'lucide-react';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import { TIMEZONE_OPTIONS, getTimezoneOffset, getCurrentDateInTimezone } from '@/lib/timezone-utils';

const TimezoneSettings: React.FC = () => {
  const { preferences, timezone, updateTimezone, isUpdating } = useUserPreferences();

  const handleTimezoneChange = (newTimezone: string) => {
    updateTimezone(newTimezone);
  };

  const currentTimezoneInfo = TIMEZONE_OPTIONS.find(tz => tz.value === timezone);
  const currentOffset = getTimezoneOffset(timezone);
  const currentTime = new Date().toLocaleString('en-US', {
    timeZone: timezone,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  });
  const currentDate = getCurrentDateInTimezone(timezone);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          Timezone Settings
        </CardTitle>
        <CardDescription>
          Set your preferred timezone for accurate date and time display throughout the application.
          This fixes the "picked 19th but submitting 18th" issue by ensuring all dates respect your timezone.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Timezone Info */}
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Current Timezone
            </h4>
            <Badge variant="secondary">{currentOffset}</Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <Label className="text-gray-500">Timezone</Label>
              <p className="font-medium">{currentTimezoneInfo?.label || timezone}</p>
            </div>
            <div>
              <Label className="text-gray-500">Current Time</Label>
              <p className="font-medium">{currentTime}</p>
            </div>
            <div>
              <Label className="text-gray-500">Current Date</Label>
              <p className="font-medium">{currentDate}</p>
            </div>
          </div>
        </div>

        {/* Timezone Selection */}
        <div className="space-y-3">
          <Label htmlFor="timezone-select">Select Timezone</Label>
          <Select
            value={timezone}
            onValueChange={handleTimezoneChange}
            disabled={isUpdating}
          >
            <SelectTrigger id="timezone-select">
              <SelectValue placeholder="Select a timezone" />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {TIMEZONE_OPTIONS.map((tz) => (
                <SelectItem key={tz.value} value={tz.value}>
                  <div className="flex items-center justify-between w-full">
                    <span>{tz.label}</span>
                    <Badge variant="outline" className="ml-2">
                      {tz.offset}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-sm text-gray-500">
            Choose the timezone that matches your location for accurate date and time handling.
          </p>
        </div>

        {/* Regional Recommendations */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Regional Recommendations
          </Label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="p-3 border rounded-lg">
              <h5 className="font-medium text-sm mb-2">Southeast Asia</h5>
              <div className="space-y-1 text-xs">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 justify-start"
                  onClick={() => handleTimezoneChange('Asia/Jakarta')}
                  disabled={isUpdating}
                >
                  Jakarta (UTC+7) - Indonesia
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 justify-start"
                  onClick={() => handleTimezoneChange('Asia/Singapore')}
                  disabled={isUpdating}
                >
                  Singapore (UTC+8)
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 justify-start"
                  onClick={() => handleTimezoneChange('Asia/Kuala_Lumpur')}
                  disabled={isUpdating}
                >
                  Kuala Lumpur (UTC+8) - Malaysia
                </Button>
              </div>
            </div>
            
            <div className="p-3 border rounded-lg">
              <h5 className="font-medium text-sm mb-2">East Asia</h5>
              <div className="space-y-1 text-xs">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 justify-start"
                  onClick={() => handleTimezoneChange('Asia/Tokyo')}
                  disabled={isUpdating}
                >
                  Tokyo (UTC+9) - Japan
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 justify-start"
                  onClick={() => handleTimezoneChange('Asia/Seoul')}
                  disabled={isUpdating}
                >
                  Seoul (UTC+9) - South Korea
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 justify-start"
                  onClick={() => handleTimezoneChange('Asia/Shanghai')}
                  disabled={isUpdating}
                >
                  Shanghai (UTC+8) - China
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Impact Information */}
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
            How This Affects Your Experience
          </h4>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Date selections in forms will be accurate to your timezone</li>
            <li>• Calculation details will display dates correctly</li>
            <li>• No more "picked 19th but submitting 18th" issues</li>
            <li>• All timestamps will be shown in your local time</li>
            <li>• Reports and exports will use your timezone</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default TimezoneSettings;
