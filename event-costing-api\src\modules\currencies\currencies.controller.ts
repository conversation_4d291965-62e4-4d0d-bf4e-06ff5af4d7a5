import { Controller, Get, Logger } from '@nestjs/common';
import { CurrenciesService } from './currencies.service';
import { CurrencyDto } from './dto/currency.dto';
import { ApiTags, ApiOkResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('Currencies')
@ApiBearerAuth()
@Controller('currencies') // Change base path to /currencies
export class CurrenciesController {
  private readonly logger = new Logger(CurrenciesController.name);

  constructor(private readonly currenciesService: CurrenciesService) {}

  @Get()
  @ApiOkResponse({ type: [CurrencyDto] })
  async getCurrencies(): Promise<CurrencyDto[]> {
    this.logger.log('Fetching all currencies');
    return this.currenciesService.findAll();
  }
}
