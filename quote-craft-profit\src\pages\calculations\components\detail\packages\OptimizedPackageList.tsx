import React, { useRef, useMemo } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { PackageWithOptions } from '@/types/calculation';
import PackageCard from './PackageCard';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

interface OptimizedPackageListProps {
  packages: PackageWithOptions[];
  packageForms: Record<string, any>;
  onQuantityChange: (packageId: string, quantity: number) => void;
  onItemQuantityBasisChange: (packageId: string, itemQuantityBasis: number) => void;
  onOptionToggle: (packageId: string, optionId: string, isSelected: boolean) => void;
  onAddToCalculation: (packageId: string) => void;
  isLoading?: boolean;
  isError?: boolean;
  maxHeight?: number;
  selectedPackageIds?: string[]; // New prop to track selected packages
}

const OptimizedPackageList: React.FC<OptimizedPackageListProps> = ({
  packages,
  packageForms,
  onQuantityChange,
  onItemQuantityBasisChange,
  onOptionToggle,
  onAddToCalculation,
  isLoading = false,
  isError = false,
  maxHeight = 600,
  selectedPackageIds = [],
}) => {
  const parentRef = useRef<HTMLDivElement>(null);

  // Estimate item size based on package complexity
  const estimateSize = useMemo(() => {
    if (packages.length === 0) return 200;

    // Calculate average estimated height based on package options
    const avgOptionsCount = packages.reduce((sum, pkg) => sum + (pkg.options?.length || 0), 0) / packages.length;
    const baseHeight = 180; // Base card height
    const optionHeight = 30; // Height per option

    // Calculate average description height
    const avgDescriptionHeight = packages.reduce((sum, pkg) => sum + (pkg.description ? 40 : 0), 0) / packages.length;

    return Math.max(200, baseHeight + (avgOptionsCount * optionHeight) + avgDescriptionHeight);
  }, [packages]);

  // Set up virtualization
  const virtualizer = useVirtualizer({
    count: isLoading ? 5 : packages.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimateSize,
    overscan: 3, // Render 3 items outside visible area for smooth scrolling
  });

  // Loading skeleton
  const LoadingSkeleton = () => (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex justify-between items-start">
            <div className="space-y-2 flex-1">
              <Skeleton className="h-5 w-48" />
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-64" />
            </div>
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
            </div>
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Error state
  if (isError) {
    return (
      <div className="text-center py-8 border border-red-200 rounded-lg bg-red-50">
        <p className="text-red-600 font-medium">Failed to load packages</p>
        <p className="text-sm text-red-500 mt-2">
          Please try again later or contact support if the problem persists
        </p>
      </div>
    );
  }

  // Empty state
  if (!isLoading && packages.length === 0) {
    return (
      <div className="text-center py-8 border border-gray-200 rounded-lg bg-gray-50">
        <p className="text-gray-600 font-medium">No packages found</p>
        <p className="text-sm text-gray-500 mt-2">
          Try adjusting your search or filter criteria
        </p>
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className="overflow-auto border rounded-lg"
      style={{
        height: `${Math.min(maxHeight, packages.length * estimateSize)}px`,
        maxHeight: `${maxHeight}px`,
      }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {isLoading ? (
          // Show loading skeletons
          Array.from({ length: 5 }).map((_, index) => (
            <div
              key={`skeleton-${index}`}
              style={{
                position: 'absolute',
                top: index * estimateSize,
                left: 0,
                width: '100%',
                padding: '8px',
              }}
            >
              <LoadingSkeleton />
            </div>
          ))
        ) : (
          // Show virtualized packages
          virtualizer.getVirtualItems().map((virtualItem) => {
            const pkg = packages[virtualItem.index];

            // Ensure we have valid form data with proper defaults
            const form = packageForms[pkg.id] || {
              quantity: 1,
              item_quantity_basis: 1,
              selectedOptions: [],
            };

            // Ensure all values are valid numbers to prevent NaN
            const safeQuantity = Number(form.quantity) || 1;
            const safeItemQuantityBasis = Number(form.item_quantity_basis || form.itemQuantityBasis) || 1;
            const safeSelectedOptions = Array.isArray(form.selectedOptions) ? form.selectedOptions : [];

            return (
              <div
                key={virtualItem.key}
                data-index={virtualItem.index}
                ref={virtualizer.measureElement}
                className="absolute top-0 left-0 w-full px-2 py-1"
                style={{
                  transform: `translateY(${virtualItem.start}px)`,
                }}
              >
                <PackageCard
                  pkg={pkg}
                  quantity={safeQuantity}
                  itemQuantityBasis={safeItemQuantityBasis}
                  selectedOptions={safeSelectedOptions}
                  onQuantityChange={onQuantityChange}
                  onItemQuantityBasisChange={(packageId, itemQuantityBasis) =>
                    onItemQuantityBasisChange(packageId, itemQuantityBasis)
                  }
                  onOptionToggle={(packageId, optionId, isSelected) => onOptionToggle(packageId, optionId, isSelected)}
                  onAddToCalculation={() => onAddToCalculation(pkg.id)}
                  isAlreadySelected={selectedPackageIds.includes(pkg.id)}
                />
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default OptimizedPackageList;
