import { Injectable, Logger } from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { TemplateSummaryDto, TemplateDetailDto } from '../dto/template-summary.dto';
import { TemplateConstants } from '../constants/template.constants';

@Injectable()
export class TemplateVenueService {
  private readonly logger = new Logger(TemplateVenueService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Add venue IDs to a list of templates
   * @param templates - The templates to add venue IDs to
   */
  async addVenueIdsToTemplates(templates: TemplateSummaryDto[] | TemplateDetailDto[]): Promise<void> {
    if (templates.length === 0) {
      return;
    }

    const supabase = this.supabaseService.getClient();
    const templateIds = templates.map(template => template.id);
    
    const { data: venueData, error: venueError } = await supabase
      .from('template_venues')
      .select('template_id, venue_id')
      .in('template_id', templateIds);

    if (venueError) {
      this.logger.error(
        `Error fetching venues for templates: ${venueError.message}`,
        venueError.stack,
      );
      // Continue without venues if there's an error
      return;
    } 
    
    if (venueData) {
      // Group venues by template_id
      const venuesByTemplate = venueData.reduce((acc, curr) => {
        if (!acc[curr.template_id]) {
          acc[curr.template_id] = [];
        }
        acc[curr.template_id].push(curr.venue_id);
        return acc;
      }, {});

      // Add venue_ids to each template
      templates.forEach(template => {
        template.venue_ids = venuesByTemplate[template.id] || [];
      });

      // Debug log to check if venue_ids are included in the response
      if (templates.length > 0) {
        this.logger.log(
          `Template venue info sample: ${JSON.stringify({
            id: templates[0].id,
            name: templates[0].name,
            venue_ids: templates[0].venue_ids,
            has_venue_ids: !!templates[0].venue_ids,
            venue_ids_length: templates[0].venue_ids?.length || 0,
          })}`,
        );
      }
    }
  }

  /**
   * Add venue IDs to a single template
   * @param template - The template to add venue IDs to
   */
  async addVenueIdsToTemplate(template: TemplateSummaryDto | TemplateDetailDto): Promise<void> {
    const supabase = this.supabaseService.getClient();
    
    const { data: venueData, error: venueError } = await supabase
      .from('template_venues')
      .select('venue_id')
      .eq('template_id', template.id);

    if (venueError) {
      this.logger.error(
        `Error fetching venues for template ${template.id}: ${venueError.message}`,
        venueError.stack,
      );
      // Continue without venues if there's an error
      return;
    }

    // Extract venue IDs
    template.venue_ids = venueData?.map(v => v.venue_id) || [];
    
    this.logger.log(
      `Added ${template.venue_ids.length} venue IDs to template ${template.id}`,
    );
  }

  /**
   * Create venue associations for a template
   * @param templateId - The template ID
   * @param venueIds - The venue IDs to associate
   */
  async createTemplateVenueAssociations(
    templateId: string, 
    venueIds: string[]
  ): Promise<void> {
    if (venueIds.length === 0) {
      return;
    }

    const supabase = this.supabaseService.getClient();
    const templateVenues = venueIds.map(venueId => ({
      template_id: templateId,
      venue_id: venueId,
    }));

    const { error: venueInsertError } = await supabase
      .from('template_venues')
      .insert(templateVenues);

    if (venueInsertError) {
      this.logger.error(
        `Error inserting template venues: ${venueInsertError.message}`,
        venueInsertError.stack,
      );
      // Continue even if venue association fails
    } else {
      this.logger.log(
        `Successfully associated ${venueIds.length} venues with template ${templateId}`,
      );
    }
  }
}
