import { Injectable, Logger } from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { CalculationTotalsDto } from './dto/calculation-totals.dto';

// Define interfaces for the expected structure within JSONB columns
interface TaxInfo {
  name?: string;
  rate?: number;
  amount?: number;
  type?: string;
  basis?: string;
}

interface DiscountInfo {
  name?: string;
  amount?: number;
  reason?: string;
  percentage?: number; // Added based on previous logic attempt
}

// Interface for the data selected from calculation_history
interface CalculationTotalsData {
  id: string;
  subtotal: number | null;
  taxes: TaxInfo[] | null; // Use defined interface array
  discount: DiscountInfo | null; // Use defined interface
  total: number | null;
  total_cost: number | null;
  estimated_profit: number | null;
}

@Injectable()
export class CalculationLogicService {
  private readonly logger = new Logger(CalculationLogicService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  async fetchAndCalculateTotals(
    calcId: string,
  ): Promise<CalculationTotalsDto | null> {
    const {
      data,
      error,
    }: { data: CalculationTotalsData | null; error: unknown } =
      await this.supabaseService
        .getClient()
        .from('calculation_history')
        .select(
          `
          id,
          subtotal,
          taxes,
          discount,
          total,
          total_cost,
          estimated_profit
        `,
        )
        .eq('id', calcId)
        .maybeSingle<CalculationTotalsData>();

    if (error) {
      let message: string;
      let stack: string | undefined = undefined;

      if (error instanceof Error) {
        message = error.message;
        stack = error.stack;
      } else if (typeof error === 'object' && error !== null) {
        // Safely stringify object errors
        try {
          message = JSON.stringify(error);
        } catch {
          // Empty catch block without unused parameter
          message = '[Object cannot be stringified]';
        }
      } else {
        // For primitive types or any other case
        message =
          typeof error === 'string'
            ? error
            : typeof error === 'number' || typeof error === 'boolean'
              ? error.toString()
              : '[Unknown error type]';
      }

      this.logger.error(
        `Error fetching calculation totals data for ID ${calcId}: ${message}`,
        stack,
      );
      return null;
    }

    if (!data) {
      this.logger.warn(
        `No calculation history found for totals fetch with ID: ${calcId}`,
      );
      return null;
    }

    let calculatedTotalTax = 0;
    if (data.taxes && Array.isArray(data.taxes)) {
      const subtotalForTax = data.subtotal ?? 0;
      calculatedTotalTax = data.taxes.reduce((sum, tax: TaxInfo) => {
        let currentTaxAmount = 0;
        if (tax && typeof tax.amount === 'number') {
          currentTaxAmount = tax.amount;
        } else if (tax && typeof tax.rate === 'number' && subtotalForTax > 0) {
          currentTaxAmount = subtotalForTax * (tax.rate / 100);
        }
        return sum + currentTaxAmount;
      }, 0);
    }

    let discountAmount = 0;
    const fetchedDiscountAmount = data.discount?.amount;
    if (typeof fetchedDiscountAmount === 'number') {
      discountAmount = fetchedDiscountAmount;
    }

    const totalsDto: CalculationTotalsDto = {
      subtotal: data.subtotal ?? 0,
      lineItemsTotal: 0,
      customItemsTotal: 0,
      taxTotal: calculatedTotalTax,
      discountAmount: discountAmount,
      total: data.total ?? 0,
      totalCost: data.total_cost ?? 0,
      estimatedProfit: data.estimated_profit ?? 0,
    };

    return totalsDto;
  }

  /**
   * Recalculate totals for a calculation using the database RPC function
   * @param calcId - The calculation ID
   * @returns Promise resolving to void
   */
  async recalculateTotals(calcId: string): Promise<void> {
    this.logger.log(`Triggering recalculation for calculation ID: ${calcId}`);

    const supabase = this.supabaseService.getClient();
    const { error } = await supabase.rpc('recalculate_calculation_totals', {
      p_calculation_id: calcId,
    });

    if (error) {
      this.logger.error(
        `Error recalculating totals for calculation ${calcId}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to recalculate totals: ${error.message}`);
    }

    this.logger.log(`Recalculation completed for calculation ID: ${calcId}`);
  }
}
