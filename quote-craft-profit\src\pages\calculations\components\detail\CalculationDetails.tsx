import React from "react";
import { Edit, X, Save } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import {
  EventTypeSelector,
  useEventType,
} from "@/components/ui/event-type-selector";
import { DateRange } from "react-day-picker";

interface CalculationDetailsProps {
  calculation: any;
  isEditMode: boolean;
  isSaving: boolean;
  editedName: string;
  editedEventType: string;
  editedAttendees: number;
  dateRange: DateRange | undefined;
  setEditedName: (value: string) => void;
  setEditedEventType: (value: string) => void;
  setEditedAttendees: (value: number) => void;
  setDateRange: (value: DateRange | undefined) => void;
  handleToggleEditMode: () => void;
  handleSaveChanges: () => void;
  formatDate: (dateString: string) => string;
}

const CalculationDetails: React.FC<CalculationDetailsProps> = ({
  calculation,
  isEditMode,
  isSaving,
  editedName,
  editedEventType,
  editedAttendees,
  dateRange,
  setEditedName,
  setEditedEventType,
  setEditedAttendees,
  setDateRange,
  handleToggleEditMode,
  handleSaveChanges,
  formatDate,
}) => {
  // Get event type name for display
  const { eventTypeName } = useEventType(calculation.event_type_id);
  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border dark:border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold dark:text-white">Event Details</h2>
        <div className="flex gap-2">
          {isEditMode ? (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleEditMode}
                disabled={isSaving}
              >
                <X className="h-4 w-4 mr-1" /> Cancel
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleSaveChanges}
                disabled={isSaving}
                className="bg-primary text-white"
              >
                {isSaving ? (
                  <>
                    <span className="animate-spin mr-1">⏳</span> Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-1" /> Save Changes
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button variant="outline" size="sm" onClick={handleToggleEditMode}>
              <Edit className="h-4 w-4 mr-1" /> Edit Details
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="text-gray-500 dark:text-gray-400 text-sm">
            Event Name
          </label>
          <Input
            value={isEditMode ? editedName : calculation.name}
            onChange={(e) => setEditedName(e.target.value)}
            className="mt-1"
            disabled={!isEditMode}
          />
        </div>

        <div>
          <label className="text-gray-500 dark:text-gray-400 text-sm">
            Event Type
          </label>
          {isEditMode ? (
            <div className="mt-1">
              <EventTypeSelector
                value={editedEventType}
                onValueChange={setEditedEventType}
                placeholder="Select event type"
                allowEmpty={true}
                emptyLabel="None"
              />
            </div>
          ) : (
            <Input
              value={eventTypeName || "None"}
              className="mt-1"
              disabled={true}
            />
          )}
        </div>

        <div>
          <label className="text-gray-500 dark:text-gray-400 text-sm">
            Date Range
          </label>
          {isEditMode ? (
            <div className="mt-1">
              <DateRangePicker
                value={dateRange}
                onChange={setDateRange}
                placeholder="Select date range"
                numberOfMonths={2}
              />
            </div>
          ) : (
            <Input
              value={`${formatDate(
                calculation.event_start_date
              )} - ${formatDate(calculation.event_end_date)}`}
              className="mt-1"
              disabled={true}
            />
          )}
        </div>

        <div>
          <label className="text-gray-500 dark:text-gray-400 text-sm">
            Attendees
          </label>
          <Input
            type="number"
            min="1"
            value={isEditMode ? editedAttendees : calculation.attendees}
            onChange={(e) => setEditedAttendees(parseInt(e.target.value) || 0)}
            className="mt-1"
            disabled={!isEditMode}
          />
        </div>

        <div>
          <label className="text-gray-500 dark:text-gray-400 text-sm">
            City
          </label>
          <Input
            value={calculation.city.name}
            className="mt-1"
            disabled={true}
          />
        </div>

        <div>
          <label className="text-gray-500 dark:text-gray-400 text-sm">
            Venues
          </label>
          <div className="mt-1 p-2 border dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-800">
            {calculation.venues && calculation.venues.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {calculation.venues.map((venue: any) => (
                  <Badge key={venue.id} variant="secondary">
                    {venue.name}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No venues selected
              </p>
            )}
          </div>
        </div>

        <div>
          <label className="text-gray-500 dark:text-gray-400 text-sm">
            Currency
          </label>
          <Input
            value={calculation.currency.code}
            className="mt-1"
            disabled={true}
          />
        </div>
      </div>
    </div>
  );
};

export default CalculationDetails;
