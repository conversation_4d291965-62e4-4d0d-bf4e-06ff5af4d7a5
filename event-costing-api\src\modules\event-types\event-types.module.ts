import { Module } from '@nestjs/common';
import { EventTypesService } from './event-types.service';
import {
  EventTypesController,
  AdminEventTypesController,
} from './event-types.controller';
import { SupabaseModule } from '../../core/supabase/supabase.module';

@Module({
  imports: [SupabaseModule],
  controllers: [EventTypesController, AdminEventTypesController],
  providers: [EventTypesService],
  exports: [EventTypesService],
})
export class EventTypesModule {}
