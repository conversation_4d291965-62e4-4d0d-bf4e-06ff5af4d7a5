import {
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Logger,
  Body,
  Patch,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import { StorageService } from '../../core/storage/storage.service';
import { SupabaseService } from '../../core/supabase/supabase.service';
// Import proper types for Multer
import { Express } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
  ApiOkResponse,
} from '@nestjs/swagger';
import { ProfilePictureResponseDto } from './dto/profile-picture-response.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';

@Controller('profile')
@ApiTags('Profile')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProfileController {
  private readonly logger = new Logger(ProfileController.name);
  private readonly BUCKET_NAME = 'profiles';

  constructor(
    private readonly storageService: StorageService,
    private readonly supabaseService: SupabaseService,
  ) {}

  @Post('picture')
  @ApiOperation({ summary: 'Upload a profile picture' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiOkResponse({
    description: 'Profile picture uploaded successfully',
    type: ProfilePictureResponseDto,
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  )
  async uploadProfilePicture(
    @UploadedFile() file: Express.Multer.File,
    @GetCurrentUser() user: User,
  ): Promise<ProfilePictureResponseDto> {
    this.logger.log(
      `Profile picture upload request received for user: ${user.id}`,
    );

    // Log detailed information about the file
    if (file) {
      this.logger.log(`File details: {
        originalname: ${file.originalname},
        mimetype: ${file.mimetype},
        size: ${file.size} bytes,
        buffer: ${file.buffer ? 'Present' : 'Missing'}
      }`);
    } else {
      this.logger.error('No file received in the request');
      throw new BadRequestException('No file uploaded');
    }

    // Validate file type
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
    ];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      this.logger.warn(`Invalid file type: ${file.mimetype}`);
      throw new BadRequestException(
        'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.',
      );
    }

    try {
      // Generate file path
      const fileExt = file.originalname.split('.').pop();
      const filePath = `profile-pictures/${user.id}-${Date.now()}.${fileExt}`;
      this.logger.log(`Generated file path: ${filePath}`);

      // Ensure the bucket exists
      this.logger.log(`Ensuring bucket exists: ${this.BUCKET_NAME}`);

      // Upload file to storage
      this.logger.log(
        `Uploading file to storage: ${this.BUCKET_NAME}/${filePath}`,
      );
      await this.storageService.uploadFile(
        this.BUCKET_NAME,
        filePath,
        file.buffer,
        file.mimetype,
      );
      this.logger.log(`File uploaded successfully to storage`);

      // Get public URL
      this.logger.log(`Generating public URL for file`);
      const publicUrl = this.storageService.getPublicUrl(
        this.BUCKET_NAME,
        filePath,
      );
      this.logger.log(`Generated public URL: ${publicUrl}`);

      // Update user profile with new image URL
      this.logger.log(`Updating user profile with new image URL`);
      const supabase = this.supabaseService.getClient();
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ profile_picture_url: publicUrl })
        .eq('id', user.id);

      if (updateError) {
        this.logger.error(
          `Failed to update profile with new image URL: ${updateError.message}`,
          updateError.stack,
        );
        throw new BadRequestException(
          'Failed to update profile with new image URL',
        );
      }
      this.logger.log(`User profile updated successfully with new image URL`);

      // Return success response
      this.logger.log(`Profile picture upload completed successfully`);
      return {
        success: true,
        message: 'Profile picture uploaded successfully',
        profilePictureUrl: publicUrl,
      };
    } catch (error) {
      this.logger.error(
        `Error uploading profile picture: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to upload profile picture: ${error.message}`,
      );
    }
  }

  @Patch()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiOkResponse({
    description: 'Profile updated successfully',
  })
  async updateProfile(
    @Body() updateProfileDto: UpdateProfileDto,
    @GetCurrentUser() user: User,
  ): Promise<{
    success: boolean;
    message: string;
    updatedFields?: string[];
    profile?: any;
  }> {
    try {
      // Transform DTO fields to match database schema
      const transformedData = {
        ...updateProfileDto,
        // Map 'company' to 'company_name' if it exists
        ...(updateProfileDto.company && {
          company_name: updateProfileDto.company,
        }),
        // Map 'phone' to 'phone_number' if it exists
        ...(updateProfileDto.phone && { phone_number: updateProfileDto.phone }),
      };

      // Remove original fields that were transformed
      if ('company' in transformedData) {
        delete transformedData.company;
      }

      if ('phone' in transformedData) {
        delete transformedData.phone;
      }

      this.logger.log(
        `Updating profile for user ${user.id} with data:`,
        transformedData,
      );

      const supabase = this.supabaseService.getClient();
      // Update the profile
      const { error } = await supabase
        .from('profiles')
        .update(transformedData)
        .eq('id', user.id);

      if (error) {
        this.logger.error(
          `Failed to update profile: ${error.message}`,
          error.stack,
        );
        throw new BadRequestException(
          `Failed to update profile: ${error.message}`,
        );
      }

      // Verify the update was successful by fetching the updated profile with role information
      const { data: updatedProfile, error: fetchError } = await supabase
        .from('profiles')
        .select(
          `
          *,
          roles (role_name)
        `,
        )
        .eq('id', user.id)
        .single();

      // Log the fetched profile for debugging
      this.logger.log(
        `Fetched updated profile: ${JSON.stringify(updatedProfile)}`,
      );

      if (fetchError) {
        this.logger.error(
          `Failed to verify profile update: ${fetchError.message}`,
          fetchError.stack,
        );
        throw new BadRequestException(
          `Failed to verify profile update: ${fetchError.message}`,
        );
      }

      // Check if the update was actually applied
      const fieldsToCheck = Object.keys(transformedData);
      const missingUpdates = fieldsToCheck.filter(
        field =>
          transformedData[field] !== undefined &&
          updatedProfile[field] !== transformedData[field],
      );

      if (missingUpdates.length > 0) {
        this.logger.error(
          `Profile update verification failed. The following fields were not updated: ${missingUpdates.join(', ')}`,
        );
        throw new BadRequestException(
          `Profile update verification failed. Some fields were not updated correctly.`,
        );
      }

      this.logger.log(`Profile updated successfully for user ${user.id}`);
      return {
        success: true,
        message: 'Profile updated successfully',
        updatedFields: fieldsToCheck,
        profile: updatedProfile, // Return the updated profile data
      };
    } catch (error) {
      this.logger.error(
        `Error updating profile: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to update profile: ${error.message}`,
      );
    }
  }
}
