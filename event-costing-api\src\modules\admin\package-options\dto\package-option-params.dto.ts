import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class PackageOptionParamsDto {
  @ApiProperty({
    description: 'The UUID of the package',
    format: 'uuid',
  })
  @IsNotEmpty()
  @IsUUID()
  package_id: string;

  @ApiProperty({
    description: 'The UUID of the package option',
    format: 'uuid',
  })
  @IsNotEmpty()
  @IsUUID()
  option_id: string;
}
