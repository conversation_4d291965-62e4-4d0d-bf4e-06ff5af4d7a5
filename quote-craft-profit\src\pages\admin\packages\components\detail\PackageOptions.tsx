import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Pencil, Trash2, AlertCircle } from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
  getPackageOptions,
  deletePackageOption,
} from "../../../../../services/admin/packages/packageOptionService";
import {
  PackageOption,
  PackageOptionDisplay,
} from "../../types/packageOptions";
import { PackageOptionForm } from "../../components/form/PackageOptionForm";
import { formatRupiah } from "@/lib/utils";

interface PackageOptionsProps {
  packageId: string;
}

export const PackageOptions: React.FC<PackageOptionsProps> = ({
  packageId,
}) => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedOptionId, setSelectedOptionId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [optionToDelete, setOptionToDelete] = useState<string | null>(null);

  const {
    data: options = [],
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["packageOptions", packageId],
    queryFn: () => getPackageOptions(packageId),
    enabled: !!packageId,
    meta: {
      onError: () => {
        toast.error("Failed to load package options");
      },
    },
  });

  // Transform options for display with proper type checking
  const displayOptions: PackageOptionDisplay[] = Array.isArray(options)
    ? options.map(
        (option: PackageOption & { currencies?: { code: string } }) => ({
          ...option,
          currency_code: option.currencies?.code || "IDR",
          profit_margin: option.price_adjustment - option.cost_adjustment,
          profit_percentage:
            option.price_adjustment > 0
              ? ((option.price_adjustment - option.cost_adjustment) /
                  option.price_adjustment) *
                100
              : 0,
        })
      )
    : [];

  const handleAddOption = () => {
    setSelectedOptionId(null);
    setIsFormOpen(true);
  };

  const handleEditOption = (optionId: string) => {
    setSelectedOptionId(optionId);
    setIsFormOpen(true);
  };

  const handleDeleteOption = (optionId: string) => {
    setOptionToDelete(optionId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!optionToDelete) return;

    try {
      await deletePackageOption(packageId, optionToDelete);
      toast.success("Package option deleted successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to delete package option");
      console.error(error);
    } finally {
      setIsDeleteDialogOpen(false);
      setOptionToDelete(null);
    }
  };

  const handleFormClose = (shouldRefresh?: boolean) => {
    setIsFormOpen(false);
    setSelectedOptionId(null);
    if (shouldRefresh) {
      refetch();
    }
  };

  // Find the selected option for editing
  const selectedOption = selectedOptionId
    ? options.find((option) => option.id === selectedOptionId)
    : null;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Package Options</CardTitle>
          <CardDescription>
            Manage options like upgrades or add-ons for this package
          </CardDescription>
        </div>
        <Button
          onClick={handleAddOption}
          className="flex items-center"
          disabled={!packageId}
        >
          <Plus className="w-4 h-4 mr-2" /> Add Option
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-4">Loading options...</div>
        ) : isError ? (
          <div className="text-center py-4 text-red-500">
            <AlertCircle className="w-8 h-8 mx-auto mb-2" />
            <p>Failed to load package options. Please try again.</p>
          </div>
        ) : displayOptions.length === 0 ? (
          <div className="text-center py-8 border rounded-lg">
            No options defined for this package yet. Click "Add Option" to
            create one.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted">
                  <th className="text-left p-2">Option Name</th>
                  <th className="text-left p-2">Code</th>
                  <th className="text-right p-2">Price Adjustment</th>
                  <th className="text-right p-2">Cost Adjustment</th>
                  <th className="text-center p-2">Status</th>
                  <th className="text-right p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {displayOptions.map((option) => (
                  <tr key={option.id} className="border-b hover:bg-muted/50">
                    <td className="p-2 font-medium">{option.option_name}</td>
                    <td className="p-2">{option.option_code}</td>
                    <td className="p-2 text-right">
                      <span
                        className={
                          option.price_adjustment < 0 ? "text-red-600" : ""
                        }
                      >
                        {formatRupiah(option.price_adjustment)}
                      </span>
                    </td>
                    <td className="p-2 text-right">
                      <span
                        className={
                          option.cost_adjustment < 0 ? "text-red-600" : ""
                        }
                      >
                        {formatRupiah(option.cost_adjustment)}
                      </span>
                    </td>
                    <td className="p-2 text-center">
                      <div className="flex flex-wrap justify-center gap-1">
                        {option.is_default_for_package && option.is_required ? (
                          <Badge className="bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800">
                            Default & Required
                          </Badge>
                        ) : (
                          <>
                            {option.is_default_for_package && (
                              <Badge
                                variant="outline"
                                className="bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800"
                              >
                                Default
                              </Badge>
                            )}
                            {option.is_required && (
                              <Badge
                                variant="outline"
                                className="bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800"
                              >
                                Required
                              </Badge>
                            )}
                          </>
                        )}
                        {!option.is_default_for_package &&
                          !option.is_required && (
                            <Badge
                              variant="outline"
                              className="bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700"
                            >
                              Optional
                            </Badge>
                          )}
                      </div>
                    </td>
                    <td className="p-2 text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditOption(option.id)}
                        >
                          <Pencil className="h-4 w-4 mr-1" /> Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteOption(option.id)}
                          className="text-destructive hover:bg-destructive/10 hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Package Option Form Dialog */}
        {isFormOpen && packageId && (
          <PackageOptionForm
            isOpen={isFormOpen}
            onClose={handleFormClose}
            packageId={packageId}
            optionId={selectedOptionId}
            initialData={
              selectedOption
                ? {
                    option_name: selectedOption.option_name,
                    option_code: selectedOption.option_code,
                    description: selectedOption.description || "",
                    price_adjustment:
                      selectedOption.price_adjustment.toString(),
                    cost_adjustment: selectedOption.cost_adjustment.toString(),
                    is_default_for_package:
                      selectedOption.is_default_for_package,
                    is_required: selectedOption.is_required,
                  }
                : undefined
            }
          />
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete this package option. This action
                cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-destructive text-destructive-foreground"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
};
