import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import AdminLayout from "@/components/layout/AdminLayout";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import { showError } from "@/lib/notifications";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import {
  TemplateList,
  TemplateDetailsDialog,
  TemplateEditDialog,
} from "./components";
import { CreateTemplateFromCalculationDialog } from "@/pages/templates/components";
import { getAllTemplates } from "@/services/admin/templates";
import { API_CONFIG } from "@/integrations/api/config";

const TemplatesPage: React.FC = () => {
  const [isTemplateFromCalculationOpen, setIsTemplateFromCalculationOpen] =
    useState(false);
  const [isTemplateDetailsOpen, setIsTemplateDetailsOpen] = useState(false);
  const [isTemplateEditOpen, setIsTemplateEditOpen] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(
    null
  );
  const [viewingTemplateId, setViewingTemplateId] = useState<string | null>(
    null
  );

  // State for showing inactive templates
  const [showInactiveTemplates, setShowInactiveTemplates] = useState(false);

  // Fetch templates
  const {
    data: allTemplates,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["templates"],
    queryFn: async () => {
      try {
        console.log(
          "%cTemplatesPage: Starting API request",
          "color: blue; font-weight: bold"
        );
        console.log("API Config BASE_URL:", API_CONFIG.BASE_URL);
        console.log(
          "Environment variable:",
          import.meta.env.VITE_EVENT_COSTING_API_URL
        );

        console.log(
          "%cCalling getAllTemplates service function",
          "color: blue"
        );
        // Always fetch all templates (both active and inactive)
        const result = await getAllTemplates();

        console.log(
          "%cTemplatesPage: Templates fetched",
          "color: green; font-weight: bold",
          {
            resultType: typeof result,
            isArray: Array.isArray(result),
            length: Array.isArray(result) ? result.length : "N/A",
            data: result,
          }
        );

        // Debug: Log each template's is_deleted status
        if (Array.isArray(result)) {
          console.log(
            "%cTemplate is_deleted status:",
            "color: blue; font-weight: bold"
          );
          result.forEach((template) => {
            console.log(
              `Template ${template.id} (${template.name}): is_deleted = ${template.is_deleted}`
            );
          });
        }

        // Ensure we always return an array
        if (!Array.isArray(result)) {
          console.warn(
            "%cgetAllTemplates did not return an array:",
            "color: orange",
            result
          );
          return [];
        }

        return result;
      } catch (error) {
        console.error(
          "%cError in templates query function:",
          "color: red; font-weight: bold",
          {
            error,
            message: error.message,
            stack: error.stack,
          }
        );
        showError("Failed to load templates");
        return [];
      }
    },
    meta: {
      onError: (error: Error) => {
        console.error(
          "%cError fetching templates:",
          "color: red; font-weight: bold",
          {
            error,
            message: error.message,
            stack: error.stack,
          }
        );
        showError("Failed to load templates");
      },
    },
  });

  // Log error details if query failed
  React.useEffect(() => {
    if (isError && error) {
      console.error("%cQuery error details:", "color: red; font-weight: bold", {
        error,
        message: error.message,
        stack: error.stack,
      });
    }
  }, [isError, error]);

  const handleOpenTemplateFromCalculation = () => {
    // In a real implementation, you would first show a dialog to select a calculation
    // and then set the calculationId, calculationName, attendees, and eventType
    // For now, we'll use the sample values
    setIsTemplateFromCalculationOpen(true);
  };

  const handleEditTemplate = (templateId: string) => {
    setEditingTemplateId(templateId);
    setIsTemplateEditOpen(true);
  };

  const handleViewTemplate = (templateId: string) => {
    setViewingTemplateId(templateId);
    setIsTemplateDetailsOpen(true);
  };

  const handleTemplateFromCalculationClose = (
    shouldRefresh: boolean = false
  ) => {
    setIsTemplateFromCalculationOpen(false);

    if (shouldRefresh) {
      refetch();
    }
  };

  const handleTemplateDetailsClose = () => {
    setIsTemplateDetailsOpen(false);
    setViewingTemplateId(null);
  };

  const handleTemplateEditClose = (shouldRefresh: boolean = false) => {
    setIsTemplateEditOpen(false);
    setEditingTemplateId(null);

    if (shouldRefresh) {
      refetch();
    }
  };

  return (
    <AdminLayout title="Manage Calculation Templates">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Templates</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="mb-6 text-muted-foreground">
        Create and manage calculation templates for quick event setup.
      </div>

      {/* Action Bar Block */}
      <div className="mb-6 flex justify-between items-center">
        <Button onClick={handleOpenTemplateFromCalculation}>
          <Plus className="w-4 h-4 mr-2" /> Create Template from Calculation
        </Button>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="show-inactive"
            checked={showInactiveTemplates}
            onCheckedChange={(checked) =>
              setShowInactiveTemplates(checked as boolean)
            }
          />
          <Label htmlFor="show-inactive" className="text-sm cursor-pointer">
            Show Inactive Templates
          </Label>
        </div>
      </div>

      {/* Template List Block */}
      <TemplateList
        templates={
          allTemplates
            ? showInactiveTemplates
              ? allTemplates // Show all templates when showInactiveTemplates is true
              : allTemplates.filter((template) => !template.is_deleted) // Filter out inactive templates
            : []
        }
        isLoading={isLoading}
        isError={isError}
        onEdit={handleEditTemplate}
        onView={handleViewTemplate}
        onRefresh={refetch}
      />

      {/* Create Template from Calculation Dialog */}
      <CreateTemplateFromCalculationDialog
        isOpen={isTemplateFromCalculationOpen}
        onClose={handleTemplateFromCalculationClose}
      />

      {/* View Template Details Dialog */}
      <TemplateDetailsDialog
        isOpen={isTemplateDetailsOpen}
        onClose={handleTemplateDetailsClose}
        templateId={viewingTemplateId}
      />

      {/* Edit Template Dialog */}
      <TemplateEditDialog
        isOpen={isTemplateEditOpen}
        onClose={handleTemplateEditClose}
        templateId={editingTemplateId}
      />
    </AdminLayout>
  );
};

export default TemplatesPage;
