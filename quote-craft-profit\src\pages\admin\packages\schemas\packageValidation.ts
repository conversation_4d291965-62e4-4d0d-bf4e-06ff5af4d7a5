import { z } from 'zod';
import { QuantityBasisEnum } from '@/types/types';

/**
 * Shared validation schemas for package forms
 * This file centralizes all package-related validation logic to avoid duplication
 */

// Base package form validation schema
export const packageFormSchema = z.object({
  name: z.string().min(2, 'Package name must be at least 2 characters'),
  description: z.string().optional(),
  categoryId: z.string().min(1, 'Category is required'),
  divisionId: z.string().min(1, 'Division is required'),
  cityIds: z.array(z.string()).min(1, 'At least one city must be selected'),
  venueIds: z.array(z.string()).optional(),
  enableVenues: z.boolean().optional(),
  quantityBasis: z.nativeEnum(QuantityBasisEnum, {
    required_error: 'Quantity basis is required',
  }),
  isActive: z.boolean(),
  price: z.string()
    .min(1, 'Price is required')
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
      message: 'Price must be a valid number greater than 0',
    }),
  unitBaseCost: z.string()
    .min(1, 'Base cost is required')
    .refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
      message: 'Base cost must be a valid number',
    }),
  currencyId: z.string().uuid('Currency ID must be a valid UUID'),
}).refine((data) => {
  // If venues are enabled, at least one venue must be selected
  if (data.enableVenues && (!data.venueIds || data.venueIds.length === 0)) {
    return false;
  }
  return true;
}, {
  message: 'At least one venue must be selected when venue restrictions are enabled',
  path: ['venueIds'],
});

// Default form values for package forms
export const defaultPackageFormValues = {
  name: '',
  description: '',
  categoryId: '',
  divisionId: '',
  cityIds: [],
  venueIds: [],
  enableVenues: false,
  quantityBasis: QuantityBasisEnum.PER_EVENT,
  isActive: true,
  price: '',
  unitBaseCost: '',
  currencyId: '',
} as const;

// Validation schema for package creation (same as base)
export const createPackageSchema = packageFormSchema;

// Validation schema for package updates (same as base)
export const updatePackageSchema = packageFormSchema;
