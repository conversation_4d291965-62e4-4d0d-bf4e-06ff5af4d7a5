/**
 * API integration for calculation line items
 *
 * This file contains functions for interacting with the backend API
 * for line item operations, replacing direct Supabase calls.
 */

import {
  LineItem,
  LineItemInput,
  LineItemOption,
  QuantityBasisEnum,
} from "@/types/calculation";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";
import {
  showError,
  showSuccess,
  showLoading,
  dismissToast,
  updateToast,
} from "@/lib/notifications";

/**
 * Fetch all line items for a calculation from the backend API
 * @param calculationId - The calculation ID
 * @returns List of line items
 */
export const getCalculationLineItemsFromApi = async (
  calculationId: string
): Promise<LineItem[]> => {
  try {
    console.log(
      `[API] Fetching line items for calculation ID: ${calculationId}`
    );
    // Don't show loading toast for routine data fetching

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to get all line items (using updated endpoint)
    const response = await authClient.get(
      API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.GET_ALL(calculationId)
    );

    // No loading toast to dismiss since we don't show them for routine operations

    // Transform the data to match the expected format
    const lineItems: LineItem[] = response.data.map((item: any) => ({
      id: item.id,
      calculation_id: calculationId,
      package_id: item.package_id || undefined,
      name: item.is_custom ? item.item_name : item.item_name_snapshot,
      description: item.is_custom ? item.description : item.notes || undefined,
      quantity: item.is_custom ? item.item_quantity : item.item_quantity, // Both use item_quantity from backend
      item_quantity_basis: item.is_custom
        ? item.item_quantity_basis || 1 // Use item_quantity_basis for custom items too
        : item.item_quantity_basis || item.duration_days || 1,
      unit_price: item.is_custom ? item.unit_price : item.unit_base_price,
      total_price: item.is_custom
        ? item.item_quantity * item.unit_price // Use item_quantity for calculation
        : item.calculated_line_total,
      category_id: item.category_id || "",
      is_custom: item.is_custom,
      // Convert quantity_basis string to enum
      quantity_basis:
        item.quantity_basis && typeof item.quantity_basis === "string"
          ? QuantityBasisEnum[
              item.quantity_basis as keyof typeof QuantityBasisEnum
            ] || QuantityBasisEnum.PER_DAY
          : QuantityBasisEnum.PER_DAY,
      selectedOptions:
        item.options?.map((option: any) => option.option_id) || [],
      created_at: item.created_at,
      updated_at: item.updated_at,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at),
    }));

    console.log(`[API] Fetched ${lineItems.length} line items:`, lineItems);

    // Don't show routine loading notifications - they're not important enough for users

    return lineItems;
  } catch (error) {
    console.error(
      `[API] Error fetching line items for calculation ID ${calculationId}:`,
      error
    );
    showError("Failed to load line items", {
      description:
        "There was an error loading the line items. Please try again.",
    });
    throw error;
  }
};

/**
 * Get line item options for a specific line item from the backend API
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID
 * @returns List of line item options
 */
export const getLineItemOptionsFromApi = async (
  calculationId: string,
  lineItemId: string
): Promise<LineItemOption[]> => {
  try {
    console.log(`[API] Fetching options for line item ID: ${lineItemId}`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to get line item details including options
    const response = await authClient.get(
      API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.GET_BY_ID(calculationId, lineItemId)
    );

    // Extract options from the response
    const options: LineItemOption[] =
      response.data.options?.map((option: any) => ({
        id: option.id,
        line_item_id: lineItemId,
        package_option_id: option.option_id,
        name: option.option_name || "Unknown Option",
        price_adjustment: option.price_adjustment_snapshot,
      })) || [];

    console.log(`[API] Fetched ${options.length} options for line item`);
    return options;
  } catch (error) {
    console.error(
      `[API] Error fetching options for line item ID ${lineItemId}:`,
      error
    );
    showError("Failed to load line item options", {
      description:
        "There was an error loading the options for this line item. Please try again.",
    });
    throw error;
  }
};

/**
 * Add a line item to a calculation using the backend API
 * @param calculationId - The calculation ID
 * @param lineItem - The line item data to add
 * @returns The created line item
 */
export const addLineItemFromApi = async (
  calculationId: string,
  lineItem: LineItemInput
): Promise<LineItem> => {
  try {
    console.log(
      `[API] Adding line item to calculation ID ${calculationId}:`,
      lineItem
    );

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Determine if this is a package or custom item
    if (!lineItem.is_custom && lineItem.package_id) {
      // This is a package line item
      const response = await authClient.post(
        API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.ADD_PACKAGE(calculationId),
        {
          package_id: lineItem.package_id,
          option_ids: lineItem.selectedOptions || [],
          quantity: lineItem.quantity,
          duration_days: lineItem.item_quantity_basis || 1,
          notes: lineItem.description || "",
        }
      );

      // Fetch the newly created line item
      const newItem = await getLineItemByIdFromApi(
        calculationId,
        response.data.id
      );

      // Show success notification
      showSuccess("Line item added successfully", {
        description: `Added "${newItem.name}" to the calculation.`,
      });

      return newItem;
    } else {
      // This is a custom line item
      const response = await authClient.post(
        API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.ADD_CUSTOM(calculationId),
        {
          itemName: lineItem.name, // Use camelCase field name
          description: lineItem.description || "",
          quantity: lineItem.quantity,
          unitPrice: lineItem.unit_price || 0, // Use camelCase field name
          unitCost: 0, // Default unit cost
          itemQuantityBasis: lineItem.item_quantity_basis || 1, // Add missing field
          quantityBasis: lineItem.quantity_basis || "PER_DAY", // Add missing field
          categoryId: lineItem.category_id || null, // Use camelCase field name
        }
      );

      // Fetch the newly created line item
      const newItem = await getLineItemByIdFromApi(
        calculationId,
        response.data.id
      );

      // Show success notification
      showSuccess("Custom line item added successfully", {
        description: `Added "${newItem.name}" to the calculation.`,
      });

      return newItem;
    }
  } catch (error) {
    console.error(
      `[API] Error adding line item to calculation ID ${calculationId}:`,
      error
    );
    showError("Failed to add line item", {
      description:
        "There was an error adding the line item to the calculation. Please try again.",
    });
    throw error;
  }
};

/**
 * Helper function to get a line item by ID from the backend API
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID
 * @returns The line item
 */
export const getLineItemByIdFromApi = async (
  calculationId: string,
  lineItemId: string
): Promise<LineItem> => {
  try {
    console.log(
      `[API] Fetching line item ID: ${lineItemId} for calculation ID: ${calculationId}`
    );

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to get line item details
    const response = await authClient.get(
      API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.GET_BY_ID(calculationId, lineItemId)
    );

    const item = response.data;

    // Transform to LineItem format
    return {
      id: item.id,
      calculation_id: calculationId,
      package_id: item.package_id || undefined,
      name: item.is_custom ? item.item_name : item.item_name_snapshot,
      description: item.is_custom ? item.description : item.notes || undefined,
      quantity: item.is_custom ? item.item_quantity : item.item_quantity, // Both use item_quantity from backend
      item_quantity_basis: item.is_custom
        ? item.item_quantity_basis || 1 // Use item_quantity_basis for custom items too
        : item.item_quantity_basis || item.duration_days || 1,
      unit_price: item.is_custom ? item.unit_price : item.unit_base_price,
      total_price: item.is_custom
        ? item.item_quantity * item.unit_price // Use item_quantity for calculation
        : item.calculated_line_total,
      category_id: item.category_id || "",
      is_custom: item.is_custom,
      // Convert quantity_basis string to enum
      quantity_basis:
        item.quantity_basis && typeof item.quantity_basis === "string"
          ? QuantityBasisEnum[
              item.quantity_basis as keyof typeof QuantityBasisEnum
            ] || QuantityBasisEnum.PER_DAY
          : QuantityBasisEnum.PER_DAY,
      selectedOptions:
        item.options?.map((option: any) => option.option_id) || [],
      created_at: item.created_at,
      updated_at: item.updated_at,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at),
    };
  } catch (error) {
    console.error(`[API] Error fetching line item ID ${lineItemId}:`, error);
    showError("Failed to load line item", {
      description:
        "There was an error loading the line item details. Please try again.",
    });
    throw error;
  }
};

/**
 * Update a line item using the backend API
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID
 * @param updates - The line item data to update
 * @returns The updated line item
 */
export const updateLineItemFromApi = async (
  calculationId: string,
  lineItemId: string,
  updates: Partial<LineItemInput>
): Promise<LineItem> => {
  try {
    console.log(
      `[API] Updating line item ${lineItemId} in calculation ${calculationId}:`,
      updates
    );

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Get the current line item to determine if it's a package or custom item
    const currentItem = await getLineItemByIdFromApi(calculationId, lineItemId);

    // Prepare the update payload based on item type
    const updatePayload = currentItem.is_custom
      ? {
          itemName: updates.name, // Use camelCase field name
          description: updates.description,
          quantity: updates.quantity,
          unitPrice: updates.unit_price, // Use camelCase field name
          itemQuantityBasis: updates.item_quantity_basis, // Add missing field
          quantityBasis: updates.quantity_basis, // Add missing field
          categoryId: updates.category_id, // Use camelCase field name
        }
      : {
          quantity: updates.quantity,
          duration_days: updates.item_quantity_basis,
          notes: updates.description,
        };

    // Make API request to update the line item
    await authClient.put(
      API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.UPDATE(calculationId, lineItemId),
      updatePayload
    );

    // Fetch the updated line item
    const updatedItem = await getLineItemByIdFromApi(calculationId, lineItemId);

    // Show success notification
    showSuccess("Line item updated successfully", {
      description: `Updated "${updatedItem.name}" in the calculation.`,
    });

    return updatedItem;
  } catch (error) {
    console.error(
      `[API] Error updating line item ${lineItemId} in calculation ${calculationId}:`,
      error
    );
    showError("Failed to update line item", {
      description:
        "There was an error updating the line item. Please try again.",
    });
    throw error;
  }
};

/**
 * Remove a line item from a calculation using the backend API
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID
 */
export const removeLineItemFromApi = async (
  calculationId: string,
  lineItemId: string
): Promise<void> => {
  try {
    console.log(
      `[API] Removing line item ${lineItemId} from calculation ${calculationId}`
    );

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to delete the line item
    await authClient.delete(
      API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.DELETE(calculationId, lineItemId)
    );

    console.log(
      `[API] Successfully removed line item ${lineItemId} from calculation ${calculationId}`
    );

    // Show success notification
    showSuccess("Line item removed successfully", {
      description: "The line item has been removed from the calculation.",
    });
  } catch (error) {
    console.error(
      `[API] Error removing line item ${lineItemId} from calculation ${calculationId}:`,
      error
    );
    showError("Failed to remove line item", {
      description:
        "There was an error removing the line item from the calculation. Please try again.",
    });
    throw error;
  }
};

/**
 * Delete a line item using the backend API
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID to delete
 */
export const deleteLineItemFromApi = async (
  calculationId: string,
  lineItemId: string
): Promise<void> => {
  try {
    console.log(
      `[API] Deleting line item ${lineItemId} from calculation ${calculationId}`
    );

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to delete line item
    await authClient.delete(
      API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.DELETE(calculationId, lineItemId)
    );

    console.log(
      `[API] Successfully deleted line item ${lineItemId} from calculation ${calculationId}`
    );
  } catch (error) {
    console.error(
      `[API] Error deleting line item ${lineItemId} from calculation ${calculationId}:`,
      error
    );
    showError("Failed to delete line item", {
      description:
        "There was an error deleting the line item. Please try again.",
    });
    throw error;
  }
};

/**
 * Recalculate totals for a calculation using the backend API
 * This replaces the direct Supabase RPC call with an API endpoint
 * @param calculationId - The calculation ID
 */
export const recalculateCalculationTotalsFromApi = async (
  calculationId: string
): Promise<void> => {
  try {
    console.log(
      `[API] Triggering recalculation for calculation ID: ${calculationId}`
    );

    // Show loading toast
    const loadingToastId = showLoading("Recalculating totals...");

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to recalculate totals
    await authClient.post(
      API_ENDPOINTS.CALCULATIONS.RECALCULATE(calculationId)
    );

    // Update toast to success
    if (typeof loadingToastId === "string") {
      dismissToast(loadingToastId);
    }
    showSuccess("Recalculation complete", {
      description: "Calculation totals have been updated.",
    });

    console.log(
      `[API] Recalculation completed for calculation ID: ${calculationId}`
    );
  } catch (error) {
    console.error(
      `[API] Error recalculating totals for calculation ${calculationId}:`,
      error
    );
    showError("Failed to recalculate totals", {
      description:
        "There was an error recalculating the totals. Please try again.",
    });
    throw error;
  }
};
