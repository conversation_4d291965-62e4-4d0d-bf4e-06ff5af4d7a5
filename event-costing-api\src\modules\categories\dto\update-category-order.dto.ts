import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Int, <PERSON>UUI<PERSON>, <PERSON>, ValidateNested } from 'class-validator';

/**
 * DTO for a single category order item
 */
export class CategoryOrderItemDto {
  /**
   * The UUID of the category
   */
  @IsUUID()
  id: string;

  /**
   * The display order of the category (1-based)
   */
  @IsInt()
  @Min(1)
  display_order: number;
}

/**
 * DTO for updating the order of multiple categories
 */
export class UpdateCategoryOrderDto {
  /**
   * Array of category order items
   */
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CategoryOrderItemDto)
  categories: CategoryOrderItemDto[];
}
