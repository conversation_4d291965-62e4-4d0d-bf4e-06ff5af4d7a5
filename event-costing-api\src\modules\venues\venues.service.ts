import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { VenueReferenceDto } from './dto/venue-reference.dto';
import {
  AdminVenueDto,
  CreateVenueDto,
  ListVenuesQueryDto,
  PaginatedVenuesResponse,
  UpdateVenueDto,
} from './dto/admin-venue.dto';

@Injectable()
export class VenuesService {
  private readonly logger = new Logger(VenuesService.name);
  private readonly tableName = 'venues';

  constructor(private readonly supabaseService: SupabaseService) {}

  async findAll(): Promise<VenueReferenceDto[]> {
    this.logger.log('Finding all venues');
    return this.findAllWithFilters();
  }

  /**
   * Find all venues with optional filters
   * @param cityId - Optional city ID to filter venues by
   * @param active - Optional active status to filter venues by
   * @returns Promise resolving to an array of venues
   */
  async findAllWithFilters(
    cityId?: string,
    active?: boolean,
  ): Promise<VenueReferenceDto[]> {
    this.logger.log(
      `Finding venues with filters: cityId=${cityId}, active=${active}`,
    );
    const supabase = this.supabaseService.getClient();

    let query = supabase
      .from(this.tableName)
      .select(
        `
        id,
        name,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        cities (name)
      `,
      )
      .eq('is_deleted', false);

    // Apply city filter if provided
    if (cityId) {
      query = query.eq('city_id', cityId);
    }

    // Apply active filter if provided
    if (active !== undefined) {
      query = query.eq('is_active', active);
    } else {
      // Default to active venues only
      query = query.eq('is_active', true);
    }

    // Order by name
    query = query.order('name');

    const { data, error } = await query;

    if (error) {
      this.logger.error(`Error fetching venues: ${error.message}`);
      throw error;
    }

    // Log the structure of the first venue to understand the data format
    if (data.length > 0) {
      this.logger.debug(`Venue data structure: ${JSON.stringify(data[0])}`);
      this.logger.debug(`Cities structure: ${JSON.stringify(data[0].cities)}`);
    }

    // Transform the data to match the VenueReferenceDto
    return data.map(venue => {
      let cityName: string | undefined = undefined;
      if (venue.cities) {
        // Check if cities is an array with at least one item
        if (Array.isArray(venue.cities) && venue.cities.length > 0) {
          cityName = venue.cities[0].name;
        }
        // Check if cities has a name property directly
        else if (typeof venue.cities === 'object' && 'name' in venue.cities) {
          cityName = venue.cities.name as string;
        }
      }

      return {
        id: venue.id,
        name: venue.name,
        address: venue.address,
        city_id: venue.city_id,
        city_name: cityName,
        classification: venue.classification,
        capacity: venue.capacity,
        image_url: venue.image_url,
        features: venue.features,
      };
    });
  }

  async findByIds(ids: string[]): Promise<VenueReferenceDto[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    this.logger.log(`Finding venues by IDs: ${ids.join(', ')}`);
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .select(
        `
        id,
        name,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        cities (name)
      `,
      )
      .in('id', ids)
      .eq('is_deleted', false)
      .eq('is_active', true)
      .order('name');

    if (error) {
      this.logger.error(`Error fetching venues by IDs: ${error.message}`);
      throw error;
    }

    // Transform the data to match the VenueReferenceDto
    return data.map(venue => {
      // Extract city name from the cities object/array
      let cityName: string | undefined = undefined;
      if (venue.cities) {
        // Check if cities is an array with at least one item
        if (Array.isArray(venue.cities) && venue.cities.length > 0) {
          cityName = venue.cities[0].name;
        }
        // Check if cities has a name property directly
        else if (typeof venue.cities === 'object' && 'name' in venue.cities) {
          cityName = venue.cities.name as string;
        }
      }

      return {
        id: venue.id,
        name: venue.name,
        address: venue.address,
        city_id: venue.city_id,
        city_name: cityName,
        classification: venue.classification,
        capacity: venue.capacity,
        image_url: venue.image_url,
        features: venue.features,
      };
    });
  }

  /**
   * Find venues with enhanced filtering for Dashboard V2
   * @param cityId - Optional city ID to filter venues by
   * @param active - Optional active status to filter venues by
   * @param classification - Optional classification to filter venues by
   * @param minCapacity - Optional minimum capacity filter
   * @param maxCapacity - Optional maximum capacity filter
   * @returns Promise resolving to an array of venues
   */
  async findWithEnhancedFilters(
    cityId?: string,
    active?: boolean,
    classification?: string,
    minCapacity?: number,
    maxCapacity?: number,
  ): Promise<VenueReferenceDto[]> {
    this.logger.log(
      `Finding venues with enhanced filters: cityId=${cityId}, active=${active}, classification=${classification}, minCapacity=${minCapacity}, maxCapacity=${maxCapacity}`,
    );
    const supabase = this.supabaseService.getClient();

    let query = supabase
      .from(this.tableName)
      .select(
        `
        id,
        name,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        cities (name)
      `,
      )
      .eq('is_deleted', false);

    // Apply city filter if provided
    if (cityId) {
      query = query.eq('city_id', cityId);
    }

    // Apply active filter if provided
    if (active !== undefined) {
      query = query.eq('is_active', active);
    } else {
      // Default to active venues only
      query = query.eq('is_active', true);
    }

    // Apply classification filter if provided
    if (classification) {
      query = query.eq('classification', classification);
    }

    // Apply capacity filters if provided
    if (minCapacity !== undefined) {
      query = query.gte('capacity', minCapacity);
    }

    if (maxCapacity !== undefined) {
      query = query.lte('capacity', maxCapacity);
    }

    // Order by capacity descending, then by name
    query = query.order('capacity', { ascending: false }).order('name');

    const { data, error } = await query;

    if (error) {
      this.logger.error(
        `Error fetching venues with enhanced filters: ${error.message}`,
      );
      throw error;
    }

    // Transform the data to match the VenueReferenceDto
    return data.map(venue => {
      let cityName: string | undefined = undefined;
      if (venue.cities) {
        // Check if cities is an array with at least one item
        if (Array.isArray(venue.cities) && venue.cities.length > 0) {
          cityName = venue.cities[0].name;
        }
        // Check if cities has a name property directly
        else if (typeof venue.cities === 'object' && 'name' in venue.cities) {
          cityName = venue.cities.name as string;
        }
      }

      return {
        id: venue.id,
        name: venue.name,
        address: venue.address,
        city_id: venue.city_id,
        city_name: cityName,
        classification: venue.classification,
        capacity: venue.capacity,
        image_url: venue.image_url,
        features: venue.features,
      };
    });
  }

  // --- Admin Methods --- //

  /**
   * Find all venues with pagination and filtering (admin)
   */
  async findAllAdmin(
    queryDto: ListVenuesQueryDto,
  ): Promise<PaginatedVenuesResponse> {
    this.logger.log(
      `Finding all venues (admin) with filters: ${JSON.stringify(queryDto)}`,
    );
    const supabase = this.supabaseService.getClient();

    // Set default values
    const page = queryDto.page || 1;
    const pageSize = queryDto.pageSize || 10;
    const sortBy = queryDto.sortBy || 'name';
    const sortOrder = queryDto.sortOrder || 'asc';

    // Calculate pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // Build the query
    let query = supabase.from(this.tableName).select(
      `
        id,
        name,
        description,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        is_deleted,
        deleted_at,
        created_at,
        updated_at,
        cities (name)
        `,
      { count: 'exact' },
    );

    // Apply filters
    if (queryDto.search) {
      query = query.ilike('name', `%${queryDto.search}%`);
    }

    if (queryDto.cityId) {
      query = query.eq('city_id', queryDto.cityId);
    }

    if (queryDto.classification) {
      query = query.eq('classification', queryDto.classification);
    }

    if (queryDto.minCapacity) {
      query = query.gte('capacity', queryDto.minCapacity);
    }

    if (queryDto.maxCapacity) {
      query = query.lte('capacity', queryDto.maxCapacity);
    }

    // Only show non-deleted venues unless showDeleted is true
    if (!queryDto.showDeleted) {
      query = query.eq('is_deleted', false);
    }

    // Apply sorting and pagination
    query = query
      .order(sortBy, { ascending: sortOrder === 'asc' })
      .range(from, to);

    // Execute the query
    const { data, error, count } = await query;

    if (error) {
      this.logger.error(`Error fetching venues (admin): ${error.message}`);
      throw new InternalServerErrorException('Failed to fetch venues');
    }

    // Log the structure of the first venue to understand the data format
    if (data.length > 0) {
      this.logger.debug(
        `Admin venue data structure: ${JSON.stringify(data[0])}`,
      );
      this.logger.debug(`Cities structure: ${JSON.stringify(data[0].cities)}`);
    }

    // Transform the data to match the AdminVenueDto
    const venues = data.map(venue => {
      // Extract city name from the cities object/array
      let cityName: string | undefined = undefined;
      if (venue.cities) {
        // Check if cities is an array with at least one item
        if (Array.isArray(venue.cities) && venue.cities.length > 0) {
          cityName = venue.cities[0].name;
        }
        // Check if cities has a name property directly
        else if (typeof venue.cities === 'object' && 'name' in venue.cities) {
          cityName = venue.cities.name as string;
        }
      }

      return {
        id: venue.id,
        name: venue.name,
        description: venue.description,
        address: venue.address,
        city_id: venue.city_id,
        city_name: cityName,
        classification: venue.classification,
        capacity: venue.capacity,
        image_url: venue.image_url,
        features: venue.features,
        is_active: venue.is_active,
        is_deleted: venue.is_deleted,
        deleted_at: venue.deleted_at,
        created_at: venue.created_at,
        updated_at: venue.updated_at,
      };
    });

    // Calculate total pages
    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      data: venues,
      totalCount,
      page,
      pageSize,
      totalPages,
    };
  }

  /**
   * Find a venue by ID (admin)
   */
  async findOneAdmin(id: string): Promise<AdminVenueDto> {
    this.logger.log(`Finding venue (admin) with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .select(
        `
        id,
        name,
        description,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        is_deleted,
        deleted_at,
        created_at,
        updated_at,
        cities (name)
        `,
      )
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(`Venue with ID ${id} not found`);
      }
      this.logger.error(
        `Error fetching venue (admin) with ID ${id}: ${error.message}`,
      );
      throw new InternalServerErrorException('Failed to fetch venue');
    }

    // Log the structure to understand the data format
    this.logger.debug(`Venue detail data structure: ${JSON.stringify(data)}`);
    if (data.cities) {
      this.logger.debug(`Cities structure: ${JSON.stringify(data.cities)}`);
    }

    // Extract city name from the cities object/array
    let cityName: string | undefined = undefined;
    if (data.cities) {
      // Check if cities is an array with at least one item
      if (Array.isArray(data.cities) && data.cities.length > 0) {
        cityName = data.cities[0].name;
      }
      // Check if cities has a name property directly
      else if (typeof data.cities === 'object' && 'name' in data.cities) {
        cityName = data.cities.name as string;
      }
    }

    return {
      id: data.id,
      name: data.name,
      description: data.description,
      address: data.address,
      city_id: data.city_id,
      city_name: cityName,
      classification: data.classification,
      capacity: data.capacity,
      image_url: data.image_url,
      features: data.features,
      is_active: data.is_active,
      is_deleted: data.is_deleted,
      deleted_at: data.deleted_at,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
  }

  /**
   * Create a new venue
   */
  async create(createDto: CreateVenueDto): Promise<AdminVenueDto> {
    this.logger.log(`Creating venue: ${JSON.stringify(createDto)}`);
    const supabase = this.supabaseService.getClient();

    // Set default values
    const venueData = {
      name: createDto.name,
      description: createDto.description || null,
      address: createDto.address || null,
      city_id: createDto.city_id || null,
      classification: createDto.classification || null,
      capacity: createDto.capacity || null,
      image_url: createDto.image_url || null,
      features: createDto.features || null,
      is_active: createDto.is_active !== undefined ? createDto.is_active : true,
      is_deleted: false,
    };

    const { data, error } = await supabase
      .from(this.tableName)
      .insert([venueData])
      .select(
        `
        id,
        name,
        description,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        is_deleted,
        deleted_at,
        created_at,
        updated_at,
        cities (name)
        `,
      )
      .single();

    if (error) {
      this.logger.error(`Error creating venue: ${error.message}`);
      throw new InternalServerErrorException('Failed to create venue');
    }

    // Log the structure to understand the data format
    if (data.cities) {
      this.logger.debug(
        `Cities structure for create: ${JSON.stringify(data.cities)}`,
      );
    }

    // Extract city name from the cities object/array
    let cityName: string | undefined = undefined;
    if (data.cities) {
      // Check if cities is an array with at least one item
      if (Array.isArray(data.cities) && data.cities.length > 0) {
        cityName = data.cities[0].name;
      }
      // Check if cities has a name property directly
      else if (typeof data.cities === 'object' && 'name' in data.cities) {
        cityName = data.cities.name as string;
      }
    }

    return {
      id: data.id,
      name: data.name,
      description: data.description,
      address: data.address,
      city_id: data.city_id,
      city_name: cityName,
      classification: data.classification,
      capacity: data.capacity,
      image_url: data.image_url,
      features: data.features,
      is_active: data.is_active,
      is_deleted: data.is_deleted,
      deleted_at: data.deleted_at,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
  }

  /**
   * Update a venue
   */
  async update(id: string, updateDto: UpdateVenueDto): Promise<AdminVenueDto> {
    this.logger.log(
      `Updating venue with ID ${id}: ${JSON.stringify(updateDto)}`,
    );
    const supabase = this.supabaseService.getClient();

    // Check if venue exists
    const { data: existingVenue, error: checkError } = await supabase
      .from(this.tableName)
      .select('id')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        throw new NotFoundException(`Venue with ID ${id} not found`);
      }
      this.logger.error(
        `Error checking venue existence: ${checkError.message}`,
      );
      throw new InternalServerErrorException('Failed to update venue');
    }

    // Update the venue
    const { data, error } = await supabase
      .from(this.tableName)
      .update({
        name: updateDto.name,
        description: updateDto.description,
        address: updateDto.address,
        city_id: updateDto.city_id,
        classification: updateDto.classification,
        capacity: updateDto.capacity,
        image_url: updateDto.image_url,
        features: updateDto.features,
        is_active: updateDto.is_active,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select(
        `
        id,
        name,
        description,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        is_deleted,
        deleted_at,
        created_at,
        updated_at,
        cities (name)
        `,
      )
      .single();

    if (error) {
      this.logger.error(`Error updating venue: ${error.message}`);
      throw new InternalServerErrorException('Failed to update venue');
    }

    // Log the structure to understand the data format
    if (data.cities) {
      this.logger.debug(
        `Cities structure for update: ${JSON.stringify(data.cities)}`,
      );
    }

    // Extract city name from the cities object/array
    let cityName: string | undefined = undefined;
    if (data.cities) {
      // Check if cities is an array with at least one item
      if (Array.isArray(data.cities) && data.cities.length > 0) {
        cityName = data.cities[0].name;
      }
      // Check if cities has a name property directly
      else if (typeof data.cities === 'object' && 'name' in data.cities) {
        cityName = data.cities.name as string;
      }
    }

    return {
      id: data.id,
      name: data.name,
      description: data.description,
      address: data.address,
      city_id: data.city_id,
      city_name: cityName,
      classification: data.classification,
      capacity: data.capacity,
      image_url: data.image_url,
      features: data.features,
      is_active: data.is_active,
      is_deleted: data.is_deleted,
      deleted_at: data.deleted_at,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
  }

  /**
   * Soft delete a venue
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`Soft deleting venue with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    // Check if venue exists
    const { data: existingVenue, error: checkError } = await supabase
      .from(this.tableName)
      .select('id, is_deleted')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        throw new NotFoundException(`Venue with ID ${id} not found`);
      }
      this.logger.error(
        `Error checking venue existence: ${checkError.message}`,
      );
      throw new InternalServerErrorException('Failed to delete venue');
    }

    if (existingVenue.is_deleted) {
      this.logger.warn(`Venue with ID ${id} is already deleted`);
      return;
    }

    // Soft delete the venue
    const { error } = await supabase
      .from(this.tableName)
      .update({
        is_deleted: true,
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);

    if (error) {
      this.logger.error(`Error deleting venue: ${error.message}`);
      throw new InternalServerErrorException('Failed to delete venue');
    }
  }

  /**
   * Restore a soft-deleted venue
   */
  async restore(id: string): Promise<void> {
    this.logger.log(`Restoring venue with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    // Check if venue exists and is deleted
    const { data: existingVenue, error: checkError } = await supabase
      .from(this.tableName)
      .select('id, is_deleted')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        throw new NotFoundException(`Venue with ID ${id} not found`);
      }
      this.logger.error(
        `Error checking venue existence: ${checkError.message}`,
      );
      throw new InternalServerErrorException('Failed to restore venue');
    }

    if (!existingVenue.is_deleted) {
      this.logger.warn(`Venue with ID ${id} is not deleted`);
      return;
    }

    // Restore the venue
    const { error } = await supabase
      .from(this.tableName)
      .update({
        is_deleted: false,
        deleted_at: null,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);

    if (error) {
      this.logger.error(`Error restoring venue: ${error.message}`);
      throw new InternalServerErrorException('Failed to restore venue');
    }
  }
}
