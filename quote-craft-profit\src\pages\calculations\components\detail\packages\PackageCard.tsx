import React, { memo, useMemo } from "react";
import { Plus, Minus, Info } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PackageWithOptions } from "@/types/calculation";
import { calculatePackagePrice } from "../../../utils/calculationUtils";

interface PackageCardProps {
  pkg: PackageWithOptions;
  quantity: number;
  itemQuantityBasis: number; // Renamed from days to itemQuantityBasis
  selectedOptions?: string[];
  onQuantityChange: (packageId: string, value: number) => void;
  onItemQuantityBasisChange: (packageId: string, value: number) => void; // Renamed from onDaysChange to onItemQuantityBasisChange
  onOptionToggle?: (
    packageId: string,
    optionId: string,
    isSelected: boolean
  ) => void;
  onAddToCalculation: (packageId: string) => void;
  isAlreadySelected?: boolean; // New prop to indicate if package is already in line items
}

const PackageCard: React.FC<PackageCardProps> = ({
  pkg,
  quantity,
  itemQuantityBasis,
  selectedOptions = [],
  onQuantityChange,
  onItemQuantityBasisChange,
  onOptionToggle = () => {},
  onAddToCalculation,
  isAlreadySelected = false,
}) => {
  // Ensure we have valid numeric values to prevent NaN errors
  const safeQuantity = Number(quantity) || 1;
  const safeItemQuantityBasis = Number(itemQuantityBasis) || 1;
  // Use the utility function to calculate price and generate formula (memoized)
  const packagePriceDetails = useMemo(() => {
    // Debug log to verify quantity_basis is being passed correctly
    if (process.env.NODE_ENV === "development") {
      console.log(`[PackageCard] Package ${pkg.name} data:`, {
        id: pkg.id,
        quantity_basis: pkg.quantity_basis,
        price: pkg.price,
        currency_symbol: pkg.currency_symbol,
        safeQuantity,
        safeItemQuantityBasis,
      });
    }

    const selectedOptionObjects = pkg.options.filter((opt) =>
      selectedOptions.includes(opt.id)
    );

    return calculatePackagePrice(
      pkg.price,
      safeQuantity,
      safeItemQuantityBasis,
      pkg.quantity_basis,
      selectedOptionObjects,
      pkg.currency_symbol
    );
  }, [
    pkg.id,
    pkg.name,
    pkg.price,
    pkg.quantity_basis,
    pkg.currency_symbol,
    pkg.options,
    safeQuantity,
    safeItemQuantityBasis,
    selectedOptions,
  ]);
  return (
    <Card
      className={`overflow-hidden border-2 transition-colors w-full ${
        isAlreadySelected
          ? "opacity-60 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800"
          : "hover:border-primary"
      }`}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle
            className={`text-base ${
              isAlreadySelected
                ? "text-gray-500 dark:text-gray-400"
                : "dark:text-white"
            }`}
          >
            {pkg.name}
            {isAlreadySelected && (
              <span className="ml-2 text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                Already Selected
              </span>
            )}
          </CardTitle>
        </div>
        <CardDescription className="line-clamp-2 text-xs">
          {pkg.description || "No description available"}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2 pt-0">
        <div className="flex flex-col space-y-3">
          {/* Package information */}
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center">
              <Badge variant="outline">
                {pkg.quantity_basis
                  ? pkg.quantity_basis.replace(/_/g, " ").toLowerCase()
                  : "fixed"}
              </Badge>
            </div>
            <div className="flex justify-end">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 cursor-help">
                      <Info size={14} className="mr-1" />
                      {pkg.options.length} options
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      This package has {pkg.options.length} options available
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {/* Base Price */}
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center">
              <span className="font-medium dark:text-gray-300">
                Base Price:
              </span>
              <span className="ml-2 dark:text-gray-300">
                {pkg.currency_symbol} {Number(pkg.price).toLocaleString()}
              </span>
            </div>
          </div>

          {/* Quantity and Days form */}
          <div className="grid grid-cols-2 gap-2 border-t dark:border-gray-700 border-b dark:border-gray-700 py-2">
            <div>
              <label className="text-xs text-gray-500 dark:text-gray-400 block mb-1">
                Quantity
              </label>
              <div className="flex items-center">
                <Button
                  type="button"
                  size="icon"
                  variant="outline"
                  className="h-7 w-7"
                  onClick={() =>
                    onQuantityChange(pkg.id, Math.max(1, safeQuantity - 1))
                  }
                  disabled={isAlreadySelected}
                >
                  <Minus size={12} />
                </Button>
                <Input
                  type="number"
                  min="1"
                  className="h-7 text-center mx-1 text-sm"
                  value={safeQuantity}
                  onChange={(e) =>
                    onQuantityChange(pkg.id, parseInt(e.target.value) || 1)
                  }
                  disabled={isAlreadySelected}
                />
                <Button
                  type="button"
                  size="icon"
                  variant="outline"
                  className="h-7 w-7"
                  onClick={() => onQuantityChange(pkg.id, safeQuantity + 1)}
                  disabled={isAlreadySelected}
                >
                  <Plus size={12} />
                </Button>
              </div>
            </div>

            <div>
              <label className="text-xs text-gray-500 dark:text-gray-400 block mb-1 capitalize">
                {(() => {
                  // Determine the appropriate label based on quantity_basis
                  const basis = pkg.quantity_basis || "PER_ITEM";
                  switch (basis) {
                    case "PER_EVENT":
                      return "Per Event";
                    case "PER_DAY":
                      return "Days";
                    case "PER_ATTENDEE":
                      return "Attendees";
                    case "PER_ITEM":
                      return "Items";
                    case "PER_ITEM_PER_DAY":
                      return "Days";
                    case "PER_ATTENDEE_PER_DAY":
                      return "Days";
                    default:
                      return "Days";
                  }
                })()}
                {!pkg.quantity_basis && (
                  <span className="text-xs text-red-500"> (missing)</span>
                )}
              </label>
              <div className="flex items-center">
                <Button
                  type="button"
                  size="icon"
                  variant="outline"
                  className="h-7 w-7"
                  onClick={() =>
                    onItemQuantityBasisChange(
                      pkg.id,
                      Math.max(1, safeItemQuantityBasis - 1)
                    )
                  }
                  disabled={isAlreadySelected}
                >
                  <Minus size={12} />
                </Button>
                <Input
                  type="number"
                  min="1"
                  className="h-7 text-center mx-1 text-sm"
                  value={safeItemQuantityBasis}
                  onChange={(e) =>
                    onItemQuantityBasisChange(
                      pkg.id,
                      parseInt(e.target.value) || 1
                    )
                  }
                  disabled={isAlreadySelected}
                />
                <Button
                  type="button"
                  size="icon"
                  variant="outline"
                  className="h-7 w-7"
                  onClick={() =>
                    onItemQuantityBasisChange(pkg.id, safeItemQuantityBasis + 1)
                  }
                  disabled={isAlreadySelected}
                >
                  <Plus size={12} />
                </Button>
              </div>
            </div>
          </div>

          {/* Total price with calculation formula */}
          <div className="flex flex-col space-y-1">
            <div className="flex justify-between items-center">
              <span className="font-medium text-sm dark:text-gray-300">
                Total Price:
              </span>
              <span className="font-bold text-primary">
                {pkg.currency_symbol}{" "}
                {packagePriceDetails.totalPrice.toLocaleString()}
              </span>
            </div>

            {/* Price calculation formula */}
            <div className="text-xs text-gray-500 dark:text-gray-400 italic text-right">
              {packagePriceDetails.formula}
            </div>
          </div>
        </div>

        {pkg.options.length > 0 && (
          <div className="mt-2 border-t dark:border-gray-700 pt-2">
            <p className="text-xs font-medium mb-1 dark:text-gray-300">
              Options:
            </p>
            <ul className="text-xs space-y-2">
              {pkg.options.slice(0, 3).map((option) => (
                <li
                  key={option.id}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <Checkbox
                      id={`option-${option.id}`}
                      checked={selectedOptions.includes(option.id)}
                      onCheckedChange={(checked) =>
                        onOptionToggle(pkg.id, option.id, checked === true)
                      }
                      disabled={isAlreadySelected}
                    />
                    <label
                      htmlFor={`option-${option.id}`}
                      className="text-xs cursor-pointer dark:text-gray-300"
                    >
                      {option.option_name}
                    </label>
                  </div>
                  <span
                    className={
                      (option.price_adjustment || 0) >= 0
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    {(option.price_adjustment || 0) >= 0 ? "+" : ""}
                    {pkg.currency_symbol}{" "}
                    {(option.price_adjustment || 0).toLocaleString()}
                  </span>
                </li>
              ))}
              {pkg.options.length > 3 && (
                <li className="text-gray-500 dark:text-gray-400 italic">
                  +{pkg.options.length - 3} more options
                </li>
              )}
            </ul>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-2 flex justify-end">
        <Button
          size="sm"
          onClick={() => onAddToCalculation(pkg.id)}
          disabled={isAlreadySelected}
        >
          <Plus size={16} className="mr-1" />
          {isAlreadySelected ? "Already Added" : "Add to Calculation"}
        </Button>
      </CardFooter>
    </Card>
  );
};

// Wrap with memo to prevent unnecessary re-renders
export default memo(PackageCard);
