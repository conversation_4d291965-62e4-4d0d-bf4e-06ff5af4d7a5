import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import {
  TemplateCalculationResultDto,
  CalculationBreakdownDto,
  TemplateCalculationSummaryDto,
} from '../dto/template-calculation.dto';
import { TemplateConstants } from '../constants/template.constants';

interface PackageSelection {
  package_id: string;
  option_ids: string[];
}

interface PackagePrice {
  id: string;
  price: number;
  unit_base_cost: number;
  currency_id: string;
  currencies: {
    id: string;
    code: string;
    description: string;
  } | null;
}

interface PackageData {
  id: string;
  name: string;
  description: string;
  quantity_basis: string;
  package_prices: PackagePrice[];
}

interface RPCPackageData {
  id: string;
  name: string;
  description: string;
  quantity_basis: string;
  package_prices: any; // This will be parsed from JSONB
}

@Injectable()
export class TemplateCalculationService {
  private readonly logger = new Logger(TemplateCalculationService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Calculate the total value of a template based on its package selections
   */
  async calculateTemplateTotal(templateId: string): Promise<TemplateCalculationResultDto> {
    this.logger.log(`Calculating template total for ID: ${templateId}`);
    const supabase = this.supabaseService.getClient();

    // 1. Fetch template data
    const { data: template, error: templateError } = await supabase
      .from(TemplateConstants.TABLE_NAME)
      .select('id, name, attendees, package_selections')
      .eq('id', templateId)
      .eq('is_deleted', false)
      .single();

    if (templateError) {
      this.logger.error(
        `Error fetching template ${templateId}: ${templateError.message}`,
        templateError.stack,
      );
      throw new InternalServerErrorException('Failed to fetch template details.');
    }

    if (!template) {
      throw new NotFoundException(`Template with ID ${templateId} not found.`);
    }

    // Initialize result
    const result: TemplateCalculationResultDto = {
      packagesTotal: 0,
      customItemsTotal: 0,
      grandTotal: 0,
      breakdown: [],
      currency: 'IDR', // Default currency
      hasValidPrices: true,
      missingPrices: [],
      totalCost: 0,
      estimatedProfit: 0,
    };

    // Check if template has package selections
    const packageSelections: PackageSelection[] = template.package_selections || [];
    if (packageSelections.length === 0) {
      this.logger.log('Template has no package selections');
      return result;
    }

    // Get all package IDs from selections
    const packageIds = packageSelections.map(selection => selection.package_id);

    if (packageIds.length === 0) {
      this.logger.log('No package IDs found in selections');
      return result;
    }

    // 2. Fetch package details with prices using RPC function
    const { data: packages, error: packagesError } = await supabase
      .rpc('get_packages_with_prices', { package_ids: packageIds });

    if (packagesError) {
      this.logger.error('Error fetching packages:', packagesError);
      throw new InternalServerErrorException('Failed to fetch package details');
    }

    if (!packages || packages.length === 0) {
      this.logger.log('No packages found for the given IDs');
      return result;
    }

    this.logger.log(`Fetched ${packages.length} packages`);

    // 3. Calculate totals for each package selection
    for (const selection of packageSelections) {
      const rpcPackageData = packages.find(pkg => pkg.id === selection.package_id) as RPCPackageData;

      if (!rpcPackageData) {
        this.logger.warn(`Package not found: ${selection.package_id}`);
        result.missingPrices.push(`Package ${selection.package_id} not found`);
        result.hasValidPrices = false;
        continue;
      }

      // Parse the JSONB package_prices
      const packagePrices: PackagePrice[] = Array.isArray(rpcPackageData.package_prices)
        ? rpcPackageData.package_prices
        : [];

      const packageData: PackageData = {
        ...rpcPackageData,
        package_prices: packagePrices,
      };

      // Find the best price (prefer IDR, then any available currency)
      let bestPrice: PackagePrice | undefined = undefined;

      if (packageData.package_prices && packageData.package_prices.length > 0) {
        // First try to find IDR price
        bestPrice = packageData.package_prices.find(price =>
          price.currencies?.code === 'IDR'
        );

        // If no IDR price, use the first available price
        if (!bestPrice) {
          bestPrice = packageData.package_prices[0];
        }
      }

      if (!bestPrice || !bestPrice.price) {
        this.logger.warn(`No price found for package: ${packageData.name}`);
        result.missingPrices.push(`No price for ${packageData.name}`);
        result.hasValidPrices = false;
        continue;
      }

      // Calculate quantity based on attendees and quantity basis
      let quantity = 1;
      if (template.attendees && packageData.quantity_basis) {
        switch (packageData.quantity_basis.toLowerCase()) {
          case 'per person':
          case 'per attendee':
            quantity = template.attendees;
            break;
          case 'per 10 people':
            quantity = Math.ceil(template.attendees / 10);
            break;
          case 'per 100 people':
            quantity = Math.ceil(template.attendees / 100);
            break;
          case 'per event':
          case 'fixed':
          default:
            quantity = 1;
            break;
        }
      }

      const unitPrice = bestPrice.price;
      const totalPrice = unitPrice * quantity;
      const unitCost = bestPrice.unit_base_cost || 0;
      const totalCost = unitCost * quantity;

      // Add to breakdown
      const breakdownItem: CalculationBreakdownDto = {
        packageId: packageData.id,
        packageName: packageData.name,
        quantity,
        unitPrice,
        totalPrice,
        currency: bestPrice.currencies?.code || 'IDR',
        unitCost,
        totalCost,
      };

      result.breakdown.push(breakdownItem);

      // Add to totals
      result.packagesTotal += totalPrice;
      result.totalCost = (result.totalCost || 0) + totalCost;

      // Update currency if this is the first package or if we found an IDR price
      if (result.breakdown.length === 1 || bestPrice.currencies?.code === 'IDR') {
        result.currency = bestPrice.currencies?.code || 'IDR';
      }
    }

    // Calculate grand total and profit
    result.grandTotal = result.packagesTotal + result.customItemsTotal;
    result.estimatedProfit = result.grandTotal - (result.totalCost || 0);

    this.logger.log(`Template calculation result: ${JSON.stringify({
      templateId,
      packagesTotal: result.packagesTotal,
      grandTotal: result.grandTotal,
      totalCost: result.totalCost,
      estimatedProfit: result.estimatedProfit,
      currency: result.currency,
      hasValidPrices: result.hasValidPrices,
      missingPricesCount: result.missingPrices.length,
    })}`);

    return result;
  }

  /**
   * Get a summary of the template calculation
   */
  async getCalculationSummary(templateId: string): Promise<TemplateCalculationSummaryDto> {
    const result = await this.calculateTemplateTotal(templateId);

    const summary: TemplateCalculationSummaryDto = {
      totalPackages: result.breakdown.length,
      totalValue: result.grandTotal,
      currency: result.currency,
      hasValidPrices: result.hasValidPrices,
      missingPricesCount: result.missingPrices.length,
      averagePackageValue: result.breakdown.length > 0
        ? result.packagesTotal / result.breakdown.length
        : 0,
      totalCost: result.totalCost,
      profitMarginPercentage: result.grandTotal > 0 && result.estimatedProfit !== undefined
        ? Math.round((result.estimatedProfit / result.grandTotal) * 100)
        : 0,
    };

    return summary;
  }
}
