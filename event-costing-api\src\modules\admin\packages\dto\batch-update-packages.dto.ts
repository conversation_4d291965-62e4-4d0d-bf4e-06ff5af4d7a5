import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for a single package update in a batch
 */
export class PackageUpdateItem {
  @ApiProperty({
    description: 'Package ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'New category ID (optional)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  categoryId?: string;

  @ApiProperty({
    description: 'New division ID (optional)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  divisionId?: string;
}

/**
 * DTO for batch updating packages
 */
export class BatchUpdatePackagesDto {
  @ApiProperty({
    description: 'Array of package updates',
    type: [PackageUpdateItem],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageUpdateItem)
  packages: PackageUpdateItem[];
}
