import { useEffect, useRef, useCallback, useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { showError } from "@/lib/notifications";
import { optimizedNotifications } from "@/lib/optimized-notifications";
import { QUERY_KEYS } from "@/lib/queryKeys";

interface UseAutoSaveOptions<T> {
  data: T;
  onSave: (data: T) => Promise<void>;
  delay?: number; // Debounce delay in milliseconds
  enabled?: boolean;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

interface AutoSaveState {
  isSaving: boolean;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;
  error: Error | null;
}

export function useAutoSave<T>({
  data,
  onSave,
  delay = 2000, // 2 seconds default
  enabled = true,
  onSuccess,
  onError,
}: UseAutoSaveOptions<T>) {
  const [state, setState] = useState<AutoSaveState>({
    isSaving: false,
    lastSaved: null,
    hasUnsavedChanges: false,
    error: null,
  });

  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastDataRef = useRef<T>(data);

  // Mutation for saving
  const saveMutation = useMutation({
    mutationFn: onSave,
    onMutate: () => {
      setState((prev) => ({ ...prev, isSaving: true, error: null }));
    },
    onSuccess: () => {
      setState((prev) => ({
        ...prev,
        isSaving: false,
        lastSaved: new Date(),
        hasUnsavedChanges: false,
        error: null,
      }));
      onSuccess?.();
    },
    onError: (error: Error) => {
      setState((prev) => ({
        ...prev,
        isSaving: false,
        error,
      }));
      onError?.(error);
      // Use the new notification system with auto-save category
      showError("Auto-save failed", {
        category: "auto-save",
        description: "Your changes may not be saved. Please save manually.",
      });
    },
  });

  // Check if data has changed
  const hasDataChanged = useCallback((newData: T, oldData: T): boolean => {
    return JSON.stringify(newData) !== JSON.stringify(oldData);
  }, []);

  // Debounced save function
  const debouncedSave = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      // Use current state values at the time of execution, not from closure
      setState((currentState) => {
        if (
          enabled &&
          currentState.hasUnsavedChanges &&
          !currentState.isSaving
        ) {
          saveMutation.mutate(data);
        }
        return currentState;
      });
    }, delay);
  }, [delay, enabled, data, saveMutation]);

  // Effect to detect data changes and trigger auto-save
  useEffect(() => {
    if (hasDataChanged(data, lastDataRef.current)) {
      lastDataRef.current = data;
      setState((prev) => ({ ...prev, hasUnsavedChanges: true }));

      if (enabled) {
        debouncedSave();
      }
    }
  }, [data, enabled, debouncedSave, hasDataChanged]);

  // Manual save function
  const saveNow = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Use current state values at the time of execution
    setState((currentState) => {
      if (enabled && !currentState.isSaving) {
        saveMutation.mutate(data);
      }
      return currentState;
    });
  }, [data, enabled, saveMutation]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Save before page unload if there are unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (state.hasUnsavedChanges) {
        event.preventDefault();
        // Modern way to show confirmation dialog
        return "You have unsaved changes. Are you sure you want to leave?";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [state.hasUnsavedChanges]);

  return {
    ...state,
    saveNow,
    isEnabled: enabled,
  };
}

// Hook specifically for calculation auto-save
export function useCalculationAutoSave(
  calculationId: string,
  calculationData: any,
  updateFunction: (id: string, data: any) => Promise<void>
) {
  const queryClient = useQueryClient();

  return useAutoSave({
    data: calculationData,
    onSave: async (data) => {
      await updateFunction(calculationId, data);
      // Invalidate related queries to ensure UI consistency
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.detail(calculationId),
      });
    },
    delay: 3000, // 3 seconds for calculations
    enabled: !!calculationId && !!calculationData,
    onSuccess: () => {
      // Optional: Show subtle success indicator
      console.log("Calculation auto-saved successfully");
    },
    onError: (error) => {
      console.error("Calculation auto-save failed:", error);
    },
  });
}

// Hook for line items auto-save
export function useLineItemsAutoSave(
  calculationId: string,
  lineItems: any[],
  updateFunction: (calculationId: string, lineItems: any[]) => Promise<void>
) {
  const queryClient = useQueryClient();

  return useAutoSave({
    data: lineItems,
    onSave: async (items) => {
      await updateFunction(calculationId, items);
      // Invalidate line items query
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.lineItems(calculationId),
      });
    },
    delay: 1500, // Faster save for line items
    enabled: !!calculationId && Array.isArray(lineItems),
    onSuccess: () => {
      console.log("Line items auto-saved successfully");
    },
    onError: (error) => {
      console.error("Line items auto-save failed:", error);
    },
  });
}
