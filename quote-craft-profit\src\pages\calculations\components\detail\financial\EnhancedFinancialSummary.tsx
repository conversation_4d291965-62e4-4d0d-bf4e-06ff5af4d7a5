import React, { useMemo, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, Percent, Eye, EyeOff } from 'lucide-react';
import { LineItem } from '@/types/calculation';
import { formatCurrency } from '../../../utils/formatUtils';

interface EnhancedFinancialSummaryProps {
  lineItems: LineItem[];
  calculation: {
    profit_margin?: number;
    total_cost?: number;
    total_price?: number;
  };
  categories: Array<{ id: string; name: string }>;
  onExport?: () => void;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

const EnhancedFinancialSummary: React.FC<EnhancedFinancialSummaryProps> = ({
  lineItems,
  calculation,
  categories,
  onExport,
}) => {
  const [showCosts, setShowCosts] = useState(false);

  // Calculate financial metrics
  const financialData = useMemo(() => {
    const totalRevenue = lineItems.reduce((sum, item) => sum + (item.total_price || 0), 0);
    const totalCost = calculation.total_cost || 0;
    const profit = totalRevenue - totalCost;
    const profitMargin = totalRevenue > 0 ? (profit / totalRevenue) * 100 : 0;

    return {
      totalRevenue,
      totalCost,
      profit,
      profitMargin,
      itemCount: lineItems.length,
    };
  }, [lineItems, calculation.total_cost]);

  // Category breakdown for pie chart
  const categoryData = useMemo(() => {
    const categoryTotals = new Map<string, number>();

    lineItems.forEach(item => {
      const categoryId = item.category_id || 'uncategorized';
      const current = categoryTotals.get(categoryId) || 0;
      categoryTotals.set(categoryId, current + (item.total_price || 0));
    });

    return Array.from(categoryTotals.entries()).map(([categoryId, total]) => {
      const category = categories.find(c => c.id === categoryId);
      return {
        name: category?.name || 'Uncategorized',
        value: total,
        percentage: financialData.totalRevenue > 0 ? (total / financialData.totalRevenue) * 100 : 0,
      };
    }).sort((a, b) => b.value - a.value);
  }, [lineItems, categories, financialData.totalRevenue]);

  // Top items for bar chart
  const topItems = useMemo(() => {
    return [...lineItems]
      .sort((a, b) => (b.total_price || 0) - (a.total_price || 0))
      .slice(0, 5)
      .map(item => ({
        name: item.name.length > 20 ? `${item.name.substring(0, 20)}...` : item.name,
        fullName: item.name,
        value: item.total_price || 0,
        cost: showCosts ? (item.total_price || 0) * 0.7 : undefined, // Estimated cost
      }));
  }, [lineItems, showCosts]);

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {formatCurrency(entry.value, 'IDR')}
              {entry.payload.percentage && (
                <span className="text-gray-500 ml-1">
                  ({entry.payload.percentage.toFixed(1)}%)
                </span>
              )}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Financial Summary
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCosts(!showCosts)}
            >
              {showCosts ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showCosts ? 'Hide' : 'Show'} Costs
            </Button>
            {onExport && (
              <Button variant="outline" size="sm" onClick={onExport}>
                Export
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="items">Top Items</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(financialData.totalRevenue, 'IDR')}
                </p>
              </div>

              {showCosts && (
                <div className="text-center p-4 border rounded-lg">
                  <p className="text-sm text-gray-600">Total Cost</p>
                  <p className="text-2xl font-bold text-red-600">
                    {formatCurrency(financialData.totalCost, 'IDR')}
                  </p>
                </div>
              )}

              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-gray-600">
                  {showCosts ? 'Profit' : 'Items'}
                </p>
                <p className={`text-2xl font-bold ${
                  showCosts
                    ? financialData.profit >= 0 ? 'text-green-600' : 'text-red-600'
                    : 'text-blue-600'
                }`}>
                  {showCosts
                    ? formatCurrency(financialData.profit, 'IDR')
                    : financialData.itemCount
                  }
                </p>
              </div>

              {showCosts && (
                <div className="text-center p-4 border rounded-lg">
                  <p className="text-sm text-gray-600">Profit Margin</p>
                  <div className="flex items-center justify-center gap-1">
                    {financialData.profitMargin >= 0 ? (
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600" />
                    )}
                    <p className={`text-2xl font-bold ${
                      financialData.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {financialData.profitMargin.toFixed(1)}%
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Profit Margin Indicator */}
            {showCosts && (
              <div className="p-4 border rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Profit Margin Health</span>
                  <Badge variant={
                    financialData.profitMargin >= 20 ? 'default' :
                    financialData.profitMargin >= 10 ? 'secondary' : 'destructive'
                  }>
                    {financialData.profitMargin >= 20 ? 'Excellent' :
                     financialData.profitMargin >= 10 ? 'Good' : 'Needs Improvement'}
                  </Badge>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      financialData.profitMargin >= 20 ? 'bg-green-500' :
                      financialData.profitMargin >= 10 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${Math.min(Math.max(financialData.profitMargin, 0), 100)}%` }}
                  />
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="categories">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name} (${percentage.toFixed(1)}%)`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="items">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={topItems}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis tickFormatter={(value) => formatCurrency(value, 'IDR', true)} />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="value" fill="#8884d8" name="Revenue" />
                  {showCosts && (
                    <Bar dataKey="cost" fill="#82ca9d" name="Cost" />
                  )}
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default EnhancedFinancialSummary;
