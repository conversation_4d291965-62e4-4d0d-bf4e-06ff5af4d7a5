import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { TableColumnSettings as TableColumnSettingsType } from '../../types';
import { useSettings } from '@/hooks/useSettings';
import { toast } from 'sonner';
import { RotateCcw } from 'lucide-react';

export const TableColumnSettings: React.FC = () => {
  const { settings, updateSettings, resetSettings } = useSettings();
  const columnSettings = settings.packageTable;

  const handleToggleColumn = (column: keyof TableColumnSettingsType, value: boolean) => {
    // Prevent disabling the Name column as it's essential
    if (column === 'name' && !value) {
      toast.error('The Name column cannot be hidden as it is essential');
      return;
    }

    // Prevent disabling all columns
    const updatedSettings = { ...columnSettings, [column]: value };
    const enabledColumnsCount = Object.values(updatedSettings).filter(Boolean).length;

    if (enabledColumnsCount === 0) {
      toast.error('At least one column must be visible');
      return;
    }

    updateSettings('packageTable', { [column]: value });
    toast.success(
      `${column.charAt(0).toUpperCase() + column.slice(1)} column ${
        value ? 'shown' : 'hidden'
      }`,
    );
  };

  const handleResetColumns = () => {
    resetSettings();
    toast.success('Table column settings reset to defaults');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Package Table Columns</CardTitle>
        <CardDescription>
          Configure which columns are displayed in the packages table
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-4'>
            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='number-column' className='cursor-pointer'>
                Number (#)
              </Label>
              <Switch
                id='number-column'
                checked={columnSettings.number}
                onCheckedChange={(checked) => handleToggleColumn('number', checked)}
              />
            </div>

            <div className='flex items-center justify-between space-x-2'>
              <Label
                htmlFor='name-column'
                className='flex items-center space-x-2 cursor-pointer'
              >
                <span>Name</span>
                <span className='text-xs text-muted-foreground'>(Required)</span>
              </Label>
              <Switch
                id='name-column'
                checked={columnSettings.name}
                onCheckedChange={(checked) => handleToggleColumn('name', checked)}
                disabled={true} // Name column is always required
              />
            </div>

            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='category-column' className='cursor-pointer'>
                Category
              </Label>
              <Switch
                id='category-column'
                checked={columnSettings.category}
                onCheckedChange={(checked) => handleToggleColumn('category', checked)}
              />
            </div>

            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='division-column' className='cursor-pointer'>
                Division
              </Label>
              <Switch
                id='division-column'
                checked={columnSettings.division}
                onCheckedChange={(checked) => handleToggleColumn('division', checked)}
              />
            </div>

            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='cities-column' className='cursor-pointer'>
                Cities
              </Label>
              <Switch
                id='cities-column'
                checked={columnSettings.cities}
                onCheckedChange={(checked) => handleToggleColumn('cities', checked)}
              />
            </div>
          </div>

          <div className='space-y-4'>
            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='quantity-basis-column' className='cursor-pointer'>
                Quantity Basis
              </Label>
              <Switch
                id='quantity-basis-column'
                checked={columnSettings.quantityBasis}
                onCheckedChange={(checked) =>
                  handleToggleColumn('quantityBasis', checked)
                }
              />
            </div>

            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='price-column' className='cursor-pointer'>
                Price
              </Label>
              <Switch
                id='price-column'
                checked={columnSettings.price}
                onCheckedChange={(checked) => handleToggleColumn('price', checked)}
              />
            </div>

            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='lastModified-column' className='cursor-pointer'>
                Last Modified
              </Label>
              <Switch
                id='lastModified-column'
                checked={columnSettings.lastModified}
                onCheckedChange={(checked) => handleToggleColumn('lastModified', checked)}
              />
            </div>

            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='status-column' className='cursor-pointer'>
                Status
              </Label>
              <Switch
                id='status-column'
                checked={columnSettings.status}
                onCheckedChange={(checked) => handleToggleColumn('status', checked)}
              />
            </div>

            <div className='flex items-center justify-between space-x-2'>
              <Label htmlFor='actions-column' className='cursor-pointer'>
                Actions
              </Label>
              <Switch
                id='actions-column'
                checked={columnSettings.actions}
                onCheckedChange={(checked) => handleToggleColumn('actions', checked)}
              />
            </div>
          </div>
        </div>

        <div className='mt-6 flex justify-end'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleResetColumns}
            className='flex items-center gap-2'
          >
            <RotateCcw className='h-4 w-4' />
            Reset to Defaults
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
