/**
 * User API Service
 *
 * This service provides methods for interacting with the user API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import { apiClient, getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';

// User with profile information
export interface UserWithProfile {
  id: string;
  email: string;
  username: string;
  full_name: string;
  role_name: string;
  created_at: string;
  last_sign_in_at: string | null;
  profile_picture_url: string | null;
  status: 'ACTIVE' | 'INACTIVE';
}

// Role interface
export interface Role {
  id: number;
  role_name: string;
  description: string;
  created_at: string;
}

/**
 * Get all users from the backend API
 * @returns Promise resolving to an array of users with profile information
 */
export const getUsersFromApi = async (): Promise<UserWithProfile[]> => {
  try {
    console.log('Fetching users from backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to admin users endpoint
    const response = await authClient.get(API_ENDPOINTS.ADMIN_USERS.LIST);

    console.log('Users fetched successfully from backend API');

    return response.data;
  } catch (error) {
    console.error('Error fetching users from backend API:', error);
    throw error;
  }
};

/**
 * Update a user's role using the backend API
 * @param userId - The user ID
 * @param roleId - The role ID to assign
 * @returns Promise resolving when the role is updated
 */
export const updateUserRoleFromApi = async (
  userId: string,
  roleId: number
): Promise<void> => {
  try {
    console.log(`Updating role for user ${userId} to role ID ${roleId} using backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.patch(
      API_ENDPOINTS.ADMIN_USERS.UPDATE_ROLE(userId),
      { roleId }
    );

    console.log('User role updated successfully with backend API');
  } catch (error) {
    console.error(`Error updating role for user ${userId} using backend API:`, error);
    throw error;
  }
};

/**
 * Get current user profile from the backend API
 * @returns Promise resolving to the current user's profile data
 */
export const getUserProfileFromApi = async (): Promise<any> => {
  try {
    console.log('Fetching current user profile from backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to get current user profile
    const response = await authClient.get(API_ENDPOINTS.USERS.ME);

    console.log('User profile fetched successfully from backend API');

    return response.data;
  } catch (error) {
    console.error('Error fetching user profile from backend API:', error);
    throw error;
  }
};

/**
 * Get all roles from the backend API
 * @returns Promise resolving to an array of roles
 */
export const getRolesFromApi = async (): Promise<Role[]> => {
  try {
    console.log('Fetching roles from backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.ADMIN_USERS.ROLES);

    console.log('Roles fetched successfully from backend API');

    return response.data;
  } catch (error) {
    console.error('Error fetching roles from backend API:', error);
    throw error;
  }
};

/**
 * Update a user's status (active/inactive) using the backend API
 * @param userId - The user ID
 * @param status - The new status
 * @returns Promise resolving when the status is updated
 */
export const updateUserStatusFromApi = async (
  userId: string,
  status: 'ACTIVE' | 'INACTIVE'
): Promise<void> => {
  try {
    console.log(`Updating status for user ${userId} to ${status} using backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.patch(
      API_ENDPOINTS.ADMIN_USERS.UPDATE_STATUS(userId),
      { status }
    );

    console.log('User status updated successfully with backend API');
  } catch (error) {
    console.error(`Error updating status for user ${userId} using backend API:`, error);
    throw error;
  }
};
