import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import {
  Briefcase,
  Heart,
  PartyPopper,
  Users,
  Calendar,
  Building,
  Loader2,
} from "lucide-react";
import { getAllEventTypes } from "@/services/shared/entities/event-types";
import { WizardState } from "./WizardContainer";
import { toast } from "sonner";

interface EventTypeSelectorProps {
  wizardState: WizardState;
  updateWizardState: (updates: Partial<WizardState>) => void;
  onNext: () => void;
}

// Icon mapping for event types
const ICON_MAP: Record<string, any> = {
  briefcase: Briefcase,
  heart: Heart,
  "party-popper": PartyPopper,
  users: Users,
  calendar: Calendar,
  building: Building,
};

// Fallback icon if icon not found
const DEFAULT_ICON = Briefcase;

const getColorClasses = (color: string, isSelected: boolean) => {
  const colorMap = {
    blue: {
      card: isSelected
        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
        : "border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600",
      icon: isSelected
        ? "bg-blue-500 text-white"
        : "bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400",
      text: isSelected
        ? "text-blue-700 dark:text-blue-300"
        : "text-gray-800 dark:text-white",
    },
    pink: {
      card: isSelected
        ? "border-pink-500 bg-pink-50 dark:bg-pink-900/20"
        : "border-gray-200 dark:border-gray-700 hover:border-pink-300 dark:hover:border-pink-600",
      icon: isSelected
        ? "bg-pink-500 text-white"
        : "bg-pink-100 dark:bg-pink-900/30 text-pink-600 dark:text-pink-400",
      text: isSelected
        ? "text-pink-700 dark:text-pink-300"
        : "text-gray-800 dark:text-white",
    },
    purple: {
      card: isSelected
        ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
        : "border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600",
      icon: isSelected
        ? "bg-purple-500 text-white"
        : "bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400",
      text: isSelected
        ? "text-purple-700 dark:text-purple-300"
        : "text-gray-800 dark:text-white",
    },
    green: {
      card: isSelected
        ? "border-green-500 bg-green-50 dark:bg-green-900/20"
        : "border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-600",
      icon: isSelected
        ? "bg-green-500 text-white"
        : "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400",
      text: isSelected
        ? "text-green-700 dark:text-green-300"
        : "text-gray-800 dark:text-white",
    },
    orange: {
      card: isSelected
        ? "border-orange-500 bg-orange-50 dark:bg-orange-900/20"
        : "border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600",
      icon: isSelected
        ? "bg-orange-500 text-white"
        : "bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400",
      text: isSelected
        ? "text-orange-700 dark:text-orange-300"
        : "text-gray-800 dark:text-white",
    },
    indigo: {
      card: isSelected
        ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20"
        : "border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-600",
      icon: isSelected
        ? "bg-indigo-500 text-white"
        : "bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400",
      text: isSelected
        ? "text-indigo-700 dark:text-indigo-300"
        : "text-gray-800 dark:text-white",
    },
  };

  return colorMap[color as keyof typeof colorMap] || colorMap.blue;
};

export const EventTypeSelector: React.FC<EventTypeSelectorProps> = ({
  wizardState,
  updateWizardState,
  onNext,
}) => {
  // Fetch event types from API
  const {
    data: eventTypes,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["event-types"],
    queryFn: getAllEventTypes,
    meta: {
      onError: (error: Error) => {
        toast.error(`Failed to load event types: ${error.message}`);
      },
    },
  });

  const handleEventTypeSelect = (eventTypeId: string) => {
    updateWizardState({ eventType: eventTypeId });
    // Auto-advance to next step after selection
    setTimeout(() => {
      onNext();
    }, 300);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            What type of event are you planning?
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Choose the category that best describes your event
          </p>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-600 dark:text-gray-300">
              Loading event types...
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (isError || !eventTypes) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            What type of event are you planning?
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Choose the category that best describes your event
          </p>
        </div>

        <div className="text-center py-12">
          <div className="text-red-600 dark:text-red-400 mb-4">
            <Briefcase className="h-12 w-12 mx-auto mb-2" />
            <p className="text-lg font-medium">Unable to load event types</p>
            <p className="text-sm">Please try refreshing the page</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          What type of event are you planning?
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Choose the category that best describes your event
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {eventTypes.map((eventType) => {
          const isSelected = wizardState.eventType === eventType.id;
          const colors = getColorClasses(eventType.color, isSelected);
          const IconComponent = ICON_MAP[eventType.icon || ""] || DEFAULT_ICON;

          return (
            <Card
              key={eventType.id}
              className={`cursor-pointer transition-all duration-200 hover:scale-105 ${colors.card}`}
              onClick={() => handleEventTypeSelect(eventType.id)}
            >
              <CardContent className="p-6 text-center">
                <div
                  className={`w-12 h-12 rounded-full mx-auto mb-4 flex items-center justify-center ${colors.icon}`}
                >
                  <IconComponent className="h-6 w-6" />
                </div>
                <h3 className={`font-semibold text-lg mb-2 ${colors.text}`}>
                  {eventType.name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {eventType.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {wizardState.eventType && (
        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Selected:{" "}
            <span className="font-medium">
              {eventTypes.find((t) => t.id === wizardState.eventType)?.name}
            </span>
          </p>
        </div>
      )}
    </div>
  );
};
