/**
 * Cache Persistence Service
 * 
 * PHASE 2: Implements cache persistence for offline scenarios and faster app startup
 * Provides intelligent storage management and cache restoration capabilities
 */

import { QueryClient } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/queryKeys';

export interface PersistenceConfig {
  enabled: boolean;
  storage: 'localStorage' | 'indexedDB' | 'sessionStorage';
  maxAge: number; // Maximum age of persisted data in ms
  maxSize: number; // Maximum storage size in bytes
  compression: boolean;
  encryptSensitiveData: boolean;
  persistenceKey: string;
}

export interface PersistedQuery {
  queryKey: any[];
  data: any;
  timestamp: number;
  dataUpdatedAt: number;
  staleTime: number;
  sensitive: boolean;
}

export interface PersistenceStats {
  totalQueries: number;
  totalSize: number;
  lastPersisted: Date | null;
  lastRestored: Date | null;
  compressionRatio: number;
  storageUsage: number; // Percentage of max size used
}

class CachePersistenceService {
  private queryClient: QueryClient;
  private config: PersistenceConfig = {
    enabled: true,
    storage: 'localStorage',
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    maxSize: 10 * 1024 * 1024, // 10MB
    compression: true,
    encryptSensitiveData: false, // Would need crypto implementation
    persistenceKey: 'qcp_cache_persistence',
  };

  private stats: PersistenceStats = {
    totalQueries: 0,
    totalSize: 0,
    lastPersisted: null,
    lastRestored: null,
    compressionRatio: 1,
    storageUsage: 0,
  };

  // Define which queries should be persisted
  private persistableQueries = new Set([
    JSON.stringify(QUERY_KEYS.categories.all()),
    JSON.stringify(QUERY_KEYS.cities.all()),
    JSON.stringify(QUERY_KEYS.currencies.all()),
    JSON.stringify(QUERY_KEYS.divisions.all()),
    JSON.stringify(QUERY_KEYS.packages.lists()),
    JSON.stringify(QUERY_KEYS.clients.all()),
  ]);

  // Define sensitive queries that need special handling
  private sensitiveQueries = new Set([
    JSON.stringify(QUERY_KEYS.calculations.lists()),
    // Add other sensitive query patterns
  ]);

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
    this.initializePersistence();
  }

  /**
   * Initialize cache persistence
   */
  private async initializePersistence(): Promise<void> {
    if (!this.config.enabled) return;

    try {
      // Restore persisted cache on startup
      await this.restoreCache();
      
      // Setup automatic persistence
      this.setupAutoPersistence();
      
      // Setup cleanup on app close
      this.setupCleanupHandlers();
      
      console.debug('💾 Cache persistence initialized');
    } catch (error) {
      console.error('Error initializing cache persistence:', error);
    }
  }

  /**
   * Setup automatic cache persistence
   */
  private setupAutoPersistence(): void {
    // Persist cache every 5 minutes
    setInterval(() => {
      this.persistCache();
    }, 5 * 60 * 1000);

    // Persist on visibility change (when user switches tabs)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.persistCache();
      }
    });
  }

  /**
   * Setup cleanup handlers
   */
  private setupCleanupHandlers(): void {
    // Persist cache before page unload
    window.addEventListener('beforeunload', () => {
      this.persistCache();
    });

    // Cleanup old data periodically
    setInterval(() => {
      this.cleanupExpiredData();
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Persist current cache to storage
   */
  async persistCache(): Promise<void> {
    if (!this.config.enabled) return;

    try {
      const cache = this.queryClient.getQueryCache();
      const queries = cache.getAll();
      const persistableData: PersistedQuery[] = [];

      for (const query of queries) {
        const queryKeyStr = JSON.stringify(query.queryKey);
        
        // Only persist allowed queries with data
        if (this.persistableQueries.has(queryKeyStr) && query.state.data) {
          const isSensitive = this.sensitiveQueries.has(queryKeyStr);
          
          // Skip sensitive data if encryption is not enabled
          if (isSensitive && !this.config.encryptSensitiveData) {
            continue;
          }

          const persistedQuery: PersistedQuery = {
            queryKey: query.queryKey,
            data: query.state.data,
            timestamp: Date.now(),
            dataUpdatedAt: query.state.dataUpdatedAt || Date.now(),
            staleTime: 5 * 60 * 1000, // Default 5 minutes
            sensitive: isSensitive,
          };

          persistableData.push(persistedQuery);
        }
      }

      if (persistableData.length > 0) {
        await this.saveToStorage(persistableData);
        this.stats.totalQueries = persistableData.length;
        this.stats.lastPersisted = new Date();
        
        console.debug(`💾 Persisted ${persistableData.length} queries to ${this.config.storage}`);
      }
    } catch (error) {
      console.error('Error persisting cache:', error);
    }
  }

  /**
   * Restore cache from storage
   */
  async restoreCache(): Promise<void> {
    if (!this.config.enabled) return;

    try {
      const persistedData = await this.loadFromStorage();
      
      if (!persistedData || persistedData.length === 0) {
        console.debug('💾 No persisted cache data found');
        return;
      }

      let restoredCount = 0;
      const now = Date.now();

      for (const persistedQuery of persistedData) {
        // Check if data is not too old
        const age = now - persistedQuery.timestamp;
        if (age > this.config.maxAge) {
          continue;
        }

        // Check if data is still considered fresh
        const dataAge = now - persistedQuery.dataUpdatedAt;
        if (dataAge < persistedQuery.staleTime) {
          // Restore to cache
          this.queryClient.setQueryData(persistedQuery.queryKey, persistedQuery.data);
          restoredCount++;
        }
      }

      this.stats.lastRestored = new Date();
      console.debug(`💾 Restored ${restoredCount} queries from ${this.config.storage}`);
    } catch (error) {
      console.error('Error restoring cache:', error);
    }
  }

  /**
   * Save data to storage
   */
  private async saveToStorage(data: PersistedQuery[]): Promise<void> {
    let serializedData = JSON.stringify(data);
    const originalSize = new Blob([serializedData]).size;

    // Apply compression if enabled
    if (this.config.compression) {
      serializedData = this.compressData(serializedData);
      const compressedSize = new Blob([serializedData]).size;
      this.stats.compressionRatio = originalSize / compressedSize;
    }

    this.stats.totalSize = new Blob([serializedData]).size;
    this.stats.storageUsage = (this.stats.totalSize / this.config.maxSize) * 100;

    // Check size limit
    if (this.stats.totalSize > this.config.maxSize) {
      console.warn(`💾 Cache data size (${this.formatBytes(this.stats.totalSize)}) exceeds limit (${this.formatBytes(this.config.maxSize)})`);
      // Could implement data pruning here
      return;
    }

    // Save to storage
    switch (this.config.storage) {
      case 'localStorage':
        localStorage.setItem(this.config.persistenceKey, serializedData);
        break;
      case 'sessionStorage':
        sessionStorage.setItem(this.config.persistenceKey, serializedData);
        break;
      case 'indexedDB':
        await this.saveToIndexedDB(serializedData);
        break;
    }
  }

  /**
   * Load data from storage
   */
  private async loadFromStorage(): Promise<PersistedQuery[] | null> {
    let serializedData: string | null = null;

    // Load from storage
    switch (this.config.storage) {
      case 'localStorage':
        serializedData = localStorage.getItem(this.config.persistenceKey);
        break;
      case 'sessionStorage':
        serializedData = sessionStorage.getItem(this.config.persistenceKey);
        break;
      case 'indexedDB':
        serializedData = await this.loadFromIndexedDB();
        break;
    }

    if (!serializedData) return null;

    // Decompress if needed
    if (this.config.compression) {
      serializedData = this.decompressData(serializedData);
    }

    try {
      return JSON.parse(serializedData);
    } catch (error) {
      console.error('Error parsing persisted cache data:', error);
      return null;
    }
  }

  /**
   * Simple compression (placeholder - would use actual compression library)
   */
  private compressData(data: string): string {
    // This is a placeholder - in practice, you'd use a compression library like pako
    return data;
  }

  /**
   * Simple decompression (placeholder)
   */
  private decompressData(data: string): string {
    // This is a placeholder - in practice, you'd use a compression library like pako
    return data;
  }

  /**
   * Save to IndexedDB
   */
  private async saveToIndexedDB(data: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('QCPCache', 1);
      
      request.onerror = () => reject(request.error);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');
        
        store.put({ id: this.config.persistenceKey, data });
        
        transaction.oncomplete = () => resolve();
        transaction.onerror = () => reject(transaction.error);
      };
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache', { keyPath: 'id' });
        }
      };
    });
  }

  /**
   * Load from IndexedDB
   */
  private async loadFromIndexedDB(): Promise<string | null> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('QCPCache', 1);
      
      request.onerror = () => reject(request.error);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['cache'], 'readonly');
        const store = transaction.objectStore('cache');
        const getRequest = store.get(this.config.persistenceKey);
        
        getRequest.onsuccess = () => {
          const result = getRequest.result;
          resolve(result ? result.data : null);
        };
        
        getRequest.onerror = () => reject(getRequest.error);
      };
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache', { keyPath: 'id' });
        }
      };
    });
  }

  /**
   * Cleanup expired data
   */
  private async cleanupExpiredData(): Promise<void> {
    try {
      const persistedData = await this.loadFromStorage();
      if (!persistedData) return;

      const now = Date.now();
      const validData = persistedData.filter(
        query => (now - query.timestamp) <= this.config.maxAge
      );

      if (validData.length < persistedData.length) {
        await this.saveToStorage(validData);
        console.debug(`🧹 Cleaned up ${persistedData.length - validData.length} expired cache entries`);
      }
    } catch (error) {
      console.error('Error cleaning up expired data:', error);
    }
  }

  /**
   * Clear all persisted data
   */
  async clearPersistedData(): Promise<void> {
    try {
      switch (this.config.storage) {
        case 'localStorage':
          localStorage.removeItem(this.config.persistenceKey);
          break;
        case 'sessionStorage':
          sessionStorage.removeItem(this.config.persistenceKey);
          break;
        case 'indexedDB':
          await this.clearIndexedDB();
          break;
      }
      
      this.stats = {
        totalQueries: 0,
        totalSize: 0,
        lastPersisted: null,
        lastRestored: null,
        compressionRatio: 1,
        storageUsage: 0,
      };
      
      console.debug('🧹 Cleared all persisted cache data');
    } catch (error) {
      console.error('Error clearing persisted data:', error);
    }
  }

  /**
   * Clear IndexedDB
   */
  private async clearIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('QCPCache', 1);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');
        
        store.delete(this.config.persistenceKey);
        
        transaction.oncomplete = () => resolve();
        transaction.onerror = () => reject(transaction.error);
      };
    });
  }

  /**
   * Format bytes for display
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get persistence statistics
   */
  getStats(): PersistenceStats {
    return { ...this.stats };
  }

  /**
   * Configure persistence settings
   */
  configure(newConfig: Partial<PersistenceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.debug('💾 Cache persistence configured:', this.config);
  }

  /**
   * Check if storage is available
   */
  isStorageAvailable(): boolean {
    try {
      switch (this.config.storage) {
        case 'localStorage':
          return typeof localStorage !== 'undefined';
        case 'sessionStorage':
          return typeof sessionStorage !== 'undefined';
        case 'indexedDB':
          return typeof indexedDB !== 'undefined';
        default:
          return false;
      }
    } catch {
      return false;
    }
  }

  /**
   * Force immediate persistence
   */
  async forcePersist(): Promise<void> {
    await this.persistCache();
  }

  /**
   * Force immediate restoration
   */
  async forceRestore(): Promise<void> {
    await this.restoreCache();
  }
}

// Export singleton instance
let cachePersistenceInstance: CachePersistenceService | null = null;

export const createCachePersistence = (queryClient: QueryClient): CachePersistenceService => {
  if (!cachePersistenceInstance) {
    cachePersistenceInstance = new CachePersistenceService(queryClient);
  }
  return cachePersistenceInstance;
};

export const getCachePersistence = (): CachePersistenceService | null => {
  return cachePersistenceInstance;
};
