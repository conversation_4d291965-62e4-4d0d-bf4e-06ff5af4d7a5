import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/auth.module';
import { AdminModule } from '../../auth/admin.module';
import { PackageOptionsController } from './package-options.controller.js';
import { PackageOptionsService } from './package-options.service.js';

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [PackageOptionsController],
  providers: [PackageOptionsService],
  exports: [PackageOptionsService], // Export if needed by other modules
})
export class PackageOptionsModule {}
