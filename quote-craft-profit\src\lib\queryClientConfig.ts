/**
 * Optimized React Query Configuration
 *
 * Provides feature-specific cache configurations to optimize performance
 * and reduce cache-related issues. Replaces inconsistent cache settings.
 *
 * CACHE OPTIMIZATION: Feature-based cache strategies for better performance
 */

import { QueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Feature-specific cache configurations
export const CACHE_CONFIGS = {
  // Static data - cache longer (cities, categories, divisions)
  static: {
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  },

  // User data - moderate caching (clients, events, venues)
  user: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  },

  // Active editing - minimal caching (calculations, line items)
  editing: {
    staleTime: 30 * 1000, // 30 seconds for active editing
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true, // Always fresh data for editing
  },

  // Package data - moderate caching with frequent updates
  packages: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  },

  // Real-time data - no caching (financial calculations)
  realtime: {
    staleTime: 0, // Always stale
    gcTime: 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  },
};

/**
 * Create optimized QueryClient with feature-specific configurations
 */
export const createOptimizedQueryClient = (): QueryClient => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Default to user data configuration
        staleTime: CACHE_CONFIGS.user.staleTime,
        gcTime: CACHE_CONFIGS.user.gcTime,
        refetchOnWindowFocus: false,
        refetchOnMount: false,

        // Retry configuration
        retry: (failureCount, error: any) => {
          // Don't retry on 4xx errors (client errors)
          if (error?.status >= 400 && error?.status < 500) {
            return false;
          }
          // Retry up to 2 times for server errors
          return failureCount < 2;
        },

        // Progressive retry delay
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

        // Note: Global error handling moved to individual queries
      },

      mutations: {
        // Retry mutations once
        retry: 1,

        // Global mutation error handling
        onError: (error: any, variables: any, context: any) => {
          console.error("Mutation error:", {
            error,
            variables,
            context,
          });

          // Don't show toast if the mutation has its own error handling
          if (context?.skipGlobalError) {
            return;
          }

          // Handle specific error types
          if (error?.status === 401) {
            toast.error("Authentication required. Please log in again.");
            return;
          }

          if (error?.status === 403) {
            toast.error("You do not have permission to perform this action.");
            return;
          }

          if (error?.status === 409) {
            toast.error(
              "This action conflicts with existing data. Please refresh and try again."
            );
            return;
          }

          if (error?.status >= 500) {
            toast.error("Server error. Please try again later.");
            return;
          }

          // Generic error message
          toast.error("An error occurred while saving data.");
        },
      },
    },
  });
};

/**
 * Query configuration helpers for specific features
 */
export const getQueryConfig = (feature: keyof typeof CACHE_CONFIGS) => {
  return CACHE_CONFIGS[feature];
};

/**
 * Helper to create query options with feature-specific configuration
 */
export const createQueryOptions = <T>(
  feature: keyof typeof CACHE_CONFIGS,
  options: {
    queryKey: any[];
    queryFn: () => Promise<T>;
    enabled?: boolean;
    onError?: (error: any) => void;
    onSuccess?: (data: T) => void;
  }
) => {
  const config = getQueryConfig(feature);

  return {
    ...config,
    ...options,
    meta: {
      onError: options.onError,
      onSuccess: options.onSuccess,
    },
  } as const;
};

/**
 * Helper to create mutation options with optimized error handling
 */
export const createMutationOptions = <TData, TVariables>(options: {
  mutationFn: (variables: TVariables) => Promise<TData>;
  onSuccess?: (data: TData, variables: TVariables) => void | Promise<void>;
  onError?: (error: any, variables: TVariables) => void;
  skipGlobalError?: boolean;
}) => {
  return {
    ...options,
    meta: {
      skipGlobalError: options.skipGlobalError,
    },
  };
};

/**
 * Cache size monitoring and cleanup
 */
export const setupCacheMonitoring = (queryClient: QueryClient) => {
  // Monitor cache size every 5 minutes
  const interval = setInterval(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    const totalQueries = queries.length;

    // Log cache statistics in development
    if (process.env.NODE_ENV === "development") {
      console.log("📊 Cache Statistics:", {
        totalQueries,
        staleQueries: queries.filter((q) => q.isStale()).length,
        memoryEstimate: `${(totalQueries * 0.1).toFixed(1)}MB`,
      });
    }

    // Clean up old queries if cache gets too large
    if (totalQueries > 1000) {
      console.warn("🧹 Cache size exceeded 1000 queries, cleaning up...");

      // Remove queries that haven't been used in the last hour
      const oneHourAgo = Date.now() - 60 * 60 * 1000;
      queries.forEach((query) => {
        if (query.state.dataUpdatedAt < oneHourAgo) {
          cache.remove(query);
        }
      });
    }
  }, 5 * 60 * 1000); // Every 5 minutes

  // Cleanup interval on app unmount
  return () => clearInterval(interval);
};

/**
 * Development-only cache debugging utilities
 */
export const cacheDebugUtils = {
  logCacheState: (queryClient: QueryClient) => {
    if (process.env.NODE_ENV !== "development") return;

    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    console.group("🔍 Cache Debug Information");
    console.log("Total queries:", queries.length);
    console.log("Stale queries:", queries.filter((q) => q.isStale()).length);
    console.log(
      "Loading queries:",
      queries.filter((q) => q.state.status === "pending").length
    );
    console.log(
      "Error queries:",
      queries.filter((q) => q.state.status === "error").length
    );

    // Group by query key prefix
    const groupedQueries = queries.reduce((acc, query) => {
      const prefix = query.queryKey[0] as string;
      if (!acc[prefix]) acc[prefix] = [];
      acc[prefix].push(query);
      return acc;
    }, {} as Record<string, any[]>);

    console.log(
      "Queries by feature:",
      Object.keys(groupedQueries).map((key) => ({
        feature: key,
        count: groupedQueries[key].length,
      }))
    );

    console.groupEnd();
  },

  clearFeatureCache: (queryClient: QueryClient, feature: string) => {
    if (process.env.NODE_ENV !== "development") return;

    queryClient.invalidateQueries({
      predicate: (query) => {
        return Array.isArray(query.queryKey) && query.queryKey[0] === feature;
      },
    });

    console.log(`🧹 Cleared cache for feature: ${feature}`);
  },
};
