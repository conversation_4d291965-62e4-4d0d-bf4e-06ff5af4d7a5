import { ApiProperty } from '@nestjs/swagger';

export class ExportResponseDto {
  @ApiProperty({
    description: 'The unique ID for this export history record.',
    format: 'uuid',
  })
  exportId: string;

  @ApiProperty({
    description: 'The initial status of the export process.',
    example: 'PENDING',
  })
  status: string; // Could be an enum later: PENDING, PROCESSING, COMPLETED, FAILED

  @ApiProperty({
    description: 'A confirmation message.',
  })
  message: string;

  @ApiProperty({
    description: 'Timestamp when the export process was initiated.',
  })
  createdAt: Date;

  // Optional: Add fields like downloadUrl if the export is generated synchronously
  // or if a status check endpoint can provide it later.
}
