import { apiClient, API_ENDPOINTS, getAuthenticatedApiClient } from '@/integrations/api';

export interface AdminUser {
  id: string;
  email: string;
  username: string;
  full_name: string;
  role_name: string;
  created_at: string;
  last_sign_in_at: string | null;
  profile_picture_url: string | null;
  status: 'ACTIVE' | 'INACTIVE';
}

export interface Role {
  id: number;
  role_name: string;
  description: string;
  created_at: string;
}

/**
 * Fetch all users from the external API
 * @returns List of users with their profiles
 */
export async function getAdminUsers(): Promise<AdminUser[]> {
  try {
    const authClient = await getAuthenticatedApiClient();
    const response = await authClient.get(API_ENDPOINTS.ADMIN_USERS.LIST);
    return response.data;
  } catch (error) {
    console.error('Error fetching admin users:', error);
    throw error;
  }
}

/**
 * Fetch all roles from the external API
 * @returns List of roles
 */
export async function getAdminRoles(): Promise<Role[]> {
  try {
    const authClient = await getAuthenticatedApiClient();
    const response = await authClient.get(API_ENDPOINTS.ADMIN_USERS.ROLES);
    return response.data;
  } catch (error) {
    console.error('Error fetching admin roles:', error);
    throw error;
  }
}

/**
 * Update a user's role
 * @param userId - The user ID
 * @param roleId - The role ID
 */
export async function updateAdminUserRole(userId: string, roleId: number): Promise<void> {
  try {
    const authClient = await getAuthenticatedApiClient();
    await authClient.patch(API_ENDPOINTS.ADMIN_USERS.UPDATE_ROLE(userId), {
      role_id: roleId,
    });
  } catch (error) {
    console.error('Error updating user role:', error);
    throw error;
  }
}

/**
 * Update a user's status (active/inactive)
 * @param userId - The user ID
 * @param status - The new status
 */
export async function updateAdminUserStatus(
  userId: string,
  status: 'ACTIVE' | 'INACTIVE',
): Promise<void> {
  try {
    const authClient = await getAuthenticatedApiClient();
    await authClient.patch(API_ENDPOINTS.ADMIN_USERS.UPDATE_STATUS(userId), { status });
  } catch (error) {
    console.error('Error updating user status:', error);
    throw error;
  }
}

/**
 * Create a new user
 * @param userData - User data including email, password, full_name, username, and role_id
 * @returns Promise resolving to the created user
 */
export async function createAdminUser(userData: {
  email: string;
  password?: string;
  full_name: string;
  username: string;
  role_id: number;
}): Promise<AdminUser> {
  try {
    console.log('Creating new user using backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.post(API_ENDPOINTS.ADMIN_USERS.CREATE, userData);

    console.log('User created successfully with backend API');

    return response.data;
  } catch (error) {
    console.error('Error creating user with backend API:', error);
    throw error;
  }
}

/**
 * Update a user
 * @param userId - The user ID
 * @param userData - User data to update
 * @returns Promise resolving to the updated user
 */
export async function updateAdminUser(
  userId: string,
  userData: {
    email?: string;
    password?: string;
    full_name?: string;
    username?: string;
    role_id?: number;
  },
): Promise<AdminUser> {
  try {
    console.log(`Updating user ${userId} using backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.patch(
      API_ENDPOINTS.ADMIN_USERS.UPDATE(userId),
      userData,
    );

    console.log('User updated successfully with backend API');

    return response.data;
  } catch (error) {
    console.error(`Error updating user ${userId} using backend API:`, error);
    throw error;
  }
}
