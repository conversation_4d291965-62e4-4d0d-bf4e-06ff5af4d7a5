import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CalculationBreakdownDto {
  @ApiProperty({
    description: 'Package ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  packageId: string;

  @ApiProperty({
    description: 'Package name',
    example: 'Catering Service',
  })
  packageName: string;

  @ApiProperty({
    description: 'Quantity calculated based on attendees and package basis',
    example: 100,
  })
  quantity: number;

  @ApiProperty({
    description: 'Unit price for this package',
    example: 50000,
  })
  unitPrice: number;

  @ApiProperty({
    description: 'Total price for this package (quantity * unitPrice)',
    example: 5000000,
  })
  totalPrice: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'IDR',
  })
  currency: string;

  @ApiPropertyOptional({
    description: 'Unit cost for this package (for profit calculation)',
    example: 35000,
  })
  unitCost?: number;

  @ApiPropertyOptional({
    description: 'Total cost for this package (quantity * unitCost)',
    example: 3500000,
  })
  totalCost?: number;
}

export class TemplateCalculationResultDto {
  @ApiProperty({
    description: 'Total value of all packages',
    example: 15000000,
  })
  packagesTotal: number;

  @ApiProperty({
    description: 'Total value of custom items (currently always 0)',
    example: 0,
  })
  customItemsTotal: number;

  @ApiProperty({
    description: 'Grand total (packages + custom items)',
    example: 15000000,
  })
  grandTotal: number;

  @ApiProperty({
    description: 'Detailed breakdown of each package calculation',
    type: [CalculationBreakdownDto],
  })
  breakdown: CalculationBreakdownDto[];

  @ApiProperty({
    description: 'Currency code for the calculation',
    example: 'IDR',
  })
  currency: string;

  @ApiProperty({
    description: 'Whether all packages have valid prices',
    example: true,
  })
  hasValidPrices: boolean;

  @ApiProperty({
    description: 'List of packages or issues with missing prices',
    example: [],
    type: [String],
  })
  missingPrices: string[];

  @ApiPropertyOptional({
    description: 'Total cost of all packages (for profit calculation)',
    example: 10500000,
  })
  totalCost?: number;

  @ApiPropertyOptional({
    description: 'Estimated profit (grandTotal - totalCost)',
    example: 4500000,
  })
  estimatedProfit?: number;
}

export class TemplateCalculationSummaryDto {
  @ApiProperty({
    description: 'Total number of packages in the template',
    example: 5,
  })
  totalPackages: number;

  @ApiProperty({
    description: 'Total value of the template',
    example: 15000000,
  })
  totalValue: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'IDR',
  })
  currency: string;

  @ApiProperty({
    description: 'Whether all packages have valid prices',
    example: true,
  })
  hasValidPrices: boolean;

  @ApiProperty({
    description: 'Number of packages with missing prices',
    example: 0,
  })
  missingPricesCount: number;

  @ApiProperty({
    description: 'Average value per package',
    example: 3000000,
  })
  averagePackageValue: number;

  @ApiPropertyOptional({
    description: 'Total cost (for profit calculation)',
    example: 10500000,
  })
  totalCost?: number;

  @ApiPropertyOptional({
    description: 'Estimated profit margin percentage',
    example: 30,
  })
  profitMarginPercentage?: number;
}
