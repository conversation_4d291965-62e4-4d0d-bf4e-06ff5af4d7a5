import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';

// DTO for returning package option details
@ApiExtraModels()
export class PackageOptionDto {
  @ApiProperty({ description: 'Option record UUID', format: 'uuid' })
  id: string;

  @ApiProperty({
    description: 'Associated package UUID',
    format: 'uuid',
  })
  applicable_package_id: string;

  @ApiProperty({ description: 'Option code (unique within package/currency)' })
  option_code: string;

  @ApiProperty({ description: 'Display name' })
  option_name: string;

  @ApiProperty({ description: 'Description', nullable: true })
  description: string | null;

  @ApiProperty({ description: 'Price adjustment', type: Number })
  price_adjustment: number;

  @ApiProperty({ description: 'Cost adjustment', type: Number })
  cost_adjustment: number;

  @ApiProperty({ description: 'Currency ID', format: 'uuid' })
  currency_id: string;
  example: '685860b9-257f-41eb-b223-b3e1fad8f3b9';

  @ApiProperty({ description: 'Option group name', nullable: true })
  option_group: string | null;

  @ApiProperty({ description: 'Is default option?', type: Boolean })
  is_default_for_package: boolean;

  @ApiProperty({ description: 'Is required option?', type: Boolean })
  is_required: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  created_at: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updated_at: string;
}
