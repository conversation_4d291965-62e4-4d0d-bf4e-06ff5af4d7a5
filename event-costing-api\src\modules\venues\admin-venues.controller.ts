import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Logger,
  Query,
  Put,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { VenuesService } from './venues.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';
import {
  AdminVenueDto,
  CreateVenueDto,
  ListVenuesQueryDto,
  PaginatedVenuesResponse,
  UpdateVenueDto,
} from './dto/admin-venue.dto';

@ApiTags('Admin - Venues')
@ApiBearerAuth()
@Controller('admin/venues')
@UseGuards(JwtAuthGuard, AdminRoleGuard)
export class AdminVenuesController {
  private readonly logger = new Logger(AdminVenuesController.name);

  constructor(private readonly venuesService: VenuesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all venues (admin)' })
  @ApiQuery({ type: ListVenuesQueryDto })
  @ApiResponse({
    status: 200,
    description: 'List of venues with pagination',
    type: PaginatedVenuesResponse,
  })
  async findAll(
    @Query() queryDto: ListVenuesQueryDto,
  ): Promise<PaginatedVenuesResponse> {
    this.logger.log(`Admin request to get all venues with query: ${JSON.stringify(queryDto)}`);
    return this.venuesService.findAllAdmin(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a venue by ID (admin)' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({
    status: 200,
    description: 'Venue details',
    type: AdminVenueDto,
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<AdminVenueDto> {
    this.logger.log(`Admin request to get venue with ID: ${id}`);
    return this.venuesService.findOneAdmin(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new venue' })
  @ApiBody({ type: CreateVenueDto })
  @ApiResponse({
    status: 201,
    description: 'Venue created successfully',
    type: AdminVenueDto,
  })
  async create(@Body() createVenueDto: CreateVenueDto): Promise<AdminVenueDto> {
    this.logger.log(`Admin request to create venue: ${JSON.stringify(createVenueDto)}`);
    return this.venuesService.create(createVenueDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a venue' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiBody({ type: UpdateVenueDto })
  @ApiResponse({
    status: 200,
    description: 'Venue updated successfully',
    type: AdminVenueDto,
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateVenueDto: UpdateVenueDto,
  ): Promise<AdminVenueDto> {
    this.logger.log(`Admin request to update venue with ID ${id}: ${JSON.stringify(updateVenueDto)}`);
    return this.venuesService.update(id, updateVenueDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a venue (soft delete)' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponse({
    status: 204,
    description: 'Venue deleted successfully',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Admin request to delete venue with ID: ${id}`);
    await this.venuesService.remove(id);
  }

  @Post(':id/restore')
  @ApiOperation({ summary: 'Restore a deleted venue' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponse({
    status: 204,
    description: 'Venue restored successfully',
  })
  async restore(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Admin request to restore venue with ID: ${id}`);
    await this.venuesService.restore(id);
  }
}
