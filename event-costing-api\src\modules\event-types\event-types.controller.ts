import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { EventTypesService } from './event-types.service';
import {
  CreateEventTypeDto,
  UpdateEventTypeDto,
  EventTypeDto,
} from './dto/event-type.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('event-types')
export class EventTypesController {
  constructor(private readonly eventTypesService: EventTypesService) {}

  @Get()
  async findAll(): Promise<{ data: EventTypeDto[] }> {
    const data = await this.eventTypesService.findAll();
    return { data };
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<{ data: EventTypeDto }> {
    const data = await this.eventTypesService.findOne(id);
    return { data };
  }
}

@Controller('admin/event-types')
@UseGuards(JwtAuthGuard)
export class AdminEventTypesController {
  constructor(private readonly eventTypesService: EventTypesService) {}

  @Get()
  async findAllAdmin(): Promise<{ data: EventTypeDto[] }> {
    const data = await this.eventTypesService.findAllAdmin();
    return { data };
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<{ data: EventTypeDto }> {
    const data = await this.eventTypesService.findOne(id);
    return { data };
  }

  @Post()
  async create(
    @Body() createEventTypeDto: CreateEventTypeDto,
  ): Promise<{ data: EventTypeDto }> {
    const data = await this.eventTypesService.create(createEventTypeDto);
    return { data };
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateEventTypeDto: UpdateEventTypeDto,
  ): Promise<{ data: EventTypeDto }> {
    const data = await this.eventTypesService.update(id, updateEventTypeDto);
    return { data };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    await this.eventTypesService.remove(id);
  }
}
