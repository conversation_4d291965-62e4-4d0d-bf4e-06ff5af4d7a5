import { useMemo } from "react";
import { LineItem } from "@/types/calculation";
import {
  calculateLineItemTotal,
  formatCurrency,
  calculateFinalTotal,
  Tax,
  Discount,
} from "../../utils/calculationUtils";
import { debug } from "@/lib/debugUtils";
import { FINANCIAL_CONSTANTS } from "../../constants";

/**
 * Interface for financial calculation results
 */
export interface FinancialCalculationResults {
  subtotal: number;
  totalCost: number;
  profit: number;
  taxesTotal: number;
  discountAmount: number;
  total: number;
  formattedSubtotal: string;
  formattedTaxesTotal: string;
  formattedDiscountAmount: string;
  formattedTotal: string;
  formattedTotalCost: string;
  formattedProfit: string;
  profitPercentage: number;
  calculatedTaxes: { tax: Tax; amount: number }[];
  taxes: Tax[];
  discount: Discount;
}

/**
 * Enhanced custom hook for comprehensive financial calculations
 * Handles both line item-based calculations and simple total-based calculations
 * with proper dependency tracking and optimistic update filtering
 *
 * @param lineItems - Array of line items to calculate totals for (optional)
 * @param subtotalOverride - Direct subtotal value (used when lineItems not provided)
 * @param taxes - Array of taxes to apply
 * @param discount - Discount to apply
 * @returns Object with calculated financial totals
 */
export const useFinancialCalculations = (
  lineItems?: LineItem[],
  subtotalOverride?: number,
  taxes: Tax[] = [],
  discount: Discount | null = { name: "No Discount", type: "fixed", value: 0 }
): FinancialCalculationResults => {
  // CRITICAL FIX: Create a stable hash of line items for dependency tracking
  // This prevents recalculation when lineItems array reference changes but data is the same
  const lineItemsHash = useMemo(() => {
    if (!lineItems) return null;
    return lineItems
      .filter((item) => !item._isOptimistic) // Filter out optimistic updates
      .map(
        (item) =>
          `${item.id}-${item.total_price}-${item.quantity}-${item.unit_price}`
      )
      .join("|");
  }, [lineItems]);

  // CRITICAL FIX: Create stable hash for taxes to prevent recalculation
  const taxesHash = useMemo(() => {
    if (!taxes || taxes.length === 0) return "no-taxes";
    return taxes.map((tax) => `${tax.name}-${tax.type}-${tax.value}`).join("|");
  }, [taxes]);

  // CRITICAL FIX: Create stable hash for discount to prevent recalculation
  const discountHash = useMemo(() => {
    if (!discount) return "no-discount";
    return `${discount.name}-${discount.type}-${discount.value}`;
  }, [discount]);

  // Memoize the calculations to prevent unnecessary recalculations
  return useMemo(() => {
    debug("Calculating financial totals for line items:", lineItems);

    // Determine subtotal: either from line items or override value
    let subtotal = 0;

    if (typeof subtotalOverride === "number" && !isNaN(subtotalOverride)) {
      // Use provided subtotal override
      subtotal = subtotalOverride;
      debug("Using subtotal override:", subtotal);
    } else if (lineItems && Array.isArray(lineItems) && lineItems.length > 0) {
      // Filter out optimistic updates and calculate subtotal from valid line items
      const validLineItems = lineItems.filter((item) => !item._isOptimistic);
      subtotal = validLineItems.reduce((sum, item) => {
        const itemTotal = calculateLineItemTotal(item);
        debug(`Line item ${item.name} total: ${itemTotal}`);
        return sum + itemTotal;
      }, 0);
      debug(
        `Calculated subtotal from valid line items: ${subtotal} (${validLineItems.length}/${lineItems.length} items)`
      );
    } else {
      debug("No line items or subtotal override, returning zeros");
      const emptyResult: FinancialCalculationResults = {
        subtotal: 0,
        totalCost: 0,
        profit: 0,
        taxesTotal: 0,
        discountAmount: 0,
        total: 0,
        formattedSubtotal: formatCurrency(0),
        formattedTaxesTotal: formatCurrency(0),
        formattedDiscountAmount: formatCurrency(0),
        formattedTotal: formatCurrency(0),
        formattedTotalCost: formatCurrency(0),
        formattedProfit: formatCurrency(0),
        profitPercentage: 0,
        calculatedTaxes: [],
        taxes: [],
        discount: { name: "No Discount", type: "fixed", value: 0 },
      };
      return emptyResult;
    }

    try {
      // Calculate final total with taxes and discount
      const { taxesTotal, discountAmount, total, calculatedTaxes } =
        calculateFinalTotal(subtotal, taxes, discount);

      // Calculate profit using configurable cost ratio
      // In a real app, you'd calculate based on actual costs and other factors
      const totalCost = subtotal * FINANCIAL_CONSTANTS.DEFAULT_COST_RATIO;
      const profit = total - totalCost;
      const profitPercentage = total > 0 ? (profit / total) * 100 : 0;

      // Format the values for display
      const formattedSubtotal = formatCurrency(subtotal);
      const formattedTaxesTotal = formatCurrency(taxesTotal);
      const formattedDiscountAmount = formatCurrency(discountAmount);
      const formattedTotal = formatCurrency(total);
      const formattedTotalCost = formatCurrency(totalCost);
      const formattedProfit = formatCurrency(profit);

      debug("Calculated financial totals:", {
        subtotal,
        taxesTotal,
        discountAmount,
        total,
        totalCost,
        profit,
        profitPercentage,
      });

      return {
        subtotal,
        totalCost,
        profit,
        taxesTotal,
        discountAmount,
        total,
        formattedSubtotal,
        formattedTaxesTotal,
        formattedDiscountAmount,
        formattedTotal,
        formattedTotalCost,
        formattedProfit,
        profitPercentage,
        calculatedTaxes,
        taxes,
        discount,
      };
    } catch (error) {
      debug("Error calculating financial totals:", error);
      const errorResult: FinancialCalculationResults = {
        subtotal: 0,
        totalCost: 0,
        profit: 0,
        taxesTotal: 0,
        discountAmount: 0,
        total: 0,
        formattedSubtotal: formatCurrency(0),
        formattedTaxesTotal: formatCurrency(0),
        formattedDiscountAmount: formatCurrency(0),
        formattedTotal: formatCurrency(0),
        formattedTotalCost: formatCurrency(0),
        formattedProfit: formatCurrency(0),
        profitPercentage: 0,
        calculatedTaxes: [],
        taxes: [],
        discount: { name: "No Discount", type: "fixed", value: 0 },
      };
      return errorResult;
    }
  }, [
    // CRITICAL FIX: Use stable hashes instead of object references
    lineItemsHash, // Stable hash of line items data
    subtotalOverride, // Primitive value - stable
    taxesHash, // Stable hash of taxes data
    discountHash, // Stable hash of discount data
  ]); // Optimized dependencies to prevent infinite re-renders
};

/**
 * Convenience hook for simple financial summary calculations
 * @param total - The subtotal to calculate taxes and discounts on
 * @param taxes - Array of taxes to apply
 * @param discount - Discount to apply
 * @returns Simplified financial calculation results
 */
export const useFinancialSummaryCalculations = (
  total: number,
  taxes: Tax[],
  discount: Discount
) => {
  const results = useFinancialCalculations(undefined, total, taxes, discount);

  return {
    validTotal: results.subtotal,
    financialTotals: {
      subtotal: results.subtotal,
      taxesTotal: results.taxesTotal,
      discountAmount: results.discountAmount,
      total: results.total,
      calculatedTaxes: results.calculatedTaxes,
    },
    finalTotal: results.total,
  };
};
