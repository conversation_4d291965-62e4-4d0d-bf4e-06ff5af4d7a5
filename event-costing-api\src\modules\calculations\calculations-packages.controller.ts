import {
  <PERSON>,
  Get,
  Param,
  Query,
  ParseU<PERSON><PERSON>ipe,
  UseGuards,
  Logger,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { CalculationsService } from './calculations.service';
import { PackagesService } from '../packages/packages.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import {
  ApiTags,
  ApiBearerAuth,
  ApiParam,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
} from '@nestjs/swagger';
import { PackagesByCategoryResponseDto } from '../packages/dto/packages-by-category-response.dto';

@ApiTags('Calculation Packages')
@ApiBearerAuth()
@Controller('calculations')
@UseGuards(JwtAuthGuard)
export class CalculationsPackagesController {
  private readonly logger = new Logger(CalculationsPackagesController.name);

  constructor(
    private readonly calculationsService: CalculationsService,
    private readonly packagesService: PackagesService,
  ) {}

  @Get(':id/available-packages')
  @ApiOperation({
    summary: 'Get available packages organized by category for a calculation',
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiQuery({
    name: 'includeOptions',
    required: false,
    type: Boolean,
    description: 'Whether to include package options in the response',
  })
  @ApiOkResponse({ type: PackagesByCategoryResponseDto })
  async getAvailablePackagesByCategory(
    @Param('id', ParseUUIDPipe) calculationId: string,
    @Query('includeOptions') includeOptions: boolean = false,
    @GetCurrentUser() user: User,
  ): Promise<PackagesByCategoryResponseDto> {
    this.logger.log(
      `User ${user.email} fetching available packages for calculation ID: ${calculationId} (includeOptions: ${includeOptions})`,
    );

    try {
      // Check calculation ownership
      await this.calculationsService.checkCalculationOwnership(
        calculationId,
        user.id,
      );

      // Get calculation details to determine city, venue, currency
      const calculation = await this.calculationsService.findCalculationById(
        calculationId,
        user,
      );

      if (!calculation) {
        throw new NotFoundException(
          `Calculation with ID ${calculationId} not found`,
        );
      }

      if (!calculation.currency?.id) {
        throw new NotFoundException(
          `Calculation ${calculationId} has no currency set`,
        );
      }

      // Get packages organized by category
      // Use the first venue from the venues array if available
      const venueId =
        calculation.venues && calculation.venues.length > 0
          ? calculation.venues[0].id
          : undefined;

      return this.packagesService.getPackagesByCategory(
        calculation.currency.id,
        calculation.city?.id,
        venueId,
        includeOptions,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Error fetching available packages for calculation ${calculationId}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve available packages',
      );
    }
  }
}

