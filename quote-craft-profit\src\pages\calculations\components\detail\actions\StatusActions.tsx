import React from 'react';
import { Button } from '@/components/ui/button';
import { Save, CheckCircle } from 'lucide-react';

interface StatusActionsProps {
  status?: 'draft' | 'completed' | 'canceled';
  isProcessing: boolean;
  onSaveDraft: () => void;
  onComplete: () => void;
}

/**
 * Status action buttons component
 * Provides save draft and complete buttons with proper state handling
 */
export const StatusActions: React.FC<StatusActionsProps> = ({
  status,
  isProcessing,
  onSaveDraft,
  onComplete,
}) => {
  return (
    <div className='flex space-x-2'>
      <Button
        variant='outline'
        size='sm'
        onClick={onSaveDraft}
        disabled={isProcessing}
      >
        <Save size={16} className='mr-1' /> Save Draft
      </Button>
      <Button
        variant='default'
        size='sm'
        onClick={onComplete}
        disabled={isProcessing || status === 'completed'}
      >
        <CheckCircle size={16} className='mr-1' /> Complete
      </Button>
    </div>
  );
};
