import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  FileText,
  Calendar,
  Users,
  MapPin,
  Clock,
  ArrowLeft,
  DollarSign,
  Tag,
  Layers,
  CheckCircle,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { getEnhancedTemplateById } from "@/services/templates/userTemplateService";
import { CreateCalculationFromTemplateDialog } from "@/pages/templates/components";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

// Format currency
const formatCurrency = (amount: number, currency: string = "IDR") => {
  try {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: currency,
      maximumFractionDigits: 0,
    })
      .format(amount)
      .replace("IDR", "Rp");
  } catch (error) {
    return `${currency === "IDR" ? "Rp" : currency} ${amount.toLocaleString()}`;
  }
};

const TemplateDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { formatForDisplay } = useTimezoneAwareDates();

  const {
    data: template,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["templateDetailEnhanced", id],
    queryFn: () => getEnhancedTemplateById(id!),
    enabled: !!id,
    meta: {
      onError: (error: Error) => {
        toast.error(`Failed to load template details: ${error.message}`);
      },
    },
  });

  if (isLoading) {
    return (
      <MainLayout>
        <div className="mb-6">
          <Button variant="ghost" onClick={() => navigate(-1)} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-1/4 mt-2" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-1/4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full mt-2" />
                <Skeleton className="h-4 w-3/4 mt-2" />
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <Skeleton className="h-6 w-1/4" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i}>
                      <Skeleton className="h-5 w-1/5 mb-2" />
                      <div className="space-y-2">
                        {[1, 2, 3].map((j) => (
                          <div key={j} className="flex justify-between">
                            <Skeleton className="h-4 w-1/3" />
                            <Skeleton className="h-4 w-1/6" />
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex justify-between">
                      <Skeleton className="h-4 w-1/3" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Skeleton className="h-10 w-full" />
              </CardFooter>
            </Card>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (isError || !template) {
    return (
      <MainLayout>
        <div className="mb-6">
          <Button variant="ghost" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
        </div>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
              <FileText className="h-8 w-8 text-red-500" />
            </div>
            <h3 className="text-lg font-medium mb-2">Template not found</h3>
            <p className="text-gray-500 mb-4">
              The template you're looking for doesn't exist or has been removed.
            </p>
            <Link to="/templates">
              <Button>Browse all templates</Button>
            </Link>
          </CardContent>
        </Card>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mb-6">
        <Button variant="ghost" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Templates
        </Button>
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              {template.name}
            </h1>
            <div className="flex items-center mt-1">
              {template.event_type && (
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 mr-2"
                >
                  {template.event_type}
                </Badge>
              )}
              <Badge
                variant={template.is_public ? "default" : "outline"}
                className="mr-2"
              >
                {template.is_public ? "Public" : "Private"}
              </Badge>
              <p className="text-sm text-gray-500">
                Added {formatForDisplay(template.created_at, "MMM d, yyyy")}
              </p>
            </div>
          </div>
          <Button onClick={() => setIsDialogOpen(true)}>
            <FileText className="h-4 w-4 mr-2" />
            Use This Template
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Template Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">
                {template.description ||
                  "No description provided for this template."}
              </p>

              <div className="grid grid-cols-2 gap-4 mt-6">
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Attendees</p>
                    <p className="text-sm text-gray-500">
                      {template.attendees
                        ? `${template.attendees} guests`
                        : "Not specified"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Event Dates</p>
                    <p className="text-sm text-gray-500">
                      {template.template_start_date &&
                      template.template_end_date
                        ? `${formatForDisplay(
                            template.template_start_date,
                            "MMM d, yyyy"
                          )} - ${formatForDisplay(
                            template.template_end_date,
                            "MMM d, yyyy"
                          )}`
                        : "Not specified"}
                    </p>
                  </div>
                </div>
                {template.venue_ids && template.venue_ids.length > 0 && (
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-gray-400 mr-2" />
                    <div>
                      <p className="text-sm font-medium">Venues</p>
                      <p className="text-sm text-gray-500">
                        {template.venue_ids.length} venue
                        {template.venue_ids.length > 1 ? "s" : ""} associated
                      </p>
                    </div>
                  </div>
                )}
                <div className="flex items-center">
                  <Tag className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Package Selections</p>
                    <p className="text-sm text-gray-500">
                      {template.package_selections?.length || 0} package
                      {(template.package_selections?.length || 0) !== 1
                        ? "s"
                        : ""}{" "}
                      selected
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Package Selections</CardTitle>
            </CardHeader>
            <CardContent>
              {template.package_selections &&
              template.package_selections.length > 0 ? (
                <div className="space-y-4">
                  {template.package_selections.map((selection, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center mb-2">
                        <Layers className="h-4 w-4 text-gray-400 mr-2" />
                        <h3 className="font-medium text-gray-800">
                          {selection.package_name || `Package ${index + 1}`}
                        </h3>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">
                            Package ID:
                          </span>
                          <span className="text-sm font-mono">
                            {selection.package_id}
                          </span>
                        </div>
                        {selection.item_quantity && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">
                              Quantity:
                            </span>
                            <span className="text-sm">
                              {selection.item_quantity}
                            </span>
                          </div>
                        )}
                        {selection.duration_days && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">
                              Duration:
                            </span>
                            <span className="text-sm">
                              {selection.duration_days} days
                            </span>
                          </div>
                        )}
                        {selection.option_names &&
                          selection.option_names.length > 0 && (
                            <div>
                              <span className="text-sm text-gray-600">
                                Selected Options:
                              </span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {selection.option_names.map(
                                  (optionName, optionIndex) => (
                                    <Badge
                                      key={optionIndex}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {optionName}
                                    </Badge>
                                  )
                                )}
                              </div>
                            </div>
                          )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Layers className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No packages selected
                  </h3>
                  <p className="text-gray-500">
                    This template doesn't have any package selections configured
                    yet.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle className="text-lg">Template Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {template.event_type && (
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Tag className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm">Event Type</span>
                    </div>
                    <span className="text-sm font-medium">
                      {template.event_type}
                    </span>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm">Attendees</span>
                  </div>
                  <span className="text-sm font-medium">
                    {template.attendees
                      ? `${template.attendees} guests`
                      : "Not specified"}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Layers className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm">Packages</span>
                  </div>
                  <span className="text-sm font-medium">
                    {template.package_selections?.length || 0}
                  </span>
                </div>

                {template.venue_ids && template.venue_ids.length > 0 && (
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm">Venues</span>
                    </div>
                    <span className="text-sm font-medium">
                      {template.venue_ids.length}
                    </span>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm">Status</span>
                  </div>
                  <Badge variant={template.is_public ? "default" : "outline"}>
                    {template.is_public ? "Public" : "Private"}
                  </Badge>
                </div>

                <Separator />

                <div className="text-center">
                  <p className="text-xs text-gray-500 mb-2">Created on</p>
                  <p className="text-sm font-medium">
                    {formatForDisplay(template.created_at, "MMM d, yyyy")}
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full" onClick={() => setIsDialogOpen(true)}>
                <FileText className="h-4 w-4 mr-2" />
                Use This Template
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Create Calculation Dialog */}
      <CreateCalculationFromTemplateDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        template={template}
      />
    </MainLayout>
  );
};

export default TemplateDetailPage;
