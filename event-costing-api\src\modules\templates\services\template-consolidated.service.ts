import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { TemplateDetailService } from './template-detail.service';
import { TemplateCalculationService } from './template-calculation.service';
import { TemplateQueryService } from './template-query.service';
import { PackagesService } from '../../packages/packages.service';
import { CategoriesService } from '../../categories/categories.service';
import { TemplateCompleteDataDto } from '../dto/template-complete-data.dto';
import { TemplateFiltersDto } from '../dto/template-filters.dto';

/**
 * Service responsible for providing consolidated template management data
 * Implements the consolidated endpoint pattern for template operations
 */
@Injectable()
export class TemplateConsolidatedService {
  private readonly logger = new Logger(TemplateConsolidatedService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly templateDetailService: TemplateDetailService,
    private readonly templateCalculationService: TemplateCalculationService,
    private readonly templateQueryService: TemplateQueryService,
    private readonly packagesService: PackagesService,
    private readonly categoriesService: CategoriesService,
  ) {}

  /**
   * Get complete template management data in a single API call
   * Replaces the need for multiple separate API calls for template management
   *
   * @param filters - Template filters
   * @param user - The authenticated user
   * @returns Complete template management data with metadata
   */
  async getTemplateManagementData(
    filters: TemplateFiltersDto = {},
    user: User,
  ): Promise<TemplateCompleteDataDto> {
    this.logger.log(
      `Fetching template management data for user: ${user.email}`,
    );

    const startTime = Date.now();

    try {
      // Parallel data fetching with error handling
      const [
        templatesResult,
        categoriesResult,
        eventTypesResult,
        packagesResult,
        summaryResult,
      ] = await Promise.allSettled([
        this.getTemplates(filters, user),
        this.getCategories(),
        this.getEventTypes(),
        this.getAvailablePackages(user),
        this.getTemplateSummary(user),
      ]);

      // Handle partial failures gracefully
      const templates = this.extractResult(templatesResult, 'templates', {
        data: [],
        totalCount: 0,
        page: 1,
        pageSize: 10,
        totalPages: 0,
      });
      const categories = this.extractResult(categoriesResult, 'categories', []);
      const eventTypes = this.extractResult(eventTypesResult, 'eventTypes', []);
      const packages = this.extractResult(packagesResult, 'packages', []);
      const summaryData = this.extractResult(summaryResult, 'summary', null);
      const summary = summaryData || undefined;

      const loadTime = Date.now() - startTime;
      const errors = this.collectErrors([
        templatesResult,
        categoriesResult,
        eventTypesResult,
        packagesResult,
        summaryResult,
      ]);

      const result: TemplateCompleteDataDto = {
        templates,
        categories,
        eventTypes,
        packages,
        summary,
        filters: {
          applied: filters,
          available: {
            categories: categories.map(cat => ({ id: cat.id, name: cat.name })),
            eventTypes: eventTypes.map(et => ({ id: et.id, name: et.name })),
          },
        },
        metadata: {
          loadTime,
          cacheVersion: '1.0',
          userId: user.id,
          errors,
          timestamp: new Date().toISOString(),
          totalTemplates: templates.totalCount,
          appliedFilters: Object.keys(filters).length,
        },
      };

      this.logger.log(
        `Successfully fetched template management data in ${loadTime}ms`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch template management data`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to load template management data. Please try again.',
      );
    }
  }

  /**
   * Get complete template detail data for editing/viewing
   * Includes template details, packages, calculations, and metadata
   */
  async getTemplateDetailData(templateId: string, user: User): Promise<any> {
    this.logger.log(
      `Fetching complete template detail data for ID: ${templateId}`,
    );

    const startTime = Date.now();

    try {
      // Parallel data fetching for template detail view
      const [
        templateResult,
        packagesResult,
        calculationResult,
        categoriesResult,
      ] = await Promise.allSettled([
        this.getTemplateById(templateId, user),
        this.getTemplatePackages(templateId, user),
        this.getTemplateCalculation(templateId, user),
        this.getCategories(),
      ]);

      // Handle partial failures gracefully
      const template = this.extractResult(templateResult, 'template');
      const packages = this.extractResult(packagesResult, 'packages', []);
      const calculation = this.extractResult(
        calculationResult,
        'calculation',
        null,
      );
      const categories = this.extractResult(categoriesResult, 'categories', []);

      const loadTime = Date.now() - startTime;
      const errors = this.collectErrors([
        templateResult,
        packagesResult,
        calculationResult,
        categoriesResult,
      ]);

      return {
        template,
        packages,
        calculation,
        categories,
        metadata: {
          loadTime,
          cacheVersion: '1.0',
          userId: user.id,
          errors,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch template detail data for ID: ${templateId}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to load template detail data. Please try again.',
      );
    }
  }

  /**
   * Get templates with filters
   */
  private async getTemplates(filters: TemplateFiltersDto, user: User) {
    // Convert TemplateFiltersDto to ListTemplatesDto
    const queryDto = {
      search: filters.search,
      eventType: filters.eventType,
      cityId: filters.cityId,
      categoryId: filters.categoryId,
      dateStart: filters.createdAfter,
      dateEnd: filters.createdBefore,
      sortBy: filters.sortBy as any,
      sortOrder: filters.sortOrder as any,
      limit: filters.pageSize || 10,
      offset: ((filters.page || 1) - 1) * (filters.pageSize || 10),
    };

    const result = await this.templateQueryService.findUserTemplates(
      user,
      queryDto,
    );

    // Convert to paginated format
    return {
      data: result.data,
      totalCount: result.count,
      page: filters.page || 1,
      pageSize: filters.pageSize || 10,
      totalPages: Math.ceil(result.count / (filters.pageSize || 10)),
    };
  }

  /**
   * Get template by ID
   */
  private async getTemplateById(templateId: string, user: User) {
    return this.templateDetailService.findOnePublic(templateId);
  }

  /**
   * Get template packages
   */
  private async getTemplatePackages(templateId: string, user: User) {
    // This would need to be implemented based on your template package structure
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from('template_packages')
      .select('*')
      .eq('template_id', templateId);

    if (error) {
      throw error;
    }

    return data || [];
  }

  /**
   * Get template calculation data
   */
  private async getTemplateCalculation(templateId: string, user: User) {
    try {
      return await this.templateCalculationService.calculateTemplateTotal(
        templateId,
      );
    } catch (error) {
      // Template might not have calculation data yet
      return null;
    }
  }

  /**
   * Get all active categories
   */
  private async getCategories() {
    return this.categoriesService.findAll();
  }

  /**
   * Get all event types
   */
  private async getEventTypes() {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from('event_types')
      .select('id, name, description')
      .eq('is_deleted', false)
      .order('name');

    if (error) {
      throw error;
    }

    return data || [];
  }

  /**
   * Get available packages for templates
   */
  private async getAvailablePackages(user: User) {
    // Get packages that can be used in templates
    const queryDto = {
      currencyId: 'default-currency-id', // You'll need to handle this
      limit: 100, // Get more packages for template creation
      offset: 0,
    };

    const result = await this.packagesService.findVariations(queryDto);
    return result.data;
  }

  /**
   * Get template summary statistics
   */
  private async getTemplateSummary(user: User) {
    const supabase = this.supabaseService.getClient();

    // Get template counts by status
    const { data: statusCounts, error: statusError } = await supabase
      .from('templates')
      .select('is_active')
      .eq('created_by', user.id);

    if (statusError) {
      this.logger.warn('Failed to fetch template status counts:', statusError);
      return null;
    }

    const activeCount = statusCounts?.filter(t => t.is_active).length || 0;
    const inactiveCount = statusCounts?.filter(t => !t.is_active).length || 0;

    return {
      totalTemplates: statusCounts?.length || 0,
      activeTemplates: activeCount,
      inactiveTemplates: inactiveCount,
      publicTemplates: 0, // Would need to query for public templates
      privateTemplates: statusCounts?.length || 0,
    };
  }

  /**
   * Extract result from Promise.allSettled result
   */
  private extractResult<T>(
    result: PromiseSettledResult<T>,
    name: string,
    defaultValue?: T,
  ): T {
    if (result.status === 'fulfilled') {
      return result.value;
    }

    this.logger.warn(
      `Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`,
    );

    if (defaultValue !== undefined) {
      return defaultValue;
    }

    // For critical data like template, throw error
    if (name === 'template') {
      throw result.reason;
    }

    // For non-critical data, return empty array/object
    return [] as unknown as T;
  }

  /**
   * Collect errors from Promise.allSettled results
   */
  private collectErrors(results: PromiseSettledResult<any>[]): string[] {
    return results
      .filter(result => result.status === 'rejected')
      .map(
        result =>
          (result as PromiseRejectedResult).reason?.message || 'Unknown error',
      );
  }
}
