/**
 * Optimized notification system with debouncing and smart filtering
 * Reduces excessive notifications and improves user experience
 */

import { toast } from '@/components/ui/sonner';

// Debounce tracking for notifications
const notificationDebounce = new Map<string, NodeJS.Timeout>();
const recentNotifications = new Map<string, number>();

// Configuration for different notification types
const NOTIFICATION_CONFIG = {
  // Auto-save notifications - heavily debounced
  AUTO_SAVE: {
    debounceMs: 2000,
    cooldownMs: 10000, // Don't show same auto-save message for 10 seconds
    showSuccess: false, // Don't show success for auto-save
  },
  // Form submissions - moderate debouncing
  FORM_SUBMIT: {
    debounceMs: 500,
    cooldownMs: 3000,
    showSuccess: true,
  },
  // API errors - immediate but with cooldown
  API_ERROR: {
    debounceMs: 0,
    cooldownMs: 5000,
    showSuccess: false,
  },
  // Line item operations - debounced
  LINE_ITEM: {
    debounceMs: 1000,
    cooldownMs: 3000,
    showSuccess: true,
  },
  // Critical operations - immediate
  CRITICAL: {
    debounceMs: 0,
    cooldownMs: 1000,
    showSuccess: true,
  },
  // Data loading operations - completely suppressed
  DATA_LOADING: {
    debounceMs: 0,
    cooldownMs: 0,
    showSuccess: false, // Never show success for routine data loading
  },
};

type NotificationCategory = keyof typeof NOTIFICATION_CONFIG;

interface OptimizedNotificationOptions {
  category?: NotificationCategory;
  id?: string;
  description?: string;
  duration?: number;
  force?: boolean; // Bypass debouncing and cooldown
}

/**
 * Check if notification should be shown based on cooldown
 */
function shouldShowNotification(key: string, cooldownMs: number, force: boolean = false): boolean {
  if (force) return true;

  const lastShown = recentNotifications.get(key);
  if (!lastShown) return true;

  return Date.now() - lastShown > cooldownMs;
}

/**
 * Mark notification as shown
 */
function markNotificationShown(key: string): void {
  recentNotifications.set(key, Date.now());
}

/**
 * Create debounced notification function
 */
function createDebouncedNotification(
  key: string,
  notificationFn: () => void,
  debounceMs: number
): void {
  // Clear existing timeout
  const existingTimeout = notificationDebounce.get(key);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
  }

  if (debounceMs === 0) {
    // Show immediately
    notificationFn();
    return;
  }

  // Set new timeout
  const timeout = setTimeout(() => {
    notificationFn();
    notificationDebounce.delete(key);
  }, debounceMs);

  notificationDebounce.set(key, timeout);
}

/**
 * Show optimized success notification
 */
export function showOptimizedSuccess(
  message: string,
  options: OptimizedNotificationOptions = {}
): void {
  const { category = 'FORM_SUBMIT', id, description, duration, force = false } = options;
  const config = NOTIFICATION_CONFIG[category];

  if (!config.showSuccess && !force) return;

  const key = id || `success-${category}-${message}`;

  if (!shouldShowNotification(key, config.cooldownMs, force)) return;

  createDebouncedNotification(
    key,
    () => {
      toast.success(message, {
        description,
        duration: duration || 4000,
        dismissible: true,
      });
      markNotificationShown(key);
    },
    config.debounceMs
  );
}

/**
 * Show optimized error notification
 */
export function showOptimizedError(
  message: string,
  options: OptimizedNotificationOptions = {}
): void {
  const { category = 'API_ERROR', id, description, duration, force = false } = options;
  const config = NOTIFICATION_CONFIG[category];

  const key = id || `error-${category}-${message}`;

  if (!shouldShowNotification(key, config.cooldownMs, force)) return;

  createDebouncedNotification(
    key,
    () => {
      toast.error(message, {
        description,
        duration: duration || 6000,
        dismissible: true,
      });
      markNotificationShown(key);
    },
    config.debounceMs
  );
}

/**
 * Show optimized info notification
 */
export function showOptimizedInfo(
  message: string,
  options: OptimizedNotificationOptions = {}
): void {
  const { category = 'FORM_SUBMIT', id, description, duration, force = false } = options;
  const config = NOTIFICATION_CONFIG[category];

  const key = id || `info-${category}-${message}`;

  if (!shouldShowNotification(key, config.cooldownMs, force)) return;

  createDebouncedNotification(
    key,
    () => {
      toast.info(message, {
        description,
        duration: duration || 4000,
        dismissible: true,
      });
      markNotificationShown(key);
    },
    config.debounceMs
  );
}

/**
 * Show optimized loading notification
 */
export function showOptimizedLoading(
  message: string,
  options: OptimizedNotificationOptions = {}
): string | number {
  const { id, description } = options;

  return toast.loading(message, {
    id,
    description,
    dismissible: true,
  });
}

/**
 * Dismiss notification by ID
 */
export function dismissOptimizedNotification(id: string | number): void {
  toast.dismiss(id);
}

/**
 * Clear all debounced notifications (useful for cleanup)
 */
export function clearNotificationDebounce(): void {
  notificationDebounce.forEach((timeout) => clearTimeout(timeout));
  notificationDebounce.clear();
}

/**
 * Specialized notifications for common use cases
 */
export const optimizedNotifications = {
  // Auto-save notifications (heavily suppressed)
  autoSave: {
    success: (message: string) => showOptimizedSuccess(message, { category: 'AUTO_SAVE' }),
    error: (message: string) => showOptimizedError(message, { category: 'AUTO_SAVE' }),
  },

  // Line item operations
  lineItem: {
    success: (message: string) => showOptimizedSuccess(message, { category: 'LINE_ITEM' }),
    error: (message: string) => showOptimizedError(message, { category: 'LINE_ITEM' }),
  },

  // Form submissions
  form: {
    success: (message: string) => showOptimizedSuccess(message, { category: 'FORM_SUBMIT' }),
    error: (message: string) => showOptimizedError(message, { category: 'FORM_SUBMIT' }),
  },

  // Critical operations (always shown)
  critical: {
    success: (message: string) => showOptimizedSuccess(message, { category: 'CRITICAL', force: true }),
    error: (message: string) => showOptimizedError(message, { category: 'CRITICAL', force: true }),
  },

  // Data loading operations (completely suppressed for success)
  dataLoading: {
    success: (message: string) => showOptimizedSuccess(message, { category: 'DATA_LOADING' }),
    error: (message: string) => showOptimizedError(message, { category: 'DATA_LOADING' }),
  },
};
