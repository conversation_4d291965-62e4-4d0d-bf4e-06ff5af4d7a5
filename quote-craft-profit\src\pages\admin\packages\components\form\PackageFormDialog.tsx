import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useQuery } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  showSuccess,
  showError,
  showLoading,
  dismissToast,
} from "@/lib/notifications";
import {
  getPackageById,
  savePackage,
} from "../../../../../services/admin/packages/packageService";
import { getAllCategories } from "@/services/admin/categories";
import { getAllDivisions } from "@/services/admin/divisions";
import { getAllCities } from "@/services/shared/entities/cities";
import { getAllCurrencies } from "@/services/shared/entities/currencies";
import { PackageDetailsForm } from "./PackageDetailsForm";
import { PackageFormProps, PackageFormValues } from "../../types";
import { Package } from "../../types/package";
import { QuantityBasisEnum } from "@/types/types";

// Define validation schema
const packageFormSchema = z
  .object({
    name: z.string().min(2, "Package name must be at least 2 characters"),
    description: z.string().optional(),
    categoryId: z.string().min(1, "Category is required"),
    divisionId: z.string().min(1, "Division is required"),
    cityIds: z.array(z.string()).min(1, "At least one city must be selected"),
    venueIds: z.array(z.string()).optional(),
    enableVenues: z.boolean().optional(),
    quantityBasis: z.nativeEnum(QuantityBasisEnum, {
      required_error: "Quantity basis is required",
    }),
    isActive: z.boolean(),
    price: z
      .string()
      .min(1, "Price is required")
      .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
        message: "Price must be a valid number greater than 0",
      }),
    unitBaseCost: z
      .string()
      .min(1, "Base cost is required")
      .refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
        message: "Base cost must be a valid number",
      }),
    currencyId: z.string().uuid("Currency ID must be a valid UUID"),
  })
  .refine(
    (data) => {
      // If venues are enabled, at least one venue must be selected
      if (data.enableVenues && (!data.venueIds || data.venueIds.length === 0)) {
        return false;
      }
      return true;
    },
    {
      message:
        "At least one venue must be selected when venue restrictions are enabled",
      path: ["venueIds"],
    }
  );

export const PackageFormDialog: React.FC<PackageFormProps> = ({
  isOpen,
  onClose,
  packageId,
}) => {
  const isEditMode = !!packageId;

  const form = useForm<PackageFormValues>({
    resolver: zodResolver(packageFormSchema),
    defaultValues: {
      name: "",
      description: "",
      categoryId: "",
      divisionId: "",
      cityIds: [],
      venueIds: [],
      enableVenues: false,
      quantityBasis: QuantityBasisEnum.PER_EVENT,
      isActive: true,
      price: "",
      unitBaseCost: "",
      currencyId: "",
    },
  });

  // Fetch categories
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ["categories"],
    queryFn: getAllCategories,
    enabled: isOpen,
    meta: {
      onError: () => {
        showError("Failed to load categories");
      },
    },
  });

  // Fetch divisions
  const { data: divisions = [], isLoading: isLoadingDivisions } = useQuery({
    queryKey: ["divisions"],
    queryFn: () => getAllDivisions(),
    enabled: isOpen,
    meta: {
      onError: () => {
        showError("Failed to load divisions");
      },
    },
  });

  // Fetch cities
  const { data: cities = [], isLoading: isLoadingCities } = useQuery({
    queryKey: ["cities"],
    queryFn: getAllCities,
    enabled: isOpen,
    meta: {
      onError: () => {
        showError("Failed to load cities");
      },
    },
  });

  // Fetch currencies
  const { data: currencies = [], isLoading: isLoadingCurrencies } = useQuery({
    queryKey: ["currencies"],
    queryFn: getAllCurrencies,
    enabled: isOpen,
    meta: {
      onError: () => {
        showError("Failed to load currencies");
      },
    },
  });

  // Fetch package details if editing
  const { data: packageDetails, isLoading: isLoadingPackage } = useQuery({
    queryKey: ["package", packageId],
    queryFn: async () => {
      if (packageId) {
        console.log("Fetching package details for ID:", packageId);
        const data = await getPackageById(packageId);
        console.log("Package details fetched:", data);
        return data;
      }
      return null;
    },
    enabled: !!packageId && isOpen,
    meta: {
      onSuccess: (data: Package | null) => {
        if (data) {
          console.log("Setting form data from package:", data);

          // Force a timeout to ensure the form is ready to receive values
          setTimeout(() => {
            form.reset({
              name: data.name,
              description: data.description || "",
              categoryId: data.categoryId || "",
              divisionId: data.divisionId || "",
              cityIds: data.cityIds || [],
              venueIds: data.venueIds || [],
              enableVenues: data.venueIds && data.venueIds.length > 0,
              quantityBasis: data.quantityBasis || QuantityBasisEnum.PER_EVENT,
              isActive: !data.isDeleted, // Convert from isDeleted to isActive
              price: data.price || "",
              unitBaseCost: data.unitBaseCost || "",
              currencyId: data.currencyId || "",
            });

            // Manually set values for select fields to ensure they update
            if (data.categoryId) form.setValue("categoryId", data.categoryId);
            if (data.divisionId) form.setValue("divisionId", data.divisionId);
            if (data.quantityBasis)
              form.setValue("quantityBasis", data.quantityBasis);
            if (data.cityIds && data.cityIds.length > 0)
              form.setValue("cityIds", data.cityIds);
            if (data.venueIds && data.venueIds.length > 0)
              form.setValue("venueIds", data.venueIds);

            // Show success message
            showSuccess("Package data loaded successfully");
          }, 100);
        }
      },
      onError: (error: Error) => {
        console.error("Error loading package details:", error);
        showError("Failed to load package details");
        onClose();
      },
    },
  });

  const handleSubmit = async (values: PackageFormValues) => {
    try {
      // Show loading toast
      const loadingToastId = showLoading(
        `${isEditMode ? "Updating" : "Creating"} package...`
      );

      // Validate form data
      console.log("Submitting form with values:", values);

      await savePackage({
        id: packageId || undefined,
        name: values.name,
        description: values.description,
        categoryId: values.categoryId || undefined,
        divisionId: values.divisionId || undefined,
        cityIds: values.cityIds || [],
        venueIds: values.enableVenues ? values.venueIds || [] : [],
        quantityBasis: values.quantityBasis,
        isDeleted: !values.isActive, // Convert from isActive to isDeleted
        price: values.price ? parseFloat(values.price) : undefined,
        unitBaseCost: values.unitBaseCost
          ? parseFloat(values.unitBaseCost)
          : undefined,
        currencyId: values.currencyId || undefined,
      });

      // Dismiss loading toast and show success toast
      dismissToast(loadingToastId);
      showSuccess(
        `Package ${isEditMode ? "updated" : "created"} successfully`,
        {
          category: "package",
          description: `${formData.name} has been ${
            isEditMode ? "updated" : "added to your package catalog"
          }.`,
        }
      );

      // Close dialog and refresh the package list
      onClose(true);
    } catch (error) {
      // Show detailed error message
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      showError(`Failed to ${isEditMode ? "update" : "create"} package`, {
        category: "package",
        description: errorMessage,
      });
      console.error("Error in handleSubmit:", error);

      // Highlight form errors if validation failed
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          const fieldName = err.path.join(".");
          form.setError(fieldName as keyof PackageFormValues, {
            type: "manual",
            message: err.message,
          });
        });
      }
    }
  };

  // Reset form when dialog opens or packageId changes
  useEffect(() => {
    if (isOpen && !packageId && currencies.length > 0) {
      // Get default currency (first one, which should be IDR)
      const defaultCurrencyId = currencies[0].id;

      // Reset form with default values
      form.reset({
        name: "",
        description: "",
        categoryId: "",
        divisionId: "",
        cityIds: [],
        venueIds: [],
        enableVenues: false,
        quantityBasis: QuantityBasisEnum.PER_EVENT,
        isActive: true,
        price: "",
        unitBaseCost: "",
        currencyId: defaultCurrencyId,
      });

      // Ensure the quantityBasis select has a default value
      form.setValue("quantityBasis", QuantityBasisEnum.PER_EVENT);

      // Set default currency
      form.setValue("currencyId", defaultCurrencyId);
    }
  }, [isOpen, packageId, form, currencies]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit Package" : "Add New Package"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Update the details of this service package."
              : "Create a new service package that can be offered to clients."}
          </DialogDescription>
        </DialogHeader>

        {isEditMode && isLoadingPackage ? (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
            <p className="text-muted-foreground">Loading package data...</p>
            <p className="text-xs text-muted-foreground mt-2">
              Please wait while we fetch the package details...
            </p>
          </div>
        ) : (
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            {isEditMode && !packageDetails ? (
              <div className="p-4 border border-red-200 bg-red-50 rounded-md">
                <p className="text-red-600 font-medium">
                  Error loading package data
                </p>
                <p className="text-sm text-red-500 mt-1">
                  The package data could not be loaded. Please try again or
                  contact support.
                </p>
                <Button
                  variant="outline"
                  className="mt-3"
                  onClick={() => onClose()}
                >
                  Close
                </Button>
              </div>
            ) : (
              <>
                <PackageDetailsForm
                  form={form}
                  categories={categories.map((cat) => ({
                    id: cat.id,
                    name: cat.name,
                    code: cat.code || "",
                    description: cat.description || "",
                    icon: cat.icon,
                    created_at: cat.created_at,
                    updated_at: cat.updated_at,
                    display_order: cat.display_order || 0,
                  }))}
                  divisions={divisions}
                  cities={cities}
                  currencies={currencies}
                  isLoading={
                    isLoadingCategories ||
                    isLoadingDivisions ||
                    isLoadingCities ||
                    isLoadingCurrencies
                  }
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onClose()}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      isLoadingPackage ||
                      isLoadingCategories ||
                      isLoadingDivisions ||
                      isLoadingCities ||
                      isLoadingCurrencies
                    }
                  >
                    {isEditMode ? "Update Package" : "Create Package"}
                  </Button>
                </DialogFooter>
              </>
            )}
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};
