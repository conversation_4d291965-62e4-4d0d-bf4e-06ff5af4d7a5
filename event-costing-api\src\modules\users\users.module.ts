import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { ProfileController } from './profile.controller';
import { AuthModule } from '../auth/auth.module';
import { StorageModule } from '../../core/storage/storage.module';

@Module({
  imports: [
    AuthModule, // Importing AuthModule makes AuthService available if needed, guards are usually handled separately
    StorageModule, // Import StorageModule for profile picture uploads
  ],
  controllers: [UsersController, ProfileController],
  providers: [UsersService],
  exports: [UsersService], // Export if needed by other modules
})
export class UsersModule {}
