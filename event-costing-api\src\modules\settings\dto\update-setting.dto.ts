import { <PERSON>NotEmpty, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, IsJSO<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateSettingDto {
  @ApiProperty({
    description: 'The new value for the setting (must be valid JSON)',
    example: '{ "name": "My Updated Event Co", "version": 1.3 }',
    type: String, // Input is expected as a JSON string
  })
  @IsNotEmpty()
  @IsJSON() // Ensures the input string is valid JSON
  value: string; // Receive value as a string

  @ApiPropertyOptional({
    description: 'Optional updated description for the setting',
    example: 'The main configuration object for the site.',
  })
  @IsOptional()
  @IsString()
  description?: string;
}
