import { IsOptional, IsEnum, IsUUID } from 'class-validator';
import { CalculationStatus } from '../enums/calculation-status.enum';
import { PaginationQueryDto } from '../../../shared/dtos/pagination-query.dto';

export class ListCalculationsDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(CalculationStatus)
  status?: CalculationStatus;

  @IsOptional()
  @IsUUID()
  clientId?: string;

  // Add other potential filter fields as needed
  // @IsOptional()
  // @IsString()
  // searchTerm?: string;
}
