/**
 * Export all calculation services
 *
 * This is the single source of truth for all calculation-related services.
 * All components and hooks should import from this file.
 */

// Export from new organized structure with explicit exports to avoid conflicts
export {
  getAllCalculations,
  getCalculationById,
  getCalculationSummary,
  createCalculation,
  updateCalculation,
  deleteCalculation,
  formatCurrency,
} from "./core";

export {
  getCalculationLineItems,
  getLineItemOptions,
  getLineItemById,
  addLineItem,
  removeLineItem,
  recalculateCalculationTotals,
  getCalculationLineItemsFromApi,
  addLineItemFromApi,
  recalculateCalculationTotalsFromApi,
  // Add missing exports
  addLineItemWithSupabase,
  recalculateTotalsWithSupabase,
  getLineItemOptionsFromSupabase,
} from "./line-items";

export {
  calculateTemplateTotal,
  calculateTemplateFromObject,
  getTemplateCalculationSummary,
  createCalculationFromTemplate,
  getCalculationSuggestions,
  validateTemplateCustomization,
  formatCurrency as formatCurrencyTemplate,
} from "./templates";

// Export missing functions that are needed by components
export { getCalculationLineItems as getCalculationLineItemsSupabase } from "./line-items";

export { getCalculationById as getCalculationByIdFromSupabase } from "./core";

// These are already exported above

// Export package service functions (removed incorrect import)

// Export calculation package service with renamed types to avoid conflicts
export {
  getPackagesByCategoryForCalculation,
  getBatchPackageOptions,
  type Package as CalculationPackage,
  type CategoryWithPackages as CalculationCategoryWithPackages,
  type PackagesByCategoryResponse,
  type BatchPackageOptionsResponse,
} from "./calculationPackageService";
export type { PackageOption as CalculationPackageOption } from "./calculationPackageService";
