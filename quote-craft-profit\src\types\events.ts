/**
 * Event types for the external API
 */

import {
  formatDateTimeForSubmission,
  DEFAULT_TIMEZONE,
} from "@/lib/timezone-utils";

// Event status enum from Supabase
export type EventStatus =
  | "LEAD"
  | "PLANNING"
  | "CONFIRMED"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "POST_EVENT"
  | "CANCELLED"
  | "ON_HOLD";

// API Event interface for creating/updating events
export interface EventRequest {
  event_name: string;
  client_id?: string; // Optional UUID field
  event_start_datetime: string; // ISO date string
  event_end_datetime: string; // ISO date string
  status: EventStatus;
  venue_details?: string;
  primary_contact_id?: string; // Optional UUID field
  notes?: string;
}

// API Event response interface
export interface EventResponse {
  id: string;
  event_name: string;
  event_start_datetime: string; // ISO date string
  event_end_datetime: string; // ISO date string
  location?: string;
  notes?: string;
  created_at: string; // ISO date string
  status?: EventStatus;
  client_id?: string;
  client_name?: string;
  primary_contact_id?: string;
  primary_contact_name?: string;
  venue_details?: string;
}

// Transformed Event interface for frontend use
export interface Event {
  id: string;
  name: string;
  clientId: string;
  clientName: string;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  location: string;
  status: string;
  primaryContactId?: string;
  primaryContactName?: string;
  notes?: string;
  createdAt: string;
}

// Event form data interface (for date range picker)
export interface EventFormData {
  name: string;
  clientId: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  location?: string;
  status: string;
  primaryContactId?: string;
  notes?: string;
}

// Function to transform API response to frontend Event
export const transformApiEvent = (apiEvent: EventResponse): Event => {
  return {
    id: apiEvent.id,
    name: apiEvent.event_name,
    clientId: apiEvent.client_id || "no-client-id", // Ensure clientId is never empty
    clientName: apiEvent.client_name || "No Client", // Ensure clientName is never empty
    startDate: apiEvent.event_start_datetime,
    endDate: apiEvent.event_end_datetime,
    location: apiEvent.venue_details || apiEvent.location || "No Location", // Ensure location is never empty
    status: apiEvent.status?.toLowerCase() || "draft", // Ensure status is never empty
    primaryContactId: apiEvent.primary_contact_id || undefined, // Keep as undefined if not provided
    primaryContactName: apiEvent.primary_contact_name || "No Contact", // Ensure primaryContactName is never empty
    notes: apiEvent.notes,
    createdAt: apiEvent.created_at,
  };
};

// Function to transform frontend Event to API request (legacy)
export const transformEventToApiRequest = (
  event: Partial<Event>,
  timezone: string = DEFAULT_TIMEZONE
): EventRequest => {
  return {
    event_name: event.name || "",
    // Convert empty string to undefined for UUID validation
    client_id:
      event.clientId && event.clientId.trim() !== ""
        ? event.clientId
        : undefined,
    event_start_datetime: event.startDate
      ? formatDateTimeForSubmission(new Date(event.startDate), timezone)
      : formatDateTimeForSubmission(new Date(), timezone),
    event_end_datetime: event.endDate
      ? formatDateTimeForSubmission(new Date(event.endDate), timezone)
      : formatDateTimeForSubmission(new Date(), timezone),
    status: (event.status?.toUpperCase() as EventStatus) || "PLANNING",
    venue_details: event.location,
    // Convert empty string to undefined for UUID validation
    primary_contact_id:
      event.primaryContactId && event.primaryContactId.trim() !== ""
        ? event.primaryContactId
        : undefined,
    notes: event.notes,
  };
};

// Function to transform EventFormData (with date range) to API request
export const transformEventFormDataToApiRequest = (
  eventData: EventFormData,
  timezone: string = DEFAULT_TIMEZONE
): EventRequest => {
  return {
    event_name: eventData.name,
    // Convert empty string to undefined for UUID validation
    client_id:
      eventData.clientId && eventData.clientId.trim() !== ""
        ? eventData.clientId
        : undefined,
    event_start_datetime: formatDateTimeForSubmission(
      eventData.dateRange.from,
      timezone
    ),
    event_end_datetime: formatDateTimeForSubmission(
      eventData.dateRange.to,
      timezone
    ),
    status: (eventData.status?.toUpperCase() as EventStatus) || "PLANNING",
    venue_details: eventData.location,
    // Convert empty string to undefined for UUID validation
    primary_contact_id:
      eventData.primaryContactId && eventData.primaryContactId.trim() !== ""
        ? eventData.primaryContactId
        : undefined,
    notes: eventData.notes,
  };
};

// Function to transform Event to EventFormData (for editing)
export const transformEventToFormData = (event: Event): EventFormData => {
  return {
    name: event.name,
    clientId: event.clientId,
    dateRange: {
      from: new Date(event.startDate),
      to: new Date(event.endDate),
    },
    location: event.location,
    status: event.status,
    primaryContactId: event.primaryContactId,
    notes: event.notes,
  };
};
