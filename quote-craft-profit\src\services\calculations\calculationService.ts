/**
 * Legacy Calculation Service
 * PHASE 5 CLEANUP: Simplified backward compatibility layer
 *
 * This service provides backward compatibility by re-exporting functions
 * from the new consolidated service structure.
 *
 * @deprecated Use specific services from calculationDataService, calculationMutationService, or templates/ instead
 * This file will be removed in a future version. Migrate to direct imports from consolidated services.
 */

// Re-export data functions from consolidated data service
export {
  getAllCalculations,
  getCalculationById,
  getCalculationSummary,
  createCalculation,
  getPackagesByCategory,
} from "./calculationDataService";

// Re-export mutation functions from consolidated mutation service
export {
  addLineItem,
  recalculateCalculationTotals as recalculateTotals,
  updateCalculation,
  deleteCalculation,
} from "./calculationMutationService";

// Re-export line item read functions from line-items service
export {
  getCalculationLineItems,
  getLineItemOptions,
  getLineItemById,
} from "./line-items";

// Re-export template functions
export { createCalculationFromTemplate } from "./templates";

// PHASE 5 CLEANUP: Removed deprecated stub functions that threw errors
// Use the appropriate services directly instead
