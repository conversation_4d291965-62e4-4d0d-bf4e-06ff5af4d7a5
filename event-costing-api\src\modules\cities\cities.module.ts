import { Module } from '@nestjs/common';
import { CitiesService } from './cities.service';
import { CitiesController } from './cities.controller';
import { AdminCitiesController } from './admin-cities.controller';
import { AuthModule } from '../auth/auth.module';
import { AdminModule } from '../auth/admin.module';
// Assuming SupabaseModule is global

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [CitiesController, AdminCitiesController],
  providers: [CitiesService],
  exports: [CitiesService], // Export CitiesService so it can be used in other modules
})
export class CitiesModule {}
