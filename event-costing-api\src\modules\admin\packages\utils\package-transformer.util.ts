import { PackageDto } from '../dto/package.dto';

/**
 * Utility class for transforming package data
 */
export class PackageTransformerUtil {
  /**
   * Transform packages with related data
   * @param packages - Raw package data
   * @param categoriesMap - Map of category ID to category name
   * @param divisionsMap - Map of division ID to division name
   * @param pricesMap - Map of package ID to price info
   * @param citiesMap - Map of package ID to city names array
   * @returns Transformed packages with related data
   */
  static transformPackagesWithRelations(
    packages: any[],
    categoriesMap: Map<string, string>,
    divisionsMap: Map<string, string>,
    pricesMap: Map<string, any>,
    citiesMap: Map<string, string[]>,
  ): PackageDto[] {
    return packages.map(pkg => {
      const priceInfo = pricesMap.get(pkg.id) || {
        price: null,
        unitBaseCost: null,
        currencySymbol: 'Rp',
        hasPricing: false,
      };

      const cities = citiesMap.get(pkg.id) || [];

      return {
        id: pkg.id,
        name: pkg.name,
        description: pkg.description,
        category_id: pkg.category_id,
        categoryName: categoriesMap.get(pkg.category_id) || undefined,
        division_id: pkg.division_id,
        divisionName: divisionsMap.get(pkg.division_id) || undefined,
        variation_group_code: pkg.variation_group_code,
        seq_number: pkg.seq_number || 0,
        quantity_basis: pkg.quantity_basis,
        created_at: pkg.created_at,
        updated_at: pkg.updated_at,
        created_by: pkg.created_by || '',
        updated_by: pkg.updated_by,
        is_deleted: pkg.is_deleted,
        cityNames: cities,
        price: priceInfo.price,
        unitBaseCost: priceInfo.unitBaseCost,
        currencySymbol: priceInfo.currencySymbol,
        hasPricing: priceInfo.hasPricing,
      };
    });
  }

  /**
   * Transform a single package with related data
   * @param pkg - Raw package data
   * @param categoryName - Category name
   * @param divisionName - Division name
   * @param priceInfo - Price information
   * @param cities - Array of city names
   * @returns Transformed package
   */
  static transformSinglePackageWithRelations(
    pkg: any,
    categoryName?: string,
    divisionName?: string,
    priceInfo?: any,
    cities?: string[],
  ): PackageDto {
    const defaultPriceInfo = {
      price: null,
      unitBaseCost: null,
      currencySymbol: 'Rp',
      hasPricing: false,
    };

    const finalPriceInfo = priceInfo || defaultPriceInfo;

    return {
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      category_id: pkg.category_id,
      categoryName: categoryName || undefined,
      division_id: pkg.division_id,
      divisionName: divisionName || undefined,
      variation_group_code: pkg.variation_group_code,
      seq_number: pkg.seq_number || 0,
      quantity_basis: pkg.quantity_basis,
      created_at: pkg.created_at,
      updated_at: pkg.updated_at,
      created_by: pkg.created_by || '',
      updated_by: pkg.updated_by,
      is_deleted: pkg.is_deleted,
      cityNames: cities || [],
      price: finalPriceInfo.price,
      unitBaseCost: finalPriceInfo.unitBaseCost,
      currencySymbol: finalPriceInfo.currencySymbol,
      hasPricing: finalPriceInfo.hasPricing,
    };
  }

  /**
   * Extract unique IDs from packages
   * @param packages - Array of packages
   * @param field - Field name to extract IDs from
   * @returns Array of unique IDs
   */
  static extractUniqueIds(packages: any[], field: string): string[] {
    return [
      ...new Set(
        packages
          .filter(pkg => pkg[field])
          .map(pkg => pkg[field]),
      ),
    ];
  }

  /**
   * Group packages by a specific field
   * @param packages - Array of packages
   * @param field - Field to group by
   * @returns Map of field value to packages array
   */
  static groupPackagesByField(packages: any[], field: string): Map<string, any[]> {
    const grouped = new Map<string, any[]>();

    packages.forEach(pkg => {
      const fieldValue = pkg[field];
      if (fieldValue) {
        if (!grouped.has(fieldValue)) {
          grouped.set(fieldValue, []);
        }
        grouped.get(fieldValue)!.push(pkg);
      }
    });

    return grouped;
  }

  /**
   * Filter packages by criteria
   * @param packages - Array of packages
   * @param criteria - Filter criteria
   * @returns Filtered packages
   */
  static filterPackages(
    packages: any[],
    criteria: {
      isDeleted?: boolean;
      categoryId?: string;
      divisionId?: string;
      hasName?: string;
    },
  ): any[] {
    return packages.filter(pkg => {
      if (criteria.isDeleted !== undefined && pkg.is_deleted !== criteria.isDeleted) {
        return false;
      }

      if (criteria.categoryId && pkg.category_id !== criteria.categoryId) {
        return false;
      }

      if (criteria.divisionId && pkg.division_id !== criteria.divisionId) {
        return false;
      }

      if (criteria.hasName && !pkg.name?.toLowerCase().includes(criteria.hasName.toLowerCase())) {
        return false;
      }

      return true;
    });
  }

  /**
   * Sort packages by field
   * @param packages - Array of packages
   * @param field - Field to sort by
   * @param order - Sort order ('asc' or 'desc')
   * @returns Sorted packages
   */
  static sortPackages(
    packages: any[],
    field: string,
    order: 'asc' | 'desc' = 'asc',
  ): any[] {
    return [...packages].sort((a, b) => {
      const aValue = a[field];
      const bValue = b[field];

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      let comparison = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }

      return order === 'desc' ? -comparison : comparison;
    });
  }

  /**
   * Paginate packages
   * @param packages - Array of packages
   * @param offset - Starting index
   * @param limit - Number of items per page
   * @returns Paginated packages with metadata
   */
  static paginatePackages(
    packages: any[],
    offset: number = 0,
    limit: number = 20,
  ): {
    data: any[];
    count: number;
    offset: number;
    limit: number;
    hasMore: boolean;
  } {
    const total = packages.length;
    const data = packages.slice(offset, offset + limit);

    return {
      data,
      count: total,
      offset,
      limit,
      hasMore: offset + limit < total,
    };
  }

  /**
   * Validate package data structure
   * @param pkg - Package data to validate
   * @returns Validation result
   */
  static validatePackageStructure(pkg: any): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!pkg.id) {
      errors.push('Package ID is required');
    }

    if (!pkg.name || typeof pkg.name !== 'string') {
      errors.push('Package name is required and must be a string');
    }

    if (pkg.category_id && typeof pkg.category_id !== 'string') {
      errors.push('Category ID must be a string');
    }

    if (pkg.division_id && typeof pkg.division_id !== 'string') {
      errors.push('Division ID must be a string');
    }

    if (pkg.quantity_basis && typeof pkg.quantity_basis !== 'string') {
      errors.push('Quantity basis must be a string');
    }

    if (pkg.is_deleted !== undefined && typeof pkg.is_deleted !== 'boolean') {
      errors.push('is_deleted must be a boolean');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Clean package data by removing null/undefined values
   * @param pkg - Package data to clean
   * @returns Cleaned package data
   */
  static cleanPackageData(pkg: any): any {
    const cleaned: any = {};

    Object.keys(pkg).forEach(key => {
      const value = pkg[key];
      if (value !== null && value !== undefined) {
        cleaned[key] = value;
      }
    });

    return cleaned;
  }
}
