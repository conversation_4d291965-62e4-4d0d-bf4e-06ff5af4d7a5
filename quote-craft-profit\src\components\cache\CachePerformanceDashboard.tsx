/**
 * Cache Performance Dashboard Component
 * 
 * PHASE 2: Comprehensive cache performance monitoring dashboard
 * Displays real-time cache metrics, trends, and optimization recommendations
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCacheAnalytics } from '@/hooks/useCacheAnalytics';
import { 
  Activity, 
  Database, 
  TrendingUp, 
  Zap, 
  RefreshCw, 
  HardDrive,
  Network,
  Brain,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface CachePerformanceDashboardProps {
  enabled?: boolean;
  refreshInterval?: number;
}

export const CachePerformanceDashboard: React.FC<CachePerformanceDashboardProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  refreshInterval = 30000, // 30 seconds
}) => {
  const { metrics, health, recommendations, clearAnalytics, exportMetrics } = useCacheAnalytics({
    enabled,
    updateInterval: refreshInterval,
  });

  const [isVisible, setIsVisible] = useState(false);

  if (!enabled) {
    return null;
  }

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'good': return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'fair': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'poor': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Toggle Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(!isVisible)}
        className="mb-2 shadow-lg"
      >
        <Activity className="h-4 w-4 mr-2" />
        Cache Analytics
      </Button>

      {/* Dashboard Panel */}
      {isVisible && (
        <Card className="w-96 max-h-96 overflow-y-auto shadow-xl">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <Database className="h-5 w-5" />
                Cache Performance
              </CardTitle>
              <div className="flex items-center gap-2">
                {getHealthIcon(health)}
                <Badge variant="outline" className={getHealthColor(health)}>
                  {health}
                </Badge>
              </div>
            </div>
            <CardDescription>
              Real-time cache performance monitoring
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
                <TabsTrigger value="actions">Actions</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-3">
                {/* Hit Rate */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Hit Rate</span>
                    <span className="font-medium">{metrics.hitRate}%</span>
                  </div>
                  <Progress value={metrics.hitRate} className="h-2" />
                </div>

                {/* Query Stats */}
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-blue-500" />
                    <div>
                      <div className="font-medium">{metrics.totalQueries}</div>
                      <div className="text-xs text-muted-foreground">Total Queries</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <div>
                      <div className="font-medium">{metrics.staleQueries}</div>
                      <div className="text-xs text-muted-foreground">Stale Queries</div>
                    </div>
                  </div>
                </div>

                {/* Memory Usage */}
                <div className="flex justify-between items-center text-sm">
                  <span className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4" />
                    Memory
                  </span>
                  <span className="font-medium">{metrics.memoryEstimate}</span>
                </div>

                {/* Error Rate */}
                {metrics.errorQueries > 0 && (
                  <div className="flex justify-between items-center text-sm text-red-600">
                    <span className="flex items-center gap-2">
                      <XCircle className="h-4 w-4" />
                      Errors
                    </span>
                    <span className="font-medium">{metrics.errorQueries}</span>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="advanced" className="space-y-3">
                {/* Background Updates */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <RefreshCw className="h-4 w-4" />
                    Background Updates
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>Active: {metrics.backgroundUpdates?.activeUpdates || 0}</div>
                    <div>Queued: {metrics.backgroundUpdates?.queuedUpdates || 0}</div>
                    <div>Success: {metrics.backgroundUpdates?.successfulUpdates || 0}</div>
                    <div>Failed: {metrics.backgroundUpdates?.failedUpdates || 0}</div>
                  </div>
                </div>

                {/* Predictive Warming */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Brain className="h-4 w-4" />
                    Predictive Warming
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>Patterns: {metrics.predictiveWarming?.totalPatterns || 0}</div>
                    <div>Sessions: {metrics.predictiveWarming?.totalSessions || 0}</div>
                  </div>
                </div>

                {/* Request Deduplication */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Network className="h-4 w-4" />
                    Request Deduplication
                  </div>
                  <div className="text-xs">
                    Pending: {metrics.requestDeduplication?.pendingCount || 0}
                  </div>
                </div>

                {/* Cache Persistence */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <HardDrive className="h-4 w-4" />
                    Cache Persistence
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>Stored: {metrics.persistence?.totalQueries || 0}</div>
                    <div>Usage: {metrics.persistence?.storageUsage?.toFixed(1) || 0}%</div>
                  </div>
                  {metrics.persistence?.storageUsage && (
                    <Progress value={metrics.persistence.storageUsage} className="h-1" />
                  )}
                </div>
              </TabsContent>

              <TabsContent value="actions" className="space-y-3">
                {/* Recommendations */}
                {recommendations.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Recommendations</div>
                    <div className="space-y-1">
                      {recommendations.slice(0, 3).map((rec, index) => (
                        <div key={index} className="text-xs p-2 bg-yellow-50 rounded border-l-2 border-yellow-400">
                          {rec}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAnalytics}
                    className="w-full text-xs"
                  >
                    Clear Analytics
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportMetrics}
                    className="w-full text-xs"
                  >
                    Export Metrics
                  </Button>
                </div>

                {/* Top Query Breakdown */}
                {Object.keys(metrics.queryBreakdown).length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Top Queries</div>
                    <div className="space-y-1">
                      {Object.entries(metrics.queryBreakdown)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 3)
                        .map(([feature, count]) => (
                          <div key={feature} className="flex justify-between text-xs">
                            <span className="truncate">{feature}</span>
                            <span className="font-medium">{count}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CachePerformanceDashboard;
