import React from "react";
import { StatusActions } from "./StatusActions";
import { DeleteAction } from "./DeleteAction";

interface CalculationActionsProps {
  calculationId?: string;
  status?: "draft" | "completed" | "canceled";
  isProcessing: boolean;
  isConfirmingDelete: boolean;
  onSaveDraft: () => void;
  onComplete: () => void;
  onStartDelete: () => void;
  onCancelDelete: () => void;
  onConfirmDelete: () => void;
}

/**
 * Calculation actions component
 * Combines status actions and delete actions in a proper layout
 */
export const CalculationActions: React.FC<CalculationActionsProps> = ({
  calculationId,
  status,
  isProcessing,
  isConfirmingDelete,
  onSaveDraft,
  onComplete,
  onStartDelete,
  onCancelDelete,
  onConfirmDelete,
}) => {
  if (!calculationId) {
    return null;
  }

  return (
    <div className="flex justify-between pt-4 border-t dark:border-gray-700">
      <DeleteAction
        isConfirmingDelete={isConfirmingDelete}
        isProcessing={isProcessing}
        onStartDelete={onStartDelete}
        onCancelDelete={onCancelDelete}
        onConfirmDelete={onConfirmDelete}
      />

      <StatusActions
        status={status}
        isProcessing={isProcessing}
        onSaveDraft={onSaveDraft}
        onComplete={onComplete}
      />
    </div>
  );
};
