import React from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteClient } from "@/services/shared/entities/clients";
import { Client } from "@/types/types";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { showSuccess, showError } from "@/lib/notifications";

interface DeleteClientDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  client: Client | null;
}

const DeleteClientDialog: React.FC<DeleteClientDialogProps> = ({
  open,
  onOpenChange,
  client,
}) => {
  const queryClient = useQueryClient();

  // Delete client mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteClient(id),
    onSuccess: () => {
      showSuccess("Client deleted successfully", {
        category: "client",
        description: `${
          client?.client_name || "Client"
        } has been removed from your client list.`,
      });
      // Invalidate both the clients list and the specific client
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      if (client?.id) {
        queryClient.invalidateQueries({ queryKey: ["client", client.id] });
      }
      onOpenChange(false);
    },
    onError: (error) => {
      showError("Failed to delete client", {
        category: "client",
        description:
          "Please try again or contact support if the problem persists.",
      });
      console.error("Error deleting client:", error);
    },
  });

  const handleDelete = () => {
    if (client?.id) {
      deleteMutation.mutate(client.id);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent aria-describedby="delete-client-description">
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription id="delete-client-description">
            This action will permanently delete the client{" "}
            <span className="font-semibold">{client?.name}</span>. This action
            cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteMutation.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="bg-red-600 hover:bg-red-700"
          >
            {deleteMutation.isPending ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteClientDialog;
