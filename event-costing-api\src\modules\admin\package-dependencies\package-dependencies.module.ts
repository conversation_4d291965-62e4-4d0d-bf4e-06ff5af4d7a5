import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/auth.module';
import { AdminModule } from '../../auth/admin.module';
import { PackageDependenciesController } from './package-dependencies.controller';
import { PackageDependenciesService } from './package-dependencies.service';

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [PackageDependenciesController],
  providers: [PackageDependenciesService],
})
export class PackageDependenciesModule {}
