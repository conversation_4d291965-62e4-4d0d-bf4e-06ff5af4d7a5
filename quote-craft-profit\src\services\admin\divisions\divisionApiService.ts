/**
 * Division API Service
 *
 * This service provides methods for interacting with the divisions API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import { Division } from '@/types/types';
import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';

export interface CreateDivisionRequest {
  name: string;
  code: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdateDivisionRequest {
  name?: string;
  code?: string;
  description?: string;
  is_active?: boolean;
}

/**
 * Get all divisions from the backend API
 * @param active - Optional filter for active status
 * @returns Promise resolving to an array of divisions
 */
export const getAllDivisionsFromApi = async (active?: boolean): Promise<Division[]> => {
  try {
    console.log('Fetching divisions from backend API');

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (active !== undefined) {
      queryParams.append('active', active.toString());
    }

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(
      `${API_ENDPOINTS.DIVISIONS.GET_ALL}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    );

    console.log('Divisions fetched successfully from backend API');

    // Transform the data to match the expected format
    return response.data.map((division: any) => ({
      id: division.id,
      name: division.name,
      code: division.code,
      description: division.description || "",
      is_active: division.is_active,
      created_at: division.created_at,
      updated_at: division.updated_at
    }));
  } catch (error) {
    console.error('Error fetching divisions from backend API:', error);
    throw error;
  }
};

/**
 * Get a division by ID from the backend API
 * @param id - The division ID
 * @returns Promise resolving to a division
 */
export const getDivisionByIdFromApi = async (id: string): Promise<Division> => {
  try {
    console.log(`Fetching division with ID ${id} from backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.DIVISIONS.GET_BY_ID(id));

    console.log('Division fetched successfully from backend API');

    // Transform the data to match the expected format
    const division = response.data;
    return {
      id: division.id,
      name: division.name,
      code: division.code,
      description: division.description || "",
      is_active: division.is_active,
      created_at: division.created_at,
      updated_at: division.updated_at
    };
  } catch (error) {
    console.error(`Error fetching division with ID ${id} from backend API:`, error);
    throw error;
  }
};

/**
 * Create a new division
 * @param data - The division data to create
 * @returns Promise resolving to the created division
 */
export const createDivisionFromApi = async (data: CreateDivisionRequest): Promise<Division> => {
  try {
    console.log('Creating new division:', data);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.post(API_ENDPOINTS.DIVISIONS.CREATE, data);

    console.log('Division created successfully');
    return response.data;
  } catch (error) {
    console.error('Error creating division:', error);
    throw error;
  }
};

/**
 * Update a division
 * @param id - The division ID
 * @param data - The division data to update
 * @returns Promise resolving to the updated division
 */
export const updateDivisionFromApi = async (
  id: string,
  data: UpdateDivisionRequest
): Promise<Division> => {
  try {
    console.log(`Updating division with ID ${id}:`, data);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.put(API_ENDPOINTS.DIVISIONS.UPDATE(id), data);

    console.log('Division updated successfully');
    return response.data;
  } catch (error) {
    console.error(`Error updating division with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a division
 * @param id - The division ID
 * @returns Promise resolving when the division is deleted
 */
export const deleteDivisionFromApi = async (id: string): Promise<void> => {
  try {
    console.log(`Deleting division with ID ${id}`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.delete(API_ENDPOINTS.DIVISIONS.DELETE(id));

    console.log('Division deleted successfully');
  } catch (error) {
    console.error(`Error deleting division with ID ${id}:`, error);
    throw error;
  }
};
