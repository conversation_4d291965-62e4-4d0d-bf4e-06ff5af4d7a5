import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useQuery } from "@tanstack/react-query";
import { PlusCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { CurrencyInput } from "@/components/ui/currency-input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLineItemMutations } from "../../hooks/data/useLineItemMutations";
import { getAllCategories } from "@/services/admin/categories";
import { LineItemInput } from "@/types/calculation";

// Form schema for custom line item
const customLineItemSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
  unit_price: z.coerce.number().min(0, "Unit price cannot be negative"),
  category_id: z.string().min(1, "Category is required"),
});

type CustomLineItemFormValues = z.infer<typeof customLineItemSchema>;

interface AddCustomLineItemFormProps {
  calculationId: string;
  onSuccess?: () => void;
}

/**
 * Form component for adding custom line items with optimistic updates
 */
export function AddCustomLineItemForm({
  calculationId,
  onSuccess,
}: AddCustomLineItemFormProps) {
  // Get categories for the dropdown
  const { data: categories = [] } = useQuery({
    queryKey: ["categories"],
    queryFn: getAllCategories,
  });

  // Get line item mutations
  const { addLineItem, isProcessing } = useLineItemMutations(calculationId);

  // Initialize form
  const form = useForm<CustomLineItemFormValues>({
    resolver: zodResolver(customLineItemSchema),
    defaultValues: {
      name: "",
      description: "",
      quantity: 1,
      unit_price: 0,
      category_id: "",
    },
  });

  // Handle form submission
  const onSubmit = async (values: CustomLineItemFormValues) => {
    try {
      // Create line item input
      const lineItemInput: LineItemInput = {
        name: values.name,
        description: values.description || "",
        quantity: values.quantity,
        unit_price: values.unit_price, // Already a number from CurrencyInput
        category_id: values.category_id,
        is_custom: true,
        item_quantity_basis: 1,
        total_price: 0,
      };

      // Add line item with queue management
      await addLineItem(lineItemInput);

      // Reset form on success
      form.reset();

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error adding custom item:", error);
      // Error handling is done in the mutation queue
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4 p-4 border rounded-md"
      >
        <h3 className="text-lg font-medium">Add Custom Item</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Item name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Item description (optional)"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity *</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min={1}
                    step={1}
                    placeholder="Enter quantity"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="unit_price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unit Price *</FormLabel>
                <FormControl>
                  <CurrencyInput
                    value={field.value || 0}
                    onChange={(numericValue) => field.onChange(numericValue)}
                    placeholder="Enter unit price"
                    showSymbol={true}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" className="w-full" disabled={isProcessing}>
          <PlusCircle className="h-4 w-4 mr-2" />
          {isProcessing ? "Adding..." : "Add Custom Item"}
        </Button>
      </form>
    </Form>
  );
}

export default AddCustomLineItemForm;
