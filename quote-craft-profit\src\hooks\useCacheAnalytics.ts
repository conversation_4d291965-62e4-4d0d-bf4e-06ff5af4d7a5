/**
 * Cache Analytics Hook
 * 
 * Provides comprehensive cache performance monitoring and analytics.
 * Helps identify cache issues and optimization opportunities.
 * 
 * CACHE OPTIMIZATION: Real-time cache performance monitoring
 */

import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useRef, useState } from 'react';

export interface CacheMetrics {
  totalQueries: number;
  staleQueries: number;
  loadingQueries: number;
  errorQueries: number;
  hitRate: number;
  missRate: number;
  invalidationCount: number;
  memoryEstimate: string;
  slowQueries: string[];
  queryBreakdown: Record<string, number>;
}

export interface CacheAnalyticsOptions {
  enabled?: boolean;
  updateInterval?: number;
  trackSlowQueries?: boolean;
  slowQueryThreshold?: number;
}

/**
 * Hook for monitoring cache performance and analytics
 */
export const useCacheAnalytics = (options: CacheAnalyticsOptions = {}) => {
  const {
    enabled = process.env.NODE_ENV === 'development',
    updateInterval = 30000, // 30 seconds
    trackSlowQueries = true,
    slowQueryThreshold = 2000, // 2 seconds
  } = options;

  const queryClient = useQueryClient();
  const [metrics, setMetrics] = useState<CacheMetrics>({
    totalQueries: 0,
    staleQueries: 0,
    loadingQueries: 0,
    errorQueries: 0,
    hitRate: 0,
    missRate: 0,
    invalidationCount: 0,
    memoryEstimate: '0MB',
    slowQueries: [],
    queryBreakdown: {},
  });

  const invalidationCountRef = useRef(0);
  const slowQueriesRef = useRef<Set<string>>(new Set());

  // Calculate cache metrics
  const calculateMetrics = (): CacheMetrics => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const totalQueries = queries.length;
    const staleQueries = queries.filter(q => q.isStale()).length;
    const loadingQueries = queries.filter(q => q.state.isFetching).length;
    const errorQueries = queries.filter(q => q.state.isError).length;
    
    const hitRate = totalQueries > 0 ? ((totalQueries - staleQueries) / totalQueries) * 100 : 0;
    const missRate = 100 - hitRate;
    
    // Estimate memory usage (rough calculation)
    const memoryEstimate = `${(totalQueries * 0.1).toFixed(1)}MB`;
    
    // Group queries by feature/domain
    const queryBreakdown = queries.reduce((acc, query) => {
      const feature = Array.isArray(query.queryKey) ? query.queryKey[0] as string : 'unknown';
      acc[feature] = (acc[feature] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalQueries,
      staleQueries,
      loadingQueries,
      errorQueries,
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round(missRate * 100) / 100,
      invalidationCount: invalidationCountRef.current,
      memoryEstimate,
      slowQueries: Array.from(slowQueriesRef.current),
      queryBreakdown,
    };
  };

  // Track query performance
  const trackQueryPerformance = (query: any) => {
    if (!trackSlowQueries) return;

    const startTime = query.state.dataUpdatedAt;
    const endTime = Date.now();
    const duration = endTime - startTime;

    if (duration > slowQueryThreshold) {
      const queryKey = Array.isArray(query.queryKey) 
        ? query.queryKey.join('/')
        : String(query.queryKey);
      
      slowQueriesRef.current.add(`${queryKey} (${duration}ms)`);
      
      // Keep only the last 10 slow queries
      if (slowQueriesRef.current.size > 10) {
        const slowQueriesArray = Array.from(slowQueriesRef.current);
        slowQueriesRef.current = new Set(slowQueriesArray.slice(-10));
      }
    }
  };

  useEffect(() => {
    if (!enabled) return;

    const cache = queryClient.getQueryCache();
    
    // Subscribe to cache events
    const unsubscribe = cache.subscribe((event) => {
      switch (event.type) {
        case 'added':
          // Track new queries
          break;
        case 'removed':
          // Track removed queries
          break;
        case 'updated':
          // Track query updates and performance
          if (event.query) {
            trackQueryPerformance(event.query);
          }
          break;
        case 'observerAdded':
          // Track when queries start being observed
          break;
        case 'observerRemoved':
          // Track when queries stop being observed
          break;
      }
    });

    // Track invalidations
    const originalInvalidateQueries = queryClient.invalidateQueries.bind(queryClient);
    queryClient.invalidateQueries = (...args: any[]) => {
      invalidationCountRef.current += 1;
      return originalInvalidateQueries(...args);
    };

    // Periodic metrics collection
    const interval = setInterval(() => {
      const newMetrics = calculateMetrics();
      setMetrics(newMetrics);

      // Log metrics in development
      if (process.env.NODE_ENV === 'development') {
        console.group('📊 Cache Analytics');
        console.log('Total Queries:', newMetrics.totalQueries);
        console.log('Hit Rate:', `${newMetrics.hitRate}%`);
        console.log('Stale Queries:', newMetrics.staleQueries);
        console.log('Loading Queries:', newMetrics.loadingQueries);
        console.log('Error Queries:', newMetrics.errorQueries);
        console.log('Memory Estimate:', newMetrics.memoryEstimate);
        console.log('Invalidations:', newMetrics.invalidationCount);
        
        if (newMetrics.slowQueries.length > 0) {
          console.warn('Slow Queries:', newMetrics.slowQueries);
        }
        
        console.log('Query Breakdown:', newMetrics.queryBreakdown);
        console.groupEnd();
      }
    }, updateInterval);

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, [queryClient, enabled, updateInterval, trackSlowQueries, slowQueryThreshold]);

  // Helper methods for cache analysis
  const getCacheHealth = (): 'excellent' | 'good' | 'fair' | 'poor' => {
    if (metrics.hitRate >= 90) return 'excellent';
    if (metrics.hitRate >= 75) return 'good';
    if (metrics.hitRate >= 60) return 'fair';
    return 'poor';
  };

  const getRecommendations = (): string[] => {
    const recommendations: string[] = [];

    if (metrics.hitRate < 70) {
      recommendations.push('Consider increasing stale time for frequently accessed data');
    }

    if (metrics.totalQueries > 500) {
      recommendations.push('Cache size is large, consider implementing cache cleanup');
    }

    if (metrics.slowQueries.length > 5) {
      recommendations.push('Multiple slow queries detected, review query optimization');
    }

    if (metrics.invalidationCount > 100) {
      recommendations.push('High invalidation count, review invalidation strategy');
    }

    if (metrics.errorQueries > 10) {
      recommendations.push('Multiple query errors, check error handling');
    }

    return recommendations;
  };

  const clearAnalytics = () => {
    invalidationCountRef.current = 0;
    slowQueriesRef.current.clear();
    setMetrics({
      totalQueries: 0,
      staleQueries: 0,
      loadingQueries: 0,
      errorQueries: 0,
      hitRate: 0,
      missRate: 0,
      invalidationCount: 0,
      memoryEstimate: '0MB',
      slowQueries: [],
      queryBreakdown: {},
    });
  };

  const exportMetrics = () => {
    const exportData = {
      timestamp: new Date().toISOString(),
      metrics,
      health: getCacheHealth(),
      recommendations: getRecommendations(),
    };

    // Create downloadable JSON file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `cache-analytics-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return {
    metrics,
    health: getCacheHealth(),
    recommendations: getRecommendations(),
    clearAnalytics,
    exportMetrics,
    enabled,
  };
};

/**
 * Hook for monitoring specific query performance
 */
export const useQueryPerformance = (queryKey: any[]) => {
  const queryClient = useQueryClient();
  const [performance, setPerformance] = useState({
    fetchCount: 0,
    lastFetchTime: 0,
    averageFetchTime: 0,
    errorCount: 0,
    isStale: false,
    isFetching: false,
  });

  useEffect(() => {
    const cache = queryClient.getQueryCache();
    const query = cache.find({ queryKey });

    if (!query) return;

    const updatePerformance = () => {
      setPerformance({
        fetchCount: query.state.fetchFailureCount + (query.state.data ? 1 : 0),
        lastFetchTime: query.state.dataUpdatedAt,
        averageFetchTime: 0, // Would need to track this over time
        errorCount: query.state.fetchFailureCount,
        isStale: query.isStale(),
        isFetching: query.state.isFetching,
      });
    };

    // Initial update
    updatePerformance();

    // Subscribe to query changes
    const unsubscribe = query.subscribe(updatePerformance);

    return unsubscribe;
  }, [queryClient, queryKey]);

  return performance;
};
