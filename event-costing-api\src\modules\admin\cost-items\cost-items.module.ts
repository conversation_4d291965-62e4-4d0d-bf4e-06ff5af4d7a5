import { Module } from '@nestjs/common';
import { CostItemsService } from './cost-items.service';
import { CostItemsController } from './cost-items.controller';
import { AuthModule } from '../../auth/auth.module';
import { AdminModule } from '../../auth/admin.module';

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [CostItemsController],
  providers: [CostItemsService],
  exports: [CostItemsService], // Export service if needed elsewhere
})
export class CostItemsModule {}
