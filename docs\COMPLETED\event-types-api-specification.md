# Event Types API Specification

## 📋 **API Endpoints Required**

### **1. Public Event Types Endpoint**

#### **GET /event-types**
```typescript
// Response
interface EventTypeDto {
  id: string;
  name: string;
  code: string;
  description?: string;
  icon?: string;
  color: string;
  display_order: number;
  is_active: boolean;
}

// Response format
{
  data: EventTypeDto[];
}
```

**Usage**: Dashboard V2 wizard, template forms, calculation forms

### **2. Admin Event Types Management**

#### **GET /admin/event-types**
```typescript
// Query parameters
interface ListEventTypesQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  includeInactive?: boolean;
}

// Response
interface PaginatedEventTypesResponse {
  data: EventTypeDto[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
```

#### **POST /admin/event-types**
```typescript
// Request
interface CreateEventTypeDto {
  name: string;
  code: string;
  description?: string;
  icon?: string;
  color?: string;
  display_order?: number;
}

// Response
{
  data: EventTypeDto;
}
```

#### **PUT /admin/event-types/:id**
```typescript
// Request
interface UpdateEventTypeDto {
  name?: string;
  code?: string;
  description?: string;
  icon?: string;
  color?: string;
  display_order?: number;
  is_active?: boolean;
}

// Response
{
  data: EventTypeDto;
}
```

#### **DELETE /admin/event-types/:id**
```typescript
// Soft delete - sets is_active = false
// Response: 204 No Content
```

## 🔧 **Backend Implementation Plan**

### **1. NestJS Module Structure**
```
event-costing-api/src/modules/event-types/
├── event-types.module.ts
├── event-types.controller.ts
├── event-types.service.ts
└── dto/
    ├── event-type.dto.ts
    ├── create-event-type.dto.ts
    ├── update-event-type.dto.ts
    └── list-event-types.dto.ts
```

### **2. Service Implementation**
```typescript
@Injectable()
export class EventTypesService {
  async findAll(): Promise<EventTypeDto[]> {
    // SELECT * FROM event_types WHERE is_active = true ORDER BY display_order
  }

  async findAllAdmin(query: ListEventTypesQuery): Promise<PaginatedEventTypesResponse> {
    // Admin endpoint with pagination and search
  }

  async create(dto: CreateEventTypeDto): Promise<EventTypeDto> {
    // INSERT INTO event_types
  }

  async update(id: string, dto: UpdateEventTypeDto): Promise<EventTypeDto> {
    // UPDATE event_types SET ... WHERE id = ?
  }

  async delete(id: string): Promise<void> {
    // UPDATE event_types SET is_active = false WHERE id = ?
  }
}
```

## 🎨 **Frontend Integration Plan**

### **1. Event Types Service**
```typescript
// src/services/shared/entities/event-types/eventTypeService.ts
export const getAllEventTypes = async (): Promise<EventType[]> => {
  const response = await apiClient.get('/event-types');
  return response.data.data;
};

export const getEventTypeById = async (id: string): Promise<EventType> => {
  const response = await apiClient.get(`/event-types/${id}`);
  return response.data.data;
};
```

### **2. Update Dashboard V2 EventTypeSelector**
```typescript
// Replace hardcoded EVENT_TYPES with API call
const { data: eventTypes, isLoading } = useQuery({
  queryKey: ['event-types'],
  queryFn: getAllEventTypes,
});
```

### **3. Update Template Forms**
```typescript
// Replace text input with select dropdown
<Select value={eventTypeId} onValueChange={setEventTypeId}>
  <SelectTrigger>
    <SelectValue placeholder="Select event type" />
  </SelectTrigger>
  <SelectContent>
    {eventTypes.map(type => (
      <SelectItem key={type.id} value={type.id}>
        {type.name}
      </SelectItem>
    ))}
  </SelectContent>
</Select>
```

## 📊 **Migration Timeline**

### **Phase 1: Backend API** (Week 1)
- [ ] Create EventTypes module in NestJS
- [ ] Implement CRUD operations
- [ ] Add validation and error handling
- [ ] Create API documentation

### **Phase 2: Frontend Service** (Week 1)
- [ ] Create event types service
- [ ] Add React Query hooks
- [ ] Update type definitions

### **Phase 3: Component Updates** (Week 2)
- [ ] Update Dashboard V2 EventTypeSelector
- [ ] Update template creation forms
- [ ] Update calculation forms
- [ ] Update filtering components

### **Phase 4: Admin Interface** (Week 2)
- [ ] Create admin event types page
- [ ] Add CRUD interface for event types
- [ ] Add bulk operations
- [ ] Add validation and error handling

### **Phase 5: Testing & Cleanup** (Week 3)
- [ ] End-to-end testing
- [ ] Performance testing
- [ ] Remove deprecated string columns
- [ ] Update documentation

## 🔍 **Testing Strategy**

### **1. Backend Testing**
- Unit tests for EventTypesService
- Integration tests for API endpoints
- Validation testing for DTOs
- Error handling testing

### **2. Frontend Testing**
- Component testing for updated forms
- Integration testing for API calls
- E2E testing for Dashboard V2 wizard
- Performance testing for filtering

### **3. Migration Testing**
- Data integrity verification
- Backward compatibility testing
- Rollback procedure testing
- Performance impact assessment

## 🚀 **Benefits Realization**

### **Immediate Benefits**
- ✅ Consistent event types across application
- ✅ Eliminated typos and data inconsistencies
- ✅ Better Dashboard V2 integration
- ✅ Improved query performance

### **Long-term Benefits**
- ✅ Centralized event type management
- ✅ Easy addition of new event types
- ✅ Better reporting and analytics
- ✅ Scalable architecture for future features

This specification provides a complete roadmap for implementing the normalized event types system across the entire application.
