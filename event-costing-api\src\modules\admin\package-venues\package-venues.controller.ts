import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Body,
  Logger,
  UseGuards,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../../auth/guards/admin-role.guard';
import { PackageVenuesService } from './package-venues.service';
import { PackageVenueDto } from './dto/package-venue.dto';
import { AddPackageVenueDto } from './dto/add-package-venue.dto';

@ApiTags('Admin - Package Venues')
@ApiBearerAuth()
@Controller('admin/packages/:packageId/venues')
@UseGuards(JwtAuthGuard, AdminRoleGuard)
export class PackageVenuesController {
  private readonly logger = new Logger(PackageVenuesController.name);

  constructor(private readonly packageVenuesService: PackageVenuesService) {}

  // --- Add Venue to Package ---
  @Post()
  @ApiOperation({ summary: 'Add a venue to a package' })
  @ApiBody({ type: AddPackageVenueDto })
  @ApiResponse({
    status: 201,
    description: 'Venue successfully added to package.',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  @ApiResponse({ status: 409, description: 'Venue already associated' })
  @ApiResponse({ status: 404, description: 'Package or Venue not found' })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'Package UUID',
  })
  async addVenue(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Body() addDto: AddPackageVenueDto,
  ): Promise<{ id: string }> {
    this.logger.log(`Adding venue ${addDto.venue_id} to package ${packageId}`);
    const newAssociation = await this.packageVenuesService.addVenueToPackage(
      packageId,
      addDto.venue_id,
    );
    return { id: newAssociation.id };
  }

  // --- List Venues for Package ---
  @Get()
  @ApiOperation({ summary: 'List venues associated with a package' })
  @ApiResponse({
    status: 200,
    description: 'List of associated venues.',
    type: [PackageVenueDto],
  })
  @ApiResponse({ status: 404, description: 'Package not found' })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'Package UUID',
  })
  async listVenues(
    @Param('packageId', ParseUUIDPipe) packageId: string,
  ): Promise<PackageVenueDto[]> {
    this.logger.log(`Listing venues for package ${packageId}`);
    return this.packageVenuesService.listVenuesForPackage(packageId);
  }

  // --- Remove Venue from Package ---
  @Delete(':venueId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remove a venue from a package' })
  @ApiResponse({ status: 204, description: 'Venue successfully removed from package.' })
  @ApiResponse({ status: 404, description: 'Package or Venue not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'Package UUID',
  })
  @ApiParam({
    name: 'venueId',
    type: 'string',
    format: 'uuid',
    description: 'Venue UUID',
  })
  async removeVenue(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('venueId', ParseUUIDPipe) venueId: string,
  ): Promise<void> {
    this.logger.log(`Removing venue ${venueId} from package ${packageId}`);
    await this.packageVenuesService.removeVenueFromPackage(packageId, venueId);
  }
}
