import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Clock } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { toast } from "sonner";

import { createEvent, updateEvent } from "@/services/shared/entities/events";
import { getAllClients } from "@/services/shared/entities/clients";
import { getUsers, UserWithProfile } from "@/services/shared/users";
import {
  Event,
  EventFormData,
  transformEventFormDataToApiRequest,
  transformEventToFormData,
} from "@/types/events";
import { Client } from "@/types/types";
import { DateRange } from "react-day-picker";

// Import schema from centralized location
import { eventFormSchema, type EventFormValues } from "../schemas";

interface EventFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  event?: Event;
  isEditing?: boolean;
  onEventCreated?: (event: Event) => void; // Callback for when event is created
  disableNavigation?: boolean; // Flag to disable automatic navigation
}

const EventFormDialog: React.FC<EventFormDialogProps> = ({
  isOpen,
  onClose,
  event,
  isEditing = false,
  onEventCreated,
  disableNavigation = false,
}) => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch clients for dropdown
  const { data: clientsResult } = useQuery({
    queryKey: ["clients"],
    queryFn: getAllClients,
  });

  // Fetch users data for primary contact selection
  const { data: users } = useQuery({
    queryKey: ["users"],
    queryFn: getUsers,
  });

  // Extract clients array from the paginated result
  const clients = clientsResult?.data || [];

  // Status options
  const statusOptions = [
    { value: "lead", label: "Lead" },
    { value: "planning", label: "Planning" },
    { value: "confirmed", label: "Confirmed" },
    { value: "in_progress", label: "In Progress" },
    { value: "completed", label: "Completed" },
    { value: "post_event", label: "Post Event" },
    { value: "cancelled", label: "Cancelled" },
    { value: "on_hold", label: "On Hold" },
  ];

  // Initialize form
  const form = useForm<EventFormValues>({
    resolver: zodResolver(eventFormSchema),
    defaultValues: {
      name: "",
      clientId: "", // Required field, use empty string
      dateRange: {
        from: undefined,
        to: undefined,
      },
      location: "",
      status: "planning",
      primaryContactId: "none", // Use 'none' instead of empty string
      notes: "",
    },
  });

  // Update form values when editing an existing event
  useEffect(() => {
    if (isEditing && event) {
      const formData = transformEventToFormData(event);
      form.reset({
        name: formData.name,
        clientId: formData.clientId,
        dateRange: formData.dateRange,
        location: formData.location,
        status: formData.status,
        primaryContactId: formData.primaryContactId || "none", // Use 'none' instead of empty string
        notes: formData.notes || "",
      });
    }
  }, [isEditing, event]);

  // Handle form submission
  const onSubmit = async (values: EventFormValues) => {
    try {
      setIsSubmitting(true);

      // Validate date range
      if (!values.dateRange?.from || !values.dateRange?.to) {
        toast.error("Please select both start and end dates");
        return;
      }

      // Transform form data to API request format
      const apiEventData = transformEventFormDataToApiRequest(
        values as EventFormData
      );

      let result;

      if (isEditing && event) {
        // Update existing event
        result = await updateEvent(event.id, apiEventData);
        toast.success("Event updated successfully");
      } else {
        // Create new event
        result = await createEvent(apiEventData);
        toast.success("Event created successfully");

        // Call the callback if provided (for auto-selection in calculation flow)
        if (onEventCreated) {
          onEventCreated(result);
        }
      }

      onClose();

      // Only navigate if not disabled (e.g., when used in calculation flow)
      if (!disableNavigation) {
        navigate(`/events/${result.id}`);
      }
    } catch (error) {
      console.error("Error saving event:", error);
      toast.error("Failed to save event. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get available internal staff members for primary contact assignment
  const getAvailableStaff = (): { id: string; name: string }[] => {
    // Return all users as potential primary contacts (internal staff)
    if (!Array.isArray(users)) {
      return [];
    }

    return users.map((user: UserWithProfile) => ({
      id: user.id,
      name: user.full_name || user.username || "Unnamed User",
    }));
  };

  const selectedClientId = form.watch("clientId");
  const availableStaff = getAvailableStaff();

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        className="sm:max-w-[600px]"
        aria-describedby="event-form-description"
      >
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Event" : "Create New Event"}
          </DialogTitle>
          <DialogDescription id="event-form-description">
            {isEditing
              ? "Update the event details below."
              : "Fill in the details to create a new event."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter event name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="clientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a client" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {clients
                        .filter(
                          (client: Client) =>
                            client.id && client.id.trim() !== ""
                        ) // Filter out clients with empty IDs
                        .map((client: Client) => (
                          <SelectItem key={client.id} value={client.id}>
                            {client.name || "Unnamed Client"}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dateRange"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Event Date Range *</FormLabel>
                  <FormControl>
                    <DateRangePicker
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Select event dates"
                      disablePastDates={true}
                      numberOfMonths={2}
                    />
                  </FormControl>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    Select both start and end dates for your event
                  </p>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Venue Details</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter venue details" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {statusOptions.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="primaryContactId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Contact (Internal Staff)</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select internal staff member" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {availableStaff
                        .filter((staff) => staff.id && staff.id.trim() !== "") // Filter out empty IDs
                        .map((staff) => (
                          <SelectItem key={staff.id} value={staff.id}>
                            {staff.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    Select the internal staff member responsible for managing
                    this event
                  </p>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes about the event"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                  ? "Update Event"
                  : "Create Event"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EventFormDialog;
