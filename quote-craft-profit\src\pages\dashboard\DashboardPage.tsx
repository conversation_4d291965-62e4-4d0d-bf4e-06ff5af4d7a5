import React, { useEffect, useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Link, useNavigate } from "react-router-dom";
import { PlusCircle, FilePlus, HelpCircle } from "lucide-react";
import { useOnboarding, ONBOARDING_STEPS } from "@/contexts/OnboardingContext";
import { useAuth } from "@/contexts/useAuth";
import {
  QuickStats,
  RecentCalculations,
  TemplatesList,
  QuickStart,
  WhatsNew,
  GuidedTooltip,
} from "./components";

const DashboardPage: React.FC = () => {
  const { isFirstTimeUser } = useOnboarding();
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [greeting, setGreeting] = useState("");
  const [contextMessage, setContextMessage] = useState("");

  // Check for admin redirect from localStorage
  useEffect(() => {
    try {
      // Check if we have a stored admin URL in localStorage
      const adminUrl = localStorage.getItem("admin_redirect_url");

      if (adminUrl && user && isAdmin) {
        console.log(
          `[DashboardPage] Found stored admin URL: ${adminUrl}, redirecting...`
        );
        localStorage.removeItem("admin_redirect_url");
        sessionStorage.removeItem("admin_redirect_url");
        sessionStorage.removeItem("redirect_url");

        // Use a slight delay to ensure all auth processes are complete
        setTimeout(() => {
          navigate(adminUrl);
        }, 100);
      }
    } catch (error) {
      console.error(
        "[DashboardPage] Error checking for admin redirect:",
        error
      );
    }
  }, [user, isAdmin, navigate]);

  // Get user's name
  const userName = user?.user_metadata?.full_name?.split(" ")[0] || "there";

  // Set greeting based on time of day
  useEffect(() => {
    const hour = new Date().getHours();
    let newGreeting = "";

    if (hour < 12) {
      newGreeting = "Good morning";
    } else if (hour < 18) {
      newGreeting = "Good afternoon";
    } else {
      newGreeting = "Good evening";
    }

    setGreeting(newGreeting);

    // Set contextual message
    const dayOfWeek = new Date().getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

    if (isFirstTimeUser) {
      setContextMessage("Welcome to EventCost Pro");
    } else if (isWeekend) {
      setContextMessage("Hope you're having a great weekend");
    } else {
      const messages = [
        "Here's your event planning overview",
        "Ready to create some amazing events?",
        "Your event dashboard is looking good today",
      ];
      setContextMessage(messages[Math.floor(Math.random() * messages.length)]);
    }
  }, [isFirstTimeUser]);

  return (
    <MainLayout>
      {isFirstTimeUser ? (
        <GuidedTooltip
          step={ONBOARDING_STEPS.DASHBOARD_INTRO}
          title="Welcome to EventCost Pro!"
          description="This is your dashboard where you can see an overview of your calculations, templates, and frequently used packages."
          position="bottom"
          isFirst={true}
          isLast={true}
        >
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
                {greeting}, {userName}
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                {contextMessage}
              </p>
            </div>
            <div className="flex gap-3">
              <Link to="/templates">
                <Button variant="outline" className="flex items-center gap-2">
                  <FilePlus className="h-4 w-4" />
                  Create from Template
                </Button>
              </Link>
              <Link to="/calculations/new">
                <Button className="flex items-center gap-2">
                  <PlusCircle className="h-4 w-4" />
                  New Calculation
                </Button>
              </Link>
              <Button variant="ghost" size="icon" className="ml-2">
                <HelpCircle className="h-5 w-5 text-blue-500" />
              </Button>
            </div>
          </div>
        </GuidedTooltip>
      ) : (
        <div className="mb-6 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              {greeting}, {userName}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">{contextMessage}</p>
          </div>
          <div className="flex gap-3">
            <Link to="/templates">
              <Button variant="outline" className="flex items-center gap-2">
                <FilePlus className="h-4 w-4" />
                Create from Template
              </Button>
            </Link>
            <Link to="/calculations/new">
              <Button className="flex items-center gap-2">
                <PlusCircle className="h-4 w-4" />
                New Calculation
              </Button>
            </Link>
          </div>
        </div>
      )}

      {/* What's New Section */}
      <WhatsNew />

      {/* Quick Start Section */}
      <div className="mb-8">
        <QuickStart />
      </div>

      {/* Quick Stats */}
      <QuickStats />

      {/* Recent Calculations */}
      <div className="mt-8">
        <RecentCalculations />
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Trending Templates</CardTitle>
            <CardDescription>
              Recent calculation templates for quick event setup
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TemplatesList />
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default DashboardPage;
