/**
 * Render tracking utilities for debugging infinite render issues
 * Use only in development mode
 */
import { useRef, useEffect } from "react";

interface RenderTrackingOptions {
  enabled?: boolean;
  logLevel?: "minimal" | "detailed" | "verbose";
  maxLogs?: number;
}

/**
 * Hook to track and log component re-renders with dependency analysis
 */
export const useRenderTracker = (
  componentName: string,
  dependencies: Record<string, any>,
  options: RenderTrackingOptions = {}
) => {
  const {
    enabled = process.env.NODE_ENV === "development",
    logLevel = "detailed",
    maxLogs = 50,
  } = options;

  const renderCountRef = useRef(0);
  const previousDepsRef = useRef<Record<string, any>>({});
  const logCountRef = useRef(0);

  // Always call hooks - conditional logic inside useEffect
  renderCountRef.current += 1;

  useEffect(() => {
    if (!enabled) return;
    if (logCountRef.current >= maxLogs) return;

    const currentDeps = dependencies;
    const previousDeps = previousDepsRef.current;
    const changedDeps: string[] = [];

    // Find changed dependencies
    Object.keys(currentDeps).forEach((key) => {
      if (previousDeps[key] !== currentDeps[key]) {
        changedDeps.push(key);
      }
    });

    // Log render information
    if (logLevel === "minimal" && changedDeps.length > 0) {
      console.log(
        `🔄 ${componentName} render #${
          renderCountRef.current
        } - Changed: ${changedDeps.join(", ")}`
      );
    } else if (logLevel === "detailed") {
      console.group(`🔄 ${componentName} render #${renderCountRef.current}`);
      if (changedDeps.length > 0) {
        console.log("📝 Changed dependencies:", changedDeps);
        changedDeps.forEach((key) => {
          console.log(`  • ${key}:`, {
            previous: previousDeps[key],
            current: currentDeps[key],
            type: typeof currentDeps[key],
            isObject:
              typeof currentDeps[key] === "object" && currentDeps[key] !== null,
            isFunction: typeof currentDeps[key] === "function",
          });
        });
      } else {
        console.log("⚠️ Re-render with no dependency changes detected");
      }
      console.groupEnd();
    } else if (logLevel === "verbose") {
      console.group(`🔄 ${componentName} render #${renderCountRef.current}`);
      console.log("📊 All dependencies:", currentDeps);
      console.log("📝 Changed dependencies:", changedDeps);
      console.log(
        "🔍 Dependency analysis:",
        Object.keys(currentDeps).map((key) => ({
          key,
          changed: changedDeps.includes(key),
          type: typeof currentDeps[key],
          isStable: previousDeps[key] === currentDeps[key],
        }))
      );
      console.groupEnd();
    }

    // Update previous dependencies
    previousDepsRef.current = { ...currentDeps };
    logCountRef.current += 1;

    // Warn about excessive renders
    if (renderCountRef.current > 10) {
      console.warn(
        `⚠️ ${componentName} has rendered ${renderCountRef.current} times - possible infinite loop!`
      );
    }
  }, [enabled, componentName, dependencies, logLevel, maxLogs]);
};

/**
 * Hook to track React Query cache invalidations
 */
export const useQueryInvalidationTracker = (
  hookName: string,
  queryKeys: string[],
  enabled = process.env.NODE_ENV === "development"
) => {
  const invalidationCountRef = useRef<Record<string, number>>({});

  useEffect(() => {
    if (!enabled) return;
    queryKeys.forEach((key) => {
      invalidationCountRef.current[key] =
        (invalidationCountRef.current[key] || 0) + 1;

      if (invalidationCountRef.current[key] > 5) {
        console.warn(
          `🚨 Query "${key}" invalidated ${invalidationCountRef.current[key]} times in ${hookName} - possible invalidation loop!`
        );
      } else {
        console.log(
          `🔄 Query "${key}" invalidated in ${hookName} (count: ${invalidationCountRef.current[key]})`
        );
      }
    });
  }, [enabled, hookName, queryKeys]);
};

/**
 * Hook to track object reference changes
 */
export const useObjectReferenceTracker = (
  objectName: string,
  obj: any,
  enabled = process.env.NODE_ENV === "development"
) => {
  const previousRefRef = useRef<any>(null);
  const changeCountRef = useRef(0);

  useEffect(() => {
    if (!enabled) return;
    if (previousRefRef.current !== obj) {
      changeCountRef.current += 1;

      console.log(
        `🔗 ${objectName} reference changed (count: ${changeCountRef.current})`,
        {
          previous: previousRefRef.current,
          current: obj,
          type: typeof obj,
          isObject: typeof obj === "object" && obj !== null,
          keys:
            typeof obj === "object" && obj !== null
              ? Object.keys(obj)
              : undefined,
        }
      );

      if (changeCountRef.current > 10) {
        console.warn(
          `⚠️ ${objectName} reference has changed ${changeCountRef.current} times - possible object recreation issue!`
        );
      }

      previousRefRef.current = obj;
    }
  }, [enabled, obj, objectName]);
};

/**
 * Utility to detect circular dependencies in hook chains
 */
export const detectCircularDependencies = (
  hookChain: string[],
  enabled = process.env.NODE_ENV === "development"
) => {
  if (!enabled) return;

  const seen = new Set<string>();
  const path: string[] = [];

  for (const hook of hookChain) {
    if (seen.has(hook)) {
      const circularPath = path.slice(path.indexOf(hook));
      console.error(
        `🔄 Circular dependency detected: ${circularPath.join(" → ")} → ${hook}`
      );
      return true;
    }
    seen.add(hook);
    path.push(hook);
  }

  return false;
};
