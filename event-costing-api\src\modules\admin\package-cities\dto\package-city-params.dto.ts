import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class PackageCityParamsDto {
  @ApiProperty({
    description: 'The UUID of the parent package',
    format: 'uuid',
  })
  @IsNotEmpty()
  @IsUUID()
  package_id: string;

  @ApiProperty({
    description: 'The UUID of the city',
    format: 'uuid',
  })
  @IsNotEmpty()
  @IsUUID()
  city_id: string;
}
