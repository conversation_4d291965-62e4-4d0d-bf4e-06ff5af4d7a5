import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { getCityById, createCity, updateCity } from "@/services/shared/entities/cities";

interface CityFormDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  cityId: string | null;
}

// Import schema from centralized location
import { cityFormSchema, type CityFormValues } from '../../schemas';

type FormValues = CityFormValues;

const CityFormDialog: React.FC<CityFormDialogProps> = ({
  isOpen,
  onClose,
  cityId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!cityId;

  const form = useForm<FormValues>({
    resolver: zodResolver(cityFormSchema),
    defaultValues: {
      name: "",
    },
  });

  useEffect(() => {
    const loadCityDetails = async () => {
      if (isEditMode && isOpen) {
        setIsLoading(true);
        try {
          console.log("Loading city details for id:", cityId);
          const cityData = await getCityById(cityId);

          console.log("Loaded city details:", cityData);
          if (cityData) {
            form.reset({
              name: cityData.name,
            });
          }
        } catch (error) {
          console.error("Error loading city:", error);
          toast.error("Failed to load city details");
          onClose();
        } finally {
          setIsLoading(false);
        }
      }
    };

    if (isOpen) {
      form.reset({ name: "" }); // Reset form on open
      if (isEditMode) {
        loadCityDetails();
      }
    }
  }, [isOpen, cityId, isEditMode, form, onClose]);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      console.log("Submitting city form:", isEditMode ? "update" : "create", values);

      if (isEditMode) {
        // Update existing city
        await updateCity(cityId, {
          name: values.name,
        });
        toast.success("City updated successfully");
      } else {
        // Create new city
        const cityData = await createCity({
          name: values.name,
        });
        console.log("City created:", cityData);
        toast.success("City created successfully");
      }

      onClose(true); // Close and refresh
    } catch (error) {
      console.error("Error saving city:", error);
      toast.error(
        `Failed to ${isEditMode ? "update" : "create"} city: ${
          (error as Error).message
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit City" : "Create New City"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode ? "Update the city details below." : "Enter the details for the new city."}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Loading city details...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter city name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onClose()}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isEditMode ? "Updating..." : "Creating..."}
                    </>
                  ) : isEditMode ? (
                    "Update City"
                  ) : (
                    "Create City"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CityFormDialog;
