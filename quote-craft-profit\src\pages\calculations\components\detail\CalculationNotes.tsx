import React from "react";
import { Textarea } from "@/components/ui/textarea";

interface CalculationNotesProps {
  notes: string;
  isEditMode: boolean;
  editedNotes: string;
  setEditedNotes: (value: string) => void;
}

const CalculationNotes: React.FC<CalculationNotesProps> = ({
  notes,
  isEditMode,
  editedNotes,
  setEditedNotes,
}) => {
  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border dark:border-gray-700">
      <h2 className="text-xl font-bold mb-4 dark:text-white">Notes</h2>
      <Textarea
        value={isEditMode ? editedNotes : notes || ""}
        onChange={(e) => setEditedNotes(e.target.value)}
        placeholder="Add notes about this calculation..."
        className="min-h-[100px]"
        disabled={!isEditMode}
      />
    </div>
  );
};

export default CalculationNotes;
