/**
 * Hook for managing category expansion state
 */
import { useState, useCallback, useMemo } from "react";

/**
 * Hook for managing which categories are expanded
 *
 * @returns State and functions for managing expanded categories
 */
export function useCategoryExpansion() {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);

  // Toggle category expansion
  const toggleCategory = useCallback((categoryId: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  }, []);

  return useMemo(
    () => ({
      expandedCategories,
      toggleCategory,
    }),
    [expandedCategories, toggleCategory]
  );
}
