import {
  Controller,
  Get,
  Post,
  Param,
  <PERSON>,
  Body,
  UseGuards,
  Logger,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../../auth/guards/admin-role.guard';
import { AdminUsersService } from './admin-users.service';
import { AdminUserDto } from './dto/admin-user.dto';
import { RoleDto } from './dto/role.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { UpdateUserStatusDto } from './dto/update-user-status.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@ApiTags('Admin - Users')
@ApiBearerAuth()
@Controller('admin/users')
@UseGuards(JwtAuthGuard, AdminRoleGuard)
export class AdminUsersController {
  private readonly logger = new Logger(AdminUsersController.name);

  constructor(private readonly adminUsersService: AdminUsersService) {}

  @Get()
  @ApiOperation({ summary: 'Get all users (admin)' })
  @ApiResponse({
    status: 200,
    description: 'List of users with profile information',
    type: [AdminUserDto],
  })
  async findAll(): Promise<AdminUserDto[]> {
    this.logger.log('Admin request to get all users');
    return this.adminUsersService.findAll();
  }

  @Get('roles')
  @ApiOperation({ summary: 'Get all roles (admin)' })
  @ApiResponse({
    status: 200,
    description: 'List of roles',
    type: [RoleDto],
  })
  async getRoles(): Promise<RoleDto[]> {
    this.logger.log('Admin request to get all roles');
    return this.adminUsersService.getRoles();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a user by ID (admin)' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({
    status: 200,
    description: 'User with profile information',
    type: AdminUserDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<AdminUserDto> {
    this.logger.log(`Admin request to get user with ID: ${id}`);
    return this.adminUsersService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new user (admin)' })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: AdminUserDto,
  })
  async createUser(
    @Body() createUserDto: CreateUserDto,
  ): Promise<AdminUserDto> {
    this.logger.log(
      `Admin request to create user with email: ${createUserDto.email}`,
    );
    return this.adminUsersService.createUser(createUserDto);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a user (admin)' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: AdminUserDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUser(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<AdminUserDto> {
    this.logger.log(`Admin request to update user with ID: ${id}`);
    return this.adminUsersService.updateUser(id, updateUserDto);
  }

  @Patch(':id/role')
  @ApiOperation({ summary: "Update a user's role (admin)" })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Role updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserRoleDto: UpdateUserRoleDto,
  ): Promise<void> {
    this.logger.log(
      `Admin request to update role for user ${id} to role ID ${updateUserRoleDto.roleId}`,
    );
    return this.adminUsersService.updateRole(id, updateUserRoleDto);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: "Update a user's status (admin)" })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Status updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserStatusDto: UpdateUserStatusDto,
  ): Promise<void> {
    this.logger.log(
      `Admin request to update status for user ${id} to ${updateUserStatusDto.status}`,
    );
    return this.adminUsersService.updateStatus(id, updateUserStatusDto);
  }
}

