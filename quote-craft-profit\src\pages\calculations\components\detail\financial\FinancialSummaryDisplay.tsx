import React from "react";

interface FinancialSummaryDisplayProps {
  validTotal: number;
  finalTotal: number;
  formatCurrency: (amount: number) => string;
}

/**
 * Pure display component for financial totals
 * Shows subtotal and final total in a clean format
 */
export const FinancialSummaryDisplay: React.FC<
  FinancialSummaryDisplayProps
> = ({ validTotal, finalTotal, formatCurrency }) => {
  return (
    <>
      {/* Subtotal */}
      <div className="flex justify-between items-center pb-2 border-b dark:border-gray-700">
        <span className="text-gray-600 dark:text-gray-300">Sub Total</span>
        <span className="font-medium dark:text-gray-200">
          {formatCurrency(validTotal)}
        </span>
      </div>

      {/* Total */}
      <div className="flex justify-between items-center pb-2 border-b dark:border-gray-700">
        <span className="text-gray-800 dark:text-gray-200 font-semibold">
          Total
        </span>
        <span className="font-bold text-lg dark:text-white">
          {formatCurrency(finalTotal)}
        </span>
      </div>
    </>
  );
};
