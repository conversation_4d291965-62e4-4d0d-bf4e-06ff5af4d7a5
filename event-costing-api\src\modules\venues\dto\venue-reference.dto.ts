import { ApiProperty } from '@nestjs/swagger';

export class VenueReferenceDto {
  @ApiProperty({
    type: String,
    format: 'uuid',
    description: 'Venue ID',
  })
  id: string;

  @ApiProperty({
    type: String,
    description: 'Venue name',
  })
  name: string;

  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue address',
  })
  address: string | null;

  @ApiProperty({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'City ID',
  })
  city_id: string | null;

  @ApiProperty({
    type: String,
    nullable: true,
    description: 'City name',
  })
  city_name?: string;

  @ApiProperty({
    type: String,
    nullable: true,
    description:
      'Venue classification (outdoor, hotel, indoor, premium, luxury)',
    enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
  })
  classification?: string | null;

  @ApiProperty({
    type: Number,
    nullable: true,
    description: 'Maximum attendee capacity',
  })
  capacity?: number | null;

  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue image URL',
  })
  image_url?: string | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    nullable: true,
    description: 'Venue features and amenities',
  })
  features?: string[] | null;
}
