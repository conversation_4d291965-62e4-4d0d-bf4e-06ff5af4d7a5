# Dashboard V2: Quick Event Setup & Template Discovery

## Overview

Dashboard V2 introduces a guided event setup wizard that helps users quickly find relevant templates based on key event parameters. The wizard collects essential information and then presents tailored template recommendations with pricing information.

## User Flow

1. User lands on dashboard
2. User engages with "Quick Event Setup" wizard
3. User selects event parameters (type, attendees, city, venue)
4. System displays relevant templates with pricing
5. User compares templates and selects one to start a calculation

## Implementation Plan

### Phase 1: Quick Event Setup Wizard

#### 1. Create Wizard Component

- Build a multi-step form with visual selection options
- Implement state management for wizard selections
- Add responsive design for mobile/desktop

#### 2. Event Type Selection

- Display card-based options with icons
- Include common event types (Corporate, Wedding, Social, etc.)
- Store selection in wizard state

#### 3. Attendee Count Input

- Implement number input with increment/decrement controls
- Add preset range options (1-50, 51-100, 101+)
- Validate input to ensure it's within acceptable ranges

#### 4. City Selection

- Display city cards with icons/images
- **Dynamic city fetching**: Load available cities from database
- Implement city data service for real-time city information
- Highlight recently used cities if available
- Handle loading states and empty city scenarios

#### 5. Venue Selection

- Show venue cards with images
- Display venue capacity relative to attendee count
- **Comprehensive venue classification system**:
  - Outdoor, Hotel, Indoor, Premium, Luxury, and other categories
  - Dynamic classification based on venue data schema
- **Clickable venue functionality**: Navigate to detailed venue pages
- Filter venues by selected city and capacity requirements
- Implement venue detail modal or navigation system

### Phase 2: Template Recommendations

#### 1. Template Filtering Service

- Create a service to filter templates based on wizard parameters
- Implement backend query optimization for fast results
- Add caching for common parameter combinations

#### 2. Template Pricing Calculation

- Extend template calculation service to handle batch calculations
- Pre-calculate pricing based on attendee count and venue
- Implement price estimation for templates without exact pricing

#### 3. Template Duration Grouping

- Add duration metadata to templates (1-day, 2-day, etc.)
- Create grouping component to organize templates by duration
- Implement tabs or sections for easy navigation between durations

#### 4. Template Recommendation Display

- Design card layout for template recommendations
- Show key information (name, description, total price)
- Include package count and highlight features
- Add visual indicators for best match or popular choices

#### 5. Template Comparison

- Implement selection mechanism for comparison
- Create side-by-side comparison view
- Highlight differences between selected templates
- Include detailed package breakdown in comparison

### Phase 3: Integration & Refinement

#### 1. Dashboard Integration

- Position wizard prominently on dashboard
- Implement smooth transitions between wizard and recommendations
- Add ability to modify parameters and see updated recommendations

#### 2. User Preference Storage

- Save user selections to preferences
- Implement quick-start with previous selections
- Add personalized recommendations based on history

#### 3. Performance Optimization

- Implement virtualized lists for template recommendations
- Add lazy loading for template images
- Optimize API calls with batching and caching

#### 4. Analytics Integration

- Track wizard usage and conversion rates
- Identify common parameter combinations
- Measure time-to-selection improvements

## Technical Considerations

### Frontend Components

| Component                 | Description                                                        |
| ------------------------- | ------------------------------------------------------------------ |
| `WizardContainer`         | Main container for the wizard flow                                 |
| `EventTypeSelector`       | Visual selection for event types                                   |
| `AttendeeCounter`         | Input for attendee count with presets                              |
| `CitySelector`            | Dynamic grid of cities from database                               |
| `VenueSelector`           | Cards with clickable venue links and comprehensive classifications |
| `VenueDetailModal`        | Modal or page for detailed venue information                       |
| `TemplateRecommendations` | Results display with filtering                                     |
| `DurationTabs`            | Navigation between duration groups                                 |
| `TemplateComparisonTool`  | Side-by-side template comparison                                   |
| `PricingSummary`          | Breakdown of template pricing                                      |

### Backend Enhancements

#### ✅ **Available APIs (Ready to Use)**

- **City Data Service**: `GET /cities` - Dynamic city fetching ✓
- **Basic Venue Filtering**: `GET /venues?cityId=&active=` - Venue filtering by city ✓
- **Venue Detail API**: `GET /venues/:id` - Detailed venue information ✓
- **Template Filter API**: `GET /templates` with eventType, cityId filtering ✓
- **Template Calculation**: `GET /templates/:id/calculate` - Individual template pricing ✓

#### ❌ **Missing APIs (Need to Be Created)**

- **Hybrid Template Pricing Service**: Pre-calculated prices with real-time fallback
- **Enhanced Template Filtering**: Add attendee range filtering (1-50, 51-100, 101+)
- **Venue Classification Service**: Support comprehensive venue categorization
- **Template Recommendation Algorithm**: Smart sorting based on wizard parameters
- **Price Cache Management**: Background jobs for price updates and cache invalidation

### Data Model Updates

#### ✅ **Available Database Fields**

- **Cities**: Basic structure with id, name (supports dynamic loading)
- **Venues**: Basic structure with city relationships
- **Templates**: Event type, city, attendees, date fields available

#### ❌ **Required Database Schema Changes**

- **Venues**: Add classification field (outdoor, hotel, indoor, premium, luxury)
- **Venues**: Add capacity field for attendee matching
- **Venues**: Add image/photo fields for venue cards
- **Cities**: Add icon/image fields for city cards (optional)
- **Templates**: Add cached pricing fields for hybrid approach
  - `cached_total_price` - Pre-calculated template total
  - `cached_currency` - Currency for cached price
  - `price_calculated_at` - Timestamp of last calculation
  - `price_cache_valid` - Boolean flag for cache validity

#### 📋 **Implementation-Ready Fields**

- **User Preferences**: Store wizard selections (new table)
- **Template Duration**: Calculate from existing start/end date fields

## Success Metrics

- **Engagement**: Increase in template usage
- **Efficiency**: Reduction in time to create new calculations
- **Conversion**: Higher percentage of users creating calculations
- **Satisfaction**: Improved user feedback on template discovery
- **Retention**: Increased return rate for template creation

## API Endpoint Specifications

### New Endpoints Required

#### 1. Hybrid Template Pricing Service

```
GET /templates/wizard-pricing?eventType=corporate&cityId=uuid&attendeesMin=50&attendeesMax=100

Response:
{
  "templates": [
    {
      "id": "uuid1",
      "name": "Corporate Event Package",
      "totalPrice": 15000000,
      "currency": "IDR",
      "priceSource": "cached", // or "calculated"
      "lastCalculated": "2024-01-15T10:30:00Z",
      "attendees": 100,
      "duration": 1
    }
  ],
  "summary": {
    "totalFound": 15,
    "cachedPrices": 12,
    "calculatedPrices": 3,
    "averagePrice": 18000000
  }
}
```

#### 2. Real-time Price Calculation (Fallback)

```
POST /templates/calculate-batch
Content-Type: application/json

Request Body:
{
  "templateIds": ["uuid1", "uuid2"],
  "attendees": 100,
  "updateCache": true
}

Response:
{
  "results": [
    {
      "templateId": "uuid1",
      "totalPrice": 15000000,
      "currency": "IDR",
      "breakdown": [...],
      "cacheUpdated": true
    }
  ]
}
```

#### 3. Price Cache Management

```
POST /admin/templates/refresh-prices
Content-Type: application/json

Request Body:
{
  "templateIds": ["uuid1", "uuid2"], // optional, all if not provided
  "force": false // force recalculation even if cache is valid
}

Response:
{
  "updated": 15,
  "failed": 2,
  "totalProcessed": 17,
  "errors": [
    {
      "templateId": "uuid3",
      "error": "Missing package pricing data"
    }
  ]
}
```

#### 4. Enhanced Template Filtering

```
GET /templates/wizard-filter?eventType=corporate&cityId=uuid&attendeesMin=50&attendeesMax=100&venueClassification=hotel

Response:
{
  "data": [TemplateSummaryDto],
  "count": number,
  "filters": {
    "eventTypes": ["corporate", "wedding", "social"],
    "cities": [CityDto],
    "venueClassifications": ["outdoor", "hotel", "indoor", "premium", "luxury"]
  }
}
```

#### 5. Venue Classification Update

```
PATCH /admin/venues/:id/classification
Content-Type: application/json

Request Body:
{
  "classification": "hotel",
  "capacity": 200,
  "imageUrl": "https://..."
}
```

### Database Migration Scripts Required

#### 1. Add Template Cached Pricing Fields

```sql
ALTER TABLE templates
ADD COLUMN cached_total_price NUMERIC DEFAULT 0,
ADD COLUMN cached_currency VARCHAR(3) DEFAULT 'IDR',
ADD COLUMN price_calculated_at TIMESTAMPTZ,
ADD COLUMN price_cache_valid BOOLEAN DEFAULT false;

CREATE INDEX idx_templates_price_cache ON templates(price_cache_valid, cached_total_price);
CREATE INDEX idx_templates_price_calculated ON templates(price_calculated_at);
```

#### 2. Add Venue Classification Fields

```sql
ALTER TABLE venues
ADD COLUMN classification VARCHAR(50),
ADD COLUMN capacity INTEGER,
ADD COLUMN image_url TEXT,
ADD COLUMN features JSONB DEFAULT '[]';

CREATE INDEX idx_venues_classification ON venues(classification);
CREATE INDEX idx_venues_capacity ON venues(capacity);
```

#### 3. Add City Image Field

```sql
ALTER TABLE cities
ADD COLUMN icon_url TEXT,
ADD COLUMN image_url TEXT;
```

#### 4. Create User Preferences Table

```sql
CREATE TABLE user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  wizard_selections JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### 5. Create Price Cache Invalidation Triggers

```sql
-- Function to invalidate template price cache when package prices change
CREATE OR REPLACE FUNCTION invalidate_template_price_cache()
RETURNS TRIGGER AS $$
BEGIN
  -- Invalidate cache for templates using this package
  UPDATE templates
  SET price_cache_valid = false
  WHERE package_selections @> jsonb_build_array(
    jsonb_build_object('package_id', NEW.package_id)
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger on package pricing updates
CREATE TRIGGER trigger_invalidate_template_cache
  AFTER UPDATE ON package_pricing
  FOR EACH ROW
  EXECUTE FUNCTION invalidate_template_price_cache();
```

## Implementation Priority & Timeline

### Phase 1: Immediate Implementation (Week 1-2)

**No Database Changes Required**

- ✅ City Data Service (existing API)
- ✅ Basic Venue Selection (existing API)
- ✅ Event Type & City Template Filtering (existing API)
- ✅ Individual Template Calculation (existing API)
- 🔨 Wizard UI Components

### Phase 2: Database Schema Updates (Week 3)

**Database Modifications Required**

- 🔨 Add venue classification fields
- 🔨 Add venue capacity fields
- 🔨 Add image fields for venues and cities
- 🔨 Create user preferences table

### Phase 3: Hybrid Pricing System (Week 4-5)

**New API Development**

- 🔨 Hybrid Template Pricing Service (cached + real-time fallback)
- 🔨 Price Cache Management APIs
- 🔨 Background price calculation jobs
- 🔨 Enhanced Template Filtering with attendee ranges
- 🔨 Venue Classification Service
- 🔨 Template Recommendation Algorithm

### Phase 4: Integration & Refinement (Week 6-7)

**Frontend Integration**

- 🔨 Template comparison and pricing display
- 🔨 Venue detail modal/navigation
- 🔨 User preference storage
- 🔨 Performance optimization

### Phase 5: Testing & Deployment (Week 8)

**Quality Assurance**

- 🔨 End-to-end testing
- 🔨 Performance optimization
- 🔨 Analytics integration

## UI Design Reference

The Quick Event Setup wizard follows a dark theme with card-based selections:

### Event Type Selection

- Three main categories displayed as cards with icons:
  - **Corporate** (briefcase icon)
  - **Wedding** (flower/ring icon)
  - **Social** (celebration icon)
- Cards have hover states and clear visual selection

### Number of Attendees

- Large number input (50) with increment/decrement buttons
- Guest range indicators below:
  - **1-50** (highlighted when in range)
  - **51-100** (orange highlight for mid-range)
  - **101+** (for large events)

### City Selection

- Grid layout with city cards showing icons
- **Dynamic city loading**: Fetch available cities from database
- Display actual cities from the system's city data
- Show city-specific icons or images when available
- Highlight recently used cities for returning users

### Venue Selection

- Large venue cards with high-quality images
- Venue information displayed:
  - **Venue name** (e.g., "Skyline Rooftop", "Grand Hyatt Ballroom")
  - **Location details** (e.g., "Midtown • Up to 200 guests")
  - **Classification tags** with comprehensive venue types:
    - **Outdoor** (gardens, parks, outdoor spaces)
    - **Hotel** (hotel ballrooms, conference rooms)
    - **Indoor** (convention centers, indoor halls)
    - **Premium** (high-end venues with premium amenities)
    - **Luxury** (ultra-premium venues with luxury services)
    - **Other categories** based on system's venue classification schema
  - **Clickable venue links** for detailed venue information and navigation
- Cards show venue capacity relative to selected attendee count
- Filter venues by selected city and attendee capacity

## Future Enhancements

- **AI-Powered Recommendations**: Suggest templates based on user history
- **Budget Filtering**: Allow users to set budget constraints
- **Template Customization**: Quick modifications to templates before selection
- **Seasonal Templates**: Highlight templates relevant to upcoming seasons

## Hybrid Pricing Strategy

### 🎯 **Approach Overview**

Dashboard V2 uses a **hybrid pricing approach** that combines pre-calculated cached prices with real-time calculation fallback to deliver both speed and accuracy.

### ⚡ **How It Works**

1. **Background Price Calculation**

   - Nightly jobs calculate and cache prices for all templates
   - Prices stored in `cached_total_price` field with validity flag
   - Cache invalidated when package prices change

2. **Smart Query Strategy**

   ```sql
   -- First, get templates with valid cached prices
   SELECT *, cached_total_price
   FROM templates
   WHERE price_cache_valid = true
   AND event_type = 'corporate';

   -- For invalid cache, trigger real-time calculation
   ```

3. **Real-time Fallback**
   - Templates with invalid cache get calculated on-demand
   - Results update the cache for future requests
   - Users always see current, accurate pricing

### 📊 **Performance Benefits**

| Metric             | Traditional Batch | Hybrid Approach | Improvement   |
| ------------------ | ----------------- | --------------- | ------------- |
| **Initial Load**   | 2-5 seconds       | 0.1-0.3 seconds | 10-50x faster |
| **Accuracy**       | 100% current      | 100% current    | Same          |
| **Server Load**    | High              | Low             | 80% reduction |
| **Cache Hit Rate** | N/A               | 85-95%          | Excellent     |

### 🔄 **Cache Management**

#### **Automatic Invalidation**

- Package price changes → Invalidate affected templates
- Currency rate changes → Invalidate all templates
- Package availability changes → Invalidate related templates

#### **Background Refresh**

```typescript
// Nightly job at 2 AM
@Cron('0 2 * * *')
async refreshTemplatePrices() {
  const staleTemplates = await this.getStaleTemplates();
  await this.batchCalculateAndCache(staleTemplates);
}
```

#### **Manual Refresh**

- Admin panel to force price recalculation
- Bulk refresh for specific templates
- Emergency refresh for all templates

### 🎯 **User Experience**

1. **User completes wizard** → Instant results from cache
2. **Some prices outdated** → Background calculation updates them
3. **Next user** → Sees fresh, cached prices
4. **Always accurate** → No stale pricing shown to users

## Implementation Analysis Summary

### ✅ **Ready for Immediate Development**

- **60% of functionality** can be implemented using existing APIs
- **City selection** and **basic venue filtering** are fully supported
- **Template filtering** by event type and city already exists
- **Individual template calculations** are working

### ⚠️ **Requires Database Schema Changes**

- **Venue classification system** needs new database fields
- **Venue capacity matching** requires capacity field
- **Image support** for venues and cities needs new fields

### 🔨 **Requires New Backend Development**

- **Hybrid pricing system** with cached prices and real-time fallback
- **Price cache management** with background jobs and invalidation
- **Enhanced filtering** with attendee ranges
- **Template recommendation algorithm** for smart sorting

### 📊 **Development Effort Estimation**

- **Phase 1 (Immediate)**: 2 weeks - UI + existing APIs
- **Phase 2 (Database)**: 1 week - schema modifications
- **Phase 3 (Backend)**: 2 weeks - new API development
- **Phase 4 (Integration)**: 2 weeks - frontend integration
- **Phase 5 (Testing)**: 1 week - QA and deployment

**Total Estimated Timeline: 8 weeks**

### 🎯 **Recommended Approach**

1. **Start with Phase 1** to deliver immediate value
2. **Parallel database design** for venue classification
3. **Incremental rollout** with feature flags
4. **User feedback integration** after Phase 1 completion

## Risk Assessment

### **Low Risk**

- ✅ City data service (existing API)
- ✅ Basic venue selection (existing API)
- ✅ Template filtering (existing API)

### **Medium Risk**

- ⚠️ Database schema changes (requires migration planning)
- ⚠️ Venue classification system (new data model)

### **High Risk**

- 🔴 Price cache consistency (ensuring cached prices stay accurate)
- 🔴 Background job reliability (price calculation jobs must not fail)
- 🔴 Template recommendation algorithm (complex business logic)

## Notes

- Dashboard V2 will coexist with the current dashboard initially
- No deletion of existing user dashboard functionality required
- Focus on seamless integration with existing template and calculation systems
- Maintain backward compatibility with current user workflows
- **Database changes require careful migration planning**
- **Performance testing required for batch operations**
