import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, <PERSON>N<PERSON><PERSON>, Min, MaxLength } from 'class-validator';

// Fields that can be updated. Currency/Package cannot be changed.
export class UpdatePackagePriceDto {
  @ApiProperty({
    description: 'The updated base unit price.',
    example: 1600000.0,
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  price?: number;

  @ApiProperty({
    description: 'The updated base unit cost.',
    example: 800000.0,
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  unit_base_cost?: number;

  @ApiProperty({
    description: 'Optional updated description.',
    example: 'Updated price for IDR market Q3.',
    required: false,
    maxLength: 500, // Example max length
  })
  @IsOptional()
  @MaxLength(500)
  description?: string;
}
