/**
 * Core calculation services
 * PHASE 5 CLEANUP: Updated to use consolidated services after removing deprecated files
 * Main calculation CRUD operations
 */

// PHASE 5: Export calculation services from consolidated services
export {
  getAllCalculations,
  getCalculationById,
  getCalculationSummary,
  createCalculation,
  getPackagesByCategory,
} from "../calculationDataService";

export {
  updateCalculation,
  deleteCalculation,
  recalculateCalculationTotals,
} from "../calculationMutationService";

// Re-export utility functions that are commonly needed
export { formatCurrency } from "@/pages/calculations/utils/formatUtils";
