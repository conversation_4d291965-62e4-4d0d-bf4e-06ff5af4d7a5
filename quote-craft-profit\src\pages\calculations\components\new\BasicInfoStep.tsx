import React from "react";
import { UseFormReturn } from "react-hook-form";
import { PlusCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import type { DateRange } from "react-day-picker";
import { EventTypeSelector } from "@/components/ui/event-type-selector";
import { Client } from "@/types/types";
import { FormValues } from "../../hooks/core/useCalculationForm";

interface BasicInfoStepProps {
  form: UseFormReturn<FormValues>;
  clients: Client[];
  onOpenClientForm: () => void;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  form,
  clients,
  onOpenClientForm,
}) => {
  return (
    <>
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Event Name</FormLabel>
            <FormControl>
              <Input placeholder="Enter event name" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event_type_id"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Event Type (Optional)</FormLabel>
            <FormControl>
              <EventTypeSelector
                value={field.value}
                onValueChange={field.onChange}
                placeholder="Select event type"
                allowEmpty={true}
                emptyLabel="None"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="date_range"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Event Date Range *</FormLabel>
            <FormControl>
              <DateRangePicker
                value={field.value as DateRange | undefined}
                onChange={(range) => {
                  field.onChange(range);
                }}
                placeholder="Select start and end dates"
                disablePastDates={true}
                numberOfMonths={2}
              />
            </FormControl>
            <FormMessage />
            <p className="text-xs text-muted-foreground">
              Both start and end dates are required
            </p>
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="client_id"
        render={({ field }) => (
          <FormItem>
            <div className="flex justify-between items-center">
              <FormLabel>Client (Optional)</FormLabel>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onOpenClientForm}
                className="h-8 px-2 text-xs"
              >
                <PlusCircle className="h-3.5 w-3.5 mr-1" />
                Add New
              </Button>
            </div>
            <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a client" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {clients
                  .filter((client) => client.id && client.id.trim() !== "") // Filter out clients with empty IDs
                  .map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="notes"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Notes (Optional)</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Add any additional notes about the calculation"
                className="resize-none"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default BasicInfoStep;
