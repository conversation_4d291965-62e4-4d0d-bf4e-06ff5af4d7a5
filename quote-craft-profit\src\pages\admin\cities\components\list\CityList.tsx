import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Pencil, Trash2, AlertCircle, Loader2 } from "lucide-react";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { showSuccess, showError } from "@/lib/notifications";
import { deleteCity } from "@/services/shared/entities/cities";
import { City } from "@/types/types";

interface CityListProps {
  cities: City[];
  isLoading: boolean;
  isError: boolean;
  onEdit: (cityId: string) => void;
  onRefresh: () => void;
}

const CityList: React.FC<CityListProps> = ({
  cities,
  isLoading,
  isError,
  onEdit,
  onRefresh,
}) => {
  const { formatForDisplay } = useTimezoneAwareDates();
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [cityToDelete, setCityToDelete] = useState<string | null>(null);

  const handleDeleteClick = (cityId: string) => {
    setCityToDelete(cityId);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!cityToDelete) return;

    try {
      setIsDeleting(true);
      console.log("Deleting city:", cityToDelete);

      // Use the shared service to delete the city
      // Note: The backend should handle checking for package references
      await deleteCity(cityToDelete);

      console.log("City deleted successfully");
      showSuccess("City deleted successfully");
      onRefresh(); // Refresh the list
    } catch (error) {
      console.error("Error during delete operation:", error);

      // Handle specific error cases
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";

      if (
        errorMessage.includes("referenced") ||
        errorMessage.includes("constraint")
      ) {
        showError("Cannot delete city because it is used by existing packages");
      } else {
        showError("Failed to delete city: " + errorMessage);
      }
    } finally {
      setIsDeleting(false);
      setDeleteConfirmOpen(false);
      setCityToDelete(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-lg text-muted-foreground">
          Loading cities...
        </span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center p-8 border rounded-lg border-destructive/50 bg-destructive/10">
        <AlertCircle className="h-6 w-6 text-destructive" />
        <span className="ml-2 text-lg text-destructive">
          Error loading cities
        </span>
      </div>
    );
  }

  if (cities.length === 0) {
    return (
      <div className="text-center py-8 border rounded-lg">
        <p className="text-muted-foreground">
          No cities found. Create your first city using the "Add New City"
          button.
        </p>
      </div>
    );
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-16">#</TableHead>
            <TableHead>City Name</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {cities.map((city, index) => (
            <TableRow key={city.id}>
              <TableCell className="text-muted-foreground">
                {index + 1}
              </TableCell>
              <TableCell className="font-medium">{city.name}</TableCell>
              <TableCell>
                {formatForDisplay(city.created_at, "MMM d, yyyy")}
              </TableCell>
              <TableCell className="text-right space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(city.id)}
                >
                  <Pencil className="h-4 w-4 mr-1" /> Edit
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteClick(city.id)}
                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                >
                  <Trash2 className="h-4 w-4 mr-1" /> Delete
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will delete the city. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteConfirm();
              }}
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default CityList;
