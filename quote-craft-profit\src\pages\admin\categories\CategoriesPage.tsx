import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import AdminLayout from '@/components/layout/AdminLayout';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from '@/components/ui/breadcrumb';
import { getAllCategories } from '@/services/admin/categories';
import { CategoryList } from './components/list';
import { CategoryFormDialog } from './components/form';

const CategoriesPage = () => {
  const [isCategoryFormOpen, setIsCategoryFormOpen] = useState(false);
  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(null);

  // Fetch categories
  const {
    data: categories,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['categories'],
    queryFn: getAllCategories,
    meta: {
      onError: () => {
        toast.error('Failed to load categories');
      },
    },
  });

  const handleOpenNewCategoryForm = () => {
    setEditingCategoryId(null);
    setIsCategoryFormOpen(true);
  };

  const handleEditCategory = (categoryId: string) => {
    setEditingCategoryId(categoryId);
    setIsCategoryFormOpen(true);
  };

  const handleCategoryFormClose = (shouldRefresh: boolean = false) => {
    setIsCategoryFormOpen(false);
    setEditingCategoryId(null);

    if (shouldRefresh) {
      refetch();
    }
  };

  return (
    <AdminLayout title='Manage Categories'>
      {/* Breadcrumbs */}
      <Breadcrumb className='mb-4'>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href='/admin'>Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href='/admin/catalogue'>Catalogue</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Categories</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className='mb-6 text-muted-foreground'>
        Create and manage service categories to organize your packages.
      </div>

      {/* Action Bar Block */}
      <div className='mb-6'>
        <Button onClick={handleOpenNewCategoryForm}>
          <Plus className='w-4 h-4 mr-2' /> Add New Category
        </Button>
      </div>

      {/* Category List Block */}
      <CategoryList
        categories={categories || []}
        isLoading={isLoading}
        isError={isError}
        onEdit={handleEditCategory}
      />

      {/* Add/Edit Category Dialog */}
      <CategoryFormDialog
        isOpen={isCategoryFormOpen}
        onClose={handleCategoryFormClose}
        categoryId={editingCategoryId}
      />
    </AdminLayout>
  );
};

export default CategoriesPage;
