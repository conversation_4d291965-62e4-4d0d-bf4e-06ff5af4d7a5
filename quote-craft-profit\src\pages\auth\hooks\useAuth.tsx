import { useContext } from 'react';
import { AuthContext } from '@/contexts/AuthContext';
import type { AuthContextType } from '@/contexts/AuthContext';

/**
 * Custom hook to consume the AuthContext
 *
 * This hook provides access to authentication state and methods.
 * It must be used within an AuthProvider component.
 *
 * @returns The authentication context value
 * @throws Error if used outside of an AuthProvider
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
