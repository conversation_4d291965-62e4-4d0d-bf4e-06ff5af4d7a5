import React from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>R<PERSON>, ArrowLeft, Loader2 } from "lucide-react";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import MainLayout from "@/components/layout/MainLayout";
import { ClientFormDialog } from "@/pages/clients";
import { EventFormDialog } from "@/pages/events/components";
import { useQueryClient } from "@tanstack/react-query";

// Import custom components
import BasicInfoStep from "./components/new/BasicInfoStep";
import AdditionalDetailsStep from "./components/new/AdditionalDetailsStep";
import ProgressIndicator from "./components/new/ProgressIndicator";

// Import custom hooks
import { useCalculationForm } from "./hooks/core/useCalculationForm";
import { useCalculationData } from "./hooks/core/useCalculationData";
import { useEntityCreationCallbacks } from "@/hooks/useEntityCreationCallbacks";

const NewCalculationPage: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Use custom hooks
  const {
    form,
    isSubmitting,
    currentStep,
    onSubmit,
    handleNextStep,
    handlePreviousStep,
  } = useCalculationForm();

  const {
    cities,
    clients,
    events,
    venues,
    isClientFormOpen,
    isEventFormOpen,
    selectedCityId,
    setIsClientFormOpen,
    setIsEventFormOpen,
    setSelectedCityId,
  } = useCalculationData();

  // Use centralized entity creation callbacks
  const { handleClientCreated, handleEventCreated } =
    useEntityCreationCallbacks({
      form,
      clientFieldName: "client_id",
      eventFieldName: "event_id",
    });

  return (
    <MainLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
          New Calculation
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Create a new event cost calculation
        </p>
      </div>

      {/* Progress Indicator */}
      <ProgressIndicator currentStep={currentStep} />

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {currentStep === "basic-info" ? (
              <BasicInfoStep
                form={form}
                clients={clients}
                onOpenClientForm={() => setIsClientFormOpen(true)}
              />
            ) : (
              <AdditionalDetailsStep
                form={form}
                events={events}
                cities={cities}
                venues={venues}
                selectedCityId={selectedCityId}
                onOpenEventForm={() => setIsEventFormOpen(true)}
                onCityChange={(cityId) => {
                  setSelectedCityId(cityId);
                  form.setValue("venue_ids", []);
                }}
              />
            )}

            <div className="flex justify-between gap-4 pt-4">
              {currentStep === "basic-info" ? (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/calculations")}
                  >
                    Cancel
                  </Button>
                  <Button type="button" onClick={handleNextStep}>
                    Next Step
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePreviousStep}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Previous Step
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      isSubmitting || form.getValues("venue_ids").length === 0
                    }
                    onClick={() => {
                      if (form.getValues("venue_ids").length === 0) {
                        form.setError("venue_ids", {
                          type: "manual",
                          message: "At least one venue is required",
                        });
                      }
                    }}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Calculation"
                    )}
                  </Button>
                </>
              )}
            </div>
          </form>
        </Form>
      </div>

      {/* Client Form Dialog */}
      <ClientFormDialog
        open={isClientFormOpen}
        onOpenChange={(open) => {
          setIsClientFormOpen(open);
          if (!open) {
            // Refetch clients when dialog is closed
            queryClient.invalidateQueries({ queryKey: ["clients"] });
          }
        }}
        mode="create"
        onClientCreated={handleClientCreated}
      />

      {/* Event Form Dialog */}
      <EventFormDialog
        isOpen={isEventFormOpen}
        onClose={() => {
          setIsEventFormOpen(false);
          // Refetch events when dialog is closed
          queryClient.invalidateQueries({ queryKey: ["events"] });
        }}
        disableNavigation={true}
        onEventCreated={handleEventCreated}
      />
    </MainLayout>
  );
};

export default NewCalculationPage;
