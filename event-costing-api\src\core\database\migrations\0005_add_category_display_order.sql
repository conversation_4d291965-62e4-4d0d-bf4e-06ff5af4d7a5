-- Migration: Add display_order field to categories table
-- Description: This migration adds a display_order integer field to the categories table
-- and sets initial values based on alphabetical order. It also creates a trigger to
-- automatically set display_order for new categories.

-- Step 1: Add display_order column with default value
ALTER TABLE categories ADD COLUMN display_order INTEGER DEFAULT 9999;

-- Step 2: Set initial display_order based on alphabetical order
WITH ordered_categories AS (
  SELECT id, ROW_NUMBER() OVER (ORDER BY name) AS row_num
  FROM categories
  WHERE deleted_at IS NULL
)
UPDATE categories c
SET display_order = oc.row_num
FROM ordered_categories oc
WHERE c.id = oc.id;

-- Step 3: Create function to set default display_order for new categories
CREATE OR REPLACE FUNCTION set_default_category_display_order()
RETURNS TRIGGER AS $$
BEGIN
  -- If display_order is not provided, set it to the highest existing order + 1
  IF NEW.display_order IS NULL THEN
    NEW.display_order := (SELECT COALESCE(MAX(display_order), 0) + 1 FROM categories WHERE deleted_at IS NULL);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create trigger to automatically set display_order for new categories
DROP TRIGGER IF EXISTS set_category_display_order ON categories;
CREATE TRIGGER set_category_display_order
BEFORE INSERT ON categories
FOR EACH ROW
EXECUTE FUNCTION set_default_category_display_order();

-- Step 5: Add index for performance optimization
CREATE INDEX IF NOT EXISTS idx_categories_display_order ON categories(display_order) WHERE deleted_at IS NULL;

-- Step 6: Update RLS policies to include display_order
-- No changes needed for RLS policies as they are already set up for the categories table
-- and will automatically apply to the new column

-- Step 7: Add comment to the column for documentation
COMMENT ON COLUMN categories.display_order IS 'Order in which categories should be displayed in the UI. Lower values appear first.';
