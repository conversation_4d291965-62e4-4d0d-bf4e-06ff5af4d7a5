
import React, { useState } from "react";
import { CurrencyInput } from "@/components/ui/currency-input";
import { Button } from "@/components/ui/button";
import { Check, Edit, X } from "lucide-react";

interface DiscountEditorProps {
  discount: number;
  onSave: (amount: number) => void;
}

const DiscountEditor: React.FC<DiscountEditorProps> = ({ discount, onSave }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(discount.toString());

  const handleSave = () => {
    const parsedValue = parseFloat(value);
    if (!isNaN(parsedValue) && parsedValue >= 0) {
      onSave(parsedValue);
    } else {
      setValue(discount.toString());
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setValue(discount.toString());
    setIsEditing(false);
  };

  return (
    <div className="flex items-center">
      <span>Discount</span>
      {isEditing ? (
        <div className="ml-2 flex items-center">
          <CurrencyInput
            value={value}
            onChange={(numericValue) => setValue(numericValue.toString())}
            className="w-24 h-8 text-sm mr-1"
            showSymbol={false}
          />
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleSave}
          >
            <Check className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleCancel}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <Button
          onClick={() => setIsEditing(true)}
          variant="ghost"
          size="sm"
          className="ml-2 h-8 w-8 p-0"
        >
          <Edit className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
};

export default DiscountEditor;
