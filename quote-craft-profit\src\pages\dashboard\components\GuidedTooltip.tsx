import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { X, ArrowRight, ArrowLeft } from 'lucide-react';
import { useOnboarding, ONBOARDING_STEPS } from '@/contexts/OnboardingContext';

interface GuidedTooltipProps {
  step: string;
  title: string;
  description: string;
  position?: 'top' | 'right' | 'bottom' | 'left';
  onNext?: () => void;
  onPrev?: () => void;
  isFirst?: boolean;
  isLast?: boolean;
  children: React.ReactNode;
}

const GuidedTooltip: React.FC<GuidedTooltipProps> = ({
  step,
  title,
  description,
  position = 'bottom',
  onNext,
  onPrev,
  isFirst = false,
  isLast = false,
  children,
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const { isFirstTimeUser, completedSteps, markStepAsCompleted } = useOnboarding();
  
  useEffect(() => {
    // Only show the tooltip if this is a first-time user and this step hasn't been completed
    if (isFirstTimeUser && !completedSteps.includes(step)) {
      setShowTooltip(true);
    }
  }, [isFirstTimeUser, completedSteps, step]);

  const handleDismiss = () => {
    setShowTooltip(false);
    markStepAsCompleted(step);
  };

  const handleNext = () => {
    setShowTooltip(false);
    markStepAsCompleted(step);
    if (onNext) onNext();
  };

  const handlePrev = () => {
    if (onPrev) onPrev();
  };

  // Position classes for the tooltip
  const positionClasses = {
    top: 'bottom-full mb-2',
    right: 'left-full ml-2',
    bottom: 'top-full mt-2',
    left: 'right-full mr-2',
  };

  return (
    <div className="relative">
      {children}
      
      {showTooltip && (
        <div 
          className={`absolute z-50 ${positionClasses[position]} w-64 bg-white rounded-lg shadow-lg p-4 border border-gray-200 animate-fadeIn`}
        >
          <Button 
            variant="ghost" 
            size="icon" 
            className="absolute top-2 right-2 h-6 w-6" 
            onClick={handleDismiss}
          >
            <X className="h-4 w-4" />
          </Button>
          
          <h4 className="font-semibold text-sm mb-1">{title}</h4>
          <p className="text-sm text-muted-foreground mb-4">{description}</p>
          
          <div className="flex justify-between">
            {!isFirst && (
              <Button variant="outline" size="sm" onClick={handlePrev}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
            )}
            
            <div className="flex-1" />
            
            <Button size="sm" onClick={handleNext}>
              {isLast ? 'Got it' : (
                <>
                  Next
                  <ArrowRight className="h-4 w-4 ml-1" />
                </>
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GuidedTooltip;
