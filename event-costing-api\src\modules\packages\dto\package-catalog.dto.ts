import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PackageVariationDto } from './package-variation.dto';
import { CategoryDto } from '../../categories/dto/category.dto';
import { PackageFiltersDto } from './package-filters.dto';

/**
 * DTO for paginated package results
 */
export class PaginatedPackagesDto {
  @ApiProperty({
    description: 'Array of packages',
    type: [PackageVariationDto],
  })
  data: PackageVariationDto[];

  @ApiProperty({
    description: 'Total number of packages',
    example: 150,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 15,
  })
  totalPages: number;
}

/**
 * DTO for city information
 */
export class CityInfoDto {
  @ApiProperty({
    description: 'City ID',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'City name',
    example: 'Jakarta',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'City code',
    example: 'JKT',
  })
  code?: string;
}

/**
 * DTO for division information
 */
export class DivisionInfoDto {
  @ApiProperty({
    description: 'Division ID',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Division name',
    example: 'Audio Visual',
  })
  name: string;

  @ApiProperty({
    description: 'Division code',
    example: 'AV',
  })
  code: string;
}

/**
 * DTO for currency information
 */
export class CurrencyInfoDto {
  @ApiProperty({
    description: 'Currency ID',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Currency code',
    example: 'IDR',
  })
  code: string;

  @ApiProperty({
    description: 'Currency symbol',
    example: 'Rp',
  })
  symbol: string;

  @ApiProperty({
    description: 'Currency name',
    example: 'Indonesian Rupiah',
  })
  name: string;
}

/**
 * DTO for available filter options
 */
export class AvailableFiltersDto {
  @ApiProperty({
    description: 'Available categories for filtering',
    type: [Object],
  })
  categories: Array<{ id: string; name: string }>;

  @ApiProperty({
    description: 'Available cities for filtering',
    type: [Object],
  })
  cities: Array<{ id: string; name: string }>;

  @ApiProperty({
    description: 'Available divisions for filtering',
    type: [Object],
  })
  divisions: Array<{ id: string; name: string }>;
}

/**
 * DTO for filter information
 */
export class FilterInfoDto {
  @ApiProperty({
    description: 'Applied filters',
    type: PackageFiltersDto,
  })
  applied: PackageFiltersDto;

  @ApiProperty({
    description: 'Available filter options',
    type: AvailableFiltersDto,
  })
  available: AvailableFiltersDto;
}

/**
 * DTO for package catalog metadata
 */
export class PackageCatalogMetadataDto {
  @ApiProperty({
    description: 'Time taken to load the data in milliseconds',
    example: 350,
  })
  loadTime: number;

  @ApiProperty({
    description: 'Cache version for the response',
    example: '1.0',
  })
  cacheVersion: string;

  @ApiProperty({
    description: 'User ID who requested the data',
    type: String,
    format: 'uuid',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Any errors encountered during data loading',
    type: [String],
    example: [],
  })
  errors?: string[];

  @ApiProperty({
    description: 'Timestamp when the data was loaded',
    type: String,
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Total number of packages in the catalog',
    example: 150,
  })
  totalPackages: number;

  @ApiProperty({
    description: 'Number of filters applied',
    example: 2,
  })
  appliedFilters: number;
}

/**
 * Complete package catalog response DTO
 * Consolidates all package catalog-related data in a single response
 */
export class PackageCatalogDto {
  @ApiProperty({
    description: 'Paginated package results',
    type: PaginatedPackagesDto,
  })
  packages: PaginatedPackagesDto;

  @ApiProperty({
    description: 'All available categories',
    type: [CategoryDto],
  })
  categories: CategoryDto[];

  @ApiProperty({
    description: 'All available cities',
    type: [CityInfoDto],
  })
  cities: CityInfoDto[];

  @ApiProperty({
    description: 'All available divisions',
    type: [DivisionInfoDto],
  })
  divisions: DivisionInfoDto[];

  @ApiProperty({
    description: 'All available currencies',
    type: [CurrencyInfoDto],
  })
  currencies: CurrencyInfoDto[];

  @ApiProperty({
    description: 'Filter information',
    type: FilterInfoDto,
  })
  filters: FilterInfoDto;

  @ApiProperty({
    description: 'Metadata about the response',
    type: PackageCatalogMetadataDto,
  })
  metadata: PackageCatalogMetadataDto;
}
