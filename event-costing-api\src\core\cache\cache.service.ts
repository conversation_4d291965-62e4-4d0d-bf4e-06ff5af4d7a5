import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

export interface CacheInvalidationEvent {
  domain: string;
  id: string;
  action: 'create' | 'update' | 'delete';
  relatedKeys?: string[];
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  lastReset: Date;
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
    lastReset: new Date(),
  };

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  /**
   * Get a value from cache with metrics tracking
   * @param key - The cache key
   * @returns The cached value or null if not found
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.cacheManager.get<T>(key);

      // Track metrics
      if (value !== null && value !== undefined) {
        this.metrics.hits++;
        this.logger.debug(`Cache hit for key: ${key}`);
      } else {
        this.metrics.misses++;
        this.logger.debug(`Cache miss for key: ${key}`);
      }

      return value;
    } catch (error: unknown) {
      this.metrics.errors++;
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error getting cache key ${key}: ${errorMessage}`,
        errorStack,
      );
      return null;
    }
  }

  /**
   * Set a value in cache with metrics tracking
   * @param key - The cache key
   * @param value - The value to cache
   * @param ttl - Time to live in seconds (optional)
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl ? ttl : undefined);
      this.metrics.sets++;
      this.logger.verbose(
        `Cache set for key: ${key}${ttl ? ` with TTL: ${ttl}s` : ''}`,
      );
    } catch (error: unknown) {
      this.metrics.errors++;
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error setting cache key ${key}: ${errorMessage}`,
        errorStack,
      );
    }
  }

  /**
   * Delete a value from cache with metrics tracking
   * @param key - The cache key to delete
   */
  async delete(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
      this.metrics.deletes++;
      this.logger.verbose(`Cache deleted for key: ${key}`);
    } catch (error: unknown) {
      this.metrics.errors++;
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error deleting cache key ${key}: ${errorMessage}`,
        errorStack,
      );
    }
  }

  /**
   * Clear the entire cache
   */
  async clear(): Promise<void> {
    try {
      await this.cacheManager.clear();
      this.logger.log('Cache cleared');
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error clearing cache: ${errorMessage}`, errorStack);
    }
  }

  /**
   * Get or set cache value (convenience method)
   * @param key - The cache key
   * @param factory - Function to generate value if not in cache
   * @param ttl - Time to live in seconds (optional)
   * @returns The cached or newly generated value
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    const cachedValue = await this.get<T>(key);

    if (cachedValue !== null && cachedValue !== undefined) {
      return cachedValue;
    }

    this.logger.debug(`Cache miss for key: ${key}, fetching data...`);
    const value = await factory();
    await this.set(key, value, ttl);
    return value;
  }

  /**
   * PHASE 2: Cache invalidation with relationship awareness
   * Invalidates cache and emits events for frontend coordination
   */
  async invalidateWithRelationships(
    domain: string,
    id: string,
    action: 'create' | 'update' | 'delete',
    relatedKeys: string[] = [],
  ): Promise<void> {
    this.logger.log(
      `Invalidating cache for ${domain}:${id} (${action}) with ${relatedKeys.length} related keys`,
    );

    // Delete specific cache keys
    const keysToDelete = [
      `${domain}:${id}`,
      `${domain}:all`,
      `${domain}:list:*`,
      ...relatedKeys,
    ];

    // Delete cache keys with pattern matching
    for (const keyPattern of keysToDelete) {
      if (keyPattern.includes('*')) {
        await this.deleteByPattern(keyPattern);
      } else {
        await this.delete(keyPattern);
      }
    }

    // Log cache invalidation completion
    this.logger.debug(
      `Cache invalidation completed for ${domain}:${id} (${action}) with ${relatedKeys.length} related keys`,
    );
  }

  /**
   * PHASE 2: Delete cache keys by pattern
   * Supports wildcard patterns for bulk invalidation
   */
  async deleteByPattern(pattern: string): Promise<void> {
    try {
      // For Redis, we can use KEYS command (note: not recommended for production with large datasets)
      // For memory cache, we need to iterate through all keys

      // This is a simplified implementation - in production, consider using Redis SCAN
      const keys = await this.getKeysByPattern(pattern);

      for (const key of keys) {
        await this.delete(key);
      }

      this.logger.verbose(
        `Deleted ${keys.length} keys matching pattern: ${pattern}`,
      );
    } catch (error: unknown) {
      this.metrics.errors++;
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error deleting keys by pattern ${pattern}: ${errorMessage}`,
      );
    }
  }

  /**
   * PHASE 2: Get cache keys by pattern (implementation depends on cache store)
   */
  private async getKeysByPattern(pattern: string): Promise<string[]> {
    // This is a simplified implementation
    // In a real Redis implementation, you would use SCAN command
    // For memory cache, this would need to be implemented differently

    try {
      // For now, return empty array as pattern matching is cache-store specific
      this.logger.debug(
        `Pattern matching not implemented for pattern: ${pattern}`,
      );
      return [];
    } catch (error) {
      this.logger.error(`Error getting keys by pattern ${pattern}:`, error);
      return [];
    }
  }

  /**
   * PHASE 2: Cache warming for frequently accessed data
   */
  async warmCache(
    warmingConfig: Array<{
      key: string;
      factory: () => Promise<any>;
      ttl?: number;
      priority?: number;
    }>,
  ): Promise<void> {
    this.logger.log(`Warming cache with ${warmingConfig.length} entries`);

    // Sort by priority (higher priority first)
    const sortedConfig = warmingConfig.sort(
      (a, b) => (b.priority || 0) - (a.priority || 0),
    );

    // Warm cache entries in parallel (with concurrency limit)
    const concurrencyLimit = 5;
    const chunks: (typeof sortedConfig)[] = [];

    for (let i = 0; i < sortedConfig.length; i += concurrencyLimit) {
      chunks.push(sortedConfig.slice(i, i + concurrencyLimit));
    }

    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(async config => {
          try {
            const existingValue = await this.get(config.key);
            if (existingValue === null || existingValue === undefined) {
              const value = await config.factory();
              await this.set(config.key, value, config.ttl);
              this.logger.debug(`Cache warmed for key: ${config.key}`);
            }
          } catch (error) {
            this.logger.error(
              `Error warming cache for key ${config.key}:`,
              error,
            );
          }
        }),
      );
    }

    this.logger.log('Cache warming completed');
  }

  /**
   * PHASE 2: Get cache metrics for monitoring
   */
  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  /**
   * PHASE 2: Reset cache metrics
   */
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      lastReset: new Date(),
    };
    this.logger.log('Cache metrics reset');
  }

  /**
   * PHASE 2: Get cache health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    hitRate: number;
    errorRate: number;
    metrics: CacheMetrics;
  } {
    const totalOperations = this.metrics.hits + this.metrics.misses;
    const hitRate =
      totalOperations > 0 ? (this.metrics.hits / totalOperations) * 100 : 0;
    const errorRate =
      totalOperations > 0 ? (this.metrics.errors / totalOperations) * 100 : 0;

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    if (errorRate > 10 || hitRate < 30) {
      status = 'unhealthy';
    } else if (errorRate > 5 || hitRate < 60) {
      status = 'degraded';
    }

    return {
      status,
      hitRate: Math.round(hitRate * 100) / 100,
      errorRate: Math.round(errorRate * 100) / 100,
      metrics: this.getMetrics(),
    };
  }
}
