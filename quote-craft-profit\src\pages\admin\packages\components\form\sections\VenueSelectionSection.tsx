import React, { useEffect, useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PackageFormValues } from "../../../types/package";
import { FormSection } from "../../shared";
import { useVenuesForCities } from "../../../hooks/useVenuesByCity";

interface Venue {
  id: string;
  name: string;
  city_id: string;
  city_name?: string;
}

interface City {
  id: string;
  name: string;
}

interface VenueSelectionSectionProps {
  form: UseFormReturn<PackageFormValues>;
  cities: City[];
}

export const VenueSelectionSection: React.FC<VenueSelectionSectionProps> = ({
  form,
  cities,
}) => {
  // Watch for changes to cityIds and enableVenues
  const watchedCityIds = form.watch("cityIds");
  const watchedEnableVenues = form.watch("enableVenues");

  // Memoize cityIds to prevent unnecessary re-renders
  const memoizedCityIds = useMemo(() => {
    return Array.isArray(watchedCityIds) ? watchedCityIds : [];
  }, [watchedCityIds]);

  // Use optimized hook for fetching venues with caching
  const {
    venues: availableVenues,
    isLoading: isLoadingVenues,
    isError: isVenuesError,
  } = useVenuesForCities(
    memoizedCityIds,
    cities,
    watchedEnableVenues && memoizedCityIds.length > 0
  );

  // Clear venue selection when venues are disabled
  useEffect(() => {
    if (!watchedEnableVenues) {
      form.setValue("venueIds", []);
    }
  }, [watchedEnableVenues, form]);

  return (
    <FormSection title="Venue Exclusive" stepNumber={4}>
      <FormField
        control={form.control}
        name="enableVenues"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border dark:border-gray-700 p-3 shadow-sm">
            <div className="space-y-0.5">
              <FormLabel>Enable Venue Restrictions</FormLabel>
              <FormDescription>
                Limit this package to specific venues in the selected cities
              </FormDescription>
            </div>
            <FormControl>
              <Switch checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
          </FormItem>
        )}
      />

      {watchedEnableVenues && (
        <div className="space-y-4">
          {isLoadingVenues ? (
            <div className="text-center py-4 dark:text-gray-300">
              Loading venues...
            </div>
          ) : isVenuesError ? (
            <div className="text-center py-4 text-red-600 dark:text-red-400">
              Error loading venues. Please try again.
            </div>
          ) : availableVenues.length === 0 ? (
            <div className="text-center py-4 text-amber-600 dark:text-amber-400">
              No venues available for the selected cities. Please select at
              least one city first.
            </div>
          ) : (
            <FormField
              control={form.control}
              name="venueIds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Select Venues</FormLabel>
                  <FormDescription>
                    Choose which venues this package is available at - At least
                    one venue is required when venue restrictions are enabled
                  </FormDescription>
                  <FormControl>
                    <ScrollArea className="h-[200px] rounded-md border dark:border-gray-700 p-4">
                      <div className="space-y-2">
                        {availableVenues.map((venue) => (
                          <div
                            key={venue.id}
                            className="flex items-start space-x-2"
                          >
                            <Checkbox
                              id={`venue-${venue.id}`}
                              checked={field.value?.includes(venue.id)}
                              onCheckedChange={(checked) => {
                                const updatedVenues = checked
                                  ? [...(field.value || []), venue.id]
                                  : (field.value || []).filter(
                                      (id) => id !== venue.id
                                    );
                                field.onChange(updatedVenues);
                              }}
                            />
                            <div className="grid gap-1.5 leading-none">
                              <Label
                                htmlFor={`venue-${venue.id}`}
                                className="font-medium dark:text-gray-200"
                              >
                                {venue.name}
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                {venue.city_name}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
      )}
    </FormSection>
  );
};
