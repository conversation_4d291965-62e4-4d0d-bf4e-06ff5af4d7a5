/**
 * Cache Monitoring Service
 *
 * PHASE 2: Provides comprehensive cache performance monitoring and alerting
 * Tracks cache health, performance metrics, and triggers alerts for degradation
 */

import { Injectable, Logger } from '@nestjs/common';
import {
  CacheService,
  CacheMetrics,
  CacheInvalidationEvent,
} from './cache.service';

export interface CacheAlert {
  id: string;
  type: 'performance' | 'error' | 'capacity' | 'health';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  metrics?: any;
  resolved?: boolean;
}

export interface CachePerformanceReport {
  timestamp: Date;
  period: string;
  metrics: CacheMetrics;
  health: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    hitRate: number;
    errorRate: number;
  };
  alerts: CacheAlert[];
  recommendations: string[];
}

@Injectable()
export class CacheMonitoringService {
  private readonly logger = new Logger(CacheMonitoringService.name);
  private alerts: CacheAlert[] = [];
  private performanceHistory: CachePerformanceReport[] = [];
  private readonly maxHistorySize = 100; // Keep last 100 reports
  private readonly maxAlertsSize = 50; // Keep last 50 alerts

  constructor(private readonly cacheService: CacheService) {}

  /**
   * Monitor cache performance every 5 minutes
   * Note: Cron functionality would need @nestjs/schedule package
   */
  async monitorCachePerformance(): Promise<void> {
    try {
      const metrics = this.cacheService.getMetrics();
      const health = this.cacheService.getHealthStatus();

      // Generate performance report
      const report: CachePerformanceReport = {
        timestamp: new Date(),
        period: '5min',
        metrics,
        health,
        alerts: [],
        recommendations: this.generateRecommendations(metrics, health),
      };

      // Check for performance issues and generate alerts
      const newAlerts = this.checkForAlerts(metrics, health);
      report.alerts = newAlerts;

      // Add alerts to the global alerts list
      this.alerts.push(...newAlerts);
      this.trimAlerts();

      // Add report to history
      this.performanceHistory.push(report);
      this.trimHistory();

      // Log performance summary
      this.logger.debug(
        `Cache Performance - Hit Rate: ${health.hitRate}%, Error Rate: ${health.errorRate}%, Status: ${health.status}`,
      );

      // Log performance event for monitoring
      this.logger.debug('Cache performance report generated', { report });

      // Handle critical alerts
      if (newAlerts.some(alert => alert.severity === 'critical')) {
        this.handleCriticalAlerts(
          newAlerts.filter(alert => alert.severity === 'critical'),
        );
      }
    } catch (error) {
      this.logger.error('Error monitoring cache performance:', error);
    }
  }

  /**
   * Listen for cache invalidation events
   */
  handleCacheInvalidation(event: CacheInvalidationEvent): void {
    this.logger.debug(
      `Cache invalidation detected: ${event.domain}:${event.id} (${event.action})`,
    );

    // Track invalidation patterns for analysis
    // Could be used to detect excessive invalidations
  }

  /**
   * Check for performance issues and generate alerts
   */
  private checkForAlerts(metrics: CacheMetrics, health: any): CacheAlert[] {
    const alerts: CacheAlert[] = [];
    const now = new Date();

    // Check hit rate
    if (health.hitRate < 30) {
      alerts.push({
        id: `hit-rate-${now.getTime()}`,
        type: 'performance',
        severity: 'critical',
        message: `Cache hit rate critically low: ${health.hitRate}%`,
        timestamp: now,
        metrics: { hitRate: health.hitRate },
      });
    } else if (health.hitRate < 60) {
      alerts.push({
        id: `hit-rate-${now.getTime()}`,
        type: 'performance',
        severity: 'medium',
        message: `Cache hit rate below optimal: ${health.hitRate}%`,
        timestamp: now,
        metrics: { hitRate: health.hitRate },
      });
    }

    // Check error rate
    if (health.errorRate > 10) {
      alerts.push({
        id: `error-rate-${now.getTime()}`,
        type: 'error',
        severity: 'critical',
        message: `Cache error rate critically high: ${health.errorRate}%`,
        timestamp: now,
        metrics: { errorRate: health.errorRate },
      });
    } else if (health.errorRate > 5) {
      alerts.push({
        id: `error-rate-${now.getTime()}`,
        type: 'error',
        severity: 'high',
        message: `Cache error rate elevated: ${health.errorRate}%`,
        timestamp: now,
        metrics: { errorRate: health.errorRate },
      });
    }

    // Check for excessive operations (potential memory issues)
    const totalOps =
      metrics.hits + metrics.misses + metrics.sets + metrics.deletes;
    if (totalOps > 10000) {
      // Threshold for high activity
      alerts.push({
        id: `high-activity-${now.getTime()}`,
        type: 'capacity',
        severity: 'medium',
        message: `High cache activity detected: ${totalOps} operations`,
        timestamp: now,
        metrics: { totalOperations: totalOps },
      });
    }

    return alerts;
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    metrics: CacheMetrics,
    health: any,
  ): string[] {
    const recommendations: string[] = [];

    if (health.hitRate < 70) {
      recommendations.push(
        'Consider increasing TTL for frequently accessed data',
      );
      recommendations.push('Review cache key strategies for better hit rates');
    }

    if (health.errorRate > 2) {
      recommendations.push('Investigate cache connection issues');
      recommendations.push('Review error handling in cache operations');
    }

    const totalOps = metrics.hits + metrics.misses;
    if (totalOps > 0 && metrics.misses / totalOps > 0.5) {
      recommendations.push(
        'High cache miss rate - consider cache warming strategies',
      );
    }

    if (metrics.deletes > metrics.sets * 0.8) {
      recommendations.push(
        'High cache invalidation rate - review invalidation strategy',
      );
    }

    return recommendations;
  }

  /**
   * Handle critical alerts
   */
  private handleCriticalAlerts(criticalAlerts: CacheAlert[]): void {
    for (const alert of criticalAlerts) {
      this.logger.error(`CRITICAL CACHE ALERT: ${alert.message}`);

      // Log critical alert for external monitoring systems
      this.logger.error('CRITICAL CACHE ALERT EMITTED', { alert });

      // Could integrate with external alerting systems here
      // e.g., Slack, email, PagerDuty, etc.
    }
  }

  /**
   * Get current cache status
   */
  getCacheStatus(): {
    health: any;
    metrics: CacheMetrics;
    activeAlerts: CacheAlert[];
    recentRecommendations: string[];
  } {
    const health = this.cacheService.getHealthStatus();
    const metrics = this.cacheService.getMetrics();
    const activeAlerts = this.alerts.filter(alert => !alert.resolved);

    const recentRecommendations = this.performanceHistory
      .slice(-5) // Last 5 reports
      .flatMap(report => report.recommendations)
      .filter((rec, index, arr) => arr.indexOf(rec) === index); // Unique recommendations

    return {
      health,
      metrics,
      activeAlerts,
      recentRecommendations,
    };
  }

  /**
   * Get performance history
   */
  getPerformanceHistory(limit: number = 20): CachePerformanceReport[] {
    return this.performanceHistory.slice(-limit);
  }

  /**
   * Get alerts history
   */
  getAlertsHistory(limit: number = 20): CacheAlert[] {
    return this.alerts.slice(-limit);
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.logger.log(`Alert resolved: ${alertId}`);
      return true;
    }
    return false;
  }

  /**
   * Generate performance dashboard data
   */
  getDashboardData(): {
    currentHealth: any;
    metrics: CacheMetrics;
    trends: {
      hitRateTrend: number[];
      errorRateTrend: number[];
      operationsTrend: number[];
    };
    alerts: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    };
  } {
    const currentHealth = this.cacheService.getHealthStatus();
    const metrics = this.cacheService.getMetrics();

    // Calculate trends from recent history
    const recentReports = this.performanceHistory.slice(-10);
    const hitRateTrend = recentReports.map(r => r.health.hitRate);
    const errorRateTrend = recentReports.map(r => r.health.errorRate);
    const operationsTrend = recentReports.map(
      r =>
        r.metrics.hits + r.metrics.misses + r.metrics.sets + r.metrics.deletes,
    );

    // Count alerts by severity
    const activeAlerts = this.alerts.filter(a => !a.resolved);
    const alertCounts = {
      critical: activeAlerts.filter(a => a.severity === 'critical').length,
      high: activeAlerts.filter(a => a.severity === 'high').length,
      medium: activeAlerts.filter(a => a.severity === 'medium').length,
      low: activeAlerts.filter(a => a.severity === 'low').length,
    };

    return {
      currentHealth,
      metrics,
      trends: {
        hitRateTrend,
        errorRateTrend,
        operationsTrend,
      },
      alerts: alertCounts,
    };
  }

  /**
   * Trim alerts history to prevent memory issues
   */
  private trimAlerts(): void {
    if (this.alerts.length > this.maxAlertsSize) {
      this.alerts = this.alerts.slice(-this.maxAlertsSize);
    }
  }

  /**
   * Trim performance history to prevent memory issues
   */
  private trimHistory(): void {
    if (this.performanceHistory.length > this.maxHistorySize) {
      this.performanceHistory = this.performanceHistory.slice(
        -this.maxHistorySize,
      );
    }
  }

  /**
   * Reset all monitoring data
   */
  resetMonitoringData(): void {
    this.alerts = [];
    this.performanceHistory = [];
    this.cacheService.resetMetrics();
    this.logger.log('Cache monitoring data reset');
  }
}
