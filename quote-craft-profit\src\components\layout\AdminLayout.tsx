import React, { useMemo } from "react";
import AdminNavbar from "./AdminNavbar";
import { Toaster } from "@/components/ui/sonner";
import { useLocation } from "react-router-dom";
import Breadcrumbs, { BreadcrumbItem } from "@/components/ui/breadcrumbs";
import { ShieldCheck } from "lucide-react";

interface AdminLayoutProps {
  children: React.ReactNode;
  title: string;
  breadcrumbs?: BreadcrumbItem[];
}

const AdminLayout: React.FC<AdminLayoutProps> = ({
  children,
  title,
  breadcrumbs = [],
}) => {
  const location = useLocation();

  // Generate default breadcrumbs based on the current path if none are provided
  // Use useMemo to prevent recalculation on every render
  const finalBreadcrumbs = useMemo(() => {
    if (breadcrumbs.length > 0) {
      return breadcrumbs;
    }

    const defaultBreadcrumbs: BreadcrumbItem[] = [];

    // Always add Admin as the first breadcrumb
    defaultBreadcrumbs.push({
      label: "Admin",
      href: "/admin",
      icon: <ShieldCheck className="h-4 w-4" />,
    });

    // Add additional breadcrumbs based on the current path
    const pathSegments = location.pathname.split("/").filter(Boolean);
    if (pathSegments.length > 1) {
      // Skip the first segment (admin) as it's already added
      let currentPath = "/admin";

      for (let i = 1; i < pathSegments.length; i++) {
        const segment = pathSegments[i];
        currentPath += `/${segment}`;

        // Format the segment for display (capitalize first letter, replace hyphens with spaces)
        const formattedSegment = segment
          .replace(/-/g, " ")
          .replace(/\b\w/g, (char) => char.toUpperCase());

        defaultBreadcrumbs.push({
          label: formattedSegment,
          href: currentPath,
        });
      }
    }

    return defaultBreadcrumbs;
  }, [breadcrumbs, location.pathname]);

  // No need for authentication or role checks here
  // AdminProtectedRoute already handles that

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <AdminNavbar />
      <main className="container mx-auto py-8 px-4">
        <div className="mb-6">
          <Breadcrumbs items={finalBreadcrumbs} className="mb-2" homeHref="/" />
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
            {title}
          </h1>
        </div>
        {children}
      </main>
      <Toaster position="top-right" />
    </div>
  );
};

export default AdminLayout;
