import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { EventFormDialog } from './components';

const NewEventPage: React.FC = () => {
  const navigate = useNavigate();
  const [isFormOpen, setIsFormOpen] = useState(true);

  // Close form and navigate back to events page
  const handleClose = () => {
    setIsFormOpen(false);
    navigate('/events');
  };

  return (
    <MainLayout>
      <EventFormDialog isOpen={isFormOpen} onClose={handleClose} />
    </MainLayout>
  );
};

export default NewEventPage;
