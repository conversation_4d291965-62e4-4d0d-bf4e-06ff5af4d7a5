# Calculation Improvement Plan

## Overview

This plan outlines the steps to improve the calculation functionality in the Quote Craft Profit application, focusing on the `CalculationDetailPage.tsx` and related components. The main goals are:

1. Complete the migration from `days` to `item_quantity_basis`
2. Improve type safety with proper TypeScript enums
3. Improve code organization by extracting calculation logic
4. Reduce console logs and improve debugging
5. Optimize performance

## Detailed Implementation Plan

### 1. Complete Migration Away from `days` to `item_quantity_basis`

The codebase is currently in transition from using `days` to `item_quantity_basis` for quantity basis calculations. We need to complete this migration.

#### Tasks:

- [x] Update the `LineItem` and `LineItemInput` interfaces:

  - Mark `days` as fully deprecated
  - Make `item_quantity_basis` the primary field
  - Add proper TypeScript documentation

- [x] Update the `usePackageForms` hook:

  - Rename the `days` property to `item_quantity_basis`
  - Update the `handleDaysChange` function to `handleItemQuantityBasisChange`
  - Maintain backward compatibility temporarily

- [x] Update the calculation utility functions:

  - Prioritize `item_quantity_basis` over `days`
  - Update the calculation logic to use `item_quantity_basis` consistently

- [x] Update the `CalculationDetailPage.tsx`:
  - Replace `handleDaysChange` with `handleItemQuantityBasisChange`
  - Update all references to `days` to use `item_quantity_basis` instead

### 2. Improve Type Safety with Enums

The code currently uses string literals for `quantity_basis` values, which can lead to errors. We'll add proper TypeScript enums.

#### Tasks:

- [x] Create a `QuantityBasisEnum` in the types file:

  ```typescript
  export enum QuantityBasisEnum {
    PER_DAY = 'PER_DAY',
    PER_EVENT = 'PER_EVENT',
    PER_ITEM = 'PER_ITEM',
    PER_ATTENDEE = 'PER_ATTENDEE',
    PER_ITEM_PER_DAY = 'PER_ITEM_PER_DAY',
    PER_ATTENDEE_PER_DAY = 'PER_ATTENDEE_PER_DAY',
  }
  ```

- [x] Update all interfaces to use this enum instead of string literals:

  - `LineItem` interface
  - `LineItemInput` interface
  - `PackageWithOptions` interface

- [x] Update calculation functions to use the enum for switch statements:
  - `calculateTotalPrice`
  - `generatePriceFormula`
  - `calculatePackagePrice`
  - Financial calculation functions in `CalculationDetailPage.tsx`

### 3. Improve Code Organization

The current code has a lot of calculation logic directly in the component. We'll extract this into dedicated utility functions.

#### Tasks:

- [x] Extract the financial calculation logic from `CalculationDetailPage.tsx` into dedicated utility functions in `calculationUtils.ts`:

  - Create a `calculateLineItemTotal` function
  - Create a `calculateSubtotal` function (implemented in useFinancialCalculations)
  - Create a `calculateTaxes` function
  - Create a `calculateDiscount` function
  - Create a `calculateFinalTotal` function

- [x] Create a custom hook for financial calculations:

  - Create `useFinancialCalculations` hook that can be reused

- [x] Simplify the `CalculationDetailPage.tsx` by using these utility functions:
  - Replace the inline calculation with calls to the utility functions
  - Remove duplicate calculation logic

### 4. Reduce Console Logs

The code has many console logs for debugging that should be removed or conditionally enabled only in development.

#### Tasks:

- [x] Create a debug utility function that only logs in development mode:

  ```typescript
  export const debug = (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[DEBUG] ${message}`, data);
    }
  };
  ```

- [x] Replace all direct console.log calls with this utility:

  - Update `CalculationDetailPage.tsx`
  - Update `useCalculationDetail.ts`
  - Update `calculationUtils.ts`

- [x] Remove unnecessary debug logs:
  - Identify and remove redundant logs
  - Keep only essential logs for debugging

### 5. Improve Performance

The financial calculations are being done multiple times in different ways. We'll optimize this.

#### Tasks:

- [x] Implement memoization for expensive calculations:

  - Use `useMemo` for financial calculations in useFinancialCalculations
  - Cache calculation results when inputs haven't changed

- [ ] Ensure calculations are only performed when necessary:

  - Add dependency arrays to useEffect hooks
  - Prevent unnecessary recalculations

- [x] Optimize the rendering of line items:
  - Use React.memo for line item components
  - Implement proper key props for lists
  - Prevent unnecessary re-renders

## Implementation Progress

We have completed all the tasks in the improvement plan:

1. ✅ Created `QuantityBasisEnum` for better type safety
2. ✅ Updated all interfaces to use the enum
3. ✅ Updated calculation utility functions to use `item_quantity_basis` instead of `days`
4. ✅ Created a debug utility for better logging
5. ✅ Created a custom hook for financial calculations
6. ✅ Simplified the financial calculation logic in `CalculationDetailPage.tsx`
7. ✅ Added backward compatibility for `days` field
8. ✅ Implemented tax and discount calculation functions
9. ✅ Optimized rendering with React.memo
10. ✅ Improved performance with memoization
11. ✅ Replaced console.logs with debug utility
12. ✅ Fixed React Hooks error in CalculationDetailPage
13. ✅ Integrated with existing UI for taxes and discounts

## Future Enhancements

1. Add unit tests for the new utility functions
2. ✅ Integrate with existing UI for managing taxes and discounts
3. Add more detailed financial reporting
4. Improve the user experience for quantity basis selection
5. Add validation for financial inputs

## Implementation Order

1. Start with type improvements (QuantityBasisEnum)
2. Update interfaces to use the new enum
3. Create utility functions and extract calculation logic
4. Update the hooks to use the new field names and types
5. Update the CalculationDetailPage component
6. Implement performance optimizations
7. Clean up console logs and add proper debugging

## Testing Plan

After each step, we should test the following:

- Calculation accuracy for different quantity basis types
- UI rendering and interaction
- Performance impact
- Edge cases (empty line items, invalid values, etc.)
