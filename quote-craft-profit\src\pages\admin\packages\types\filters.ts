/**
 * Interface for package filters
 */
export interface PackageFilters {
  search?: string;
  categoryId?: string;
  divisionId?: string;
  cityId?: string;
  venueIds?: string[]; // Array for multiple venues
  excludeId?: string; // Exclude a specific package ID
  showDeleted?: boolean; // Whether to include deleted packages
  page?: number; // Current page number (1-based)
  pageSize?: number; // Number of items per page
  sortBy?: string; // Field to sort by
  sortOrder?: 'asc' | 'desc'; // Sort order
}
