import {
  PackageDependency,
  PackageDependencyDisplay,
  SavePackageDependencyData,
} from '../../../pages/admin/packages/types/packageDependencies';
import {
  getPackageDependenciesFromApi,
  getPackageDependencyByIdFromApi,
  savePackageDependencyWithApi,
  deletePackageDependencyWithApi,
  checkDependencyExistsWithApi,
} from './packageDependencyApiService';

/**
 * Get all dependencies for a package
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageId - The package ID
 * @returns Promise resolving to an array of package dependencies
 */
export const getPackageDependencies = async (
  packageId: string,
): Promise<PackageDependencyDisplay[]> => {
  try {
    // Use the API service to fetch package dependencies
    return await getPackageDependenciesFromApi(packageId);
  } catch (error) {
    console.error(`Error in getPackageDependencies for package ID ${packageId}:`, error);
    return [];
  }
};

/**
 * Get a single package dependency by ID
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageId - The package ID
 * @param dependencyId - The dependency ID
 * @returns Promise resolving to a package dependency or null
 */
export const getPackageDependencyById = async (
  packageId: string,
  dependencyId: string,
): Promise<PackageDependencyDisplay | null> => {
  try {
    // Use the API service to fetch the package dependency
    return await getPackageDependencyByIdFromApi(packageId, dependencyId);
  } catch (error) {
    console.error(`Error in getPackageDependencyById for ID ${dependencyId}:`, error);
    return null;
  }
};

/**
 * Create or update a package dependency
 * This function now uses the backend API instead of direct Supabase calls
 * @param dependencyData - The dependency data to save
 * @returns Promise resolving to the saved dependency
 */
export const savePackageDependency = async (
  dependencyData: SavePackageDependencyData,
): Promise<PackageDependency> => {
  try {
    // Use the API service to save the package dependency
    return await savePackageDependencyWithApi(dependencyData);
  } catch (error) {
    console.error('Error in savePackageDependency:', error);
    throw error;
  }
};

/**
 * Delete a package dependency
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageId - The package ID
 * @param dependencyId - The dependency ID to delete
 * @returns Promise resolving to void
 */
export const deletePackageDependency = async (
  packageId: string,
  dependencyId: string,
): Promise<void> => {
  try {
    // Use the API service to delete the package dependency
    await deletePackageDependencyWithApi(packageId, dependencyId);
  } catch (error) {
    console.error(`Error in deletePackageDependency for ID ${dependencyId}:`, error);
    throw error;
  }
};

/**
 * Check if a dependency already exists between two packages
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageId - The package ID
 * @param dependentPackageId - The dependent package ID
 * @returns Promise resolving to a boolean indicating if the dependency exists
 */
export const checkDependencyExists = async (
  packageId: string,
  dependentPackageId: string,
): Promise<boolean> => {
  try {
    // Use the API service to check if the dependency exists
    return await checkDependencyExistsWithApi(packageId, dependentPackageId);
  } catch (error) {
    console.error(
      `Error in checkDependencyExists between packages ${packageId} and ${dependentPackageId}:`,
      error,
    );
    return false;
  }
};
