import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface PackageListHeaderProps {
  onAddPackage: () => void;
  totalCount: number;
}

export const PackageListHeader: React.FC<PackageListHeaderProps> = ({
  onAddPackage,
  totalCount,
}) => {
  return (
    <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6'>
      <div>
        <h1 className='text-2xl font-bold tracking-tight'>Packages</h1>
        <p className='text-muted-foreground'>
          {totalCount} {totalCount === 1 ? 'package' : 'packages'} available
        </p>
      </div>
      <Button onClick={onAddPackage} className='mt-4 sm:mt-0'>
        <Plus className='h-4 w-4 mr-2' />
        Add Package
      </Button>
    </div>
  );
};
