{"version": 3, "file": "cache-warming.service.js", "sourceRoot": "", "sources": ["../../../src/core/cache/cache-warming.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAOA,2CAAkE;AAClE,mDAA+C;AAC/C,mEAA+D;AAYxD,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAKX;IACA;IALF,MAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IACvD,cAAc,GAAyB,EAAE,CAAC;IAElD,YACmB,YAA0B,EAC1B,eAAgC;QADhC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,oBAAe,GAAf,eAAe,CAAiB;IAChD,CAAC;IAEJ,KAAK,CAAC,YAAY;QAEhB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAGhC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAKO,wBAAwB;QAC9B,IAAI,CAAC,cAAc,GAAG;YAEpB;gBACE,GAAG,EAAE,gBAAgB;gBACrB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE;gBACpC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;gBAChB,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,IAAI;aACd;YAGD;gBACE,GAAG,EAAE,YAAY;gBACjB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE;gBAChC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;gBACjB,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,cAAc;gBACxB,OAAO,EAAE,IAAI;aACd;YAGD;gBACE,GAAG,EAAE,gBAAgB;gBACrB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE;gBACpC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;gBACjB,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,WAAW;gBACrB,OAAO,EAAE,IAAI;aACd;YAGD;gBACE,GAAG,EAAE,kBAAkB;gBACvB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACzC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;gBAChB,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,IAAI;aACd;YAGD;gBACE,GAAG,EAAE,eAAe;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE;gBACnC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;gBAChB,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,IAAI;aACd;YAGD;gBACE,GAAG,EAAE,gBAAgB;gBACrB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACvC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;gBAChB,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,IAAI;aACd;YAGD;gBACE,GAAG,EAAE,iBAAiB;gBACtB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE;gBACpC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;gBACjB,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,cAAc;gBACxB,OAAO,EAAE,IAAI;aACd;SACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,eAAe,IAAI,CAAC,cAAc,CAAC,MAAM,+BAA+B,CACzE,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAErD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC5E,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACrD,CAAC;IAMD,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAGvD,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CACpD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,CACjD,CAAC;QAEF,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACvD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,IAAe;QACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,iCAAiC,IAAI,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAC/E,CAAC;QAEF,IAAI,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEzE,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;IAKO,KAAK,CAAC,cAAc;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,YAAY,CAAC;aAClB,MAAM,CAAC,yBAAyB,CAAC;aACjC,KAAK,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAE/C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAC1D,CAAC;QACF,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,QAAQ,CAAC;aACd,MAAM,CAAC,mBAAmB,CAAC;aAC3B,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEtC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzE,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,YAAY,CAAC;aAClB,MAAM,CAAC,wBAAwB,CAAC;aAChC,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEtC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAC1D,CAAC;QACF,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,UAAU,CAAC;aAChB,MAAM,CACL;;;;OAID,CACA;aACA,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,KAAK,CAAC,GAAG,CAAC;aACV,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAE7C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sCAAsC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAChE,CAAC;QACF,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,WAAW,CAAC;aACjB,MAAM,CAAC,gBAAgB,CAAC;aACxB,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEtC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5E,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,QAAQ,CAAC;aACd,MAAM,CACL;;;OAGD,CACA;aACA,KAAK,CAAC,EAAE,CAAC;aACT,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAE7C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,oCAAoC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAC9D,CAAC;QACF,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,aAAa,CAAC;aACnB,MAAM,CAAC,uBAAuB,CAAC;aAC/B,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEtC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAC3D,CAAC;QACF,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAKD,gBAAgB;QAMd,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YACxC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;YACjE,WAAW,EAAE,IAAI;YACjB,oBAAoB,EAAE,IAAI;SAC3B,CAAC;IACJ,CAAC;IAKD,kBAAkB,CAAC,GAAW,EAAE,OAAgB;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;QAC5D,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,qBAAqB,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAC/D,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA9TY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAMsB,4BAAY;QACT,kCAAe;GANxC,mBAAmB,CA8T/B"}