import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID, IsBooleanString } from 'class-validator';
import { PaginationQueryDto } from 'src/shared/dtos/pagination-query.dto';

export class PackageListQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by package name (partial match)',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Filter by category ID',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Filter by deletion status (defaults to false)',
    example: 'false',
  })
  @IsOptional()
  @IsBooleanString()
  isDeleted?: string = 'false';
}
