import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from './strategies/jwt.strategy';
import { AuthEventLoggerService } from './services/auth-event-logger.service';
import { JwtValidationService } from './services/jwt-validation.service';

@Module({
  imports: [
    ConfigModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    // SupabaseModule, // Not needed here as SupabaseModule should be Global or imported where needed
  ],
  controllers: [],
  providers: [JwtValidationService, JwtStrategy, AuthEventLoggerService],
  exports: [JwtValidationService, JwtStrategy, AuthEventLoggerService],
})
export class AuthModule {}
