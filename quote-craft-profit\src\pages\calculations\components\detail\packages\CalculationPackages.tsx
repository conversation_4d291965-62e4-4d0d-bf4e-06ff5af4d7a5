import React, { useState, useMemo, memo, useCallback } from "react";
import { CategoryWithPackages, PackageWithOptions } from "@/types/calculation";
import CategoryAccordion from "./CategoryAccordion";
import VirtualizedPackageList from "./VirtualizedPackageList";
import CompactPackageSearch from "./CompactPackageSearch";
import OptimizedPackageList from "./OptimizedPackageList";
import { UI_CONSTANTS } from "../../../constants";

interface CalculationPackagesProps {
  packagesByCategory: CategoryWithPackages[];
  expandedCategories: string[];
  toggleCategory: (categoryId: string) => void;
  packageForms: Record<
    string,
    {
      quantity: number;
      item_quantity_basis: number;
      selectedOptions?: string[];
    }
  >;
  onQuantityChange: (packageId: string, value: number) => void;
  onItemQuantityBasisChange: (packageId: string, value: number) => void; // Required now
  onOptionToggle?: (
    packageId: string,
    optionId: string,
    isSelected: boolean
  ) => void;
  onAddToCalculation: (packageId: string) => void;
  isLoading: boolean;
  isError?: boolean;
  selectedPackageIds?: string[]; // New prop to track selected packages
}

const CalculationPackages: React.FC<CalculationPackagesProps> = ({
  packagesByCategory,
  expandedCategories,
  toggleCategory,
  packageForms,
  onQuantityChange,
  onItemQuantityBasisChange,
  onOptionToggle = () => {},
  onAddToCalculation,
  isLoading,
  isError = false,
  selectedPackageIds = [],
}) => {
  // State for search and filter
  const [filteredPackages, setFilteredPackages] = useState<
    PackageWithOptions[]
  >([]);
  const [viewMode, setViewMode] = useState<"category" | "list">("category");

  // Memoized callbacks to prevent infinite loops
  const handleFilteredPackagesChange = useCallback(
    (packages: PackageWithOptions[]) => {
      setFilteredPackages(packages);
    },
    []
  );

  const handleViewModeChange = useCallback((mode: "category" | "list") => {
    setViewMode(mode);
  }, []);

  // Flatten all packages from categories for search/filter
  const allPackages = useMemo(() => {
    return packagesByCategory.flatMap((category) =>
      category.packages.map((pkg) => ({
        ...pkg,
        category_id: category.id,
        category_name: category.name,
      }))
    );
  }, [packagesByCategory]);

  // Ensure packageForms has entries for all packages to prevent NaN errors
  const safePackageForms = useMemo(() => {
    const safeForms = { ...packageForms };

    // Add default forms for any missing packages
    allPackages.forEach((pkg) => {
      if (!safeForms[pkg.id]) {
        safeForms[pkg.id] = {
          quantity: 1,
          item_quantity_basis: 1,
          selectedOptions: [],
        };
      }
    });

    return safeForms;
  }, [packageForms, allPackages]);

  // Extract categories for filter
  const categories = useMemo(() => {
    return packagesByCategory.map((cat) => ({
      id: cat.id,
      name: cat.name,
    }));
  }, [packagesByCategory]);

  // Component to display available packages for a calculation
  return (
    <div className="mb-8">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border dark:border-gray-700">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Available Packages
          </h2>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Select packages to add to your calculation
          </div>
        </div>

        {/* Compact Search Component - Always Visible */}
        <div className="mb-6">
          <CompactPackageSearch
            packages={allPackages}
            categories={categories}
            onFilteredPackagesChange={handleFilteredPackagesChange}
            onViewModeChange={handleViewModeChange}
          />
        </div>

        {isLoading ? (
          <div className="space-y-4">
            {/* Use VirtualizedPackageList's built-in loading state */}
            <VirtualizedPackageList
              packages={[]}
              onAddToCalculation={() => {}}
              formatCurrency={(amount) => `Rp ${amount.toLocaleString()}`}
              isLoading={true}
              isError={false}
            />
          </div>
        ) : !packagesByCategory || packagesByCategory.length === 0 ? (
          <div className="p-8 text-center border dark:border-gray-700 rounded-lg">
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              No packages available for this city
            </p>
          </div>
        ) : viewMode === "list" ? (
          // Show filtered packages in optimized list view
          // Use virtualization for large lists
          (filteredPackages.length > 0 ? filteredPackages : allPackages)
            .length > UI_CONSTANTS.VIRTUALIZATION_THRESHOLD ? (
            <VirtualizedPackageList
              packages={
                filteredPackages.length > 0 ? filteredPackages : allPackages
              }
              onAddToCalculation={onAddToCalculation}
              formatCurrency={(amount) => `Rp ${amount.toLocaleString()}`}
              isLoading={false}
              isError={isError}
              maxHeight={600}
              estimatedItemSize={200}
              overscan={5}
            />
          ) : (
            <OptimizedPackageList
              packages={
                filteredPackages.length > 0 ? filteredPackages : allPackages
              }
              packageForms={safePackageForms}
              onQuantityChange={onQuantityChange}
              onItemQuantityBasisChange={onItemQuantityBasisChange}
              onOptionToggle={onOptionToggle}
              onAddToCalculation={onAddToCalculation}
              isLoading={false}
              isError={isError}
              maxHeight={600}
              selectedPackageIds={selectedPackageIds}
            />
          )
        ) : (
          // Show original category accordion view
          <div className="space-y-4">
            {packagesByCategory.map((category) => (
              <CategoryAccordion
                key={category.id}
                category={category}
                isExpanded={expandedCategories.includes(category.id)}
                onToggle={toggleCategory}
                packageForms={safePackageForms}
                onQuantityChange={onQuantityChange}
                onDaysChange={onItemQuantityBasisChange} // Keep for backward compatibility
                onItemQuantityBasisChange={onItemQuantityBasisChange} // Add the new prop
                onOptionToggle={onOptionToggle}
                onAddToCalculation={onAddToCalculation}
                isError={isError}
                selectedPackageIds={selectedPackageIds}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(CalculationPackages);
