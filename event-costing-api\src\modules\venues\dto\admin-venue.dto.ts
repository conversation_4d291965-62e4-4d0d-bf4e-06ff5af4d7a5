import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  IsNumber,
  IsIn,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';

export class AdminVenueDto {
  @ApiProperty({
    type: String,
    format: 'uuid',
    description: 'Venue ID',
  })
  id: string;

  @ApiProperty({
    type: String,
    description: 'Venue name',
  })
  name: string;

  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue description',
  })
  description: string | null;

  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue address',
  })
  address: string | null;

  @ApiProperty({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'City ID',
  })
  city_id: string | null;

  @ApiProperty({
    type: String,
    nullable: true,
    description: 'City name',
  })
  city_name?: string;

  @ApiProperty({
    type: String,
    nullable: true,
    description:
      'Venue classification (outdoor, hotel, indoor, premium, luxury)',
    enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
  })
  classification?: string | null;

  @ApiProperty({
    type: Number,
    nullable: true,
    description: 'Maximum attendee capacity',
  })
  capacity?: number | null;

  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue image URL',
  })
  image_url?: string | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    nullable: true,
    description: 'Venue features and amenities',
  })
  features?: string[] | null;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the venue is active',
  })
  is_active: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the venue is deleted',
  })
  is_deleted: boolean;

  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Deletion timestamp',
  })
  deleted_at: string | null;

  @ApiProperty({
    type: String,
    description: 'Creation timestamp',
  })
  created_at: string;

  @ApiProperty({
    type: String,
    description: 'Last update timestamp',
  })
  updated_at: string;
}

export class CreateVenueDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    type: String,
    description: 'Venue name',
  })
  name: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue description',
    required: false,
  })
  description?: string | null;

  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue address',
    required: false,
  })
  address?: string | null;

  @IsOptional()
  @IsUUID()
  @ApiProperty({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'City ID',
    required: false,
  })
  city_id?: string | null;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    type: Boolean,
    description: 'Whether the venue is active',
    default: true,
    required: false,
  })
  is_active?: boolean;

  @IsOptional()
  @IsString()
  @IsIn(['outdoor', 'hotel', 'indoor', 'premium', 'luxury'])
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue classification',
    enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
    required: false,
  })
  classification?: string | null;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @ApiProperty({
    type: Number,
    nullable: true,
    description: 'Maximum attendee capacity',
    required: false,
  })
  capacity?: number | null;

  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue image URL',
    required: false,
  })
  image_url?: string | null;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    nullable: true,
    description: 'Venue features and amenities',
    required: false,
  })
  features?: string[] | null;
}

export class UpdateVenueDto {
  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    description: 'Venue name',
    required: false,
  })
  name?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue description',
    required: false,
  })
  description?: string | null;

  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue address',
    required: false,
  })
  address?: string | null;

  @IsOptional()
  @IsUUID()
  @ApiProperty({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'City ID',
    required: false,
  })
  city_id?: string | null;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    type: Boolean,
    description: 'Whether the venue is active',
    required: false,
  })
  is_active?: boolean;

  @IsOptional()
  @IsString()
  @IsIn(['outdoor', 'hotel', 'indoor', 'premium', 'luxury'])
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue classification',
    enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
    required: false,
  })
  classification?: string | null;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @ApiProperty({
    type: Number,
    nullable: true,
    description: 'Maximum attendee capacity',
    required: false,
  })
  capacity?: number | null;

  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Venue image URL',
    required: false,
  })
  image_url?: string | null;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    nullable: true,
    description: 'Venue features and amenities',
    required: false,
  })
  features?: string[] | null;
}

export class PaginatedVenuesResponse {
  @ApiProperty({
    type: [AdminVenueDto],
    description: 'List of venues',
  })
  data: AdminVenueDto[];

  @ApiProperty({
    type: Number,
    description: 'Total number of venues matching the query',
  })
  totalCount: number;

  @ApiProperty({
    type: Number,
    description: 'Current page number',
  })
  page: number;

  @ApiProperty({
    type: Number,
    description: 'Number of venues per page',
  })
  pageSize: number;

  @ApiProperty({
    type: Number,
    description: 'Total number of pages',
  })
  totalPages: number;
}

export class ListVenuesQueryDto {
  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    required: false,
    description: 'Search term for venue name',
  })
  search?: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty({
    type: String,
    format: 'uuid',
    required: false,
    description: 'Filter by city ID',
  })
  cityId?: string;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  @ApiProperty({
    type: Boolean,
    required: false,
    description: 'Include deleted venues',
    default: false,
  })
  showDeleted?: boolean;

  @IsOptional()
  @Type(() => Number)
  @ApiProperty({
    type: Number,
    required: false,
    description: 'Page number',
    default: 1,
  })
  page?: number;

  @IsOptional()
  @Type(() => Number)
  @ApiProperty({
    type: Number,
    required: false,
    description: 'Number of venues per page',
    default: 10,
  })
  pageSize?: number;

  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    required: false,
    description: 'Sort by field',
    default: 'name',
  })
  sortBy?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    type: String,
    required: false,
    description: 'Sort order',
    default: 'asc',
    enum: ['asc', 'desc'],
  })
  sortOrder?: 'asc' | 'desc';

  @IsOptional()
  @IsString()
  @IsIn(['outdoor', 'hotel', 'indoor', 'premium', 'luxury'])
  @ApiProperty({
    type: String,
    required: false,
    description: 'Filter by venue classification',
    enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
  })
  classification?: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @ApiProperty({
    type: Number,
    required: false,
    description: 'Minimum capacity filter',
  })
  minCapacity?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @ApiProperty({
    type: Number,
    required: false,
    description: 'Maximum capacity filter',
  })
  maxCapacity?: number;
}
