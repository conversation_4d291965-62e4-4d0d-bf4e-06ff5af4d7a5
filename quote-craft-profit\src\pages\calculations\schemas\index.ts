import { z } from "zod";
import {
  commonValidations,
  formFieldPatterns,
  optionalDateRangeSchema,
} from "@/schemas/common";
import { QuantityBasisEnum } from "@/types/types";

/**
 * Calculation-related validation schemas
 * Centralized schemas for all calculation forms and components
 */

// Enhanced date range validation for calculation form
const calculationDateRangeSchema = z
  .object({
    from: z.date({ required_error: "Start date is required" }).optional(),
    to: z.date({ required_error: "End date is required" }).optional(),
  })
  .refine(
    (data) => {
      // Both dates must be present
      return data.from && data.to;
    },
    {
      message: "Both start and end dates are required",
      path: ["from"],
    }
  )
  .refine(
    (data) => {
      if (data.from && data.to) {
        return data.from <= data.to;
      }
      return true;
    },
    {
      message: "Start date must be before or equal to end date",
      path: ["to"],
    }
  );

// Enhanced attendees validation that requires a positive number
const attendeesValidation = z
  .string()
  .min(1, "Number of attendees is required")
  .refine(
    (val) => {
      const num = Number(val);
      return !isNaN(num) && num > 0;
    },
    {
      message: "Number of attendees must be greater than 0",
    }
  );

// Main calculation form schema (extracted from useCalculationForm.ts)
export const calculationFormSchema = z.object({
  name: formFieldPatterns.name("Event name"),
  city_id: commonValidations.requiredString(1, "City"),
  venue_ids: commonValidations.requiredArray(z.string(), 1, "venues"),
  date_range: calculationDateRangeSchema,
  attendees: attendeesValidation,
  event_type_id: z.string().uuid("Invalid event type").optional(),
  notes: formFieldPatterns.notes(),
  client_id: commonValidations.optionalString(),
  event_id: commonValidations.optionalString(),
});

// Create calculation from template schema
export const createCalculationFromTemplateSchema = z
  .object({
    name: formFieldPatterns.name("Calculation name"),
    dateRange: optionalDateRangeSchema,
    attendees: z
      .number()
      .int()
      .min(1, "Attendees must be at least 1")
      .optional(),
    clientId: commonValidations.optionalString(),
    eventId: commonValidations.optionalString(),
    cityId: commonValidations.optionalString(),
    venueIds: z.array(z.string()).optional(),
    notes: formFieldPatterns.notes(),
  })
  .refine(
    (data) => {
      if (data.dateRange?.from && data.dateRange?.to) {
        return data.dateRange.from <= data.dateRange.to;
      }
      return true;
    },
    {
      message: "Event start date must be before end date",
      path: ["dateRange"],
    }
  );

// Custom item schema (extracted from AddCustomItemDialog.tsx)
export const customItemSchema = z.object({
  name: formFieldPatterns.name(),
  description: formFieldPatterns.description(),
  quantity: formFieldPatterns.quantity(),
  item_quantity_basis: z.coerce
    .number()
    .min(0, "Days/Units cannot be negative"),
  quantity_basis: z
    .nativeEnum(QuantityBasisEnum)
    .default(QuantityBasisEnum.PER_DAY),
  unit_price: z.coerce.number().min(0, "Unit price cannot be negative"),
  category_id: commonValidations.requiredString(1, "Category"),
});

// Custom line item schema (extracted from AddCustomLineItemForm.tsx)
export const customLineItemSchema = z.object({
  name: formFieldPatterns.name(),
  description: formFieldPatterns.description(),
  quantity: formFieldPatterns.quantity(),
  unit_price: formFieldPatterns.price("Unit price"),
  category_id: commonValidations.requiredString(1, "Category"),
});

// Edit line item schema (extracted from EditLineItemDialog.tsx)
export const editLineItemSchema = z.object({
  name: formFieldPatterns.name(),
  description: formFieldPatterns.description(),
  quantity: formFieldPatterns.quantity(),
  item_quantity_basis: z.coerce
    .number()
    .min(0, "Days/Units cannot be negative"),
  unit_price: z.coerce.number().min(0, "Price cannot be negative"),
  category_id: commonValidations.requiredString(1, "Category"),
});

// Type exports for TypeScript inference
export type CalculationFormValues = z.infer<typeof calculationFormSchema>;
export type CreateCalculationFromTemplateValues = z.infer<
  typeof createCalculationFromTemplateSchema
>;
export type CustomItemFormValues = z.infer<typeof customItemSchema>;
export type CustomLineItemFormValues = z.infer<typeof customLineItemSchema>;
export type EditLineItemFormValues = z.infer<typeof editLineItemSchema>;
