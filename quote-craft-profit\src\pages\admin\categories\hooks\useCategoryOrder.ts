import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { getAllCategories, updateCategoryOrder } from '@/services/admin/categories';
import { Category, CategoryOrderItem } from '../types';

/**
 * Custom hook for managing category order
 * @returns Object containing categories data and mutation functions
 */
export const useCategoryOrder = () => {
  const queryClient = useQueryClient();

  // Fetch categories
  const {
    data: categories = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['categories'],
    queryFn: getAllCategories,
    meta: {
      onError: (error: Error) => {
        console.error('Error fetching categories:', error);
        toast.error('Failed to load categories');
      },
    },
  });

  // Update category order mutation
  const updateOrderMutation = useMutation({
    mutationFn: (items: CategoryOrderItem[]) => updateCategoryOrder(items),
    onMutate: async (newItems) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['categories'] });

      // Snapshot the previous value
      const previousCategories = queryClient.getQueryData<Category[]>(['categories']);

      // Optimistically update to the new value
      if (previousCategories) {
        const updatedCategories = [...previousCategories];

        // Update display_order for each category
        newItems.forEach((item) => {
          const index = updatedCategories.findIndex((cat) => cat.id === item.id);
          if (index !== -1) {
            updatedCategories[index] = {
              ...updatedCategories[index],
              display_order: item.display_order,
            };
          }
        });

        // Sort by the new display_order
        updatedCategories.sort((a, b) => a.display_order - b.display_order);

        // Update the cache
        queryClient.setQueryData(['categories'], updatedCategories);
      }

      // Return a context object with the previous categories
      return { previousCategories };
    },
    onSuccess: () => {
      toast.success('Category order updated successfully');
    },
    onError: (error, _, context) => {
      console.error('Error updating category order:', error);
      toast.error('Failed to update category order');

      // Rollback to the previous value
      if (context?.previousCategories) {
        queryClient.setQueryData(['categories'], context.previousCategories);
      }
    },
    onSettled: () => {
      // Invalidate the categories query to refetch the latest data
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });

  return {
    categories,
    isLoading,
    isError,
    error,
    updateOrder: updateOrderMutation.mutate,
    isUpdating: updateOrderMutation.isPending,
  };
};
