"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CacheWarmingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheWarmingService = void 0;
const common_1 = require("@nestjs/common");
const cache_service_1 = require("./cache.service");
const supabase_service_1 = require("../supabase/supabase.service");
let CacheWarmingService = CacheWarmingService_1 = class CacheWarmingService {
    cacheService;
    supabaseService;
    logger = new common_1.Logger(CacheWarmingService_1.name);
    warmingConfigs = [];
    constructor(cacheService, supabaseService) {
        this.cacheService = cacheService;
        this.supabaseService = supabaseService;
    }
    async onModuleInit() {
        this.initializeWarmingConfigs();
        await this.performInitialWarming();
    }
    initializeWarmingConfigs() {
        this.warmingConfigs = [
            {
                key: 'categories:all',
                factory: () => this.warmCategories(),
                ttl: 60 * 60 * 6,
                priority: 10,
                schedule: '0 */6 * * *',
                enabled: true,
            },
            {
                key: 'cities:all',
                factory: () => this.warmCities(),
                ttl: 60 * 60 * 12,
                priority: 9,
                schedule: '0 */12 * * *',
                enabled: true,
            },
            {
                key: 'currencies:all',
                factory: () => this.warmCurrencies(),
                ttl: 60 * 60 * 24,
                priority: 9,
                schedule: '0 0 * * *',
                enabled: true,
            },
            {
                key: 'packages:popular',
                factory: () => this.warmPopularPackages(),
                ttl: 60 * 60 * 2,
                priority: 7,
                schedule: '0 */2 * * *',
                enabled: true,
            },
            {
                key: 'divisions:all',
                factory: () => this.warmDivisions(),
                ttl: 60 * 60 * 8,
                priority: 6,
                schedule: '0 */8 * * *',
                enabled: true,
            },
            {
                key: 'venues:popular',
                factory: () => this.warmPopularVenues(),
                ttl: 60 * 60 * 4,
                priority: 5,
                schedule: '0 */4 * * *',
                enabled: true,
            },
            {
                key: 'event-types:all',
                factory: () => this.warmEventTypes(),
                ttl: 60 * 60 * 12,
                priority: 4,
                schedule: '0 */12 * * *',
                enabled: true,
            },
        ];
        this.logger.log(`Initialized ${this.warmingConfigs.length} cache warming configurations`);
    }
    async performInitialWarming() {
        this.logger.log('Starting initial cache warming...');
        const enabledConfigs = this.warmingConfigs.filter(config => config.enabled);
        await this.cacheService.warmCache(enabledConfigs);
        this.logger.log('Initial cache warming completed');
    }
    async scheduledCacheWarming() {
        this.logger.log('Starting scheduled cache warming...');
        const highPriorityConfigs = this.warmingConfigs.filter(config => config.enabled && config.priority >= 7);
        await this.cacheService.warmCache(highPriorityConfigs);
        this.logger.log('Scheduled cache warming completed');
    }
    async warmCacheManually(keys) {
        this.logger.log(`Manual cache warming triggered${keys ? ` for keys: ${keys.join(', ')}` : ''}`);
        let configsToWarm = this.warmingConfigs.filter(config => config.enabled);
        if (keys && keys.length > 0) {
            configsToWarm = configsToWarm.filter(config => keys.includes(config.key));
        }
        await this.cacheService.warmCache(configsToWarm);
        this.logger.log('Manual cache warming completed');
    }
    async warmCategories() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('categories')
            .select('id, name, display_order')
            .order('display_order', { ascending: true });
        if (error) {
            this.logger.error('Error warming categories cache:', error);
            throw error;
        }
        this.logger.debug(`Warmed categories cache with ${data?.length || 0} items`);
        return data || [];
    }
    async warmCities() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('cities')
            .select('id, name, country')
            .order('name', { ascending: true });
        if (error) {
            this.logger.error('Error warming cities cache:', error);
            throw error;
        }
        this.logger.debug(`Warmed cities cache with ${data?.length || 0} items`);
        return data || [];
    }
    async warmCurrencies() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('currencies')
            .select('id, code, name, symbol')
            .order('code', { ascending: true });
        if (error) {
            this.logger.error('Error warming currencies cache:', error);
            throw error;
        }
        this.logger.debug(`Warmed currencies cache with ${data?.length || 0} items`);
        return data || [];
    }
    async warmPopularPackages() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('packages')
            .select(`
        id, name, description, category_id, division_id,
        categories!inner(name),
        divisions!inner(name)
      `)
            .eq('is_deleted', false)
            .limit(100)
            .order('updated_at', { ascending: false });
        if (error) {
            this.logger.error('Error warming popular packages cache:', error);
            throw error;
        }
        this.logger.debug(`Warmed popular packages cache with ${data?.length || 0} items`);
        return data || [];
    }
    async warmDivisions() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('divisions')
            .select('id, name, code')
            .order('name', { ascending: true });
        if (error) {
            this.logger.error('Error warming divisions cache:', error);
            throw error;
        }
        this.logger.debug(`Warmed divisions cache with ${data?.length || 0} items`);
        return data || [];
    }
    async warmPopularVenues() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('venues')
            .select(`
        id, name, city_id, capacity,
        cities!inner(name)
      `)
            .limit(50)
            .order('updated_at', { ascending: false });
        if (error) {
            this.logger.error('Error warming popular venues cache:', error);
            throw error;
        }
        this.logger.debug(`Warmed popular venues cache with ${data?.length || 0} items`);
        return data || [];
    }
    async warmEventTypes() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('event_types')
            .select('id, name, description')
            .order('name', { ascending: true });
        if (error) {
            this.logger.error('Error warming event types cache:', error);
            throw error;
        }
        this.logger.debug(`Warmed event types cache with ${data?.length || 0} items`);
        return data || [];
    }
    getWarmingStatus() {
        return {
            totalConfigs: this.warmingConfigs.length,
            enabledConfigs: this.warmingConfigs.filter(c => c.enabled).length,
            lastWarming: null,
            nextScheduledWarming: null,
        };
    }
    toggleCacheWarming(key, enabled) {
        const config = this.warmingConfigs.find(c => c.key === key);
        if (config) {
            config.enabled = enabled;
            this.logger.log(`Cache warming for ${key} ${enabled ? 'enabled' : 'disabled'}`);
            return true;
        }
        return false;
    }
};
exports.CacheWarmingService = CacheWarmingService;
exports.CacheWarmingService = CacheWarmingService = CacheWarmingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService,
        supabase_service_1.SupabaseService])
], CacheWarmingService);
//# sourceMappingURL=cache-warming.service.js.map