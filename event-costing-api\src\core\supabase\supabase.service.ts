import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SupabaseClient, createClient } from '@supabase/supabase-js';

@Injectable()
export class SupabaseService {
  private readonly logger = new Logger(SupabaseService.name);
  private clientInstance: SupabaseClient;
  private authClientInstance: SupabaseClient;

  constructor(private readonly configService: ConfigService) {
    const supabaseUrl = this.configService.get<string>('SUPABASE_URL');
    // Use the Service Role Key for backend operations
    const supabaseServiceKey = this.configService.get<string>(
      'SUPABASE_SERVICE_ROLE_KEY',
    );
    // Use the Anon Key for auth operations
    const supabaseAnonKey = this.configService.get<string>('SUPABASE_ANON_KEY');

    if (!supabaseUrl || !supabaseServiceKey || !supabaseAnonKey) {
      this.logger.error(
        'Supabase URL, Service Role Key, or Anon Key is missing. Check environment variables.',
      );
      throw new Error(
        'Supabase configuration is incomplete. Check environment variables.',
      );
    }

    // Create the main client with service role key for admin operations
    this.clientInstance = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
    });

    // Create a separate client with anon key for auth operations
    this.authClientInstance = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
    });

    this.logger.log('Supabase client initialized.');
  }

  // Get the admin client with service role key
  getClient(): SupabaseClient {
    return this.clientInstance;
  }

  // Get the auth client with anon key for auth operations
  getAuthClient(): SupabaseClient {
    return this.authClientInstance;
  }
}
