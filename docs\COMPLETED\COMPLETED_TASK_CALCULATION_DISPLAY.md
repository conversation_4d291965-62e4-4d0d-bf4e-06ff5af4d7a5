# Package Display Optimization Plan

This document outlines a comprehensive plan for optimizing the package display in calculation detail pages, focusing on both backend optimizations and frontend UI improvements.

## Overview

The current implementation of package display in calculation detail pages has several performance and usability issues:

1. **Multiple Database Queries**: The backend makes separate queries for packages, city availability, venue availability, and package options.
2. **Inefficient Data Fetching**: The frontend makes multiple API calls to fetch packages, categories, and options.
3. **UI Performance**: Large lists of packages can cause performance issues in the UI.

This plan aims to address these issues through backend optimizations and frontend UI improvements.

## Backend Optimization

### 1. Create SQL Functions for Efficient Package Retrieval

#### 1.1. Create SQL Function for Packages with Availability

```sql
CREATE OR REPLACE FUNCTION get_packages_by_category_with_availability(
  p_currency_id UUID,
  p_city_id UUID DEFAULT NULL,
  p_venue_id UUID DEFAULT NULL,
  p_category_id UUID DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  description TEXT,
  category_id UUID,
  category_name TEXT,
  category_display_order INTEGER,
  quantity_basis TEXT,
  price NUMERIC,
  unit_base_cost NUMERIC,
  is_available_in_city BOOLEAN,
  is_available_in_venue BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id,
    p.name,
    p.description,
    p.category_id,
    c.name AS category_name,
    c.display_order AS category_display_order,
    p.quantity_basis::TEXT,
    pp.price,
    pp.unit_base_cost,
    -- Check city availability with EXISTS subquery
    (p_city_id IS NULL OR EXISTS (
      SELECT 1 FROM package_cities pc
      WHERE pc.package_id = p.id AND pc.city_id = p_city_id
    )) AS is_available_in_city,
    -- Check venue availability with EXISTS subquery
    (p_venue_id IS NULL OR EXISTS (
      SELECT 1 FROM package_venues pv
      WHERE pv.package_id = p.id AND pv.venue_id = p_venue_id
    )) AS is_available_in_venue
  FROM
    packages p
  JOIN
    categories c ON p.category_id = c.id
  JOIN
    package_prices pp ON p.id = pp.package_id AND pp.currency_id = p_currency_id
  WHERE
    p.is_deleted = FALSE
    AND (p_category_id IS NULL OR p.category_id = p_category_id)
    -- Only include packages available in the specified city if provided
    AND (p_city_id IS NULL OR EXISTS (
      SELECT 1 FROM package_cities pc
      WHERE pc.package_id = p.id AND pc.city_id = p_city_id
    ))
    -- Only include packages available in the specified venue if provided
    AND (p_venue_id IS NULL OR EXISTS (
      SELECT 1 FROM package_venues pv
      WHERE pv.package_id = p.id AND pv.venue_id = p_venue_id
    ))
  ORDER BY
    c.display_order, c.name, p.name;
END;
$$ LANGUAGE plpgsql;
```

#### 1.2. Create SQL Function for Batch Package Options

```sql
CREATE OR REPLACE FUNCTION get_batch_package_options(
  p_package_ids UUID[],
  p_currency_id UUID,
  p_venue_id UUID DEFAULT NULL
)
RETURNS TABLE (
  package_id UUID,
  option_id UUID,
  option_name TEXT,
  description TEXT,
  price_adjustment NUMERIC,
  cost_adjustment NUMERIC,
  is_default_for_package BOOLEAN,
  is_required BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    po.applicable_package_id AS package_id,
    po.id AS option_id,
    po.option_name,
    po.description,
    po.price_adjustment,
    po.cost_adjustment,
    po.is_default_for_package,
    po.is_required
  FROM
    package_options po
  WHERE
    po.applicable_package_id = ANY(p_package_ids)
    AND po.currency_id = p_currency_id
    AND (p_venue_id IS NULL OR po.venue_id = p_venue_id OR po.venue_id IS NULL)
  ORDER BY
    po.applicable_package_id, po.option_name;
END;
$$ LANGUAGE plpgsql;
```

### 2. Create Database Indexes for Performance

```sql
-- Index for package_cities to speed up city availability checks
CREATE INDEX IF NOT EXISTS idx_package_cities_package_city ON package_cities(package_id, city_id);

-- Index for package_venues to speed up venue availability checks
CREATE INDEX IF NOT EXISTS idx_package_venues_package_venue ON package_venues(package_id, venue_id);

-- Index for package_prices to speed up price lookups
CREATE INDEX IF NOT EXISTS idx_package_prices_package_currency ON package_prices(package_id, currency_id);

-- Index for package_options to speed up option lookups
CREATE INDEX IF NOT EXISTS idx_package_options_package_currency ON package_options(applicable_package_id, currency_id);

-- Index for categories to speed up sorting
CREATE INDEX IF NOT EXISTS idx_categories_display_order ON categories(display_order) WHERE is_deleted = FALSE;
```

### 3. Create New DTOs for Optimized Endpoints

#### 3.1. Create PackagesByCategoryResponseDto

```typescript
// src/modules/packages/dto/packages-by-category-response.dto.ts
import { ApiProperty } from '@nestjs/swagger';

export class PackageOptionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  option_name: string;

  @ApiProperty({ required: false })
  description: string;

  @ApiProperty()
  price_adjustment: number;

  @ApiProperty()
  cost_adjustment: number;

  @ApiProperty()
  is_default_for_package: boolean;

  @ApiProperty()
  is_required: boolean;
}

export class PackageDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  description: string;

  @ApiProperty()
  quantity_basis: string;

  @ApiProperty()
  price: number;

  @ApiProperty()
  unit_base_cost: number;

  @ApiProperty({ required: false, type: [PackageOptionDto] })
  options?: PackageOptionDto[];
}

export class CategoryWithPackagesDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  display_order: number;

  @ApiProperty({ type: [PackageDto] })
  packages: PackageDto[];
}

export class PackagesByCategoryResponseDto {
  @ApiProperty({ type: [CategoryWithPackagesDto] })
  categories: CategoryWithPackagesDto[];
}
```

#### 3.2. Create BatchPackageOptionsResponseDto

```typescript
// src/modules/packages/dto/batch-package-options-response.dto.ts
import { ApiProperty } from '@nestjs/swagger';

export class PackageOptionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  option_name: string;

  @ApiProperty({ required: false })
  description: string;

  @ApiProperty()
  price_adjustment: number;

  @ApiProperty()
  cost_adjustment: number;

  @ApiProperty()
  is_default_for_package: boolean;

  @ApiProperty()
  is_required: boolean;
}

export class BatchPackageOptionsResponseDto {
  @ApiProperty({ type: Object })
  options: Record<string, PackageOptionDto[]>;
}
```

### 4. Add New Endpoints to PackagesController

#### 4.1. Add Endpoint for Packages by Category

```typescript
// src/modules/packages/packages.controller.ts
@Get('calculations/:id/available-packages')
@ApiOperation({
  summary: 'Get available packages organized by category for a calculation',
})
@ApiParam({ name: 'id', type: 'string', format: 'uuid' })
@ApiQuery({ name: 'includeOptions', required: false, type: Boolean })
@ApiOkResponse({ type: PackagesByCategoryResponseDto })
async getAvailablePackagesByCategory(
  @Param('id', ParseUUIDPipe) calculationId: string,
  @Query('includeOptions') includeOptions: boolean = false,
  @GetCurrentUser() user: User,
): Promise<PackagesByCategoryResponseDto> {
  // Check calculation ownership
  await this.calculationsService.checkCalculationOwnership(calculationId, user.id);

  // Get calculation details to determine city, venue, currency
  const calculation = await this.calculationsService.findCalculationById(calculationId, user);

  // Get packages organized by category
  return this.packagesService.getPackagesByCategory(
    calculation.currency.id,
    calculation.city?.id,
    calculation.venue?.id,
    includeOptions
  );
}
```

#### 4.2. Add Endpoint for Batch Package Options

```typescript
// src/modules/packages/packages.controller.ts
@Get('packages/batch-options')
@ApiOperation({
  summary: 'Get options for multiple packages in a single request',
})
@ApiQuery({ name: 'packageIds', type: [String], isArray: true })
@ApiQuery({ name: 'currencyId', required: true, type: String })
@ApiQuery({ name: 'venueId', required: false, type: String })
@ApiOkResponse({ type: BatchPackageOptionsResponseDto })
async getBatchPackageOptions(
  @Query('packageIds', new ParseArrayPipe({ items: String, separator: ',' })) packageIds: string[],
  @Query('currencyId') currencyId: string,
  @Query('venueId') venueId?: string,
): Promise<BatchPackageOptionsResponseDto> {
  return this.packagesService.getBatchPackageOptions(packageIds, currencyId, venueId);
}
```

### 5. Implement Service Methods in PackagesService

#### 5.1. Implement getPackagesByCategory Method

```typescript
// src/modules/packages/packages.service.ts
async getPackagesByCategory(
  currencyId: string,
  cityId?: string,
  venueId?: string,
  includeOptions: boolean = false,
): Promise<PackagesByCategoryResponseDto> {
  this.logger.log(
    `Getting packages by category for currency ${currencyId}, city ${cityId || 'any'}, venue ${venueId || 'any'}`,
  );

  // Generate a cache key
  const cacheKey = `packages-by-category:${currencyId}:${cityId || 'all'}:${venueId || 'all'}:${includeOptions}`;

  // Try to get from cache first
  return this.cacheService.getOrSet(
    cacheKey,
    () => this.fetchPackagesByCategory(currencyId, cityId, venueId, includeOptions),
    3600, // Cache for 1 hour
  );
}

private async fetchPackagesByCategory(
  currencyId: string,
  cityId?: string,
  venueId?: string,
  includeOptions: boolean = false,
): Promise<PackagesByCategoryResponseDto> {
  const supabase = this.supabaseService.getClient();

  // Use the SQL function to get packages with availability
  const { data: packages, error: packageError } = await supabase
    .rpc('get_packages_by_category_with_availability', {
      p_currency_id: currencyId,
      p_city_id: cityId,
      p_venue_id: venueId,
      p_category_id: null, // Get all categories
    });

  if (packageError) {
    this.logger.error(`Error fetching packages: ${packageError.message}`, packageError.stack);
    throw new InternalServerErrorException('Failed to retrieve packages.');
  }

  // Organize packages by category
  const packagesByCategory = this.organizePackagesByCategory(packages);

  // Fetch options if requested
  if (includeOptions && packages.length > 0) {
    await this.addOptionsToPackages(packagesByCategory, currencyId, venueId);
  }

  return {
    categories: packagesByCategory,
  };
}

private organizePackagesByCategory(packages: any[]): CategoryWithPackagesDto[] {
  // Group packages by category
  const categoryMap = new Map<string, CategoryWithPackagesDto>();

  packages.forEach(pkg => {
    const categoryId = pkg.category_id || 'uncategorized';

    if (!categoryMap.has(categoryId)) {
      categoryMap.set(categoryId, {
        id: categoryId,
        name: pkg.category_name || 'Uncategorized',
        display_order: pkg.category_display_order || 9999,
        packages: [],
      });
    }

    categoryMap.get(categoryId).packages.push({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      quantity_basis: pkg.quantity_basis,
      price: pkg.price,
      unit_base_cost: pkg.unit_base_cost,
    });
  });

  // Convert map to array and sort by display_order
  return Array.from(categoryMap.values())
    .sort((a, b) => a.display_order - b.display_order);
}

private async addOptionsToPackages(
  categories: CategoryWithPackagesDto[],
  currencyId: string,
  venueId?: string,
): Promise<void> {
  // Get all package IDs
  const packageIds = categories.flatMap(cat => cat.packages.map(pkg => pkg.id));

  if (packageIds.length === 0) {
    return;
  }

  // Get options for all packages
  const options = await this.getBatchPackageOptions(packageIds, currencyId, venueId);

  // Add options to packages
  categories.forEach(category => {
    category.packages.forEach(pkg => {
      pkg.options = options.options[pkg.id] || [];
    });
  });
}
```

#### 5.2. Implement getBatchPackageOptions Method

```typescript
// src/modules/packages/packages.service.ts
async getBatchPackageOptions(
  packageIds: string[],
  currencyId: string,
  venueId?: string,
): Promise<BatchPackageOptionsResponseDto> {
  this.logger.log(
    `Getting batch options for ${packageIds.length} packages, currency ${currencyId}, venue ${venueId || 'any'}`,
  );

  // Generate a cache key
  const cacheKey = `batch-package-options:${packageIds.join(',')}:${currencyId}:${venueId || 'all'}`;

  // Try to get from cache first
  return this.cacheService.getOrSet(
    cacheKey,
    () => this.fetchBatchPackageOptions(packageIds, currencyId, venueId),
    3600, // Cache for 1 hour
  );
}

private async fetchBatchPackageOptions(
  packageIds: string[],
  currencyId: string,
  venueId?: string,
): Promise<BatchPackageOptionsResponseDto> {
  const supabase = this.supabaseService.getClient();

  // Use the SQL function to get options for multiple packages
  const { data: options, error: optionsError } = await supabase
    .rpc('get_batch_package_options', {
      p_package_ids: packageIds,
      p_currency_id: currencyId,
      p_venue_id: venueId,
    });

  if (optionsError) {
    this.logger.error(`Error fetching batch options: ${optionsError.message}`, optionsError.stack);
    throw new InternalServerErrorException('Failed to retrieve package options.');
  }

  // Organize options by package ID
  const optionsByPackage: Record<string, PackageOptionDto[]> = {};

  options.forEach(option => {
    if (!optionsByPackage[option.package_id]) {
      optionsByPackage[option.package_id] = [];
    }

    optionsByPackage[option.package_id].push({
      id: option.option_id,
      option_name: option.option_name,
      description: option.description,
      price_adjustment: option.price_adjustment,
      cost_adjustment: option.cost_adjustment,
      is_default_for_package: option.is_default_for_package,
      is_required: option.is_required,
    });
  });

  return {
    options: optionsByPackage,
  };
}
```

## Frontend UI Implementation

### 1. Update API Endpoints in Frontend

#### 1.1. Add New API Endpoints

```typescript
// src/integrations/api/endpoints.ts
export const API_ENDPOINTS = {
  // ... existing endpoints

  /**
   * Calculation endpoints
   */
  CALCULATIONS: {
    // ... existing endpoints

    GET_PACKAGES_BY_CATEGORY: (id: string) => `/calculations/${id}/available-packages`,
  },

  /**
   * Package endpoints
   */
  PACKAGES: {
    // ... existing endpoints

    BATCH_OPTIONS: '/packages/batch-options',
  },
};
```

#### 1.2. Create New API Service Functions

```typescript
// src/services/calculationService.ts

/**
 * Get packages organized by category for a calculation
 * @param calculationId - The calculation ID
 * @param includeOptions - Whether to include package options
 * @returns Promise resolving to packages organized by category
 */
export const getPackagesByCategoryForCalculation = async (
  calculationId: string,
  includeOptions: boolean = false,
): Promise<CategoryWithPackages[]> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (includeOptions) {
      queryParams.append('includeOptions', 'true');
    }

    // Make API request
    const response = await authClient.get(
      `${API_ENDPOINTS.CALCULATIONS.GET_PACKAGES_BY_CATEGORY(
        calculationId,
      )}?${queryParams.toString()}`,
    );

    return response.data.categories;
  } catch (error) {
    console.error(
      `Error fetching packages by category for calculation ${calculationId}:`,
      error,
    );
    return [];
  }
};
```

### 2. Update useCalculationDetail Hook

```typescript
// src/hooks/useCalculationDetail.ts

// Fetch packages organized by category
const { data: packagesByCategory, isLoading: isLoadingPackages } = useQuery({
  queryKey: ['packagesByCategory', calculation?.id],
  queryFn: () => getPackagesByCategoryForCalculation(calculation.id, true),
  enabled: !!calculation?.id,
  meta: {
    onError: () => {
      toast.error('Failed to load packages');
    },
  },
});

// Remove the old code that manually organizes packages by category
// The packagesByCategory memo is no longer needed as the data comes pre-organized from the backend
```

### 3. Implement Progressive Loading in CalculationPackages Component

```typescript
// src/components/calculations/CalculationPackages.tsx
import { useVirtualizer } from '@tanstack/react-virtual';

const CalculationPackages: React.FC<CalculationPackagesProps> = ({
  packagesByCategory,
  expandedCategories,
  toggleCategory,
  packageForms,
  onQuantityChange,
  onDaysChange,
  onOptionToggle,
  onAddToCalculation,
  calculateTotalPrice,
  isLoading,
}) => {
  return (
    <div className='mb-8'>
      <div className='bg-white p-6 rounded-lg border'>
        <div className='flex justify-between items-center mb-4'>
          <h2 className='text-xl font-bold'>Available Packages</h2>
          <div className='text-sm text-gray-500'>
            Select packages to add to your calculation
          </div>
        </div>

        {isLoading ? (
          <div className='space-y-4'>
            {/* Show skeleton loaders for categories */}
            {[1, 2, 3].map((i) => (
              <div key={i} className='border rounded-md overflow-hidden'>
                <div className='px-4 py-3 bg-gray-50'>
                  <Skeleton className='h-6 w-40' />
                </div>
              </div>
            ))}
          </div>
        ) : packagesByCategory.length === 0 ? (
          <div className='p-8 text-center border rounded-lg'>
            <p className='text-gray-500 mb-4'>No packages available for this city</p>
          </div>
        ) : (
          <div className='space-y-4'>
            {packagesByCategory.map((category) => (
              <CategoryAccordion
                key={category.id}
                category={category}
                isExpanded={expandedCategories.includes(category.id)}
                onToggle={toggleCategory}
                packageForms={packageForms}
                onQuantityChange={onQuantityChange}
                onDaysChange={onDaysChange}
                onOptionToggle={onOptionToggle}
                onAddToCalculation={onAddToCalculation}
                calculateTotalPrice={calculateTotalPrice}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
```

### 4. Implement Virtualization in CategoryAccordion Component

```typescript
// src/components/calculations/CategoryAccordion.tsx
import { useVirtualizer } from '@tanstack/react-virtual';

const CategoryAccordion: React.FC<CategoryAccordionProps> = ({
  category,
  isExpanded,
  onToggle,
  packageForms,
  onQuantityChange,
  onDaysChange,
  onOptionToggle,
  onAddToCalculation,
  calculateTotalPrice,
}) => {
  const parentRef = React.useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: category.packages.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 200, // Estimated height of each package card
    overscan: 5, // Number of items to render outside of the visible area
  });

  return (
    <div className='border rounded-md overflow-hidden'>
      {/* Category header */}
      <div
        className='flex justify-between items-center px-4 py-3 bg-gray-50 cursor-pointer hover:bg-gray-100'
        onClick={() => onToggle(category.id)}
      >
        <div className='flex items-center'>
          <h3 className='text-lg font-medium'>{category.name}</h3>
          <Badge variant='secondary' className='ml-2'>
            {category.packages.length}
          </Badge>
        </div>
        <ChevronLeft
          className={`transform transition-transform ${
            isExpanded ? 'rotate-90' : '-rotate-90'
          }`}
          size={20}
        />
      </div>

      {/* Only render content when expanded */}
      {isExpanded && (
        <div
          ref={parentRef}
          className='p-4 max-h-[600px] overflow-auto'
          style={{ height: Math.min(600, category.packages.length * 200) }}
        >
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: '100%',
              position: 'relative',
            }}
          >
            {virtualizer.getVirtualItems().map((virtualRow) => (
              <div
                key={virtualRow.index}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  transform: `translateY(${virtualRow.start}px)`,
                }}
                className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
              >
                <PackageCard
                  pkg={category.packages[virtualRow.index]}
                  quantity={
                    packageForms[category.packages[virtualRow.index].id]?.quantity || 1
                  }
                  days={packageForms[category.packages[virtualRow.index].id]?.days || 1}
                  selectedOptions={
                    packageForms[category.packages[virtualRow.index].id]
                      ?.selectedOptions || []
                  }
                  onQuantityChange={onQuantityChange}
                  onDaysChange={onDaysChange}
                  onOptionToggle={onOptionToggle}
                  onAddToCalculation={onAddToCalculation}
                  calculateTotalPrice={calculateTotalPrice}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
```

## Implementation Checklist

### Backend Tasks

- [x] **Database Functions**

  - [x] Create `get_packages_by_category_with_availability` function
  - [x] Create `get_batch_package_options` function
  - [x] Test functions with sample data

- [x] **Database Indexes**

  - [x] Create index on package_cities
  - [x] Create index on package_venues
  - [x] Create index on package_prices
  - [x] Create index on package_options
  - [x] Create index on categories

- [x] **DTOs**

  - [x] Create PackagesByCategoryResponseDto
  - [x] Create BatchPackageOptionsResponseDto

- [x] **Controller Endpoints**

  - [x] Add getAvailablePackagesByCategory endpoint
  - [x] Add getBatchPackageOptions endpoint
  - [x] Update Swagger documentation

- [x] **Service Methods**

  - [x] Implement getPackagesByCategory method
  - [x] Implement getBatchPackageOptions method
  - [x] Implement helper methods for organizing data

- [x] **Caching**
  - [x] Implement caching for getPackagesByCategory
  - [x] Implement caching for getBatchPackageOptions
  - [x] Set up cache invalidation when data changes

### Frontend Tasks

- [x] **API Integration**

  - [x] Add new API endpoints to endpoints.ts
  - [x] Create getPackagesByCategoryForCalculation service function
  - [x] Update existing API service functions to use new endpoints

- [x] **Hook Updates**

  - [x] Update useCalculationDetail hook to use new API
  - [x] Remove manual package organization code
  - [x] Update state management for package forms

- [x] **Component Updates**

  - [x] Create VirtualizedPackageList component
  - [x] Implement virtualization for efficient rendering
  - [x] Add skeleton loading states

- [x] **Performance Optimizations**
  - [x] Implement virtualization for large lists
  - [x] Optimize data fetching with new API endpoints
  - [x] Add loading states and skeleton loaders

## Testing Plan

- [ ] **Backend Testing**

  - [ ] Test SQL functions with various input combinations
  - [ ] Test API endpoints with Postman or similar tool
  - [ ] Verify caching is working correctly

- [ ] **Frontend Testing**
  - [ ] Test with small and large datasets
  - [ ] Test with slow network connections
  - [ ] Test with various screen sizes

## Deployment Plan

- [ ] **Backend Deployment**

  - [ ] Create migration for SQL functions and indexes
  - [ ] Deploy backend changes
  - [ ] Monitor performance metrics

- [ ] **Frontend Deployment**
  - [ ] Deploy frontend changes
  - [ ] Monitor client-side performance
  - [ ] Collect user feedback
