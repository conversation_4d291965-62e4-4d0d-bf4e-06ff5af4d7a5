/**
 * Standardized notification system using Sonner
 *
 * This file provides a consistent API for showing toast notifications
 * throughout the application with notification replacement strategies
 * to prevent overwhelming users with multiple stacked notifications.
 */

import { toast } from "@/components/ui/sonner";

/**
 * Types of notifications
 */
export type NotificationType =
  | "success"
  | "error"
  | "info"
  | "warning"
  | "loading";

/**
 * Notification categories for replacement strategy
 */
export type NotificationCategory =
  | "export" // Export operations
  | "calculation" // Calculation operations (save, delete, duplicate)
  | "client" // Client management operations
  | "template" // Template operations
  | "package" // Package management
  | "admin" // Admin operations
  | "form" // Form submissions and validation
  | "auto-save" // Automatic save operations
  | "line-item" // Line item operations
  | "bulk" // Bulk operations
  | "critical"; // Critical alerts (should not be replaced)

/**
 * Options for notifications
 */
export interface NotificationOptions {
  /** Duration in milliseconds (default: 5000) */
  duration?: number;
  /** Whether to show the close button (default: true) */
  dismissible?: boolean;
  /** Action button configuration */
  action?: {
    label: string;
    onClick: () => void;
  };
  /** ID for the toast (useful for updating/dismissing) */
  id?: string;
  /** Description for the toast */
  description?: string;
  /** Whether to replace existing notifications of the same category (default: false) */
  replace?: boolean;
  /** Category for notification replacement */
  category?: NotificationCategory;
}

/**
 * Default options for notifications
 */
const defaultOptions: NotificationOptions = {
  duration: 5000,
  dismissible: true,
};

/**
 * Track active notifications by category for replacement
 */
const activeNotifications = new Map<string, string>();

/**
 * Categories that should always use replacement to prevent stacking
 */
const AUTO_REPLACE_CATEGORIES: NotificationCategory[] = [
  "auto-save",
  "line-item",
  "form",
  "calculation",
  "export",
];

/**
 * Categories that should never be replaced (critical alerts)
 */
const NO_REPLACE_CATEGORIES: NotificationCategory[] = ["critical"];

/**
 * Handle notification replacement logic with smart rules
 */
const handleNotificationReplacement = (
  options: NotificationOptions,
  notificationType: NotificationType
): string | undefined => {
  const category = options.category;

  // Determine if replacement should occur
  const shouldReplace =
    options.replace || (category && AUTO_REPLACE_CATEGORIES.includes(category));

  // Never replace critical notifications
  const canReplace = !category || !NO_REPLACE_CATEGORIES.includes(category);

  if (shouldReplace && canReplace && category) {
    const existingId = activeNotifications.get(category);
    if (existingId) {
      toast.dismiss(existingId);
    }
  }

  const notificationId =
    options.id || `${category || "notification"}-${Date.now()}`;

  if (category && canReplace) {
    activeNotifications.set(category, notificationId);
  }

  return notificationId;
};

/**
 * Show a success notification
 * @param message - The message to display
 * @param options - Additional options
 */
export const showSuccess = (message: string, options?: NotificationOptions) => {
  const mergedOptions = { ...defaultOptions, ...options };
  const notificationId = handleNotificationReplacement(
    mergedOptions,
    "success"
  );

  return toast.success(message, {
    duration: mergedOptions.duration,
    dismissible: mergedOptions.dismissible,
    action: mergedOptions.action,
    id: notificationId,
    description: mergedOptions.description,
  });
};

/**
 * Show an error notification
 * @param message - The message to display
 * @param options - Additional options
 */
export const showError = (message: string, options?: NotificationOptions) => {
  const mergedOptions = {
    ...defaultOptions,
    ...options,
    duration: options?.duration || 8000,
  };
  const notificationId = handleNotificationReplacement(mergedOptions, "error");

  return toast.error(message, {
    duration: mergedOptions.duration,
    dismissible: mergedOptions.dismissible,
    action: mergedOptions.action,
    id: notificationId,
    description: mergedOptions.description,
  });
};

/**
 * Show an info notification
 * @param message - The message to display
 * @param options - Additional options
 */
export const showInfo = (message: string, options?: NotificationOptions) => {
  const mergedOptions = { ...defaultOptions, ...options };
  const notificationId = handleNotificationReplacement(mergedOptions, "info");

  return toast.info(message, {
    duration: mergedOptions.duration,
    dismissible: mergedOptions.dismissible,
    action: mergedOptions.action,
    id: notificationId,
    description: mergedOptions.description,
  });
};

/**
 * Show a warning notification
 * @param message - The message to display
 * @param options - Additional options
 */
export const showWarning = (message: string, options?: NotificationOptions) => {
  const mergedOptions = { ...defaultOptions, ...options };
  const notificationId = handleNotificationReplacement(
    mergedOptions,
    "warning"
  );

  return toast.warning(message, {
    duration: mergedOptions.duration,
    dismissible: mergedOptions.dismissible,
    action: mergedOptions.action,
    id: notificationId,
    description: mergedOptions.description,
  });
};

/**
 * Show a loading notification
 * @param message - The message to display
 * @param options - Additional options
 */
export const showLoading = (message: string, options?: NotificationOptions) => {
  const mergedOptions = {
    ...defaultOptions,
    ...options,
    duration: options?.duration || Infinity,
  };

  return toast.loading(message, {
    duration: mergedOptions.duration,
    dismissible: mergedOptions.dismissible,
    id: mergedOptions.id,
    description: mergedOptions.description,
  });
};

/**
 * Dismiss a specific toast by ID
 * @param id - The ID of the toast to dismiss
 */
export const dismissToast = (id: string) => {
  toast.dismiss(id);
};

/**
 * Update an existing toast
 * @param id - The ID of the toast to update
 * @param message - The new message
 * @param type - The new type
 * @param options - Additional options
 */
export const updateToast = (
  id: string,
  message: string,
  type: NotificationType,
  options?: NotificationOptions
) => {
  const mergedOptions = { ...defaultOptions, ...options };

  switch (type) {
    case "success":
      toast.success(message, {
        id,
        duration: mergedOptions.duration,
        dismissible: mergedOptions.dismissible,
        action: mergedOptions.action,
        description: mergedOptions.description,
      });
      break;
    case "error":
      toast.error(message, {
        id,
        duration: mergedOptions.duration,
        dismissible: mergedOptions.dismissible,
        action: mergedOptions.action,
        description: mergedOptions.description,
      });
      break;
    case "info":
      toast.info(message, {
        id,
        duration: mergedOptions.duration,
        dismissible: mergedOptions.dismissible,
        action: mergedOptions.action,
        description: mergedOptions.description,
      });
      break;
    case "warning":
      toast.warning(message, {
        id,
        duration: mergedOptions.duration,
        dismissible: mergedOptions.dismissible,
        action: mergedOptions.action,
        description: mergedOptions.description,
      });
      break;
    case "loading":
      toast.loading(message, {
        id,
        duration: mergedOptions.duration,
        dismissible: mergedOptions.dismissible,
        description: mergedOptions.description,
      });
      break;
    default:
      toast(message, {
        id,
        duration: mergedOptions.duration,
        dismissible: mergedOptions.dismissible,
        action: mergedOptions.action,
        description: mergedOptions.description,
      });
  }
};

/**
 * Promise toast - shows loading while promise is pending, then success or error
 * @param promise - The promise to track
 * @param messages - Messages for different states
 * @param options - Additional options
 */
export const promiseToast = <T>(
  promise: Promise<T>,
  messages: {
    loading: string;
    success: string;
    error: string;
  },
  options?: NotificationOptions
) => {
  const mergedOptions = { ...defaultOptions, ...options };

  return toast.promise(promise, {
    loading: messages.loading,
    success: messages.success,
    error: messages.error,
    duration: mergedOptions.duration,
    dismissible: mergedOptions.dismissible,
    action: mergedOptions.action,
    id: mergedOptions.id,
    description: mergedOptions.description,
  });
};

/**
 * Show an undoable success notification with undo action
 * @param message - The message to display
 * @param undoAction - Function to call when undo is clicked
 * @param options - Additional options
 */
export const showUndoableSuccess = (
  message: string,
  undoAction: () => void,
  options?: Omit<NotificationOptions, "action">
) => {
  return showSuccess(message, {
    ...options,
    action: {
      label: "Undo",
      onClick: undoAction,
    },
  });
};

/**
 * Show batch operation success notification
 * @param count - Number of items processed
 * @param itemType - Type of items (e.g., "packages", "clients")
 * @param options - Additional options
 */
export const showBatchSuccess = (
  count: number,
  itemType: string,
  options?: NotificationOptions
) => {
  const message = `${count} ${itemType} processed successfully`;
  return showSuccess(message, options);
};

/**
 * Category-specific notification helpers with automatic replacement
 */
export const categoryNotifications = {
  // Calculation operations
  calculation: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "calculation" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "calculation" }),
  },

  // Client management
  client: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "client" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "client" }),
  },

  // Template operations
  template: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "template" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "template" }),
  },

  // Package management
  package: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "package" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "package" }),
  },

  // Form submissions
  form: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "form" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "form" }),
  },

  // Line item operations
  lineItem: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "line-item" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "line-item" }),
  },

  // Auto-save operations
  autoSave: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "auto-save" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "auto-save" }),
  },

  // Bulk operations
  bulk: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "bulk" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "bulk" }),
  },

  // Critical alerts (never replaced)
  critical: {
    success: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showSuccess(message, { ...options, category: "critical" }),
    error: (message: string, options?: Omit<NotificationOptions, "category">) =>
      showError(message, { ...options, category: "critical" }),
    warning: (
      message: string,
      options?: Omit<NotificationOptions, "category">
    ) => showWarning(message, { ...options, category: "critical" }),
  },
};

/**
 * Convenience exports for common patterns
 */
export const notifications = {
  success: showSuccess,
  error: showError,
  info: showInfo,
  warning: showWarning,
  loading: showLoading,
  promise: promiseToast,
  undoable: showUndoableSuccess,
  batch: showBatchSuccess,
  dismiss: dismissToast,
  update: updateToast,

  // Category-specific helpers
  ...categoryNotifications,
};
