## Calculations (`/calculations`)

### 1. Create Calculation

- **Method:** `POST`
- **URL:** `/calculations`
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Body:** `raw (JSON)` (Example - adjust based on optional fields)
  ```json
  {
    "name": "My New Event Calculation",
    "currency_id": "valid-currency-uuid",
    "city_id": "optional-city-uuid",
    "event_start_date": "2025-10-20",
    "event_end_date": "2025-10-22",
    "attendees": 150,
    "event_type": "Corporate Gala",
    "notes": "Initial draft for client X.",
    "client_id": "optional-client-uuid",
    "event_id": "optional-event-uuid",
    "taxes": [
      { "name": "PPN", "rate": 2, "type": "percentage" },
      { "name": "PPH", "rate": 2, "type": "percentage" }
    ],
    "discount": { "name": "Early Bird", "amount": 100.0 }
  }
  ```
- **Description:** Creates a new calculation record in a 'draft' state.
- **Success Response (201 Created):**
  ```json
  {
    "id": "newly-created-calculation-uuid"
  }
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If validation fails (e.g., missing required fields, invalid UUID format).
- **Error Response (500 Internal Server Error):** If database insertion fails.

### 2. List Calculations

- **Method:** `GET`
- **URL:** `/calculations`
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Query Parameters (from `ListCalculationsDto` & `PaginationQueryDto`):**
  - `limit`: Number (Default: 20) - Max items per page.
  - `offset`: Number (Default: 0) - Items to skip for pagination.
  - `sortBy`: String (Default: `created_at`) - Field to sort by (e.g., `name`, `status`, `created_at`, `updated_at`, `event_start_date`, `total`).
  - `sortOrder`: String (Default: `desc`) - Sort order (`asc` or `desc`).
  - `status`: String (Enum: `draft`, `quoted`, `confirmed`, `cancelled`) - Filter by calculation status.
  - `clientId`: UUID (String) - Filter by client UUID.
- **Example URL:** `/calculations?limit=10&offset=0&status=draft&sortBy=name&sortOrder=asc`
- **Description:** Retrieves a paginated list of calculations created by the authenticated user, with filtering and sorting options.
- **Success Response (200 OK):** Returns `PaginatedResponseDto<CalculationSummaryDto>`
  ```json
  {
    "data": [
      {
        "id": "calc-uuid-1",
        "name": "Calculation A",
        "status": "draft", // Enum: CalculationStatus
        "event_start_date": "2025-11-01", // ISO Date string or null
        "event_end_date": null, // ISO Date string or null
        "total": 1500.0, // Represents grand_total
        "currency_id": "currency-uuid-usd",
        "created_at": "timestamp",
        "updated_at": "timestamp",
        "client_id": "client-uuid-optional", // Optional UUID
        "event_id": "event-uuid-optional" // Optional UUID
      }
      // ... more calculation summaries
    ],
    "count": 25 // Total number of matching calculations
  }
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If query parameters are invalid (e.g., non-numeric limit/offset, invalid status enum, invalid UUID format).

### 3. Get Calculation Details

- **Method:** `GET`
- **URL:** `/calculations/{id}` (Replace `{id}` with the actual calculation UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Description:** Retrieves detailed information for a specific calculation, including related client, event, line items, and custom items.
- **Success Response (200 OK):**
  ```json
  // Example Structure (matches CalculationDetailDto)
  {
    "id": "calc-uuid-1",
    "name": "Calculation A",
    "calculation_slug": "calculation-a-qwerty",
    "currency": {
      "id": "currency-uuid-usd",
      "name": "US Dollar",
      "symbol": "$",
      "code": "USD"
    },
    "city": {
      "id": "city-uuid",
      "name": "New York"
    },
    "event_start_date": "2025-11-01",
    "event_end_date": "2025-11-01",
    "attendees": 100,
    "event_type": "Wedding Reception",
    "notes": "Notes about this calculation.",
    "version_notes": null,
    "subtotal": 1400.0,
    "taxes": [{ "name": "VAT", "rate": 0.2, "amount": 280.0 }],
    "discount": { "name": "Early Bird", "amount": 100.0 },
    "total_tax_amount": 280.0,
    "total_discount_amount": 100.0,
    "total": 1580.0,
    "estimated_profit": 780.0,
    "created_at": "timestamp",
    "updated_at": "timestamp",
    "created_by": "user-uuid",
    "client": null,
    "event": null,
    "line_items": [
      {
        "id": "line-item-uuid-1",
        "package_id": "package-uuid-catering",
        "item_name_snapshot": "Standard Catering Package",
        "option_summary_snapshot": "Vegan Option",
        "item_quantity": 100,
        "duration_days": 1,
        "price": 10.0,
        "options_total_adjustment": 2.0,
        "calculated_line_total": 1200.0,
        "notes": null,
        "unit_base_cost_snapshot": 5.0,
        "options_total_cost_snapshot": 1.0,
        "calculated_line_cost": 600.0,
        "options": [
          {
            "id": "option-uuid-vegan",
            "option_name_snapshot": "Vegan Option",
            "price_adjustment_snapshot": 2.0
          }
        ]
      }
    ],
    "custom_items": [
      {
        "id": "custom-item-uuid-1",
        "item_name": "Custom Floral Arrangement",
        "description": "Roses and lilies",
        "quantity": 1,
        "unit_price": 200.0,
        "unit_cost": 100.0
      }
    ],
    "is_template": false,
    "template_id": null
  }
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If the provided ID is not a valid UUID.
- **Error Response (404 Not Found):** If the calculation with the given ID is not found or the user does not have permission to access it.

### 4. Update Calculation

- **Method:** `PUT`
- **URL:** `/calculations/{id}` (Replace `{id}` with the calculation UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Body:** `raw (JSON)` (Send only the fields you want to update)
  ```json
  {
    "name": "Updated Calculation Name",
    "status": "completed",
    "notes": "Client approved.",
    "taxes": [{ "name": "Service Tax", "rate": 0.05 }],
    "discount": null
  }
  ```
- **Description:** Updates general information, status, taxes, or discount for a specific calculation. **Note:** Recalculation logic based on tax/discount changes is currently a TODO in the service.
- **Success Response (200 OK):** Returns the complete, updated calculation detail object (same structure as `GET /calculations/{id}`).
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If the provided ID is not a valid UUID or if the request body fails validation (e.g., invalid status enum).
- **Error Response (404 Not Found):** If the calculation with the given ID is not found or the user does not have permission to access it.

### 5. Delete Calculation (Soft Delete)

- **Method:** `DELETE`
- **URL:** `/calculations/{id}` (Replace `{id}` with the calculation UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Description:** Marks a calculation as deleted (`is_deleted = true`). Does not permanently remove the record.
- **Success Response:** `204 No Content` (Indicates successful deletion with no response body)
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If the provided ID is not a valid UUID.
- **Error Response (404 Not Found):** If the calculation with the given ID is not found, already deleted, or the user does not have permission to access it.

### 6. Create Calculation from Template

- **Method:** `POST`
- **URL:** `/calculations/from-template/{templateId}` (Replace `{templateId}` with a valid and accessible template UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Body:** None
- **Description:** Creates a new calculation in 'draft' status, based on the structure defined in the specified template's `package_selections`. Requires the user to have access to the template (either public or their own). This now includes creating the corresponding `calculation_line_items` and `calculation_line_item_options` with snapshot data.
- **Success Response (201 Created):**
  ```json
  {
    "id": "newly-created-calculation-uuid"
  }
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If `{templateId}` is not a valid UUID.
- **Error Response (404 Not Found):** If the template with `{templateId}` is not found, the user lacks access, or if any required packages/options/prices within the template blueprint are not found for the default currency (IDR).
- **Error Response (500 Internal Server Error):** If fetching/creating calculation history or line items fails due to database errors or unexpected issues.
- **Note:** The default currency for the new calculation is set to IDR (`685860b9-257f-41eb-b223-b3e1fad8f3b9`). Overall calculation totals (subtotal, total, etc.) are not recalculated automatically yet.

### 7. Add Package Line Item

- **Method:** `POST`
- **URL:** `/calculations/{calcId}/line-items/package` (Replace `{calcId}` with calculation UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Body:** `raw (JSON)` (matches `AddPackageLineItemDto`)
  ```json
  {
    "packageId": "uuid-of-package-to-add",
    "optionIds": ["uuid-of-selected-option-1", "uuid-of-selected-option-2"],
    "quantity": 100, // Optional override
    "duration": 2, // Optional override
    "notes": "Added via API test."
  }
  ```
- **Description:** Adds a standard package (and its selected options) as a line item to the specified calculation. **CORE LOGIC PENDING IMPLEMENTATION.**
- **Success Response (200 OK / 201 Created):** TBD - Likely returns updated calculation details or just the new line item ID.
- **Error Response (401 Unauthorized):** Token missing/invalid.
- **Error Response (403 Forbidden):** User doesn't own calculation.
- **Error Response (400 Bad Request):** Invalid UUIDs, missing packageId, validation errors.
- **Error Response (404 Not Found):** Calculation, Package, Option, or Price not found.
- **Error Response (409 Conflict):** If package conflicts with dependencies (Not implemented yet).
- **Error Response (500 Internal Server Error):** Database errors.

### 8. Add Custom Line Item

- **Method:** `POST`
- **URL:** `/calculations/{calcId}/line-items/custom` (Replace `{calcId}` with calculation UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Body:** `raw (JSON)` (matches `AddCustomLineItemDto`)
  ```json
  {
    "itemName": "Custom Service Fee",
    "description": "Special handling request",
    "quantity": 1.5,
    "unitPrice": 250.75,
    "unitCost": 50.0,
    "cityId": null,
    "categoryId": null
  }
  ```
- **Description:** Adds a custom line item to the specified calculation.
- **Success Response (200 OK / 201 Created):**
  ```json
  {
    "id": "new-custom-item-uuid"
  }
  ```
- **Error Response (401 Unauthorized):** Token missing/invalid.
- **Error Response (403 Forbidden):** User doesn't own calculation.
- **Error Response (400 Bad Request):** Invalid input (missing name, invalid numbers, etc.).
- **Error Response (404 Not Found):** Calculation not found.
- **Error Response (500 Internal Server Error):** Database errors.

### 9. Delete Package Line Item

- **Method:** `DELETE`
- **URL:** `/calculations/{calcId}/line-items/{itemId}` (Replace `{calcId}` and `{itemId}` with UUIDs)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Description:** Deletes a standard package line item (and its associated options via potential DB cascade) from a calculation.
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Token missing/invalid.
- **Error Response (403 Forbidden):** User doesn't own calculation.
- **Error Response (400 Bad Request):** Invalid UUIDs.
- **Error Response (404 Not Found):** Calculation or Line Item not found.
- **Error Response (500 Internal Server Error):** Database errors.

### 10. Delete Custom Line Item

- **Method:** `DELETE`
- **URL:** `/calculations/{calcId}/custom-items/{itemId}` (Replace `{calcId}` and `{itemId}` with UUIDs)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Description:** Deletes a custom line item from a calculation.
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Token missing/invalid.
- **Error Response (403 Forbidden):** User doesn't own calculation.
- **Error Response (400 Bad Request):** Invalid UUIDs.
- **Error Response (404 Not Found):** Calculation or Custom Item not found.
- **Error Response (500 Internal Server Error):** Database errors.
