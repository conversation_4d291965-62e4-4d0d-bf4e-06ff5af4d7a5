import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PackageCityDto } from './dto/package-city.dto';

// Define expected structure from Supabase query
interface PackageCityQueryResult {
  city: {
    id: string;
    name: string;
  } | null; // The joined city might be null
}

@Injectable()
export class PackageCitiesService {
  private readonly logger = new Logger(PackageCitiesService.name);
  private readonly PACKAGE_CITIES_TABLE = 'package_cities'; // Corrected table name
  private readonly CITIES_TABLE = 'cities'; // Corrected table name
  private readonly PACKAGES_TABLE = 'packages'; // Corrected table name

  constructor(private readonly supabaseService: SupabaseService) {}

  async addCityToPackage(
    packageId: string,
    cityId: string,
  ): Promise<{ id: string }> {
    this.logger.log(`Attempting to add city ${cityId} to package ${packageId}`);
    const supabase = this.supabaseService.getClient();

    // 1. Check if package exists (Optional but recommended)

    // 2. Check if city exists (Optional but recommended)

    // 3. Check if association already exists

    // 4. Create the association
    const { data, error } = await supabase
      .from(this.PACKAGE_CITIES_TABLE)
      .insert({ package_id: packageId, city_id: cityId })
      .select('id'); // Select only the ID

    // Explicitly type the result from insert().select()
    const typedData = data as { id: string }[] | null;

    if (error) {
      // Handle potential errors (e.g., duplicate entry - conflict, FK violation - not found)
      this.logger.error(
        `Error adding city ${cityId} to package ${packageId}: ${error.message}`,
      );
      // Add specific error handling based on error codes (e.g., '23505' for unique violation)
      if (error.code === '23505') {
        throw new ConflictException(
          'This city is already associated with the package.',
        );
      }
      if (error.code === '23503') {
        // Foreign key violation
        throw new NotFoundException('Package or City not found.');
      }
      throw new InternalServerErrorException('Failed to add city to package.');
    }

    // Ensure data is not null and has at least one record before accessing it
    if (!typedData || typedData.length === 0) {
      this.logger.error(
        `Insert operation did not return the expected data for package ${packageId}, city ${cityId}`,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve association ID after insert.',
      );
    }

    this.logger.log(
      `Successfully added city ${cityId} to package ${packageId} with association ID: ${typedData[0].id}`,
    );
    return { id: typedData[0].id }; // Return the newly created ID
  }

  async listCitiesForPackage(packageId: string): Promise<PackageCityDto[]> {
    this.logger.log(`Fetching cities for package ${packageId}`);
    const supabase = this.supabaseService.getClient();

    // Query the junction table and join with the cities table
    // Select id and name directly from the related cities table
    const { data, error } = await supabase
      .from(this.PACKAGE_CITIES_TABLE)
      .select(
        `
        city: ${this.CITIES_TABLE} (
          id,
          name
        )
      `,
      )
      .eq('package_id', packageId)
      .returns<PackageCityQueryResult[]>(); // Specify the expected return type

    if (error) {
      this.logger.error(
        `Error fetching cities for package ${packageId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Error fetching cities for package.',
      );
    }

    // The result 'data' will be an array of { city: { id: string, name: string } | null }
    // We need to map it to PackageCityDto[]
    const cities: PackageCityDto[] =
      data
        ?.map(item => item.city) // Extract the city object
        .filter((city): city is { id: string; name: string } => city !== null) // Type guard to filter out nulls and narrow type
        .map(city => ({ id: city.id, name: city.name })) || []; // Map to the DTO structure // Default to empty array if data is null/undefined

    // Fix persistent formatting error for comment placement
    // We need to map it to PackageCityDto[]
    // const cities: PackageCityDto[] = ...

    if (cities.length === 0) {
      // Optional: Check if the package itself exists to differentiate between
      // 'package not found' and 'package exists but has no cities'.
      const { count: packageCount } = await supabase
        .from(this.PACKAGES_TABLE)
        .select('*', { count: 'exact', head: true })
        .eq('id', packageId);

      if (packageCount === 0) {
        throw new NotFoundException(`Package with ID ${packageId} not found.`);
      }
    }

    return cities;
  }

  async removeCityFromPackage(
    packageId: string,
    cityId: string,
  ): Promise<void> {
    this.logger.log(
      `Attempting to remove city ${cityId} from package ${packageId}`,
    );
    const supabase = this.supabaseService.getClient();

    const { error, count } = await supabase
      .from(this.PACKAGE_CITIES_TABLE)
      .delete()
      .eq('package_id', packageId)
      .eq('city_id', cityId);

    if (error) {
      this.logger.error(
        `Error removing city ${cityId} from package ${packageId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Failed to remove city from package.',
      );
    }

    if (count === 0) {
      // This could mean the package doesn't exist, the city doesn't exist,
      // or the association didn't exist.
      this.logger.warn(
        `Association between package ${packageId} and city ${cityId} not found for deletion.`,
      );
      throw new NotFoundException('Package/City association not found.');
    }

    this.logger.log(
      `Successfully removed city ${cityId} from package ${packageId}`,
    );
  }
}
