import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import {
  getPackageOptions,
  savePackageOption,
  deletePackageOption,
} from "../../../../services/admin/packages/packageOptionService";
import {
  SavePackageOptionData,
  PackageOptionFormValues,
} from "../types/packageOptions";
import { toast } from "sonner";
import { QUERY_KEYS } from "@/lib/queryKeys";

/**
 * Custom hook for managing package options
 * @param packageId - The package ID
 * @returns Package options data, loading state, and CRUD functions
 */
export const usePackageOptions = (packageId: string) => {
  const queryClient = useQueryClient();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedOptionId, setSelectedOptionId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [optionToDelete, setOptionToDelete] = useState<string | null>(null);

  // Fetch package options
  const {
    data: options = [],
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: QUERY_KEYS.packages.options(packageId),
    queryFn: () => getPackageOptions(packageId),
    enabled: !!packageId,
    meta: {
      onError: () => {
        toast.error("Failed to load package options");
      },
    },
  });

  // Save package option mutation
  const saveOptionMutation = useMutation({
    mutationFn: (data: SavePackageOptionData) => savePackageOption(data),
    onSuccess: () => {
      toast.success(
        `Option ${selectedOptionId ? "updated" : "created"} successfully`
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.packages.options(packageId),
      });
      setIsFormOpen(false);
      setSelectedOptionId(null);
    },
    onError: (error) => {
      toast.error(
        `Failed to ${selectedOptionId ? "update" : "create"} option: ${
          error.message
        }`
      );
    },
  });

  // Delete package option mutation
  const deleteOptionMutation = useMutation({
    mutationFn: (optionId: string) => deletePackageOption(packageId, optionId),
    onSuccess: () => {
      toast.success("Option deleted successfully");
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.packages.options(packageId),
      });
      setIsDeleteDialogOpen(false);
      setOptionToDelete(null);
    },
    onError: (error) => {
      toast.error(`Failed to delete option: ${error.message}`);
    },
  });

  // Handle form submission
  const handleSaveOption = (values: PackageOptionFormValues) => {
    saveOptionMutation.mutate({
      id: selectedOptionId || undefined,
      option_name: values.option_name,
      option_code: values.option_code,
      description: values.description || null,
      price_adjustment: parseFloat(values.price_adjustment),
      cost_adjustment: parseFloat(values.cost_adjustment),
      currency_id: "IDR", // Default currency, should be dynamic in a real app
      is_default_for_package: values.is_default_for_package,
      is_required: values.is_required,
      applicable_package_id: packageId,
    });
  };

  // Handle option deletion
  const handleDeleteOption = () => {
    if (optionToDelete) {
      deleteOptionMutation.mutate(optionToDelete);
    }
  };

  return {
    options,
    isLoading,
    isError,
    isFormOpen,
    setIsFormOpen,
    selectedOptionId,
    setSelectedOptionId,
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    optionToDelete,
    setOptionToDelete,
    handleSaveOption,
    handleDeleteOption,
    refetch,
  };
};
