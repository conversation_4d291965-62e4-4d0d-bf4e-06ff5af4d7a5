import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { DateRange } from "react-day-picker";
import { createCalculation } from "../../../../services/calculations";
import { CalculationFormData } from "@/types/calculations";
import {
  calculationFormSchema,
  type CalculationFormValues,
} from "../../schemas";
import { CURRENCY_CONSTANTS } from "../../constants";
import { convertDateRangeForSubmission } from "@/lib/timezone-utils";
import { useUserPreferences } from "@/hooks/useUserPreferences";

export type FormValues = CalculationFormValues;

// Define step type
export type FormStep = "basic-info" | "additional-details";

export const useCalculationForm = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState<FormStep>("basic-info");

  // Get user timezone preferences
  const { timezone } = useUserPreferences();

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(calculationFormSchema),
    defaultValues: {
      name: "",
      city_id: "",
      venue_ids: [],
      date_range: {
        from: undefined,
        to: undefined,
      } as DateRange,
      attendees: "",
      event_type_id: "",
      notes: "",
      client_id: "none",
      event_id: "none",
    },
  });

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      // Additional validation before submission
      if (!values.date_range?.from || !values.date_range?.to) {
        if (!values.date_range?.from) {
          toast.error("Please select a start date for your event");
        } else {
          toast.error("Please select an end date to complete the date range");
        }
        return;
      }

      if (!values.attendees || parseInt(values.attendees) <= 0) {
        toast.error("Please enter a valid number of attendees");
        return;
      }

      if (values.venue_ids.length === 0) {
        toast.error("Please select at least one venue");
        return;
      }

      // Format dates using timezone-aware conversion to avoid "picked 19th but submitting 18th" issue
      const { startDate, endDate } = convertDateRangeForSubmission(
        values.date_range,
        timezone
      );

      // Prepare data for API
      const calculationData: CalculationFormData = {
        name: values.name,
        currency_id: CURRENCY_CONSTANTS.DEFAULT_CURRENCY_ID, // Default to IDR
        city_id: values.city_id,
        venue_ids: values.venue_ids,
        event_start_date: startDate,
        event_end_date: endDate,
        attendees: Number(values.attendees),
        event_type_id: values.event_type_id || null, // Use native event type ID
        notes: values.notes || "", // Ensure notes is never undefined
        status: "draft",
        client_id: values.client_id === "none" ? undefined : values.client_id,
        event_id: values.event_id === "none" ? undefined : values.event_id,
      };

      console.log("Submitting calculation data:", calculationData);

      // Create calculation
      const result = await createCalculation(calculationData);

      toast.success("Calculation created successfully");

      // Navigate to the calculation detail page
      navigate(`/calculations/${result.id}`);
    } catch (error) {
      console.error("Error creating calculation:", error);

      // Extract more detailed error message if available
      let errorMessage = "Failed to create calculation";

      if (error.response?.data?.message) {
        // API error with message
        errorMessage += `: ${error.response.data.message}`;
      } else if (error.response?.data?.error) {
        // Another common API error format
        errorMessage += `: ${error.response.data.error}`;
      } else if (error.message) {
        // Standard error object
        errorMessage += `: ${error.message}`;
      }

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle next step
  const handleNextStep = async () => {
    // Validate current step fields
    if (currentStep === "basic-info") {
      const isValid = await form.trigger([
        "name",
        "event_type_id",
        "date_range",
        "notes",
        "client_id",
      ]);

      // Additional validation for date range to provide better error messages
      const dateRange = form.getValues("date_range");
      if (!dateRange?.from || !dateRange?.to) {
        if (!dateRange?.from) {
          form.setError("date_range", {
            type: "manual",
            message: "Please select a start date",
          });
        } else if (!dateRange?.to) {
          form.setError("date_range", {
            type: "manual",
            message: "Please select an end date to complete the date range",
          });
        }
        return;
      }

      if (isValid) {
        setCurrentStep("additional-details");
      }
    }
  };

  // Handle previous step
  const handlePreviousStep = () => {
    setCurrentStep("basic-info");
  };

  return {
    form,
    isSubmitting,
    currentStep,
    onSubmit,
    handleNextStep,
    handlePreviousStep,
  };
};
