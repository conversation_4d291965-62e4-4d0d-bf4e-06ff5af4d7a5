import { ApiProperty } from '@nestjs/swagger';

// DTO for dependent package information
export class DependentPackageDto {
  @ApiProperty({ description: 'Package name' })
  name: string;
}

// DTO for returning package dependency details
export class PackageDependencyDto {
  @ApiProperty({ description: 'Dependency record UUID', format: 'uuid' })
  id: string;

  @ApiProperty({
    description: 'The package that has the dependency',
    format: 'uuid',
  })
  package_id: string;

  @ApiProperty({
    description: 'The package that is depended upon (or conflicted with)',
    format: 'uuid',
  })
  dependent_package_id: string;

  @ApiProperty({ description: 'Type of dependency (e.g., REQUIRES)' })
  dependency_type: string;

  @ApiProperty({ description: 'Description', nullable: true })
  description: string | null;

  @ApiProperty({ description: 'Creation timestamp' })
  created_at: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updated_at: string;

  @ApiProperty({
    description: 'Dependent package information',
    type: DependentPackageDto,
    nullable: true
  })
  dependent_package?: DependentPackageDto | null;
}
