import { ApiProperty } from '@nestjs/swagger';

export class CategoryDto {
  @ApiProperty({ type: String, format: 'uuid' })
  id: string;

  @ApiProperty({ example: 'VENUE_RENTAL' })
  code: string;

  @ApiProperty({ example: 'Venue Rental' })
  name: string;

  @ApiProperty({ nullable: true, example: 'Rental fees for event spaces.' })
  description: string | null;

  @ApiProperty({ nullable: true, example: 'fa-building' })
  icon: string | null;

  @ApiProperty({
    example: 1,
    description:
      'Order in which the category should be displayed (lower values first)',
  })
  display_order: number;

  @ApiProperty({ type: String, format: 'date-time' })
  created_at: string;

  @ApiProperty({ type: String, format: 'date-time' })
  updated_at: string;
}
