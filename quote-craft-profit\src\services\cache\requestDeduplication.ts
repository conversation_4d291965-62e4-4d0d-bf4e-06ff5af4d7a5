/**
 * Request Deduplication Service
 * 
 * PHASE 2: Prevents duplicate simultaneous requests to improve performance
 * and reduce server load. Implements intelligent request batching and caching.
 */

import { QueryClient } from '@tanstack/react-query';

export interface PendingRequest<T = any> {
  promise: Promise<T>;
  timestamp: number;
  requestKey: string;
  abortController?: AbortController;
}

export interface DeduplicationOptions {
  enabled?: boolean;
  maxAge?: number; // Maximum age of pending request in ms
  includeHeaders?: boolean;
  includeBody?: boolean;
  customKeyGenerator?: (url: string, options?: any) => string;
}

class RequestDeduplicationService {
  private pendingRequests = new Map<string, PendingRequest>();
  private readonly defaultOptions: Required<DeduplicationOptions> = {
    enabled: true,
    maxAge: 5000, // 5 seconds
    includeHeaders: false,
    includeBody: true,
    customKeyGenerator: this.defaultKeyGenerator.bind(this),
  };

  /**
   * Default key generator for request deduplication
   */
  private defaultKeyGenerator(url: string, options?: any): string {
    const method = options?.method || 'GET';
    const body = this.defaultOptions.includeBody && options?.body 
      ? JSON.stringify(options.body) 
      : '';
    const headers = this.defaultOptions.includeHeaders && options?.headers
      ? JSON.stringify(options.headers)
      : '';
    
    return `${method}:${url}:${body}:${headers}`;
  }

  /**
   * Generate request key for deduplication
   */
  private generateRequestKey(
    url: string, 
    options?: any, 
    customOptions?: DeduplicationOptions
  ): string {
    const opts = { ...this.defaultOptions, ...customOptions };
    return opts.customKeyGenerator(url, options);
  }

  /**
   * Clean up expired pending requests
   */
  private cleanupExpiredRequests(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, request] of this.pendingRequests.entries()) {
      if (now - request.timestamp > this.defaultOptions.maxAge) {
        expiredKeys.push(key);
        // Cancel the request if it has an abort controller
        if (request.abortController) {
          request.abortController.abort();
        }
      }
    }

    expiredKeys.forEach(key => this.pendingRequests.delete(key));
  }

  /**
   * Deduplicate fetch requests
   */
  async deduplicatedFetch<T = any>(
    url: string,
    options?: RequestInit,
    deduplicationOptions?: DeduplicationOptions
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...deduplicationOptions };
    
    if (!opts.enabled) {
      return fetch(url, options).then(res => res.json());
    }

    // Clean up expired requests
    this.cleanupExpiredRequests();

    const requestKey = this.generateRequestKey(url, options, opts);
    
    // Check if request is already pending
    const existingRequest = this.pendingRequests.get(requestKey);
    if (existingRequest) {
      console.debug(`🔄 Request deduplicated: ${requestKey}`);
      return existingRequest.promise as Promise<T>;
    }

    // Create abort controller for request cancellation
    const abortController = new AbortController();
    const requestOptions = {
      ...options,
      signal: abortController.signal,
    };

    // Create new request
    const promise = fetch(url, requestOptions)
      .then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .finally(() => {
        // Remove from pending requests when completed
        this.pendingRequests.delete(requestKey);
      });

    // Store pending request
    const pendingRequest: PendingRequest<T> = {
      promise,
      timestamp: Date.now(),
      requestKey,
      abortController,
    };

    this.pendingRequests.set(requestKey, pendingRequest);
    console.debug(`📤 New request created: ${requestKey}`);

    return promise;
  }

  /**
   * Deduplicate React Query requests
   */
  async deduplicatedQuery<T = any>(
    queryClient: QueryClient,
    queryKey: any[],
    queryFn: () => Promise<T>,
    deduplicationOptions?: DeduplicationOptions
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...deduplicationOptions };
    
    if (!opts.enabled) {
      return queryFn();
    }

    // Clean up expired requests
    this.cleanupExpiredRequests();

    const requestKey = `query:${JSON.stringify(queryKey)}`;
    
    // Check if query is already pending
    const existingRequest = this.pendingRequests.get(requestKey);
    if (existingRequest) {
      console.debug(`🔄 Query deduplicated: ${requestKey}`);
      return existingRequest.promise as Promise<T>;
    }

    // Create new query request
    const promise = queryFn()
      .finally(() => {
        // Remove from pending requests when completed
        this.pendingRequests.delete(requestKey);
      });

    // Store pending request
    const pendingRequest: PendingRequest<T> = {
      promise,
      timestamp: Date.now(),
      requestKey,
    };

    this.pendingRequests.set(requestKey, pendingRequest);
    console.debug(`📤 New query created: ${requestKey}`);

    return promise;
  }

  /**
   * Cancel all pending requests
   */
  cancelAllRequests(): void {
    for (const [key, request] of this.pendingRequests.entries()) {
      if (request.abortController) {
        request.abortController.abort();
      }
    }
    this.pendingRequests.clear();
    console.debug('🚫 All pending requests cancelled');
  }

  /**
   * Cancel specific request by key
   */
  cancelRequest(requestKey: string): boolean {
    const request = this.pendingRequests.get(requestKey);
    if (request) {
      if (request.abortController) {
        request.abortController.abort();
      }
      this.pendingRequests.delete(requestKey);
      console.debug(`🚫 Request cancelled: ${requestKey}`);
      return true;
    }
    return false;
  }

  /**
   * Get pending requests statistics
   */
  getStats(): {
    pendingCount: number;
    oldestRequest: number | null;
    requestKeys: string[];
  } {
    const now = Date.now();
    const requests = Array.from(this.pendingRequests.values());
    
    return {
      pendingCount: requests.length,
      oldestRequest: requests.length > 0 
        ? Math.min(...requests.map(r => now - r.timestamp))
        : null,
      requestKeys: Array.from(this.pendingRequests.keys()),
    };
  }

  /**
   * Configure deduplication options
   */
  configure(options: Partial<DeduplicationOptions>): void {
    Object.assign(this.defaultOptions, options);
    console.debug('🔧 Request deduplication configured:', this.defaultOptions);
  }

  /**
   * Check if request is currently pending
   */
  isPending(url: string, options?: any): boolean {
    const requestKey = this.generateRequestKey(url, options);
    return this.pendingRequests.has(requestKey);
  }

  /**
   * Get pending request promise if exists
   */
  getPendingRequest<T = any>(url: string, options?: any): Promise<T> | null {
    const requestKey = this.generateRequestKey(url, options);
    const request = this.pendingRequests.get(requestKey);
    return request ? request.promise as Promise<T> : null;
  }
}

// Create singleton instance
export const requestDeduplication = new RequestDeduplicationService();

/**
 * Enhanced fetch with automatic request deduplication
 */
export const deduplicatedFetch = <T = any>(
  url: string,
  options?: RequestInit,
  deduplicationOptions?: DeduplicationOptions
): Promise<T> => {
  return requestDeduplication.deduplicatedFetch<T>(url, options, deduplicationOptions);
};

/**
 * React Query wrapper with deduplication
 */
export const createDeduplicatedQuery = <T = any>(
  queryClient: QueryClient,
  queryKey: any[],
  queryFn: () => Promise<T>,
  deduplicationOptions?: DeduplicationOptions
) => {
  return () => requestDeduplication.deduplicatedQuery<T>(
    queryClient, 
    queryKey, 
    queryFn, 
    deduplicationOptions
  );
};

/**
 * Hook for request deduplication statistics
 */
export const useRequestDeduplicationStats = () => {
  return {
    getStats: () => requestDeduplication.getStats(),
    cancelAll: () => requestDeduplication.cancelAllRequests(),
    configure: (options: Partial<DeduplicationOptions>) => 
      requestDeduplication.configure(options),
  };
};
