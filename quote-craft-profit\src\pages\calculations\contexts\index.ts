/**
 * Calculation contexts exports
 * Centralized exports for all calculation-related contexts
 */

// Provider component
export { CalculationProvider } from "./CalculationContext";

// Context and types
export { CalculationContext } from "./context";
export type { CalculationContextValue } from "./context";

// Constants
export {
  CALCULATION_CONTEXT_ERROR_MESSAGE,
  CALCULATION_CONTEXT_NAME,
} from "./calculationConstants";

// Hooks
export {
  useCalculationContext,
  useCalculationState,
  useCalculationId,
  useCalculationCoreData,
  // PHASE 3: Consolidated hooks for optimized usage
  useCalculationUIData,
  useCalculationDataAndUtils,
  useCalculationFunctions,
  // PHASE 5 CLEANUP: All legacy hooks removed - use consolidated hooks above
} from "./calculationHooks";
