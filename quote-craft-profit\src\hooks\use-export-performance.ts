import { useRef, useCallback } from 'react';
import { ExportLogger } from '@/utils/export-logger';

/**
 * Hook for tracking export performance metrics
 */
export const useExportPerformance = () => {
  const startTimes = useRef<Map<string, number>>(new Map());
  
  /**
   * Track when an export starts
   */
  const trackExportStart = useCallback((exportId: string, format: string) => {
    const startTime = Date.now();
    startTimes.current.set(exportId, startTime);
    ExportLogger.logExportInitiation(exportId, format);
  }, []);

  /**
   * Track when an export completes
   */
  const trackExportComplete = useCallback((exportId: string, format: string, status: string) => {
    const startTime = startTimes.current.get(exportId);
    if (startTime) {
      const duration = Date.now() - startTime;
      ExportLogger.logPerformance(`Export ${format} ${status}`, duration);
      startTimes.current.delete(exportId);
    }
  }, []);

  /**
   * Track API call performance
   */
  const trackApiCall = useCallback((operation: string, duration: number) => {
    ExportLogger.logPerformance(`API ${operation}`, duration);
  }, []);

  /**
   * Track polling performance
   */
  const trackPollingCycle = useCallback((activeExports: number, totalExports: number, duration: number) => {
    ExportLogger.logPerformance(`Polling cycle (${activeExports}/${totalExports})`, duration);
  }, []);

  return {
    trackExportStart,
    trackExportComplete,
    trackApiCall,
    trackPollingCycle,
  };
};
