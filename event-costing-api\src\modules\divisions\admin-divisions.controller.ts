import {
  Controller,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
  ParseUUI<PERSON>ipe,
  Get,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';
import { DivisionsService } from './divisions.service';
import { CreateDivisionDto } from './dto/create-division.dto';
import { UpdateDivisionDto } from './dto/update-division.dto';
import { DivisionDto } from './dto/division.dto';

@ApiTags('Admin - Divisions')
@ApiBearerAuth()
@Controller('admin/divisions')
@UseGuards(JwtAuthGuard)
export class AdminDivisionsController {
  private readonly logger = new Logger(AdminDivisionsController.name);

  constructor(private readonly divisionsService: DivisionsService) {}

  @Post()
  @UseGuards(AdminRoleGuard)
  @ApiOperation({ summary: 'Create a new division' })
  @ApiBody({ type: CreateDivisionDto })
  @ApiResponse({
    status: 201,
    description: 'Division created',
    type: DivisionDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  @ApiResponse({ status: 409, description: 'Conflict (Name exists)' })
  async createDivision(
    @Body() createDto: CreateDivisionDto,
  ): Promise<DivisionDto> {
    this.logger.log(`Admin request to create division: ${createDto.name}`);
    return await this.divisionsService.createDivision(createDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific division by ID (Admin)' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({
    status: 200,
    description: 'Division found',
    type: DivisionDto,
  })
  @ApiResponse({ status: 404, description: 'Division not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<DivisionDto> {
    this.logger.log(`Admin request to get division ID: ${id}`);
    return await this.divisionsService.findOneById(id);
  }

  @Put(':id')
  @UseGuards(AdminRoleGuard)
  @ApiOperation({ summary: 'Update a division' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiBody({ type: UpdateDivisionDto })
  @ApiResponse({
    status: 200,
    description: 'Division updated',
    type: DivisionDto,
  })
  @ApiResponse({ status: 404, description: 'Division not found' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  @ApiResponse({ status: 409, description: 'Conflict (Name exists)' })
  async updateDivision(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateDivisionDto,
  ): Promise<DivisionDto> {
    this.logger.log(`Admin request to update division ID: ${id}`);
    return await this.divisionsService.updateDivision(id, updateDto);
  }

  @Delete(':id')
  @UseGuards(AdminRoleGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a division' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({ status: 204, description: 'Division deleted' })
  @ApiResponse({ status: 404, description: 'Division not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  @ApiResponse({ status: 409, description: 'Conflict (Division in use)' })
  async deleteDivision(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Admin request to delete division ID: ${id}`);
    await this.divisionsService.deleteDivision(id);
  }
}
