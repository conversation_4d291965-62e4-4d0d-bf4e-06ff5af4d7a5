import React, { useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserWithProfile } from "@/services/shared/users";
import { toast } from "sonner";
import { createAdminUser, updateAdminUser } from "@/services/shared/users";

interface UserFormProps {
  mode: "add" | "edit";
  user?: UserWithProfile;
  onSave: () => void;
  onCancel: () => void;
  roles: { id: number; role_name: string }[];
  isLoading: boolean;
}

const userSchema = z.object({
  email: z.string().email("Invalid email address"),
  full_name: z.string().min(1, "Full name is required"),
  username: z.string().min(1, "Username is required"),
  role_id: z.string(),
  password: z.string().optional(),
});

export default function UserForm({
  mode,
  user,
  onSave,
  onCancel,
  roles,
  isLoading,
}: UserFormProps) {
  const form = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      email: "",
      full_name: "",
      username: "",
      role_id: "1",
      password: "",
    },
  });

  // Reset form when user data or mode changes
  useEffect(() => {
    if (mode === "edit" && user && roles.length > 0) {
      // Find the role ID for the user's current role
      const userRole = roles.find((r) => r.role_name === user.role_name);
      const roleId = userRole?.id.toString() || "1";

      const formValues = {
        email: user.email || "",
        full_name: user.full_name || "",
        username: user.username || "",
        role_id: roleId,
        password: "",
      };

      // Only reset form if values have actually changed to prevent unnecessary re-renders
      const currentValues = form.getValues();
      const hasChanges = Object.keys(formValues).some((key) => {
        const currentValue = currentValues[key as keyof typeof formValues];
        const newValue = formValues[key as keyof typeof formValues];
        return currentValue !== newValue;
      });

      if (hasChanges) {
        form.reset(formValues);
      }
    } else if (mode === "add") {
      // Reset to empty values for add mode
      const formValues = {
        email: "",
        full_name: "",
        username: "",
        role_id: "1",
        password: "",
      };

      // Only reset if current values are not already empty
      const currentValues = form.getValues();
      const hasChanges = Object.keys(formValues).some((key) => {
        const currentValue = currentValues[key as keyof typeof formValues];
        const newValue = formValues[key as keyof typeof formValues];
        return currentValue !== newValue;
      });

      if (hasChanges) {
        form.reset(formValues);
      }
    }
  }, [mode, user, roles, form]);

  const onSubmit = async (values: z.infer<typeof userSchema>) => {
    try {
      if (mode === "add") {
        // Create new user using the API
        await createAdminUser({
          email: values.email,
          password: values.password,
          full_name: values.full_name,
          username: values.username,
          role_id: parseInt(values.role_id),
        });

        toast.success("User created successfully");
      } else if (user) {
        // Update existing user using the API
        const updateData: any = {
          full_name: values.full_name,
          username: values.username,
          role_id: parseInt(values.role_id),
        };

        // Only include email and password if they've changed
        if (values.email !== user.email) {
          updateData.email = values.email;
        }

        if (values.password) {
          updateData.password = values.password;
        }

        await updateAdminUser(user.id, updateData);

        toast.success("User updated successfully");
      }

      onSave();
    } catch (error) {
      console.error("Error saving user:", error);
      toast.error(error.message || "Failed to save user");
    }
  };

  return (
    <DialogContent className="sm:max-w-[500px]">
      <DialogHeader>
        <DialogTitle>
          {mode === "add" ? "Add New User" : "Edit User"}
        </DialogTitle>
        <DialogDescription>
          {mode === "add"
            ? "Create a new user account in the system."
            : "Update the user account details."}
        </DialogDescription>
      </DialogHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input placeholder="John Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Username</FormLabel>
                <FormControl>
                  <Input placeholder="johndoe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="role_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Role</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isLoading}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.id.toString()}>
                        {role.role_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {mode === "add"
                    ? "Password"
                    : "New Password (leave blank to keep current)"}
                </FormLabel>
                <FormControl>
                  <Input type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">
              {mode === "add" ? "Create User" : "Save Changes"}
            </Button>
          </DialogFooter>
        </form>
      </Form>
    </DialogContent>
  );
}
