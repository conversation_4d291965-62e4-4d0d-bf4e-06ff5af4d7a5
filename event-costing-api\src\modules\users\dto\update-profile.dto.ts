import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEmail, MaxLength } from 'class-validator';

export class UpdateProfileDto {
  @ApiProperty({
    description: 'The full name of the user',
    example: '<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  full_name?: string;

  @ApiProperty({
    description: 'The email address of the user',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiProperty({
    description: 'The phone number of the user',
    example: '+1234567890',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string;

  @ApiProperty({
    description: 'The company or organization the user belongs to',
    example: 'Acme Inc.',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  company?: string;

  @ApiProperty({
    description: 'The job title or role of the user',
    example: 'Event Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  job_title?: string;

  @ApiProperty({
    description: 'The address of the user',
    example: '123 Main St, Apt 4B',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  address?: string;

  @ApiProperty({
    description: 'The city where the user is located',
    example: 'New York',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  city?: string;
}
