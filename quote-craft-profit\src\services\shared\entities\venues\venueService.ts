import {
  SaveVenueData,
  Venue,
  VenueDisplay,
  VenueClassification,
} from "@/types/venues";
import { PaginatedResult } from "@/types/pagination";
import {
  getAllVenuesFromApi,
  getVenuesByCityFromApi,
  getVenueByIdFromApi,
  saveVenueWithApi,
  deleteVenueWithApi,
  restoreVenueWithApi,
} from "./venueApiService";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";

// Interface for venue filters
export interface VenueFilters {
  search?: string;
  cityId?: string;
  showDeleted?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Enhanced interface for Dashboard V2 venue filtering
export interface EnhancedVenueFilters {
  cityId?: string;
  active?: boolean;
  classification?: VenueClassification;
  minCapacity?: number;
  maxCapacity?: number;
}

/**
 * Get all venues with optional filtering
 * This function now uses the backend API instead of direct Supabase calls
 * @param filters - Optional filters for venues
 * @returns Promise resolving to a paginated result of venues
 */
export const getAllVenues = async (
  filters: VenueFilters = {}
): Promise<PaginatedResult<VenueDisplay>> => {
  try {
    // Use the API service to fetch venues
    return await getAllVenuesFromApi(filters);
  } catch (error) {
    console.error("Error in getAllVenues:", error);

    // Return empty result on error
    return {
      data: [],
      totalCount: 0,
      page: filters.page || 1,
      pageSize: filters.pageSize || 10,
      totalPages: 0,
    };
  }
};

/**
 * Get venues by city ID
 * This function now uses the backend API instead of direct Supabase calls
 * @param cityId - The city ID
 * @returns Promise resolving to an array of venues
 */
export const getVenuesByCity = async (cityId: string): Promise<Venue[]> => {
  try {
    // Use the API service to fetch venues by city
    return await getVenuesByCityFromApi(cityId);
  } catch (error) {
    console.error(`Error in getVenuesByCity for city ID ${cityId}:`, error);
    return [];
  }
};

/**
 * Get venues with enhanced filtering for Dashboard V2
 * @param filters - Enhanced filters including classification and capacity
 * @returns Promise resolving to an array of venues
 */
export const getVenuesWithEnhancedFilters = async (
  filters: EnhancedVenueFilters = {}
): Promise<Venue[]> => {
  try {
    console.log("Fetching venues with enhanced filters:", filters);

    // Build query parameters
    const queryParams = new URLSearchParams();

    if (filters.cityId) {
      queryParams.append("cityId", filters.cityId);
    }

    if (filters.active !== undefined) {
      queryParams.append("active", filters.active.toString());
    }

    if (filters.classification) {
      queryParams.append("classification", filters.classification);
    }

    if (filters.minCapacity !== undefined) {
      queryParams.append("minCapacity", filters.minCapacity.toString());
    }

    if (filters.maxCapacity !== undefined) {
      queryParams.append("maxCapacity", filters.maxCapacity.toString());
    }

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to the enhanced venues endpoint
    const response = await authClient.get(
      `${API_ENDPOINTS.VENUES.GET_ALL}${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`
    );

    console.log("Enhanced venues fetched successfully from backend API");

    // The response should be an array of venues directly
    return response.data || [];
  } catch (error) {
    console.error("Error fetching venues with enhanced filters:", error);
    return [];
  }
};

/**
 * Get venue by ID
 * This function now uses the backend API instead of direct Supabase calls
 * @param id - The venue ID
 * @returns Promise resolving to a venue or null
 */
export const getVenueById = async (id: string): Promise<Venue | null> => {
  try {
    // Use the API service to fetch the venue
    return await getVenueByIdFromApi(id);
  } catch (error) {
    console.error(`Error in getVenueById for ID ${id}:`, error);
    return null;
  }
};

/**
 * Create or update a venue
 * This function now uses the backend API instead of direct Supabase calls
 * @param venueData - The venue data to save
 * @returns Promise resolving to the saved venue
 */
export const saveVenue = async (venueData: SaveVenueData): Promise<Venue> => {
  try {
    // Use the API service to save the venue
    return await saveVenueWithApi(venueData);
  } catch (error) {
    console.error("Error in saveVenue:", error);
    throw error;
  }
};

/**
 * Delete a venue (soft delete)
 * This function now uses the backend API instead of direct Supabase calls
 * @param id - The venue ID to delete
 * @returns Promise resolving to void
 */
export const deleteVenue = async (id: string): Promise<void> => {
  try {
    // Use the API service to delete the venue
    await deleteVenueWithApi(id);
  } catch (error) {
    console.error(`Error in deleteVenue for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Restore a deleted venue
 * This function now uses the backend API instead of direct Supabase calls
 * @param id - The venue ID to restore
 * @returns Promise resolving to void
 */
export const restoreVenue = async (id: string): Promise<void> => {
  try {
    // Use the API service to restore the venue
    await restoreVenueWithApi(id);
  } catch (error) {
    console.error(`Error in restoreVenue for ID ${id}:`, error);
    throw error;
  }
};
