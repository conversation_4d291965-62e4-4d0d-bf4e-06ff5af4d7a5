# Authentication (`/auth`)

### 1. Login User

- **Method:** `POST`
- **URL:** `/auth/login`
- **Body:** `raw (JSON)`
  ```json
  {
    "email": "<EMAIL>",
    "password": "your_password"
  }
  ```
- **Description:** Authenticates a user using email and password via Supabase Auth.
- **Success Response (200 OK):**
  ```json
  // Example Supabase AuthResponse data structure
  {
      "user": {
          "id": "user-uuid",
          "aud": "authenticated",
          "role": "authenticated",
          "email": "<EMAIL>",
          "email_confirmed_at": "timestamp",
          // ... other Supabase user fields
      },
      "session": {
          "access_token": "supabase-jwt-token",
          "token_type": "bearer",
          "expires_in": 3600,
          "expires_at": timestamp,
          "refresh_token": "supabase-refresh-token",
          "user": { /* ... user object subset ... */ }
      }
  }
  ```
- **Error Response (401 Unauthorized):**
  ```json
  {
    "statusCode": 401,
    "message": "Invalid login credentials" // Or specific Supabase error
    // ... other standard error fields
  }
  ```

### 2. Logout User

- **Method:** `POST`
- **URL:** `/auth/logout`
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>` (Important: Supabase handles logout server-side based on the token, even though our backend call might seem simple)
- **Description:** Logs out the currently authenticated user by invalidating the session via Supabase.
- **Success Response:** `204 No Content`
- **Note:** The backend service call currently just logs success/failure. Actual session invalidation happens within Supabase based on the provided token.
