import {
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  IsISO8601,
  IsBoolean,
  IsUUID,
  ValidateIf,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateTemplateDto {
  @ApiPropertyOptional({ description: 'The name of the template' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'A description for the template' })
  @IsOptional()
  @IsString()
  description?: string | null;

  @ApiPropertyOptional({
    description: 'Event type ID (UUID reference to event_types table)',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  event_type_id?: string | null;

  @ApiPropertyOptional({
    description: 'Optional template start datetime (ISO 8601 format)',
    type: String,
    format: 'date-time',
    example: '2025-05-20T00:00:00.000Z',
  })
  @IsOptional()
  @IsISO8601()
  @ValidateIf((o: UpdateTemplateDto) => o.template_start_date !== null)
  template_start_date?: string | null;

  @ApiPropertyOptional({
    description: 'Optional template end datetime (ISO 8601 format)',
    type: String,
    format: 'date-time',
    example: '2025-05-20T23:59:59.999Z',
  })
  @IsOptional()
  @IsISO8601()
  @ValidateIf((o: UpdateTemplateDto) => o.template_end_date !== null)
  template_end_date?: string | null;

  @ApiPropertyOptional({
    description: 'Set whether the template is publicly accessible',
  })
  @IsOptional()
  @IsBoolean()
  is_public?: boolean;

  // Note: venue_ids are intentionally removed from UpdateTemplateDto
  // Venues can only be set during template creation from a calculation
  // and cannot be edited afterward
}
