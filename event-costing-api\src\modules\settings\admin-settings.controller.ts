import {
  Controller,
  Get,
  Put,
  Param,
  Body,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { SettingsService } from './settings.service';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SettingDto } from './dto/setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('Admin - Settings') // Group endpoints in Swagger
@ApiBearerAuth() // Indicate JWT Bearer auth is needed
@Controller('admin/settings')
@UseGuards(JwtAuthGuard, AdminRoleGuard)
export class AdminSettingsController {
  private readonly logger = new Logger(AdminSettingsController.name);

  constructor(private readonly settingsService: SettingsService) {}

  @Get(':key')
  @ApiOperation({ summary: 'Get a specific setting by key' })
  @ApiParam({
    name: 'key',
    description: 'The unique key of the setting',
    type: String,
  })
  @ApiResponse({ status: 200, description: 'Setting found', type: SettingDto })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Requires Admin Role)' })
  async getSetting(@Param('key') key: string): Promise<SettingDto> {
    this.logger.log(`Admin request to get setting: ${key}`);
    return await this.settingsService.getSetting(key);
  }

  @Put(':key')
  @ApiOperation({ summary: 'Update (or create) a setting by key' })
  @ApiParam({
    name: 'key',
    description: 'The unique key of the setting',
    type: String,
  })
  @ApiBody({ type: UpdateSettingDto })
  @ApiResponse({
    status: 200,
    description: 'Setting updated/created successfully',
    type: SettingDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Requires Admin Role)' })
  async updateSetting(
    @Param('key') key: string,
    @Body() updateDto: UpdateSettingDto,
  ): Promise<SettingDto> {
    this.logger.log(`Admin request to update setting: ${key}`);
    return await this.settingsService.updateSetting(key, updateDto);
  }
}
