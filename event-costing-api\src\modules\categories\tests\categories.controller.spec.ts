import { Test, TestingModule } from '@nestjs/testing';
import { CategoriesController } from '../categories.controller';
import { CategoriesService } from '../categories.service';
import { CategoryDto } from '../dto/category.dto';
import { UpdateCategoryOrderDto } from '../dto/update-category-order.dto';
import { CategoryOrderResponseDto } from '../dto/category-order-response.dto';
import { Logger } from '@nestjs/common';

// Mock the Logger to avoid console output during tests
jest.mock('@nestjs/common', () => {
  const original = jest.requireActual('@nestjs/common');
  return {
    ...original,
    Logger: jest.fn().mockImplementation(() => ({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
    })),
  };
});

describe('CategoriesController', () => {
  let controller: CategoriesController;
  let service: CategoriesService;

  // Mock categories data
  const mockCategories: CategoryDto[] = [
    {
      id: '1',
      code: 'VENUE',
      name: 'Venue',
      description: 'Venue services',
      icon: 'building',
      display_order: 1,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
    },
    {
      id: '2',
      code: 'CATERING',
      name: 'Catering',
      description: 'Food and beverage services',
      icon: 'utensils',
      display_order: 2,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
    },
    {
      id: '3',
      code: 'DECOR',
      name: 'Decoration',
      description: 'Decoration services',
      icon: 'palette',
      display_order: 3,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CategoriesController],
      providers: [
        {
          provide: CategoriesService,
          useValue: {
            findAll: jest.fn().mockResolvedValue(mockCategories),
            updateCategoryOrder: jest.fn().mockImplementation(categories => {
              return Promise.resolve({
                success: true,
                message: 'Category order updated successfully',
                categories: mockCategories.map((cat, index) => ({
                  ...cat,
                  display_order: categories[index].display_order,
                })),
              });
            }),
          },
        },
      ],
    }).compile();

    controller = module.get<CategoriesController>(CategoriesController);
    service = module.get<CategoriesService>(CategoriesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getCategories', () => {
    it('should return all categories', async () => {
      const result = await controller.getCategories();
      expect(result).toEqual(mockCategories);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('updateCategoryOrder', () => {
    it('should update category order and return success response', async () => {
      const updateOrderDto: UpdateCategoryOrderDto = {
        categories: [
          { id: '1', display_order: 3 },
          { id: '2', display_order: 1 },
          { id: '3', display_order: 2 },
        ],
      };

      const expectedResponse: CategoryOrderResponseDto = {
        success: true,
        message: 'Category order updated successfully',
        categories: [
          { ...mockCategories[0], display_order: 3 },
          { ...mockCategories[1], display_order: 1 },
          { ...mockCategories[2], display_order: 2 },
        ],
      };

      const result = await controller.updateCategoryOrder(updateOrderDto);

      expect(result).toEqual(expectedResponse);
      expect(service.updateCategoryOrder).toHaveBeenCalledWith(
        updateOrderDto.categories,
      );
    });
  });
});
