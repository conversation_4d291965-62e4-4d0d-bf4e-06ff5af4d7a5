import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  <PERSON>UUI<PERSON>,
  <PERSON><PERSON>ength,
  IsOptional,
} from 'class-validator';

// Consider defining allowed dependency types, maybe as an enum later
// For now, using string validation

export class CreatePackageDependencyDto {
  @ApiProperty({
    description:
      'The UUID of the package that this package depends on (or conflicts with).',
    format: 'uuid',
  })
  @IsNotEmpty()
  @IsUUID()
  dependent_package_id: string;

  @ApiProperty({
    description:
      "Type of dependency (e.g., 'REQUIRES', 'CONFLICTS', 'RECOMMENDS').",
    example: 'REQUIRES',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  dependency_type: string;

  @ApiProperty({ description: 'Optional description.', required: false })
  @IsOptional()
  @IsString()
  description?: string;
}
