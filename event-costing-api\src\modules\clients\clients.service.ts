import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { ClientDto } from './dto/client.dto';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { PostgrestError } from '@supabase/supabase-js';

@Injectable()
export class ClientsService {
  private readonly logger = new Logger(ClientsService.name);
  private readonly TABLE_NAME = 'clients';

  constructor(private readonly supabaseService: SupabaseService) {}

  private handleSupabaseError(error: PostgrestError, context: string): never {
    this.logger.error(`${context}: ${error.message}`, error.stack);
    // Specific error handling (e.g., unique constraints)
    if (error.code === '23505') {
      // Assuming unique constraint on email
      throw new ConflictException('Client with this email already exists.');
    }
    throw new InternalServerErrorException(`Could not ${context}.`);
  }

  async create(createClientDto: CreateClientDto): Promise<ClientDto> {
    this.logger.log(`Creating a new client: ${createClientDto.client_name}`);
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .insert({
        client_name: createClientDto.client_name,
        contact_person: createClientDto.contact_person,
        email: createClientDto.email,
        phone: createClientDto.phone,
        address: createClientDto.address,
        company_name: createClientDto.company_name,
      })
      .select()
      .single<ClientDto>();

    if (error) {
      this.handleSupabaseError(error, 'create client');
    }

    if (!data) {
      // Should not happen with single() unless error occurred
      this.logger.error('Insert operation did not return data unexpectedly.');
      throw new InternalServerErrorException('Failed to create client.');
    }

    this.logger.log(`Client created successfully with ID: ${data.id}`);
    return data;
  }

  async findAll(search?: string): Promise<ClientDto[]> {
    this.logger.log(
      `Fetching all clients ${search ? `matching '${search}'` : ''}`,
    );
    const supabase = this.supabaseService.getClient();

    // Select relevant fields for the DTO
    const selectFields =
      'id, client_name, contact_person, email, phone, company_name, created_at, updated_at';

    let query = supabase
      .from(this.TABLE_NAME)
      .select(selectFields)
      .order('client_name', { ascending: true });

    if (search) {
      // Apply OR condition for search across multiple fields
      query = query.or(
        `client_name.ilike.%${search}%,company_name.ilike.%${search}%,email.ilike.%${search}%`,
      );
    }

    const { data, error } = await query.returns<ClientDto[]>();

    if (error) {
      this.handleSupabaseError(error, 'find all clients');
    }

    return data || [];
  }

  async findOne(id: string): Promise<ClientDto> {
    this.logger.log(`Fetching client with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*') // Select all fields for detail view
      .eq('id', id)
      .single<ClientDto>();

    if (error) {
      // Handle case where error is due to not finding the row
      if (error.code === 'PGRST116') {
        // PostgREST code for 'Searched item was not found'
        throw new NotFoundException(`Client with ID ${id} not found.`);
      }
      this.handleSupabaseError(error, `find client by ID ${id}`);
    }

    if (!data) {
      // Should be caught by PGRST116, but as a fallback
      throw new NotFoundException(`Client with ID ${id} not found.`);
    }

    return data;
  }

  async update(
    id: string,
    updateClientDto: UpdateClientDto,
  ): Promise<ClientDto> {
    this.logger.log(`Updating client with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    // Fetch first to ensure it exists
    await this.findOne(id);

    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .update(updateClientDto as any) // Cast DTO for update, ensure keys match
      .eq('id', id)
      .select()
      .single<ClientDto>();

    if (error) {
      this.handleSupabaseError(error, `update client ${id}`);
    }

    if (!data) {
      // Should not happen if findOne & update succeed
      this.logger.error(
        `Update operation did not return data for client ${id}`,
      );
      throw new InternalServerErrorException('Failed to update client.');
    }

    this.logger.log(`Client ${id} updated successfully.`);
    return data;
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing client with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    // Fetch first to ensure it exists (provides 404 if not found)
    await this.findOne(id);

    const { error, count } = await supabase
      .from(this.TABLE_NAME)
      .delete()
      .eq('id', id);

    if (error) {
      this.handleSupabaseError(error, `remove client ${id}`);
    }

    if (count === 0) {
      // Should not happen if findOne succeeded, but defensive check
      this.logger.warn(
        `Client with ID ${id} was not found for deletion, despite earlier check.`,
      );
      throw new NotFoundException(`Client with ID ${id} not found.`);
    }

    this.logger.log(`Client ${id} removed successfully.`);
  }
}
