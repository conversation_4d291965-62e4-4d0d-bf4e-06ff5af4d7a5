import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a number as Indonesian Rupiah
 * @param value - The number to format
 * @returns Formatted string (e.g., "Rp 1.000.000")
 */
export function formatRupiah(value: number | string): string {
  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  // Check if it's a valid number
  if (isNaN(numValue)) return 'Rp 0';

  // Format the number with Indonesian locale
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })
    .format(numValue)
    .replace('IDR', 'Rp');
}

/**
 * Format a number with Indonesian thousand separators (periods)
 * @param value - The number to format
 * @returns Formatted string (e.g., "1.000.000")
 */
export function formatIDRNumber(value: number | string): string {
  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value.replace(/\./g, '')) : value;

  // Check if it's a valid number
  if (isNaN(numValue)) return '0';

  // Format with Indonesian locale (periods as thousand separators)
  return new Intl.NumberFormat('id-ID', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numValue);
}

/**
 * Parse Indonesian formatted number string to number
 * @param value - The formatted string (e.g., "1.000.000")
 * @returns Numeric value
 */
export function parseIDRNumber(value: string): number {
  // Remove all periods (thousand separators) and parse
  const cleanValue = value.replace(/\./g, '');
  const numValue = parseFloat(cleanValue);
  return isNaN(numValue) ? 0 : numValue;
}

/**
 * Format input value for Indonesian currency display
 * @param value - The input value
 * @returns Object with display value and numeric value
 */
export function formatCurrencyInput(value: string): { display: string; numeric: number } {
  // Remove all non-numeric characters except periods
  const cleanValue = value.replace(/[^\d.]/g, '');

  // Parse the numeric value
  const numeric = parseIDRNumber(cleanValue);

  // Format for display
  const display = formatIDRNumber(numeric);

  return { display, numeric };
}

/**
 * Parse a Rupiah formatted string to a number
 * @param value - The formatted string (e.g., "Rp 1.000.000")
 * @returns The parsed number value
 */
export function parseRupiah(value: string): number {
  // Remove currency symbol and non-numeric characters
  const numericString = value.replace(/[^\d,.-]/g, '');

  // Parse the string to a number
  return parseFloat(numericString) || 0;
}
