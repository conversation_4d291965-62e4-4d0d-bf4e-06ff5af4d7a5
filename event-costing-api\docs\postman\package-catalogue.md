## Packages Catalogue (User Facing) (`/packages`)

### 1. Find Package Variations

- **Method:** `GET`
- **URL:** `/packages/variations`
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Query Parameters (from `ListPackageVariationsDto`):**
  - `currencyId` (UUID - **Required**): Currency for prices/costs.
  - `categoryId` (UUID - Optional): Filter by category.
  - `cityId` (UUID - Optional): Check availability in this city.
  - `currentSelectionIds[]` (UUID[] - Optional): Array of package IDs already selected (e.g., `?currentSelectionIds=uuid1&currentSelectionIds=uuid2`). Used for conflict checking.
- **Example URL:** `/packages/variations?currencyId=uuid-usd&categoryId=uuid-catering&cityId=uuid-ny&currentSelectionIds[]=uuid-venue`
- **Description:** Finds available package variations based on filters, including price/cost for the specified currency, city availability, and conflicts with current selections.
- **Success Response (200 OK):** Returns an array of `PackageVariationDto`.
  ```json
  [
    {
      "package_id": "pkg-uuid-catering-std",
      "name": "Standard Catering",
      "description": "Basic catering package.",
      "category_id": "uuid-catering",
      "quantity_basis": "attendees",
      "price": 25.5,
      "unit_base_cost": 12.0,
      "is_available_in_city": true,
      "conflicts_with_selection": false
    },
    {
      "package_id": "pkg-uuid-catering-prem",
      "name": "Premium Catering",
      "description": "Upgraded catering.",
      "category_id": "uuid-catering",
      "quantity_basis": "attendees",
      "price": 45.0,
      "unit_base_cost": 20.0,
      "is_available_in_city": true,
      "conflicts_with_selection": true // Example conflict
    }
    // ... other variations
  ]
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If required `currencyId` is missing or invalid UUID format used.
- **Error Response (500 Internal Server Error):** If database query fails.

### 2. Get Package Options

- **Method:** `GET`
- **URL:** `/packages/variations/{packageId}/options` (Replace `{packageId}` with actual package UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Query Parameters (from `ListPackageOptionsDto`):**
  - `currencyId` (UUID - **Required**): Currency for price/cost adjustments.
- **Example URL:** `/packages/variations/pkg-uuid-catering-std/options?currencyId=uuid-usd`
- **Description:** Retrieves the available, active options for a specific package, including their price/cost adjustments for the given currency.
- **Success Response (200 OK):** Returns an array of `PackageOptionDetailDto`.
  ```json
  [
    {
      "id": "opt-uuid-vegan",
      "option_name": "Vegan Choice",
      "description": "Vegan meal option available.",
      "price_adjustment": 5.0,
      "cost_adjustment": 2.5
    },
    {
      "id": "opt-uuid-gluten-free",
      "option_name": "Gluten-Free Choice",
      "description": null,
      "price_adjustment": 3.0,
      "cost_adjustment": 1.5
    }
    // ... other options
  ]
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If `packageId` or `currencyId` is invalid UUID format.
- **Error Response (404 Not Found):** If an option required by the package is not found or lacks price data for the currency (as per service logic).
- **Error Response (500 Internal Server Error):** If database query fails.
