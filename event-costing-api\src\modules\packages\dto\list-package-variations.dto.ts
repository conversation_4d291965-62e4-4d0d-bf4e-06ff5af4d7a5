import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsUUID,
  IsArray,
  IsNotEmpty,
  IsString,
  IsEnum,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

// Enum for sorting options
export enum PackageSortField {
  NAME = 'name',
  PRICE = 'price',
  CATEGORY = 'category',
}

// Enum for sort direction
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

// DTO for query parameters when listing package variations
export class ListPackageVariationsDto {
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    description: 'Filter by category ID',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    description: 'Filter by city ID to check availability',
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    description: 'Filter by venue ID to check availability',
  })
  @IsOptional()
  @IsUUID()
  venueId?: string;

  @ApiPropertyOptional({
    type: [String],
    format: 'uuid',
    description: 'Filter by multiple venue IDs to check availability',
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @Transform(({ value }) => {
    // Handle both array and comma-separated string formats
    if (typeof value === 'string') {
      return value.split(',');
    }
    return value;
  })
  venueIds?: string[];

  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    description: 'Currency ID for fetching price/cost',
  })
  @IsNotEmpty()
  @IsUUID()
  currencyId: string; // Required currency for price context

  @ApiPropertyOptional({
    type: [String],
    format: 'uuid',
    description:
      'Array of currently selected package IDs to check for conflicts',
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @Transform(({ value }) => {
    // Handle both array and comma-separated string formats
    if (typeof value === 'string') {
      return value.split(',');
    }
    return value;
  })
  currentSelectionIds?: string[];

  @ApiPropertyOptional({
    description: 'Search term to filter packages by name',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    enum: PackageSortField,
    description: 'Field to sort by',
    default: PackageSortField.NAME,
  })
  @IsOptional()
  @IsEnum(PackageSortField)
  sortBy?: PackageSortField = PackageSortField.NAME;

  @ApiPropertyOptional({
    enum: SortDirection,
    description: 'Sort direction',
    default: SortDirection.ASC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortOrder?: SortDirection = SortDirection.ASC;

  @ApiPropertyOptional({
    type: Number,
    description: 'Number of items to return',
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  limit?: number = 20;

  @ApiPropertyOptional({
    type: Number,
    description: 'Number of items to skip',
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  offset?: number = 0;
}
