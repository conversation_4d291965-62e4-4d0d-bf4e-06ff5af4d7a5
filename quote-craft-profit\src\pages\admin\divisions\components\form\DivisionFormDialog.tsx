import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { getDivisionById, createDivision, updateDivision } from "@/services/admin/divisions";

interface DivisionFormDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  divisionId: string | null;
}

const formSchema = z.object({
  name: z.string().min(2, "Division name must be at least 2 characters"),
  code: z.string().min(1, "Division code is required"),
});

type FormValues = z.infer<typeof formSchema>;

export const DivisionFormDialog: React.FC<DivisionFormDialogProps> = ({
  isOpen,
  onClose,
  divisionId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!divisionId;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      code: "",
    },
  });

  useEffect(() => {
    const loadDivisionDetails = async () => {
      if (isEditMode && isOpen) {
        setIsLoading(true);
        try {
          const divisionData = await getDivisionById(divisionId);

          if (divisionData) {
            form.reset({
              name: divisionData.name,
              code: divisionData.code,
            });
          }
        } catch (error) {
          toast.error("Failed to load division details");
          console.error("Error loading division:", error);
          onClose();
        } finally {
          setIsLoading(false);
        }
      }
    };

    if (isOpen) {
      form.reset({ name: "", code: "" }); // Reset form on open
      if (isEditMode) {
        loadDivisionDetails();
      }
    }
  }, [isOpen, divisionId, isEditMode, form, onClose]);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      if (isEditMode) {
        // Update existing division - only send name, not code
        await updateDivision(divisionId, {
          name: values.name,
        });
        toast.success("Division updated successfully");
      } else {
        // Create new division - send both name and code
        await createDivision({
          name: values.name,
          code: values.code,
          is_active: true
        });
        toast.success("Division created successfully");
      }

      onClose(true); // Close and refresh
    } catch (error) {
      toast.error(
        `Failed to ${isEditMode ? "update" : "create"} division: ${
          (error as Error).message
        }`
      );
      console.error("Error saving division:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit Division" : "Create New Division"}
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Loading division details...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Division Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter division name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Division Code</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter division code"
                        {...field}
                        disabled={isEditMode}
                        className={isEditMode ? "bg-muted cursor-not-allowed" : ""}
                      />
                    </FormControl>
                    <FormMessage />
                    {isEditMode && (
                      <p className="text-xs text-muted-foreground">
                        Division code cannot be changed after creation
                      </p>
                    )}
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onClose()}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isEditMode ? "Updating..." : "Creating..."}
                    </>
                  ) : isEditMode ? (
                    "Update Division"
                  ) : (
                    "Create Division"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};
