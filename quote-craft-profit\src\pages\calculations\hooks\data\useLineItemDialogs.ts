/**
 * Hook for managing line item dialog state
 */
import { useState, useMemo, useCallback } from "react";
import { LineItem, LineItemInput } from "@/types/calculation";
import { useLineItemMutations } from "./useLineItemMutations";
import { useCustomItems } from "@/hooks/useCustomItems";
import { useLineItems } from "./useLineItems";
import {
  useLineItemDeletion,
  getLineItemDeletionParams,
} from "./useLineItemDeletion";

/**
 * Hook for managing line item dialog state (add/edit/delete)
 *
 * @param calculationId - The ID of the calculation
 * @param onPackageFormCleanup - Callback to clean up package form state when items are deleted
 * @returns State and functions for managing line item dialogs
 */
export function useLineItemDialogs(
  calculationId: string,
  onPackageFormCleanup?: (packageId: string) => void
) {
  const [isAddingCustomItem, setIsAddingCustomItem] = useState(false);
  const [isEditingLineItem, setIsEditingLineItem] = useState(false);
  const [currentEditingLineItem, setCurrentEditingLineItem] =
    useState<LineItem | null>(null);

  const { addLineItem, updateLineItem } = useLineItemMutations(calculationId);
  const { data: lineItems = [] } = useLineItems(calculationId);

  // Use the unified deletion hook with package form cleanup
  const { deleteLineItem, isDeleting } = useLineItemDeletion(
    calculationId,
    onPackageFormCleanup
  );

  // Handle adding a custom item
  const handleAddCustomItem = useCallback(
    async (customItem: LineItemInput) => {
      try {
        await addLineItem(customItem);
        // Reset form state on success
        setIsAddingCustomItem(false);
      } catch (error) {
        console.error("Error adding custom item:", error);
        // Error handling is done in the mutation queue
      }
    },
    [addLineItem, setIsAddingCustomItem]
  );

  // Handle editing a line item
  const handleEditLineItem = useCallback(
    (lineItem: LineItem) => {
      setCurrentEditingLineItem(lineItem);
      setIsEditingLineItem(true);
    },
    [setCurrentEditingLineItem, setIsEditingLineItem]
  );

  // Handle updating a line item
  const handleUpdateLineItem = useCallback(
    async (lineItemId: string, updates: Partial<LineItemInput>) => {
      try {
        await updateLineItem(lineItemId, updates);
        // Reset edit state on success
        setIsEditingLineItem(false);
        setCurrentEditingLineItem(null);
      } catch (error) {
        console.error("Error updating line item:", error);
        // Error handling is done in the mutation queue
      }
    },
    [updateLineItem, setIsEditingLineItem, setCurrentEditingLineItem]
  );

  // Handle removing a line item using the unified deletion hook
  const handleRemoveLineItem = useCallback(
    (lineItemId: string) => {
      // Find the line item to get deletion parameters
      const lineItem = lineItems.find((item) => item.id === lineItemId);

      if (lineItem) {
        // Use the unified deletion hook with proper parameters
        const deletionParams = getLineItemDeletionParams(lineItem);
        deleteLineItem(deletionParams);
      } else {
        console.error(`Line item with ID ${lineItemId} not found`);
      }
    },
    [lineItems, deleteLineItem]
  );

  return useMemo(
    () => ({
      isAddingCustomItem,
      isEditingLineItem,
      currentEditingLineItem,
      isDeleting,
      setIsAddingCustomItem,
      setIsEditingLineItem,
      handleAddCustomItem,
      handleEditLineItem,
      handleUpdateLineItem,
      handleRemoveLineItem,
    }),
    [
      isAddingCustomItem,
      isEditingLineItem,
      currentEditingLineItem,
      isDeleting,
      setIsAddingCustomItem,
      setIsEditingLineItem,
      handleAddCustomItem,
      handleEditLineItem,
      handleUpdateLineItem,
      handleRemoveLineItem,
    ]
  );
}
