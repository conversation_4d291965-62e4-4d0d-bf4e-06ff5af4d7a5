import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreatePackageDto } from '../dto/create-package.dto';
import { UpdatePackageDto } from '../dto/update-package.dto';

@Injectable()
export class PackageRelationsService {
  private readonly logger = new Logger(PackageRelationsService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Handle city associations during package creation
   * @param packageId - Package ID
   * @param cityIds - Array of city IDs
   */
  async createCityAssociations(packageId: string, cityIds: string[]): Promise<void> {
    if (!cityIds || cityIds.length === 0) return;

    this.logger.log(
      `Adding ${cityIds.length} cities to package ${packageId}`,
    );

    const supabase = this.supabaseService.getClient();
    const cityInserts = cityIds.map(cityId => ({
      package_id: packageId,
      city_id: cityId,
    }));

    const { error: cityError } = await supabase
      .from('package_cities')
      .insert(cityInserts);

    if (cityError) {
      this.logger.error(
        `Error adding cities to package: ${cityError.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to associate cities with package: ${cityError.message}`,
      );
    }
  }

  /**
   * Handle venue associations during package creation
   * @param packageId - Package ID
   * @param enableVenues - Whether venues are enabled
   * @param venueIds - Array of venue IDs
   */
  async createVenueAssociations(
    packageId: string,
    enableVenues: boolean,
    venueIds?: string[],
  ): Promise<void> {
    if (!enableVenues || !venueIds || venueIds.length === 0) return;

    this.logger.log(
      `Adding ${venueIds.length} venues to package ${packageId}`,
    );

    const supabase = this.supabaseService.getClient();
    const venueInserts = venueIds.map(venueId => ({
      package_id: packageId,
      venue_id: venueId,
    }));

    const { error: venueError } = await supabase
      .from('package_venues')
      .insert(venueInserts);

    if (venueError) {
      this.logger.error(
        `Error adding venues to package: ${venueError.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to associate venues with package: ${venueError.message}`,
      );
    }
  }

  /**
   * Handle price information during package creation
   * @param packageId - Package ID
   * @param createPackageDto - Package creation data
   */
  async createPriceInformation(
    packageId: string,
    createPackageDto: CreatePackageDto,
  ): Promise<void> {
    if (
      (createPackageDto.price === undefined &&
        createPackageDto.unit_base_cost === undefined) ||
      !createPackageDto.currency_id
    ) {
      return;
    }

    this.logger.log(`Adding price information to package ${packageId}`);

    const supabase = this.supabaseService.getClient();
    const priceData = {
      package_id: packageId,
      currency_id: createPackageDto.currency_id,
      price: createPackageDto.price || 0,
      unit_base_cost: createPackageDto.unit_base_cost || 0,
      description: 'Added via package creation',
    };

    const { error: priceError } = await supabase
      .from('package_prices')
      .insert([priceData]);

    if (priceError) {
      this.logger.error(
        `Error adding price to package: ${priceError.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to add price information to package: ${priceError.message}`,
      );
    }
  }

  /**
   * Update city associations for a package
   * @param packageId - Package ID
   * @param cityIds - Array of city IDs (undefined means no change)
   */
  async updateCityAssociations(packageId: string, cityIds?: string[]): Promise<void> {
    if (cityIds === undefined) return;

    this.logger.log(`Updating city associations for package ${packageId}`);
    const supabase = this.supabaseService.getClient();

    // First, delete existing city associations
    const { error: deleteError } = await supabase
      .from('package_cities')
      .delete()
      .eq('package_id', packageId);

    if (deleteError) {
      this.logger.error(
        `Error deleting existing city associations: ${deleteError.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to update city associations: ${deleteError.message}`,
      );
    }

    // Clean up venue associations that are no longer valid for the new cities
    await this.cleanupInvalidVenueAssociations(packageId, cityIds);

    // Then, insert new city associations if any
    if (cityIds.length > 0) {
      const cityInserts = cityIds.map(cityId => ({
        package_id: packageId,
        city_id: cityId,
      }));

      const { error: insertError } = await supabase
        .from('package_cities')
        .insert(cityInserts);

      if (insertError) {
        this.logger.error(
          `Error inserting new city associations: ${insertError.message}`,
        );
        throw new InternalServerErrorException(
          `Failed to update city associations: ${insertError.message}`,
        );
      }
    }
  }

  /**
   * Update venue associations for a package
   * @param packageId - Package ID
   * @param enableVenues - Whether venues are enabled
   * @param venueIds - Array of venue IDs (undefined means no change)
   */
  async updateVenueAssociations(
    packageId: string,
    enableVenues?: boolean,
    venueIds?: string[],
  ): Promise<void> {
    if (enableVenues === undefined) return;

    const supabase = this.supabaseService.getClient();

    if (enableVenues) {
      // Venues are enabled, update venue associations
      if (venueIds !== undefined) {
        this.logger.log(`Updating venue associations for package ${packageId}`);

        // First, completely remove all existing venue associations
        const { error: deleteError } = await supabase
          .from('package_venues')
          .delete()
          .eq('package_id', packageId);

        if (deleteError) {
          this.logger.error(
            `Error removing existing venue associations: ${deleteError.message}`,
          );
          throw new InternalServerErrorException(
            `Failed to update venue associations: ${deleteError.message}`,
          );
        }

        // Then, handle new venue associations
        if (venueIds.length > 0) {
          await this.handleVenueUpdates(packageId, venueIds);
        }
      }
    } else {
      // Venues are disabled, completely remove all venue associations
      this.logger.log(`Completely removing all venue associations for package ${packageId}`);

      const { error: deleteError } = await supabase
        .from('package_venues')
        .delete()
        .eq('package_id', packageId);

      if (deleteError) {
        this.logger.error(
          `Error removing venue associations: ${deleteError.message}`,
        );
        throw new InternalServerErrorException(
          `Failed to remove venue associations: ${deleteError.message}`,
        );
      }

      this.logger.log(`Successfully removed all venue associations for package ${packageId}`);
    }
  }

  /**
   * Handle venue updates for a package (simplified with hard delete)
   * @param packageId - Package ID
   * @param venueIds - Array of venue IDs
   */
  private async handleVenueUpdates(packageId: string, venueIds: string[]): Promise<void> {
    const supabase = this.supabaseService.getClient();

    // Since we already deleted all existing associations, just create new ones
    this.logger.log(
      `Creating ${venueIds.length} new venue associations for package ${packageId}`,
    );

    const venueInserts = venueIds.map(venueId => ({
      package_id: packageId,
      venue_id: venueId,
    }));

    const { error: insertError } = await supabase
      .from('package_venues')
      .insert(venueInserts);

    if (insertError) {
      this.logger.error(
        `Error creating new venue associations: ${insertError.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to create venue associations: ${insertError.message}`,
      );
    }

    this.logger.log(`Successfully created ${venueIds.length} venue associations for package ${packageId}`);
  }

  /**
   * Update price information for a package
   * @param packageId - Package ID
   * @param updatePackageDto - Update data
   */
  async updatePriceInformation(
    packageId: string,
    updatePackageDto: UpdatePackageDto,
  ): Promise<void> {
    if (
      (updatePackageDto.price === undefined &&
        updatePackageDto.unit_base_cost === undefined) ||
      !updatePackageDto.currency_id
    ) {
      return;
    }

    this.logger.log(`Updating price information for package ${packageId}`);
    const supabase = this.supabaseService.getClient();

    // Check if a price record already exists for this package and currency
    const { data: existingPrice, error: priceCheckError } = await supabase
      .from('package_prices')
      .select('id')
      .eq('package_id', packageId)
      .eq('currency_id', updatePackageDto.currency_id)
      .maybeSingle();

    if (priceCheckError) {
      this.logger.error(
        `Error checking existing price: ${priceCheckError.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to update price information: ${priceCheckError.message}`,
      );
    }

    const priceData = {
      package_id: packageId,
      currency_id: updatePackageDto.currency_id,
      price: updatePackageDto.price || 0,
      unit_base_cost: updatePackageDto.unit_base_cost || 0,
      description: 'Updated via package update',
    };

    if (existingPrice) {
      // Update existing price
      const { error: updatePriceError } = await supabase
        .from('package_prices')
        .update(priceData)
        .eq('id', existingPrice.id);

      if (updatePriceError) {
        this.logger.error(
          `Error updating price: ${updatePriceError.message}`,
        );
        throw new InternalServerErrorException(
          `Failed to update price information: ${updatePriceError.message}`,
        );
      }
    } else {
      // Insert new price
      const { error: insertPriceError } = await supabase
        .from('package_prices')
        .insert([priceData]);

      if (insertPriceError) {
        this.logger.error(
          `Error inserting new price: ${insertPriceError.message}`,
        );
        throw new InternalServerErrorException(
          `Failed to add price information: ${insertPriceError.message}`,
        );
      }
    }
  }

  /**
   * Clean up venue associations that are no longer valid for the new cities
   * @param packageId - Package ID
   * @param newCityIds - Array of new city IDs
   */
  private async cleanupInvalidVenueAssociations(
    packageId: string,
    newCityIds: string[],
  ): Promise<void> {
    if (newCityIds.length === 0) {
      // If no cities are selected, remove all venue associations
      this.logger.log(`No cities selected, removing all venue associations for package ${packageId}`);

      const supabase = this.supabaseService.getClient();
      const { error: deleteError } = await supabase
        .from('package_venues')
        .delete()
        .eq('package_id', packageId);

      if (deleteError) {
        this.logger.error(
          `Error removing venue associations: ${deleteError.message}`,
        );
        throw new InternalServerErrorException(
          `Failed to cleanup venue associations: ${deleteError.message}`,
        );
      }
      return;
    }

    this.logger.log(`Cleaning up invalid venue associations for package ${packageId} with new cities: ${newCityIds.join(', ')}`);
    const supabase = this.supabaseService.getClient();

    // Get all current venue associations for this package
    const { data: currentVenues, error: venueError } = await supabase
      .from('package_venues')
      .select(`
        id,
        venue_id,
        venues!inner (
          id,
          city_id
        )
      `)
      .eq('package_id', packageId);

    if (venueError) {
      this.logger.error(
        `Error fetching current venue associations: ${venueError.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to cleanup venue associations: ${venueError.message}`,
      );
    }

    if (!currentVenues || currentVenues.length === 0) {
      this.logger.log(`No venue associations found for package ${packageId}`);
      return;
    }

    // Find venues that are not in the new cities
    const invalidVenueIds = currentVenues
      .filter((pv: any) => !newCityIds.includes(pv.venues.city_id))
      .map((pv: any) => pv.venue_id);

    if (invalidVenueIds.length > 0) {
      this.logger.log(`Removing ${invalidVenueIds.length} invalid venue associations for package ${packageId}: ${invalidVenueIds.join(', ')}`);

      const { error: deleteError } = await supabase
        .from('package_venues')
        .delete()
        .eq('package_id', packageId)
        .in('venue_id', invalidVenueIds);

      if (deleteError) {
        this.logger.error(
          `Error removing invalid venue associations: ${deleteError.message}`,
        );
        throw new InternalServerErrorException(
          `Failed to cleanup venue associations: ${deleteError.message}`,
        );
      }

      this.logger.log(`Successfully removed invalid venue associations for package ${packageId}`);
    } else {
      this.logger.log(`No invalid venue associations found for package ${packageId}`);
    }
  }
}
