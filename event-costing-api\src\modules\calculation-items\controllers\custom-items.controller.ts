import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  ParseU<PERSON><PERSON>ipe,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiOkResponse,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../../modules/auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import { CustomItemsService } from '../services/custom-items.service';
import { AddCustomLineItemDto } from '../dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from '../dto/update-line-item.dto';
import { CustomItemDto } from '../dto/custom-item.dto';
import { ItemIdResponse } from '../dto/item-id-response.dto';

@ApiTags('Custom Items')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('calculations/:calcId/custom-items')
export class CustomItemsController {
  private readonly logger = new Logger(CustomItemsController.name);

  constructor(private readonly customItemsService: CustomItemsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all custom items for a calculation' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'Custom items retrieved', type: [CustomItemDto] })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  async getCustomItems(
    @Param('calcId', ParseUUIDPipe) calcId: string,
  ): Promise<CustomItemDto[]> {
    this.logger.log(`Fetching custom items for calculation ${calcId}`);
    return this.customItemsService.getCustomItems(calcId);
  }

  @Get(':itemId')
  @ApiOperation({ summary: 'Get a specific custom item' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'itemId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'Custom item retrieved', type: CustomItemDto })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation or custom item not found.',
  })
  async getCustomItemById(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Param('itemId', ParseUUIDPipe) itemId: string,
  ): Promise<CustomItemDto> {
    this.logger.log(`Fetching custom item ${itemId} for calculation ${calcId}`);
    return this.customItemsService.getCustomItemById(calcId, itemId);
  }

  @Post()
  @ApiOperation({ summary: 'Add a new custom item to a calculation' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'Custom item added', type: ItemIdResponse })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data.',
  })
  async addCustomItem(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Body() addDto: AddCustomLineItemDto,
    @GetCurrentUser() user: User,
  ): Promise<ItemIdResponse> {
    this.logger.log(
      `User ${user.email} adding custom item '${addDto.itemName}' to calc ${calcId}`,
    );
    return this.customItemsService.addCustomItem(calcId, addDto, user);
  }

  @Put(':itemId')
  @ApiOperation({ summary: 'Update a custom item' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'itemId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ description: 'Custom item updated', type: CustomItemDto })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation or custom item not found.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data.',
  })
  async updateCustomItem(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @Body() updateDto: UpdateLineItemDto,
    @GetCurrentUser() user: User,
  ): Promise<CustomItemDto> {
    this.logger.log(
      `User ${user.email} updating custom item ${itemId} in calc ${calcId}`,
    );
    return this.customItemsService.updateCustomItem(calcId, itemId, updateDto, user);
  }

  @Delete(':itemId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a custom item' })
  @ApiParam({ name: 'calcId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'itemId', type: 'string', format: 'uuid' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Custom item deleted successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation or custom item not found.',
  })
  async deleteCustomItem(
    @Param('calcId', ParseUUIDPipe) calcId: string,
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @GetCurrentUser() user: User,
  ): Promise<void> {
    this.logger.log(
      `User ${user.email} deleting custom item ${itemId} from calc ${calcId}`,
    );
    await this.customItemsService.deleteCustomItem(calcId, itemId, user);
  }
}
