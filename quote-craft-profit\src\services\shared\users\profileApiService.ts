/**
 * Profile API Service
 *
 * This service provides methods for interacting with the profile API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';
import { toast } from 'sonner';

/**
 * Upload a profile picture
 * @param file - The image file to upload
 * @returns Promise resolving to the profile picture URL
 */
export const uploadProfilePicture = async (file: File): Promise<string> => {
  try {
    console.log('Uploading profile picture to backend API', {
      fileName: file.name,
      fileType: file.type,
      fileSize: `${(file.size / 1024).toFixed(2)} KB`,
    });

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      console.error('Invalid file type:', file.type);
      toast.error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
      throw new Error('Invalid file type');
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      console.error('File too large:', `${(file.size / 1024 / 1024).toFixed(2)} MB`);
      toast.error('File too large. Maximum size is 5MB.');
      throw new Error('File too large');
    }

    // Create form data
    const formData = new FormData();
    formData.append('file', file);

    // Log FormData content (for debugging)
    console.log('FormData created with file:', {
      hasFile: formData.has('file'),
      fileEntryType: typeof formData.get('file'),
      fileName: file.name,
    });

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Log authentication status
    console.log('Authentication status:', {
      hasAuthClient: !!authClient,
      hasAuthHeader: !!authClient.defaults.headers.Authorization,
    });

    // IMPORTANT: For file uploads with FormData, we need to ensure:
    // 1. We don't set Content-Type manually (Axios will set it with the correct boundary)
    // 2. We don't transform the request data

    // Create a custom config for file uploads
    const config = {
      headers: {
        // Don't set Content-Type here, Axios will set it automatically with the correct boundary
      },
      // Prevent Axios from trying to transform the FormData
      transformRequest: [(data: any) => data],
    };

    // Make API request
    console.log('Sending request to:', API_ENDPOINTS.PROFILE.UPLOAD_PICTURE);
    const response = await authClient.post(
      API_ENDPOINTS.PROFILE.UPLOAD_PICTURE,
      formData,
      config,
    );

    console.log('Profile picture uploaded successfully:', response.data);

    return response.data.profilePictureUrl;
  } catch (error) {
    console.error('Error uploading profile picture:', error);

    // Extract more detailed error information
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);

      // Extract error message from response if available
      let errorMessage = 'Failed to upload profile picture';
      if (error.response.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      }

      toast.error(errorMessage);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Error request:', error.request);
      toast.error('No response received from server. Please check your connection.');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
      toast.error(`Error: ${error.message}`);
    }

    throw error;
  }
};

/**
 * Update user profile
 * @param profileData - The profile data to update
 * @returns Promise resolving to the updated profile data
 */
export const updateProfile = async (profileData: {
  full_name?: string;
  email?: string;
  phone?: string;
  company?: string;
  job_title?: string;
  address?: string;
  city?: string;
}): Promise<any> => {
  try {
    console.log('Updating profile with backend API:', profileData);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // The backend DTO expects 'company' and 'phone', but the database uses 'company_name' and 'phone_number'
    // We'll keep the original field names since that's what the backend DTO expects
    const transformedData = {
      ...profileData,
      // No need to transform field names as the backend will handle it
    };

    // Add detailed logging for debugging
    console.log('Original profile data:', profileData);
    console.log('Sending to backend:', transformedData);

    // Make API request
    const response = await authClient.patch(
      API_ENDPOINTS.PROFILE.UPDATE,
      transformedData,
    );

    // Log the response for debugging
    console.log('Profile update response:', {
      status: response.status,
      statusText: response.statusText,
      data: response.data,
    });

    if (response.status === 200) {
      // Check if the response includes information about updated fields
      if (response.data && response.data.updatedFields) {
        const updatedFields = response.data.updatedFields
          .map((field: string) => {
            // Convert snake_case to human-readable format
            return field
              .replace(/_/g, ' ')
              .replace(/\b\w/g, (l: string) => l.toUpperCase());
          })
          .join(', ');

        console.log('Profile updated successfully. Updated fields:', updatedFields);
        toast.success(`Profile updated successfully. Updated: ${updatedFields}`);
      } else {
        console.log('Profile updated successfully');
        toast.success('Profile updated successfully');
      }

      // Return the updated profile data from the response
      if (response.data && response.data.profile) {
        console.log('Received updated profile data from API:', response.data.profile);
        // Log the specific fields we need
        console.log('Profile fields from API:', {
          full_name: response.data.profile.full_name,
          username: response.data.profile.username,
          phone_number: response.data.profile.phone_number,
          address: response.data.profile.address,
          city: response.data.profile.city,
          company_name: response.data.profile.company_name,
          roles: response.data.profile.roles,
        });
        return response.data.profile;
      } else {
        console.warn('No profile data received from API');
        console.warn('Full response data:', response.data);
        return {};
      }
    } else {
      console.warn('Received unexpected status code:', response.status);
      toast.warning('Profile may not have been updated correctly');
      return {};
    }
  } catch (error) {
    console.error('Error updating profile:', error);

    // Extract more detailed error message if available
    let errorMessage = 'Failed to update profile';

    if (error.response && error.response.data) {
      if (error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (typeof error.response.data === 'string') {
        errorMessage = error.response.data;
      }
    }

    toast.error(errorMessage);
    throw error;
  }
};
