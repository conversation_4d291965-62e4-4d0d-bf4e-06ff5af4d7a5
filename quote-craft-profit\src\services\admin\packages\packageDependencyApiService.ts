/**
 * Package Dependency API Service
 *
 * This service provides methods for interacting with the package dependencies API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import {
  PackageDependency,
  PackageDependencyDisplay,
  SavePackageDependencyData
} from '../../../pages/admin/packages/types/packageDependencies';
import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';

/**
 * Get all dependencies for a package from the backend API
 * @param packageId - The package ID
 * @returns Promise resolving to an array of package dependencies
 */
export const getPackageDependenciesFromApi = async (
  packageId: string
): Promise<PackageDependencyDisplay[]> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(
      API_ENDPOINTS.PACKAGES.DEPENDENCIES.GET_ALL(packageId)
    );

    return response.data;
  } catch (error) {
    console.error(`Error fetching dependencies for package ID ${packageId} from backend API:`, error);
    return [];
  }
};

/**
 * Get a single package dependency by ID from the backend API
 * @param dependencyId - The dependency ID
 * @returns Promise resolving to a package dependency or null
 */
export const getPackageDependencyByIdFromApi = async (
  packageId: string,
  dependencyId: string
): Promise<PackageDependencyDisplay | null> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(
      API_ENDPOINTS.PACKAGES.DEPENDENCIES.GET_BY_ID(packageId, dependencyId)
    );

    return response.data;
  } catch (error) {
    console.error(`Error fetching dependency with ID ${dependencyId} from backend API:`, error);
    return null;
  }
};

/**
 * Create or update a package dependency using the backend API
 * @param dependencyData - The dependency data to save
 * @returns Promise resolving to the saved dependency
 */
export const savePackageDependencyWithApi = async (
  dependencyData: SavePackageDependencyData
): Promise<PackageDependency> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    if (dependencyData.id) {
      // Update existing dependency
      const response = await authClient.put(
        API_ENDPOINTS.PACKAGES.DEPENDENCIES.UPDATE(
          dependencyData.package_id,
          dependencyData.id
        ),
        dependencyData
      );

      return response.data;
    } else {
      // Create new dependency
      const response = await authClient.post(
        API_ENDPOINTS.PACKAGES.DEPENDENCIES.CREATE(dependencyData.package_id),
        dependencyData
      );

      return response.data;
    }
  } catch (error) {
    console.error('Error saving package dependency with backend API:', error);
    throw error;
  }
};

/**
 * Delete a package dependency using the backend API
 * @param packageId - The package ID
 * @param dependencyId - The dependency ID to delete
 * @returns Promise resolving to void
 */
export const deletePackageDependencyWithApi = async (
  packageId: string,
  dependencyId: string
): Promise<void> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.delete(
      API_ENDPOINTS.PACKAGES.DEPENDENCIES.DELETE(packageId, dependencyId)
    );
  } catch (error) {
    console.error(`Error deleting dependency with ID ${dependencyId} using backend API:`, error);
    throw error;
  }
};

/**
 * Check if a dependency already exists between two packages using the backend API
 * This function checks by fetching all dependencies and looking for a match
 * @param packageId - The package ID
 * @param dependentPackageId - The dependent package ID
 * @returns Promise resolving to a boolean indicating if the dependency exists
 */
export const checkDependencyExistsWithApi = async (
  packageId: string,
  dependentPackageId: string
): Promise<boolean> => {
  try {
    // Get all dependencies for the package
    const dependencies = await getPackageDependenciesFromApi(packageId);

    // Check if any dependency matches the dependent package ID
    return dependencies.some(dep => dep.dependent_package_id === dependentPackageId);
  } catch (error) {
    console.error(`Error checking dependency between packages ${packageId} and ${dependentPackageId} using backend API:`, error);
    return false;
  }
};
