import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  optimizedNotifications,
  showOptimizedLoading,
  dismissOptimizedNotification,
} from "@/lib/optimized-notifications";
import { z } from "zod";
import { PackageDetailsForm } from "./PackageDetailsForm";
import { PackageFormValues, AddPackageDialogProps } from "../../types/package";
import { savePackage } from "../../../../../services/admin/packages/packageService";
import { defaultPackageFormValues } from "../../schemas/packageValidation";
import { usePackageForm } from "../../hooks";

export const AddPackageDialog: React.FC<AddPackageDialogProps> = ({
  isOpen,
  onClose,
}) => {
  // Use the package form hook
  const { form, categories, divisions, cities, currencies, isLoading } =
    usePackageForm({
      enabled: isOpen,
    });

  // Reset form when dialog opens and set default currency
  useEffect(() => {
    if (isOpen && currencies.length > 0) {
      // Get default currency (first one, which should be IDR)
      const defaultCurrencyId = currencies[0].id;

      form.reset({
        ...defaultPackageFormValues,
        cityIds: [],
        venueIds: [],
        currencyId: defaultCurrencyId,
      });

      // Set default currency
      form.setValue("currencyId", defaultCurrencyId);
    }
  }, [isOpen, currencies]);

  const handleSubmit = async (values: PackageFormValues) => {
    try {
      // Show loading toast
      const loadingToastId = showOptimizedLoading("Creating package...");

      // Ensure we have a valid currency ID
      if (!values.currencyId) {
        optimizedNotifications.form.error("Please select a valid currency");
        return;
      }

      await savePackage({
        name: values.name,
        description: values.description,
        categoryId: values.categoryId || undefined,
        divisionId: values.divisionId || undefined,
        cityIds: values.cityIds || [],
        venueIds: values.enableVenues ? values.venueIds || [] : [],
        enableVenues: values.enableVenues,
        quantityBasis: values.quantityBasis,
        isDeleted: !values.isActive, // Convert from isActive to isDeleted
        price: values.price ? parseFloat(values.price) : undefined,
        unitBaseCost: values.unitBaseCost
          ? parseFloat(values.unitBaseCost)
          : undefined,
        currencyId: values.currencyId, // Always send the currency ID
      });

      // Dismiss loading toast and show success toast
      dismissOptimizedNotification(loadingToastId);
      optimizedNotifications.form.success("Package created successfully");

      // Close dialog and refresh the package list
      onClose(true);
    } catch (error) {
      // Show detailed error message
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      optimizedNotifications.form.error(
        `Failed to create package: ${errorMessage}`
      );
      console.error("Error in handleSubmit:", error);

      // Highlight form errors if validation failed
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          const fieldName = err.path.join(".");
          form.setError(fieldName as keyof PackageFormValues, {
            type: "manual",
            message: err.message,
          });
        });
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Package</DialogTitle>
          <DialogDescription>
            Create a new service package that can be offered to clients.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <PackageDetailsForm
            form={form}
            categories={categories}
            divisions={divisions}
            cities={cities}
            currencies={currencies}
            isLoading={isLoading}
          />

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onClose()}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              Create Package
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
