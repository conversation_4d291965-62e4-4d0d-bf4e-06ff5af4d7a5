import { z } from 'zod';

// Interface for package option data from the database
export interface PackageOption {
  id: string;
  option_name: string;
  option_code: string;
  description: string | null;
  price_adjustment: number;
  cost_adjustment: number;
  currency_id: string;
  option_group: string | null;
  is_default_for_package: boolean;
  is_required: boolean;
  applicable_package_id: string;
  created_at: string;
  updated_at: string;
}

// Interface for frontend display with formatted data
export interface PackageOptionDisplay extends Omit<PackageOption, 'currency_id'> {
  currency_code?: string;
  profit_margin?: number;
  profit_percentage?: number;
}

// Data for creating or updating a package option
export interface SavePackageOptionData {
  id?: string;
  option_name: string;
  option_code: string;
  description?: string | null;
  price_adjustment: number;
  cost_adjustment: number;
  currency_id: string;
  option_group?: string | null;
  is_default_for_package: boolean;
  is_required: boolean;
  applicable_package_id: string;
}

// Form values for package option form
export interface PackageOptionFormValues {
  option_name: string;
  option_code: string;
  description: string;
  price_adjustment: string; // Using string for input handling
  cost_adjustment: string; // Using string for input handling
  is_default_for_package: boolean;
  is_required: boolean;
}

// Zod schema for package option validation
export const packageOptionSchema = z.object({
  option_name: z.string().min(1, 'Option name is required'),
  option_code: z.string().min(1, 'Option code is required'),
  description: z.string().optional(),
  price_adjustment: z
    .string()
    .refine((val) => !isNaN(Number(val)), {
      message: 'Price adjustment must be a valid number',
    }),
  cost_adjustment: z
    .string()
    .refine((val) => !isNaN(Number(val)), {
      message: 'Cost adjustment must be a valid number',
    }),
  is_default_for_package: z.boolean(),
  is_required: z.boolean(),
});
