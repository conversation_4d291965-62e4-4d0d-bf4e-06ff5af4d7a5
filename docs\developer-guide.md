# Quote Craft Profit - Developer Guide

This guide provides comprehensive instructions for developers working on the Quote Craft Profit project, covering setup, coding standards, development workflow, and deployment processes.

## Project Structure

The project consists of two main components:

1. **Frontend Application (quote-craft-profit)**

   - React-based web application with TypeScript
   - Located in the `quote-craft-profit` directory

2. **Backend API (event-costing-api)**
   - NestJS-based RESTful API
   - Located in the `event-costing-api` directory

## Technology Stack

This section provides a comprehensive overview of the technologies used in the Quote Craft Profit project.

### Frontend Technologies (quote-craft-profit)

#### Core Framework and Build Tools

| Technology     | Version | Purpose                                                                  |
| -------------- | ------- | ------------------------------------------------------------------------ |
| **React**      | 18.x    | UI library for building component-based interfaces                       |
| **TypeScript** | 5.x     | Static typing for improved code quality and developer experience         |
| **Vite**       | 4.x     | Modern build tool for faster development and optimized production builds |

#### UI and Styling

| Technology       | Purpose                                                   |
| ---------------- | --------------------------------------------------------- |
| **Tailwind CSS** | Utility-first CSS framework for rapid UI development      |
| **shadcn/ui**    | Component library based on Radix UI with Tailwind styling |
| **Radix UI**     | Unstyled, accessible UI components                        |
| **Lucide Icons** | Icon library for consistent visual elements               |
| **Sonner**       | Toast notification library for user feedback              |

#### State Management and Data Fetching

| Technology        | Purpose                                                       |
| ----------------- | ------------------------------------------------------------- |
| **React Query**   | Data fetching, caching, and state management for server state |
| **React Context** | Global state management for application-wide state            |
| **Axios**         | HTTP client for API communication                             |

#### Routing and Navigation

| Technology       | Purpose                            |
| ---------------- | ---------------------------------- |
| **React Router** | Client-side routing and navigation |

#### Form Handling and Validation

| Technology          | Purpose                                                     |
| ------------------- | ----------------------------------------------------------- |
| **React Hook Form** | Form state management and validation                        |
| **Zod**             | Schema validation library for form inputs and API responses |

#### Backend Integration

| Technology              | Purpose                                                                |
| ----------------------- | ---------------------------------------------------------------------- |
| **Supabase Client**     | Client library for Supabase authentication and database operations     |
| **External API Client** | Custom Axios-based client for communicating with the event-costing-api |

### Backend Technologies (event-costing-api)

#### Core Framework

| Technology           | Purpose                                                             |
| -------------------- | ------------------------------------------------------------------- |
| **NestJS**           | Progressive Node.js framework for building server-side applications |
| **TypeScript**       | Static typing for improved code quality and developer experience    |
| **Express**          | Web framework for Node.js (used by NestJS)                          |
| **reflect-metadata** | Support for decorators and reflection                               |
| **rxjs**             | Reactive extensions library for handling asynchronous operations    |

#### Database and Authentication

| Technology                | Purpose                                               |
| ------------------------- | ----------------------------------------------------- |
| **Supabase**              | Backend-as-a-Service platform providing:              |
| - PostgreSQL              | Relational database for data storage                  |
| - Authentication          | User authentication and authorization                 |
| - Storage                 | File storage for documents and exports                |
| **@supabase/supabase-js** | Client library for interacting with Supabase services |

#### API and Validation

| Technology            | Purpose                                                 |
| --------------------- | ------------------------------------------------------- |
| **class-validator**   | Validation library for DTO classes                      |
| **class-transformer** | Object transformation library for API responses         |
| **Passport**          | Authentication middleware for Node.js                   |
| **JWT**               | JSON Web Token implementation for secure authentication |

#### Configuration and Logging

| Technology         | Purpose                                            |
| ------------------ | -------------------------------------------------- |
| **@nestjs/config** | Configuration management for environment variables |
| **Winston**        | Logging library for structured logging             |
| **nest-winston**   | NestJS integration for Winston logger              |

#### File Generation (Exports)

| Technology  | Purpose                               |
| ----------- | ------------------------------------- |
| **exceljs** | Library for generating Excel files    |
| **pdf-lib** | Library for programmatic PDF creation |

#### Testing

| Technology          | Purpose                                          |
| ------------------- | ------------------------------------------------ |
| **Jest**            | Testing framework for unit and integration tests |
| **Supertest**       | HTTP assertion library for API testing           |
| **@nestjs/testing** | NestJS testing utilities                         |

### Development and Deployment Tools

| Technology   | Purpose                                                   |
| ------------ | --------------------------------------------------------- |
| **ESLint**   | Static code analysis for identifying problematic patterns |
| **Prettier** | Code formatter for consistent code style                  |
| **Git**      | Version control system                                    |
| **Vercel**   | Deployment platform for the frontend application          |
| **Railway**  | Deployment platform for the backend API                   |
| **Lovable**  | Alternative deployment platform for the frontend          |

### Integration Points

#### Frontend to Backend Communication

The frontend currently communicates with the backend through two main channels:

1. **Direct Supabase Integration**:

   - Authentication using Supabase Auth
   - Some database operations using the Supabase client

2. **External API Integration**:
   - RESTful API communication using a custom Axios-based client
   - The API client is configured to:
     - Add authentication tokens to requests
     - Handle error responses
     - Transform data for UI consumption
     - Provide logging for debugging

> **Important Note**: We are transitioning to a backend-only integration model where all Supabase interactions will be channeled through our NestJS backend API. See the [Backend Integration Roadmap](#backend-integration-roadmap) section for details.

#### Authentication Flow

1. User authentication is handled by Supabase Auth
2. JWT tokens are used for secure communication
3. The frontend stores tokens in localStorage and the Supabase client
4. The backend validates tokens for protected endpoints

#### Database Access

1. The frontend uses the Supabase client for direct database operations
2. The backend uses Supabase's PostgreSQL database for data storage
3. Row-level security policies in Supabase ensure data access control
4. Complex database operations are handled through the backend API

### Backend Integration Roadmap

We are implementing a comprehensive plan to fully integrate all Supabase services through our backend API. This transition will provide several benefits:

1. **Improved Security**: Centralized authentication and authorization
2. **Consistent Business Logic**: All data operations follow the same validation rules
3. **Better Maintainability**: Simplified frontend code with consistent API patterns
4. **Enhanced Performance**: Optimized queries and caching strategies
5. **Reduced Duplication**: Elimination of redundant code between frontend and backend

#### Current Progress

- Authentication endpoints for login and logout are already implemented in the backend
- Many admin modules already have complete CRUD operations
- Some services like Calculations and Templates are partially integrated

#### Integration Phases

1. **Authentication Integration**

   - Extend existing auth service to handle all authentication flows
   - Update frontend to use backend API for all auth operations

2. **Database Operations Integration**

   - Move all direct Supabase database calls to backend services
   - Implement comprehensive validation and error handling
   - Ensure consistent response formats

3. **Storage Integration**

   - Create a centralized storage service in the backend
   - Handle all file operations through the API

4. **Realtime Subscriptions**
   - Implement WebSocket support for realtime updates
   - Replace direct Supabase subscriptions with WebSocket connections

#### Developer Guidelines During Transition

- **New Features**: All new features should use the backend-only approach
- **Existing Code**: When modifying existing code, update it to use the backend API
- **Service Functions**: Update service functions to use the API client instead of direct Supabase calls
- **Testing**: Test both approaches during the transition period
- **Documentation**: Document API endpoints thoroughly

For a detailed implementation plan, refer to the `supabase-backend-integration-plan.md` file in the project root.

### Why These Technologies Were Chosen

#### Frontend Choices

- **React**: Provides a component-based architecture for building complex UIs
- **TypeScript**: Adds static typing to prevent common errors and improve developer experience
- **Vite**: Offers faster development experience and optimized builds compared to Create React App
- **Tailwind CSS**: Enables rapid UI development with utility classes
- **shadcn/ui**: Provides accessible, customizable components based on Radix UI
- **React Query**: Simplifies data fetching and caching with a declarative API
- **React Hook Form**: Provides performant form handling with minimal re-renders

#### Backend Choices

- **NestJS**: Offers a structured, modular architecture for building scalable APIs
- **Supabase**: Provides a comprehensive backend solution with authentication, database, and storage
- **PostgreSQL**: Offers a powerful relational database with advanced features
- **JWT**: Enables stateless authentication for scalable API access
- **class-validator/transformer**: Provides robust validation and transformation for API requests/responses

### Technology Evaluation

#### Strengths

- Modern, maintainable tech stack with strong typing
- Excellent developer experience with hot reloading and type checking
- Scalable architecture for future growth
- Strong focus on performance and user experience
- Comprehensive testing capabilities
- Separation of concerns between frontend and backend

#### Considerations

- Learning curve for developers new to TypeScript, NestJS, or Supabase
- Dependency on third-party services (Supabase)
- Need for careful management of client-side state
- Coordination required between frontend and backend for API changes

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Git
- Supabase account (for database and authentication)

### Setting Up the Frontend

1. **Navigate to the frontend directory**:

   ```bash
   cd quote-craft-profit
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Set up environment variables**:

   - Copy `.env.example` to `.env`
   - Update the variables with your Supabase credentials and API URL:
     ```
     VITE_SUPABASE_URL=your_supabase_url
     VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
     VITE_EVENT_COSTING_API_URL=http://localhost:5000
     ```

4. **Start the development server**:
   ```bash
   npm run dev
   ```

### Setting Up the Backend

1. **Navigate to the backend directory**:

   ```bash
   cd event-costing-api
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Set up environment variables**:

   - Copy `.env.example` to `.env`
   - Update the variables with your Supabase credentials:
     ```
     PORT=5000
     JWT_SECRET=your_jwt_secret
     SUPABASE_URL=your_supabase_url
     SUPABASE_KEY=your_supabase_key
     SUPABASE_ANON_KEY=your_supabase_anon_key
     SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
     ```

4. **Start the development server**:
   ```bash
   npm run start:dev
   ```

### Project Structure Details

#### Frontend Structure (quote-craft-profit)

We are transitioning to a feature-based organization structure for better code organization and maintainability. The project currently has a mix of the traditional structure and the new feature-based structure.

##### Traditional Structure (Being Phased Out)

```
src/
├── components/         # Reusable UI components
│   ├── ui/             # Base UI components from shadcn
│   ├── layout/         # Layout components (Navbar, MainLayout, etc.)
│   ├── admin/          # Admin-specific components
│   ├── auth/           # Authentication components
│   ├── calculations/   # Calculation-related components
│   ├── clients/        # Client management components
│   ├── dashboard/      # Dashboard components
│   └── events/         # Event management components
├── contexts/           # React Context providers
├── data/               # Mock data and constants
├── hooks/              # Custom React hooks
├── integrations/       # Third-party integrations
│   ├── supabase/       # Supabase client and types
│   └── api/            # External API client and configuration
├── lib/                # Utility functions and helpers
├── pages/              # Page components (simple page components)
├── services/           # API service functions
└── types/              # TypeScript type definitions
```

##### Feature-Based Structure (New Approach)

```
src/
├── pages/              # Feature-based organization
│   ├── calculations/   # Calculation feature
│   │   ├── components/ # Calculation-specific components
│   │   │   ├── detail/ # Components for detail page
│   │   │   ├── new/    # Components for new calculation page
│   │   │   ├── list/   # Components for calculations list page
│   │   │   └── shared/ # Shared calculation components
│   │   ├── hooks/      # Calculation-specific hooks
│   │   ├── utils/      # Calculation-specific utility functions
│   │   ├── services/   # Calculation-specific services
│   │   ├── types/      # Calculation-specific types
│   │   ├── constants/  # Calculation-specific constants
│   │   ├── CalculationDetailPage.tsx  # Main detail page
│   │   ├── NewCalculationPage.tsx     # New calculation page
│   │   └── CalculationsPage.tsx       # List page
│   ├── clients/        # Client management feature (similar structure)
│   ├── events/         # Event management feature (similar structure)
│   └── ...             # Other features
├── components/         # Shared components used across features
├── hooks/              # Shared hooks used across features
├── lib/                # Shared utility functions
├── contexts/           # Application-wide contexts
├── integrations/       # Third-party integrations
└── types/              # Shared TypeScript type definitions
```

This feature-based organization provides several benefits:

- Improved code organization with related code in one place
- Better encapsulation of feature-specific code
- Easier onboarding for new developers
- Simplified imports with shorter paths
- Better scalability for adding new features

#### Backend Structure (event-costing-api)

```
src/
├── main.ts             # Application entry point
├── app.module.ts       # Root module
├── core/               # Core functionality
│   ├── filters/        # Global exception filters
│   ├── supabase/       # Supabase integration
│   └── database/       # Database configuration
├── modules/            # Feature modules
│   ├── auth/           # Authentication module
│   ├── users/          # User management module
│   ├── calculations/   # Calculation module
│   ├── templates/      # Template management module
│   ├── clients/        # Client management module
│   ├── packages/       # Package catalog module
│   ├── categories/     # Category management module
│   ├── cities/         # City management module
│   └── events/         # Event management module
└── shared/             # Shared DTOs and interfaces
```

## Coding Standards

### General Guidelines

1. **TypeScript**: Use TypeScript for all new code. Define proper interfaces and types.
2. **Component Structure**: Use functional components with hooks.
3. **File Naming**: Use PascalCase for component files and camelCase for utility files.
4. **Code Formatting**: Use ESLint and Prettier for consistent code formatting.
5. **Comments**: Add comments for complex logic and component props.

### Frontend Guidelines

#### Feature-Based Organization

When working on features, follow the feature-based organization structure:

1. **Place Feature-Specific Code in the Feature Folder**:

   - All components, hooks, services, and utilities specific to a feature should be placed in the feature's folder
   - Example: `src/pages/calculations/` for calculation-related code

2. **Organize Components by Page**:

   - Group components by the page they belong to
   - Example: `src/pages/calculations/components/detail/` for components used in the calculation detail page

3. **Create Index Files for Easy Importing**:

   - Create index.ts files in each folder to export all components, hooks, etc.
   - This allows for cleaner imports: `import { ComponentName } from './components/detail'`

4. **Shared vs. Feature-Specific Code**:

   - If a component, hook, or utility is used by multiple features, place it in the shared folders
   - If it's specific to a feature, place it in the feature's folder

5. **Import Paths**:
   - Use relative imports within a feature: `import { ComponentName } from './components/detail'`
   - Use absolute imports for shared code: `import { Button } from '@/components/ui/button'`

#### Component Organization

- Each component should have a single responsibility
- Break down complex components into smaller, reusable ones
- Use composition over inheritance

#### Props and State Management

```tsx
// Define prop types using TypeScript interfaces
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
}

// Use destructuring for props and provide default values
const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  onClick,
}) => {
  // Component implementation
};
```

- Use `useState` for component-level state
- Use React Context for global state
- Use React Query for server state and data fetching

#### Styling Guidelines

- Use Tailwind utility classes for styling
- Use the `className` prop for styling components
- Use shadcn/ui components for consistent UI
- Customize components using Tailwind classes
- Avoid direct modification of component source files

#### Data Fetching and State Management

```tsx
// Use React Query for API calls
const { data, isLoading, isError } = useQuery({
  queryKey: ['packages', filters],
  queryFn: () => getAllPackages(filters),
  meta: {
    onError: () => {
      toast.error('Failed to load packages');
    },
  },
});

// PREFERRED APPROACH: Use the API client for backend operations
export const getAllPackages = async (
  filters: PackageFilters = {},
): Promise<Package[]> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.categoryId) queryParams.append('categoryId', filters.categoryId);
    if (filters.page) queryParams.append('page', filters.page.toString());

    // Make API request
    const response = await authClient.get(
      `${API_ENDPOINTS.PACKAGES.LIST}?${queryParams.toString()}`,
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching packages:', error);
    throw error;
  }
};

// LEGACY APPROACH (Being phased out): Direct Supabase client usage
export const getAllPackagesLegacy = async (
  filters: PackageFilters = {},
): Promise<Package[]> => {
  let query = supabase.from('packages').select(`
      id,
      name,
      description,
      category_id,
      division_id,
      quantity_basis,
      is_deleted,
      categories (name),
      divisions (name)
    `);

  // Apply filters
  if (filters.search) {
    query = query.ilike('name', `%${filters.search}%`);
  }

  // Execute the query
  const { data, error } = await query.order('name');

  if (error) {
    console.error('Error fetching packages:', error);
    throw error;
  }

  // Transform the data
  return data.map((pkg) => ({
    // Mapping logic
  }));
};
```

#### External API Integration

```tsx
// Use the custom API client for external API calls
export const getTemplateById = async (id: string): Promise<Template> => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.TEMPLATES.GET_BY_ID(id));
    return response.data;
  } catch (error) {
    console.error('Error fetching template:', error);
    throw error;
  }
};
```

### Backend Guidelines

#### Module Organization

- Create a module for each domain entity
- Use the NestJS CLI to generate modules, controllers, and services
- Follow the repository pattern for data access

#### DTO and Validation

- Create DTOs for all API requests and responses
- Use class-validator decorators for validation
- Use class-transformer for serialization

```typescript
import { IsNotEmpty, IsString, IsOptional, IsNumber } from 'class-validator';

export class CreatePackageDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  categoryId?: string;

  @IsNotEmpty()
  @IsString()
  quantityBasis: string;

  @IsOptional()
  @IsNumber()
  price?: number;
}
```

#### Supabase Integration

- Use the `@supabase/supabase-js` client for all database operations
- Create a dedicated, injectable `SupabaseService` to manage the client instance
- Use the `SUPABASE_SERVICE_ROLE_KEY` for backend operations that need to bypass RLS policies
- For complex queries, use PostgreSQL functions and call them using `supabase.rpc()`

#### Error Handling

- Use NestJS exception filters for consistent error responses
- Provide meaningful error messages
- Log errors with appropriate context

## Development Workflow

### Core Development Principles

1. **Backend First Approach**:

   - Always implement backend API changes before frontend implementation
   - Ensure API endpoints are fully tested before beginning frontend work
   - Document API contracts (request/response formats) before implementation
   - Check existing backend modules to avoid duplicating functionality
   - **Important**: All new features should use the backend API instead of direct Supabase access

2. **Feature Implementation Flow**:

   - Start with database schema changes if needed
   - Implement backend API endpoints and services
   - Create or update DTOs and validation
   - Test backend implementation thoroughly
   - Implement frontend components and services using the backend API
   - Test frontend integration with backend

3. **Feature-Based Organization**:

   - Organize code by feature rather than by technical concerns
   - Place all feature-specific code in the feature's folder
   - Follow the structure:
     ```
     src/pages/feature-name/
     ├── components/     # Feature-specific components
     ├── hooks/          # Feature-specific hooks
     ├── utils/          # Feature-specific utilities
     ├── services/       # Feature-specific services
     ├── types/          # Feature-specific types
     └── constants/      # Feature-specific constants
     ```
   - Create index files for easy importing
   - Move shared code to appropriate shared folders

4. **Cross-Component Communication**:

   - Maintain clear documentation of API contracts
   - Use TypeScript interfaces to enforce type safety across boundaries
   - Implement proper error handling on both sides
   - Consider backward compatibility when making changes

5. **Supabase Integration Principles**:
   - All Supabase interactions should go through the backend API
   - When modifying existing code, update it to use the backend API instead of direct Supabase calls
   - Use the `SupabaseService` in the backend for all Supabase operations
   - Implement proper validation and error handling for all operations
   - Document all API endpoints thoroughly

### Git Workflow

1. **Branch Naming**:

   - Feature branches: `feature/feature-name`
   - Bug fixes: `fix/bug-name`
   - Refactoring: `refactor/description`

2. **Commit Messages**:

   - Use clear, descriptive commit messages
   - Follow the format: `type(scope): description`
   - Example: `feat(auth): add password reset functionality`

3. **Pull Requests**:
   - Create a pull request for each feature or fix
   - Provide a detailed description of changes
   - Request reviews from team members
   - Address review comments before merging

### Performance Optimization

#### Frontend Optimization

- Use React.memo for expensive components
- Implement proper React Query caching strategies
- Use code splitting for large components
- Optimize images and assets

#### Backend Optimization

- Implement database indexing for frequently queried fields
- Use pagination for large data sets
- Implement caching for expensive operations
- Use proper error handling and logging

## Deployment

### Frontend Deployment (Vercel)

1. **Build the application**:

   ```bash
   npm run build
   ```

2. **Deploy to Vercel**:
   - Connect your GitHub repository to Vercel
   - Configure environment variables in the Vercel dashboard
   - Set up automatic deployments for the main branch

### Backend Deployment (Railway)

1. **Build the application**:

   ```bash
   npm run build
   ```

2. **Deploy to Railway**:
   - Connect your GitHub repository to Railway
   - Configure environment variables in the Railway dashboard
   - Set up automatic deployments for the main branch

## Troubleshooting

### Common Issues

1. **Authentication Issues**:

   - Check Supabase credentials in environment variables
   - Verify JWT token configuration
   - Check browser console for authentication errors

2. **API Connection Issues**:

   - Verify API URL in environment variables
   - Check CORS configuration in the backend
   - Verify network connectivity

3. **Database Issues**:
   - Check Supabase connection
   - Verify database schema
   - Check for missing indexes

## Resources

- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [NestJS Documentation](https://docs.nestjs.com/)
- [Supabase Documentation](https://supabase.io/docs)
- [React Query Documentation](https://tanstack.com/query/latest/docs/react/overview)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Documentation](https://ui.shadcn.com/)
