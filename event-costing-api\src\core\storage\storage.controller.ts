import {
  <PERSON>,
  Get,
  Param,
  <PERSON>G<PERSON>s,
  <PERSON>s,
  Query,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { Response } from 'express';
import { StorageService } from './storage.service';
import { JwtAuthGuard } from '../../modules/auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../modules/auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SupabaseService } from '../supabase/supabase.service';

@ApiTags('Storage')
@Controller('storage')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class StorageController {
  private readonly logger = new Logger(StorageController.name);

  constructor(
    private readonly storageService: StorageService,
    private readonly supabaseService: SupabaseService,
  ) {}

  @Get('download/:bucket/*filePath')
  @ApiOperation({ summary: 'Download a file from storage' })
  @ApiParam({
    name: 'bucket',
    description: 'Storage bucket name',
    required: true,
  })
  @ApiParam({
    name: 'filePath',
    description: 'File path within the bucket',
    required: true,
  })
  @ApiQuery({
    name: 'download',
    description: 'Whether to download the file or view it in browser',
    required: false,
    type: Boolean,
  })
  @ApiResponse({
    status: 200,
    description: 'File downloaded successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Unauthorized access',
  })
  @Get('metadata/:bucket/*filePath')
  @ApiOperation({ summary: 'Get metadata for a file' })
  @ApiParam({
    name: 'bucket',
    description: 'Storage bucket name',
    required: true,
  })
  @ApiParam({
    name: 'filePath',
    description: 'File path within the bucket',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'File metadata retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
  })
  async getFileMetadata(
    @Param('bucket') bucket: string,
    @Param('filePath') filePath: string,
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(
      `User ${user.id} requesting file metadata for ${bucket}/${filePath}`,
    );

    try {
      // Check if the user has access to this file
      await this.checkFileAccess(user, bucket, filePath);

      // Get the file metadata
      const metadata = await this.storageService.getFileMetadata(
        bucket,
        filePath,
      );

      if (!metadata) {
        throw new NotFoundException('File not found or inaccessible');
      }

      return metadata;
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Unexpected error getting file metadata for ${bucket}/${filePath}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Failed to get file metadata');
    }
  }

  @Get('list/:bucket')
  @ApiOperation({ summary: 'List files in a bucket or folder' })
  @ApiParam({
    name: 'bucket',
    description: 'Storage bucket name',
    required: true,
  })
  @ApiQuery({
    name: 'path',
    description: 'Folder path within the bucket',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Files listed successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Unauthorized access',
  })
  async listFiles(
    @Param('bucket') bucket: string,
    @Query('path') path: string = '',
    @GetCurrentUser() user: User,
  ): Promise<any[]> {
    this.logger.log(
      `User ${user.id} requesting file listing for ${bucket}/${path}`,
    );

    try {
      // Check if the user has access to this bucket/folder
      await this.checkFileAccess(user, bucket, path);

      // List the files
      return await this.storageService.listFiles(bucket, path);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Unexpected error listing files in ${bucket}/${path}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Failed to list files');
    }
  }

  async downloadFile(
    @Param('bucket') bucket: string,
    @Param('filePath') filePath: string,
    @Query('download') download: boolean,
    @GetCurrentUser() user: User,
    @Res() res: Response,
  ): Promise<void> {
    this.logger.log(
      `User ${user.id} requesting file download from ${bucket}/${filePath}`,
    );

    try {
      // Check if the user has access to this file
      // This could be based on bucket permissions, file ownership, etc.
      await this.checkFileAccess(user, bucket, filePath);

      // Get the file data
      const supabase = this.supabaseService.getClient();
      const { data, error } = await supabase.storage
        .from(bucket)
        .download(filePath);

      if (error || !data) {
        this.logger.error(
          `Error downloading file ${bucket}/${filePath}: ${error?.message}`,
        );
        throw new NotFoundException('File not found or inaccessible');
      }

      // Set appropriate headers
      const contentType = data.type || 'application/octet-stream';
      res.setHeader('Content-Type', contentType);

      // If download parameter is true, set Content-Disposition to attachment
      if (download) {
        const fileName = filePath.split('/').pop() || 'download';
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="${fileName}"`,
        );
      }

      // Convert Blob to Buffer and send
      const arrayBuffer = await data.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      res.send(buffer);
    } catch (error: unknown) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Unexpected error downloading file ${bucket}/${filePath}: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException('Failed to download file');
    }
  }

  /**
   * Check if the user has access to the file
   * This is a placeholder for your actual access control logic
   */
  private async checkFileAccess(
    user: User,
    bucket: string,
    filePath: string,
  ): Promise<void> {
    // Example implementation - you would replace this with your actual access control logic
    // For example, check if the file belongs to the user, or if the user has the right role

    // For now, we'll implement a simple check based on bucket name
    // Public buckets are accessible to all authenticated users
    const publicBuckets = ['public', 'templates'];

    if (publicBuckets.includes(bucket)) {
      return; // Allow access to public buckets
    }

    // User-specific buckets (like profiles) should only be accessible to the owner
    if (bucket === 'profiles') {
      // Check if the filePath starts with the user's ID
      if (filePath.startsWith(`${user.id}/`) || filePath === user.id) {
        return; // Allow access to user's own files
      }
      throw new BadRequestException('You do not have access to this file');
    }

    // For other buckets, you might want to check database records
    // For example, if the file is related to a calculation, check if the user owns the calculation

    // This is where you would add async operations if needed
    // For example:
    // const hasAccess = await this.storageService.checkUserAccess(user.id, bucket, filePath);
    // if (!hasAccess) {
    //   throw new BadRequestException('You do not have access to this file');
    // }

    // For now, we'll just allow access to all files for authenticated users
    // In a real application, you would implement proper access control
    return;
  }
}
