import { apiClient } from "@/integrations/api/client";

export interface EventType {
  id: string;
  name: string;
  code: string;
  description?: string;
  icon?: string;
  color: string;
  display_order: number;
  is_active: boolean;
}

export interface CreateEventTypeRequest {
  name: string;
  code: string;
  description?: string;
  icon?: string;
  color?: string;
  display_order?: number;
}

export interface UpdateEventTypeRequest {
  name?: string;
  code?: string;
  description?: string;
  icon?: string;
  color?: string;
  display_order?: number;
  is_active?: boolean;
}

/**
 * Get all active event types for public use
 */
export const getAllEventTypes = async (): Promise<EventType[]> => {
  try {
    const response = await apiClient.get('/event-types');
    return response.data.data;
  } catch (error) {
    console.error('Error fetching event types:', error);
    throw error;
  }
};

/**
 * Get a specific event type by ID
 */
export const getEventTypeById = async (id: string): Promise<EventType> => {
  try {
    const response = await apiClient.get(`/event-types/${id}`);
    return response.data.data;
  } catch (error) {
    console.error('Error fetching event type:', error);
    throw error;
  }
};

/**
 * Get all event types for admin use (includes inactive)
 */
export const getAllEventTypesAdmin = async (): Promise<EventType[]> => {
  try {
    const response = await apiClient.get('/admin/event-types');
    return response.data.data;
  } catch (error) {
    console.error('Error fetching admin event types:', error);
    throw error;
  }
};

/**
 * Create a new event type (admin only)
 */
export const createEventType = async (eventType: CreateEventTypeRequest): Promise<EventType> => {
  try {
    const response = await apiClient.post('/admin/event-types', eventType);
    return response.data.data;
  } catch (error) {
    console.error('Error creating event type:', error);
    throw error;
  }
};

/**
 * Update an existing event type (admin only)
 */
export const updateEventType = async (id: string, eventType: UpdateEventTypeRequest): Promise<EventType> => {
  try {
    const response = await apiClient.patch(`/admin/event-types/${id}`, eventType);
    return response.data.data;
  } catch (error) {
    console.error('Error updating event type:', error);
    throw error;
  }
};

/**
 * Delete an event type (admin only) - soft delete
 */
export const deleteEventType = async (id: string): Promise<void> => {
  try {
    await apiClient.delete(`/admin/event-types/${id}`);
  } catch (error) {
    console.error('Error deleting event type:', error);
    throw error;
  }
};

/**
 * Get event type by code (utility function)
 */
export const getEventTypeByCode = async (code: string): Promise<EventType | null> => {
  try {
    const eventTypes = await getAllEventTypes();
    return eventTypes.find(type => type.code === code) || null;
  } catch (error) {
    console.error('Error finding event type by code:', error);
    return null;
  }
};
