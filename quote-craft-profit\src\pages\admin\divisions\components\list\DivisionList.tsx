import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { <PERSON>cil, AlertCircle, Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { updateDivision } from "@/services/admin/divisions";
import { Division } from "@/types/types";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

interface DivisionListProps {
  divisions: Division[];
  isLoading: boolean;
  isError: boolean;
  onEdit: (divisionId: string) => void;
  onRefresh: () => void;
}

export const DivisionList: React.FC<DivisionListProps> = ({
  divisions,
  isLoading,
  isError,
  onEdit,
  onRefresh,
}) => {
  const [processingDivisions, setProcessingDivisions] = useState<
    Record<string, boolean>
  >({});
  const { formatForDisplay } = useTimezoneAwareDates();

  // Toggle division status (active/inactive)
  const toggleDivisionStatus = async (division: Division) => {
    // Set processing state for this division
    setProcessingDivisions((prev) => ({ ...prev, [division.id]: true }));

    try {
      // Update division status using the backend API
      await updateDivision(division.id, {
        is_active: !division.is_active,
      });

      toast.success(
        `${division.name} ${
          division.is_active ? "deactivated" : "activated"
        } successfully`
      );
      onRefresh(); // Refresh the list
    } catch (error) {
      console.error(
        `Error toggling status for division ${division.id}:`,
        error
      );
      toast.error(
        `Failed to ${division.is_active ? "deactivate" : "activate"} division`
      );
    } finally {
      setProcessingDivisions((prev) => ({ ...prev, [division.id]: false }));
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-lg text-muted-foreground">
          Loading divisions...
        </span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center p-8 border rounded-lg border-destructive/50 bg-destructive/10">
        <AlertCircle className="h-6 w-6 text-destructive" />
        <span className="ml-2 text-lg text-destructive">
          Error loading divisions
        </span>
      </div>
    );
  }

  if (divisions.length === 0) {
    return (
      <div className="text-center py-8 border rounded-lg">
        <p className="text-muted-foreground">
          No divisions found. Create your first division using the "Add New
          Division" button.
        </p>
      </div>
    );
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-16">#</TableHead>
            <TableHead>Division Name</TableHead>
            <TableHead>Code</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {divisions.map((division, index) => (
            <TableRow key={division.id}>
              <TableCell className="text-muted-foreground">
                {index + 1}
              </TableCell>
              <TableCell className="font-medium">{division.name}</TableCell>
              <TableCell>{division.code}</TableCell>
              <TableCell>
                {division.created_at
                  ? formatForDisplay(division.created_at, "MMM d, yyyy")
                  : "N/A"}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  {processingDivisions[division.id] ? (
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  ) : (
                    <Switch
                      checked={division.is_active}
                      onCheckedChange={() => toggleDivisionStatus(division)}
                      aria-label={`${
                        division.is_active ? "Deactivate" : "Activate"
                      } ${division.name}`}
                    />
                  )}
                  <span
                    className={
                      division.is_active
                        ? "text-green-600"
                        : "text-muted-foreground"
                    }
                  >
                    {division.is_active ? "Active" : "Inactive"}
                  </span>
                </div>
              </TableCell>
              <TableCell className="text-right">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(division.id)}
                >
                  <Pencil className="h-4 w-4 mr-1" /> Edit
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </>
  );
};
