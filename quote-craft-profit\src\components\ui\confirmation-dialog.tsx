import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button, ButtonProps } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface ConfirmationDialogProps {
  /**
   * The title of the confirmation dialog
   */
  title: string;
  
  /**
   * The description or message of the confirmation dialog
   */
  description: string;
  
  /**
   * The text for the cancel button
   * @default "Cancel"
   */
  cancelText?: string;
  
  /**
   * The text for the confirm button
   * @default "Confirm"
   */
  confirmText?: string;
  
  /**
   * The variant of the confirm button
   * @default "destructive" for delete operations
   */
  confirmVariant?: ButtonProps['variant'];
  
  /**
   * Whether the dialog is open
   */
  open?: boolean;
  
  /**
   * Callback when the open state changes
   */
  onOpenChange?: (open: boolean) => void;
  
  /**
   * Callback when the confirm button is clicked
   */
  onConfirm: () => void;
  
  /**
   * Callback when the cancel button is clicked
   */
  onCancel?: () => void;
  
  /**
   * Whether the confirm button is in a loading state
   */
  isLoading?: boolean;
  
  /**
   * The trigger element that opens the dialog
   */
  trigger?: React.ReactNode;
  
  /**
   * Additional CSS classes for the trigger button
   */
  triggerClassName?: string;
}

/**
 * A reusable confirmation dialog component for actions that require confirmation
 * 
 * @example
 * ```tsx
 * <ConfirmationDialog
 *   title="Delete Item"
 *   description="Are you sure you want to delete this item? This action cannot be undone."
 *   onConfirm={() => deleteItem(item.id)}
 *   trigger={
 *     <Button variant="destructive" size="sm">
 *       <TrashIcon className="h-4 w-4 mr-1" />
 *       Delete
 *     </Button>
 *   }
 * />
 * ```
 */
export function ConfirmationDialog({
  title,
  description,
  cancelText = "Cancel",
  confirmText = "Confirm",
  confirmVariant = "destructive",
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  isLoading = false,
  trigger,
  triggerClassName,
}: ConfirmationDialogProps) {
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {trigger && (
        <AlertDialogTrigger asChild>
          <div className={cn(triggerClassName)}>
            {trigger}
          </div>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className={cn(
              confirmVariant === 'destructive' && 'bg-destructive hover:bg-destructive/90'
            )}
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
