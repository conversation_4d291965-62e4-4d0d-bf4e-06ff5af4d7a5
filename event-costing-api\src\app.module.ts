import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BullModule } from '@nestjs/bullmq';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { SupabaseModule } from './core/supabase/supabase.module';
import { CoreModule } from './core/core.module';
import { CacheModule } from './core/cache/cache.module';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { CalculationsModule } from './modules/calculations/calculations.module';
import { TemplatesModule } from './modules/templates/templates.module';
import { CalculationItemsModule } from './modules/calculation-items/calculation-items.module';
import { CitiesModule } from './modules/cities/cities.module';
import { CurrenciesModule } from './modules/currencies/currencies.module';
import { CategoriesModule } from './modules/categories/categories.module';
import { ClientsModule } from './modules/clients/clients.module';
import { EventsModule } from './modules/events/events.module';
import { PackagesModule } from './modules/packages/packages.module';
import { ExportsModule } from './modules/exports/exports.module';
import { SettingsModule } from './modules/settings/settings.module';
import { AdminModule as AdminUsersModule } from './modules/admin/admin.module';
import { AdminPackagesModule } from './modules/admin/packages/admin-packages.module';
import { PackagePricesModule } from './modules/admin/package-prices/package-prices.module';
import { PackageDependenciesModule } from './modules/admin/package-dependencies/package-dependencies.module';
import { PackageCitiesModule } from './modules/admin/package-cities/package-cities.module';
import { PackageVenuesModule } from './modules/admin/package-venues/package-venues.module';
import { PackageOptionsModule } from './modules/admin/package-options/package-options.module';
import { CostItemsModule } from './modules/admin/cost-items/cost-items.module';
import { ServiceCategoriesModule } from './modules/admin/service-categories/service-categories.module';
import { DivisionsModule } from './modules/divisions/divisions.module';
import { VenuesModule } from './modules/venues/venues.module';
import { EventTypesModule } from './modules/event-types/event-types.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env`,
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        connection: {
          username: configService.get<string>('REDIS_USERNAME', 'default'),
          password: configService.get<string>('REDIS_PASSWORD'),
          host: configService.get<string>('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
        },
      }),
      inject: [ConfigService],
    }),
    CacheModule,
    SupabaseModule,
    CoreModule,
    AuthModule,
    UsersModule,
    CalculationsModule,
    TemplatesModule,
    CalculationItemsModule,
    CitiesModule,
    CurrenciesModule,
    CategoriesModule,
    AdminUsersModule,
    AdminPackagesModule,
    PackagesModule,
    PackagePricesModule,
    PackageDependenciesModule,
    PackageCitiesModule,
    PackageVenuesModule,
    PackageOptionsModule,
    CostItemsModule,
    ServiceCategoriesModule,
    ClientsModule,
    EventsModule,
    DivisionsModule,
    ExportsModule,
    SettingsModule,
    VenuesModule,
    EventTypesModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
