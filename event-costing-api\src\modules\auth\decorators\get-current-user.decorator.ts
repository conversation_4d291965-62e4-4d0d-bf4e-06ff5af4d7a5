import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { User } from '@supabase/supabase-js'; // Or your custom user type if you map it
import { Request } from 'express'; // Import Request from express

/**
 * Interface representing an Express Request object augmented with a User property.
 */
interface RequestWithUser extends Request {
  user: User;
}

/**
 * Custom decorator to extract the user object attached to the request by the JwtAuthGuard.
 */
export const GetCurrentUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): User => {
    // Explicitly type the request object
    const request = ctx.switchToHttp().getRequest<RequestWithUser>();
    // Assuming the guard attaches the user object as request['user']
    return request.user;
  },
);
