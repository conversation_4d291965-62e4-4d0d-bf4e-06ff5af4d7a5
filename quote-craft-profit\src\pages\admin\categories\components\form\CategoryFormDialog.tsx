import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { getCategoryById, createCategory, updateCategory } from '@/services/admin/categories';

// Define the form schema
const categoryFormSchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  code: z.string().min(1, 'Category code is required'),
  description: z.string().optional(),
  icon: z.string().optional(),
});

type CategoryFormData = z.infer<typeof categoryFormSchema>;

interface CategoryFormDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  categoryId: string | null;
}

const CategoryFormDialog: React.FC<CategoryFormDialogProps> = ({
  isOpen,
  onClose,
  categoryId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const isEditing = !!categoryId;

  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: '',
      code: '',
      description: '',
      icon: '',
    },
  });

  // Load category data if editing
  useEffect(() => {
    const loadCategoryData = async () => {
      if (!categoryId) return;

      setIsLoading(true);
      try {
        const categoryData = await getCategoryById(categoryId);

        if (categoryData) {
          form.reset({
            name: categoryData.name || '',
            code: categoryData.code || '',
            description: categoryData.description || '',
            icon: categoryData.icon || '',
          });
        }
      } catch (error) {
        console.error('Error loading category:', error);
        toast.error('Failed to load category data');
        onClose();
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen && isEditing) {
      loadCategoryData();
    } else {
      // Reset form when opening for a new category
      form.reset({
        name: '',
        code: '',
        description: '',
        icon: '',
      });
    }
  }, [isOpen, categoryId, form, isEditing, onClose]);

  const onSubmit = async (data: CategoryFormData) => {
    setIsLoading(true);

    try {
      if (isEditing) {
        // Update existing category
        await updateCategory(categoryId, {
          name: data.name,
          code: data.code,
          description: data.description,
          icon: data.icon,
        });
        toast.success('Category updated successfully');
      } else {
        // Create new category
        await createCategory({
          name: data.name,
          code: data.code,
          description: data.description,
          icon: data.icon,
        });
        toast.success('Category created successfully');
      }

      onClose(true); // Close and refresh the list
    } catch (error) {
      console.error('Error saving category:', error);
      toast.error(isEditing ? 'Failed to update category' : 'Failed to create category');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => !isLoading && onClose()}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Category' : 'Add New Category'}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category Name</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter category name' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='code'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category Code</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter category code'
                      {...field}
                      onChange={(e) => {
                        // Auto-uppercase the code value
                        field.onChange(e.target.value.toUpperCase());
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Enter category description'
                      className='resize-none'
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='icon'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter icon name or path'
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => onClose()}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading
                  ? isEditing
                    ? 'Updating...'
                    : 'Creating...'
                  : isEditing
                  ? 'Update Category'
                  : 'Create Category'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CategoryFormDialog;
