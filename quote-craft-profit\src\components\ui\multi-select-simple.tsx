import * as React from 'react';
import { cn } from '@/lib/utils';
import { Check, X, ChevronsUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

export type Option = {
  value: string;
  label: string;
};

interface MultiSelectProps {
  options: Option[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const MultiSelectSimple = React.forwardRef<HTMLDivElement, MultiSelectProps>(
  (
    {
      options,
      selected,
      onChange,
      placeholder = 'Select options',
      className,
      disabled = false,
    },
    ref,
  ) => {
    const [open, setOpen] = React.useState(false);
    const [searchQuery, setSearchQuery] = React.useState('');

    // Ensure selected is always an array
    const selectedValues = Array.isArray(selected) ? selected : [];

    const handleUnselect = (value: string) => {
      onChange(selectedValues.filter((item) => item !== value));
    };

    const handleSelect = (value: string) => {
      if (selectedValues.includes(value)) {
        onChange(selectedValues.filter((item) => item !== value));
      } else {
        onChange([...selectedValues, value]);
      }
    };

    // Filter options based on search query
    const filteredOptions = options.filter((option) =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase()),
    );

    return (
      <div ref={ref}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              role='combobox'
              aria-expanded={open}
              className={cn(
                'w-full justify-between',
                selectedValues.length > 0 ? 'h-auto' : 'h-10',
                className,
              )}
              onClick={() => setOpen(!open)}
              disabled={disabled}
            >
              <div className='flex flex-wrap gap-1'>
                {selectedValues.length > 0 ? (
                  selectedValues.map((value) => (
                    <Badge variant='secondary' key={value} className='mr-1 mb-1'>
                      {options.find((option) => option.value === value)?.label || value}
                      <span
                        className='ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer'
                        role='button'
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleUnselect(value);
                          }
                        }}
                        onMouseDown={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleUnselect(value);
                        }}
                      >
                        <X className='h-3 w-3 text-muted-foreground hover:text-foreground' />
                      </span>
                    </Badge>
                  ))
                ) : (
                  <span className='text-muted-foreground'>{placeholder}</span>
                )}
              </div>
              <ChevronsUpDown className='h-4 w-4 shrink-0 opacity-50' />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className='w-full p-0'
            onOpenAutoFocus={(e) => e.preventDefault()}
          >
            <div className='p-2'>
              <Input
                placeholder='Search options...'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className='mb-2'
              />
              <div
                className='max-h-60 overflow-y-auto'
                style={{
                  overscrollBehavior: 'contain',
                  WebkitOverflowScrolling: 'touch',
                }}
                onWheel={(e) => {
                  // Prevent the parent from scrolling when this div is scrolled
                  e.stopPropagation();
                }}
                onTouchStart={(e) => {
                  // Ensure touch events work properly
                  e.stopPropagation();
                }}
                onClick={(e) => {
                  // Prevent closing the popover when clicking inside the scrollable area
                  e.stopPropagation();
                }}
              >
                <div className='p-1'>
                  {filteredOptions.length === 0 ? (
                    <div className='py-6 text-center text-sm'>No options found.</div>
                  ) : (
                    filteredOptions.map((option) => (
                      <div
                        key={option.value}
                        className={cn(
                          'relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground',
                          selectedValues.includes(option.value) &&
                            'bg-accent text-accent-foreground',
                        )}
                        onClick={() => handleSelect(option.value)}
                      >
                        <div
                          className={cn(
                            'flex h-4 w-4 items-center justify-center rounded-sm border border-primary mr-2',
                            selectedValues.includes(option.value)
                              ? 'bg-primary text-primary-foreground'
                              : 'opacity-50',
                          )}
                        >
                          {selectedValues.includes(option.value) && (
                            <Check className='h-3 w-3' />
                          )}
                        </div>
                        <span>{option.label}</span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    );
  },
);

MultiSelectSimple.displayName = 'MultiSelectSimple';
