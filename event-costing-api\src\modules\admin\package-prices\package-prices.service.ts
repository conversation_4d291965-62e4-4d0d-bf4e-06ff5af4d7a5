import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
  ConflictException, // Added for error handling
  BadRequestException, // Added for error handling
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PostgrestError } from '@supabase/supabase-js'; // Added for error handling
import { CreatePackagePriceDto } from './dto/create-package-price.dto';
import { PackagePriceDto } from './dto/package-price.dto';
import { UpdatePackagePriceDto } from './dto/update-package-price.dto';

@Injectable()
export class PackagePricesService {
  private readonly logger = new Logger(PackagePricesService.name);
  private readonly TABLE_NAME = 'package_prices'; // Replace with actual table name if different

  constructor(private readonly supabaseService: SupabaseService) {}

  // --- Package Price Management --- //

  async create(
    packageId: string,
    createDto: CreatePackagePriceDto,
  ): Promise<PackagePriceDto> {
    this.logger.log(
      `Attempting to add price for package ${packageId}: ${JSON.stringify(createDto)}`,
    );
    const supabase = this.supabaseService.getClient();

    // 1. Verify the parent package exists (using checkPackageExists)
    await this.checkPackageExists(packageId);

    // 2. Prepare data for insertion
    const insertData = {
      package_id: packageId,
      currency_id: createDto.currency_id,
      price: createDto.price,
      unit_base_cost: createDto.unit_base_cost,
      description: createDto.description,
    };

    // 3. Insert the new price
    const { data, error } = await supabase
      .from('package_prices')
      .insert(insertData)
      .select('*')
      .single<PackagePriceDto>();

    if (error) {
      // Error handling
      this.handlePackagePriceError(error, packageId, createDto.currency_id);
    }

    if (!data) {
      this.logger.error(
        'Package price creation did not return data unexpectedly.',
      );
      throw new InternalServerErrorException(
        'Failed to create package price after insertion.',
      );
    }

    this.logger.log(
      `Price created successfully for package ${packageId} with ID: ${data.id}`,
    );
    return data;
  }

  async findAll(packageId: string): Promise<PackagePriceDto[]> {
    this.logger.log(`Fetching prices for package ${packageId}`);
    const supabase = this.supabaseService.getClient();

    // 1. Ensure the package exists (using checkPackageExists)
    await this.checkPackageExists(packageId);

    // 2. Fetch prices for the package
    const { data, error } = await supabase
      .from('package_prices')
      .select('*')
      .eq('package_id', packageId)
      .returns<PackagePriceDto[]>();
    if (error) {
      this.logger.error(
        `Error fetching prices for package ${packageId}: ${error.message}`,
        error.details,
      );
      throw new InternalServerErrorException(
        `Failed to fetch prices: ${error.message}`,
      );
    }

    this.logger.log(
      `Found ${data?.length ?? 0} prices for package ${packageId}`,
    );
    return data ?? [];
  }

  async findOne(
    packageId: string,
    packagePriceId: string,
  ): Promise<PackagePriceDto> {
    this.logger.log(
      `Fetching price ${packagePriceId} for package ${packageId}`,
    );
    const supabase = this.supabaseService.getClient();

    // Ensure the package exists (using checkPackageExists)
    await this.checkPackageExists(packageId);

    const { data, error } = await supabase
      .from('package_prices')
      .select('*')
      .match({ id: packagePriceId, package_id: packageId })
      .single<PackagePriceDto>();

    if (error) {
      this.logger.error(
        `Error fetching price ${packagePriceId} for package ${packageId}: ${error.message}`,
      );
      this.handlePackagePriceError(error, packageId, 'unknown');
    }

    if (!data) {
      throw new NotFoundException(
        `Price record with ID ${packagePriceId} not found for package ${packageId}.`,
      );
    }

    return data;
  }

  async update(
    packageId: string,
    packagePriceId: string,
    updateDto: UpdatePackagePriceDto,
  ): Promise<PackagePriceDto> {
    this.logger.log(
      `Attempting to update price ${packagePriceId} for package ${packageId}: ${JSON.stringify(updateDto)}`,
    );
    const supabase = this.supabaseService.getClient();

    // 1. Prepare update data
    const updateData: Partial<
      Pick<PackagePriceDto, 'price' | 'unit_base_cost' | 'description'>
    > = {};
    if (updateDto.price !== undefined) {
      updateData.price = updateDto.price;
    }
    if (updateDto.unit_base_cost !== undefined) {
      updateData.unit_base_cost = updateDto.unit_base_cost;
    }
    if (updateDto.description !== undefined) {
      updateData.description = updateDto.description;
    }

    if (Object.keys(updateData).length === 0) {
      this.logger.log(
        `Update called for price ${packagePriceId} package ${packageId} with no changes. Fetching current data.`,
      );
      // Fetch and return the current record if no changes were provided
      return this.findOne(packageId, packagePriceId);
    }

    // 2. Perform the update
    const { data, error } = await supabase
      .from('package_prices')
      .update(updateData)
      .match({ id: packagePriceId, package_id: packageId })
      .select('*')
      .single<PackagePriceDto>();

    if (error) {
      this.handlePackagePriceError(error, packageId, 'unknown');
    }

    if (!data) {
      this.logger.error(
        `Price record with ID ${packagePriceId} not found for package ${packageId} during update.`,
      );
      throw new NotFoundException(
        `Price record with ID ${packagePriceId} not found for package ${packageId}.`,
      );
    }

    this.logger.log(
      `Price ${packagePriceId} for package ${packageId} updated successfully.`,
    );
    return data;
  }

  async remove(packageId: string, packagePriceId: string): Promise<void> {
    this.logger.log(
      `Attempting to delete price ${packagePriceId} for package ${packageId}`,
    );
    const supabase = this.supabaseService.getClient();

    // 1. Ensure the package exists (using checkPackageExists)
    await this.checkPackageExists(packageId);

    // 2. Perform delete
    const { error, count } = await supabase
      .from('package_prices')
      .delete()
      .match({ id: packagePriceId, package_id: packageId });

    if (error) {
      this.logger.error(
        `Error deleting price ${packagePriceId} for package ${packageId}: ${error.message}`,
        error.details,
      );
      throw new InternalServerErrorException(
        `Failed to delete price record: ${error.message}`,
      );
    }

    if (count === 0) {
      this.logger.warn(
        `Price record with ID ${packagePriceId} not found for package ${packageId} during delete.`,
      );
      throw new NotFoundException(
        `Price record with ID ${packagePriceId} not found for package ${packageId}.`,
      );
    }

    this.logger.log(
      `Price ${packagePriceId} for package ${packageId} deleted successfully.`,
    );
  }

  // --- Helper Methods --- //

  // Helper function to handle common PostgREST errors for package prices
  private handlePackagePriceError(
    error: PostgrestError,
    packageId: string,
    currencyId: string, // Can be 'unknown' during update/fetch errors
  ) {
    this.logger.error(
      `Database error managing price for package ${packageId}, currency ${currencyId}: ${error.message}`,
      error.stack,
    );
    // Check for unique constraint violation
    if (
      error.code === '23505' &&
      error.details?.includes(
        'package_prices_package_id_currency_id_effective_from_key',
      )
    ) {
      throw new ConflictException(
        `A price for package ${packageId} and currency ${currencyId} with the same effective date already exists.`,
      );
    }
    // Check for foreign key violation (e.g., currency_id doesn't exist)
    if (error.code === '23503') {
      if (error.details?.includes('package_prices_currency_id_fkey')) {
        throw new BadRequestException(`Invalid currency ID: ${currencyId}.`);
      }
      // We check package_id separately, but handle just in case
      if (error.details?.includes('package_prices_package_id_fkey')) {
        // This case should ideally be caught by checkPackageExists
        throw new NotFoundException(`Package with ID ${packageId} not found.`);
      }
    }
    // Default fallback for select errors or other issues
    if (error.message.includes('Results contain 0 rows')) {
      throw new NotFoundException(
        `Price record not found for package ${packageId} (currency: ${currencyId}).`,
      );
    }

    throw new InternalServerErrorException(
      `Failed to manage package price: ${error.message}`,
    );
  }

  // Temporary helper to check if package exists. Replace with PackagesService call later.
  private async checkPackageExists(packageId: string): Promise<void> {
    this.logger.debug(`Checking existence of package ${packageId}`);
    const supabase = this.supabaseService.getClient();
    const { error, count } = await supabase
      .from('packages')
      .select('id', { count: 'exact', head: true }) // More efficient: just check existence
      .eq('id', packageId)
      .eq('is_deleted', false); // Only consider active packages

    if (error) {
      this.logger.error(
        `Error checking package existence ${packageId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to verify package ${packageId}: ${error.message}`,
      );
    }

    if (count === 0) {
      this.logger.warn(`Package with ID ${packageId} not found or is deleted.`);
      throw new NotFoundException(`Package with ID ${packageId} not found.`);
    }
    this.logger.debug(`Package ${packageId} confirmed to exist.`);
  }
}
