import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Minus, Plus, Users } from "lucide-react";
import { WizardState } from "./WizardContainer";

interface AttendeeCounterProps {
  wizardState: WizardState;
  updateWizardState: (updates: Partial<WizardState>) => void;
  onNext: () => void;
}

const ATTENDEE_RANGES = [
  {
    id: "1-50",
    label: "1-50",
    description: "Small gatherings",
    min: 1,
    max: 50,
    color: "green",
  },
  {
    id: "51-100",
    label: "51-100",
    description: "Medium events",
    min: 51,
    max: 100,
    color: "orange",
  },
  {
    id: "101-500",
    label: "101-500",
    description: "Large events",
    min: 101,
    max: 500,
    color: "red",
  },
  {
    id: "500+",
    label: "500+",
    description: "Major events",
    min: 500,
    max: 1000,
    color: "purple",
  },
];

const getAttendeeRange = (attendees: number) => {
  if (attendees <= 50) return ATTENDEE_RANGES[0];
  if (attendees <= 100) return ATTENDEE_RANGES[1];
  if (attendees <= 500) return ATTENDEE_RANGES[2];
  return ATTENDEE_RANGES[3];
};

const getRangeColorClasses = (color: string, isActive: boolean) => {
  const colorMap = {
    green: {
      bg: isActive
        ? "bg-green-100 dark:bg-green-900/30"
        : "bg-gray-100 dark:bg-gray-800",
      text: isActive
        ? "text-green-700 dark:text-green-300"
        : "text-gray-600 dark:text-gray-400",
      border: isActive
        ? "border-green-300 dark:border-green-600"
        : "border-gray-200 dark:border-gray-700",
    },
    orange: {
      bg: isActive
        ? "bg-orange-100 dark:bg-orange-900/30"
        : "bg-gray-100 dark:bg-gray-800",
      text: isActive
        ? "text-orange-700 dark:text-orange-300"
        : "text-gray-600 dark:text-gray-400",
      border: isActive
        ? "border-orange-300 dark:border-orange-600"
        : "border-gray-200 dark:border-gray-700",
    },
    red: {
      bg: isActive
        ? "bg-red-100 dark:bg-red-900/30"
        : "bg-gray-100 dark:bg-gray-800",
      text: isActive
        ? "text-red-700 dark:text-red-300"
        : "text-gray-600 dark:text-gray-400",
      border: isActive
        ? "border-red-300 dark:border-red-600"
        : "border-gray-200 dark:border-gray-700",
    },
    purple: {
      bg: isActive
        ? "bg-purple-100 dark:bg-purple-900/30"
        : "bg-gray-100 dark:bg-gray-800",
      text: isActive
        ? "text-purple-700 dark:text-purple-300"
        : "text-gray-600 dark:text-gray-400",
      border: isActive
        ? "border-purple-300 dark:border-purple-600"
        : "border-gray-200 dark:border-gray-700",
    },
  };

  return colorMap[color as keyof typeof colorMap] || colorMap.green;
};

export const AttendeeCounter: React.FC<AttendeeCounterProps> = ({
  wizardState,
  updateWizardState,
  onNext,
}) => {
  const handleAttendeeChange = (attendees: number) => {
    const validAttendees = Math.max(1, Math.min(10000, attendees));
    updateWizardState({ attendeeCount: validAttendees });
  };

  const handleRangeSelect = (range: (typeof ATTENDEE_RANGES)[0]) => {
    const midPoint = Math.floor((range.min + range.max) / 2);
    handleAttendeeChange(midPoint);
  };

  const increment = () => {
    handleAttendeeChange(wizardState.attendeeCount + 1);
  };

  const decrement = () => {
    handleAttendeeChange(wizardState.attendeeCount - 1);
  };

  const currentRange = getAttendeeRange(wizardState.attendeeCount);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          How many people will attend?
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Enter the expected number of attendees for your event
        </p>
      </div>

      {/* Main Counter */}
      <div className="flex flex-col items-center space-y-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="lg"
            onClick={decrement}
            disabled={wizardState.attendeeCount <= 1}
            className="h-12 w-12 rounded-full"
          >
            <Minus className="h-5 w-5" />
          </Button>

          <div className="text-center">
            <Input
              type="number"
              value={wizardState.attendeeCount}
              onChange={(e) =>
                handleAttendeeChange(parseInt(e.target.value) || 1)
              }
              className="text-4xl font-bold text-center w-32 h-16 border-2"
              min="1"
              max="10000"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              attendees
            </p>
          </div>

          <Button
            variant="outline"
            size="lg"
            onClick={increment}
            disabled={wizardState.attendeeCount >= 10000}
            className="h-12 w-12 rounded-full"
          >
            <Plus className="h-5 w-5" />
          </Button>
        </div>

        {/* Current Range Indicator */}
        <Card
          className={`border-2 ${
            getRangeColorClasses(currentRange.color, true).border
          } ${getRangeColorClasses(currentRange.color, true).bg}`}
        >
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center space-x-2">
              <Users
                className={`h-5 w-5 ${
                  getRangeColorClasses(currentRange.color, true).text
                }`}
              />
              <span
                className={`font-medium ${
                  getRangeColorClasses(currentRange.color, true).text
                }`}
              >
                {currentRange.label} guests - {currentRange.description}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Select Ranges */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white text-center">
          Or choose a range:
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {ATTENDEE_RANGES.map((range) => {
            const isActive = currentRange.id === range.id;
            const colors = getRangeColorClasses(range.color, isActive);

            return (
              <Card
                key={range.id}
                className={`cursor-pointer transition-all duration-200 hover:scale-105 border-2 ${colors.border} ${colors.bg}`}
                onClick={() => handleRangeSelect(range)}
              >
                <CardContent className="p-4 text-center">
                  <div className={`font-bold text-lg ${colors.text}`}>
                    {range.label}
                  </div>
                  <div className={`text-sm ${colors.text}`}>
                    {range.description}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Continue Button */}
      <div className="text-center">
        <Button
          onClick={onNext}
          size="lg"
          className="px-8"
          disabled={wizardState.attendeeCount < 1}
        >
          Continue with {wizardState.attendeeCount} attendees
        </Button>
      </div>
    </div>
  );
};
