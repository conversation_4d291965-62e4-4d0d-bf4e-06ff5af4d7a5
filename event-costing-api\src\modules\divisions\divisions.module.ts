import { Module } from '@nestjs/common';
import { DivisionsService } from './divisions.service';
import { AdminDivisionsController } from './admin-divisions.controller';
import { DivisionsController } from './divisions.controller';
import { AuthModule } from '../auth/auth.module';
import { AdminModule } from '../auth/admin.module';

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [AdminDivisionsController, DivisionsController],
  providers: [DivisionsService],
  exports: [DivisionsService], // Export if needed by other modules
})
export class DivisionsModule {}
