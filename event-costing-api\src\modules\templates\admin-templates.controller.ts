import {
  Controller,
  Post,
  Body,
  Get,
  Put,
  Delete,
  Param,
  Query,
  UseGuards,
  Logger,
  Inject,
  forwardRef,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';
import { GetCurrentUser } from '../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import {
  TemplateSummaryDto,
  PaginatedAdminTemplatesResponse,
  TemplateDetailDto,
} from './dto/template-summary.dto';
import { CreateTemplateFromCalculationDto } from './dto/create-template-from-calculation.dto';
import { CreateTemplateDto } from './dto/create-template.dto';
import {
  TemplateCalculationResultDto,
  TemplateCalculationSummaryDto,
} from './dto/template-calculation.dto';
import { ListAdminTemplatesQueryDto } from './dto/list-admin-templates.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { UpdateTemplateStatusDto } from './dto/update-template-status.dto';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiNoContentResponse,
  ApiBadRequestResponse,
  ApiParam,
} from '@nestjs/swagger';

@Controller('admin/templates')
@ApiTags('Admin Templates')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AdminRoleGuard) // Apply guards to the whole controller
export class AdminTemplatesController {
  private readonly logger = new Logger(AdminTemplatesController.name);

  constructor(
    @Inject(forwardRef(() => TemplatesService))
    private readonly templatesService: TemplatesService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a basic template' })
  @ApiCreatedResponse({
    description: 'Template created successfully.',
    type: TemplateSummaryDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data.' })
  async createTemplate(
    @Body() createDto: CreateTemplateDto,
    @GetCurrentUser() user: User,
  ): Promise<TemplateSummaryDto> {
    this.logger.log(
      `Admin user ${user.email} creating basic template '${createDto.name}'`,
    );
    return this.templatesService.createTemplate(createDto, user);
  }

  @Post('from-calculation')
  @ApiOperation({ summary: 'Create a template from an existing calculation' })
  @ApiCreatedResponse({
    description: 'Template created successfully.',
    type: TemplateSummaryDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data.' })
  @ApiNotFoundResponse({
    description: 'Calculation not found or has no items.',
  })
  async createTemplateFromCalculation(
    @Body() createDto: CreateTemplateFromCalculationDto,
    @GetCurrentUser() user: User,
  ): Promise<TemplateSummaryDto> {
    this.logger.log(
      `Admin user ${user.email} creating template from calculation ${createDto.calculationId}`,
    );
    return this.templatesService.createTemplateFromCalculation(createDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'List all templates (admin view)' })
  @ApiOkResponse({
    description: 'Paginated list of templates.',
    type: PaginatedAdminTemplatesResponse,
  })
  async findAllAdmin(
    @Query() queryDto: ListAdminTemplatesQueryDto,
  ): Promise<PaginatedAdminTemplatesResponse> {
    this.logger.log(
      `Admin fetching templates list with query: ${JSON.stringify(queryDto)}`,
    );
    return this.templatesService.findAllAdmin(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get detailed template information (admin view)' })
  @ApiOkResponse({
    description: 'Template details.',
    type: TemplateDetailDto,
  })
  @ApiNotFoundResponse({ description: 'Template not found.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async findOneAdmin(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<TemplateDetailDto> {
    this.logger.log(`Admin fetching template details for ID: ${id}`);
    const result = await this.templatesService.findOneAdmin(id);

    // Debug log to check if venue_ids are included in the response
    this.logger.log(
      `Template response for ID ${id}: ${JSON.stringify({
        id: result.id,
        name: result.name,
        venue_ids: result.venue_ids,
        has_venue_ids: !!result.venue_ids,
        venue_ids_length: result.venue_ids?.length || 0,
      })}`,
    );

    return result;
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a template' })
  @ApiOkResponse({
    description: 'Template updated successfully.',
    type: TemplateDetailDto,
  })
  @ApiNotFoundResponse({ description: 'Template not found.' })
  @ApiBadRequestResponse({ description: 'Invalid input data.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async updateTemplate(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateTemplateDto,
  ): Promise<TemplateDetailDto> {
    this.logger.log(`Admin updating template ID: ${id}`);
    return this.templatesService.updateTemplate(id, updateDto);
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update template active status' })
  @ApiOkResponse({
    description: 'Template status updated successfully.',
    type: TemplateDetailDto,
  })
  @ApiNotFoundResponse({ description: 'Template not found.' })
  @ApiBadRequestResponse({ description: 'Invalid input data.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async updateTemplateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateStatusDto: UpdateTemplateStatusDto,
  ): Promise<TemplateDetailDto> {
    this.logger.log(
      `Admin updating template status ID: ${id} to ${updateStatusDto.isActive ? 'active' : 'inactive'}`,
    );
    return this.templatesService.updateTemplateStatus(
      id,
      updateStatusDto.isActive,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a template' })
  @ApiNoContentResponse({ description: 'Template deleted successfully.' })
  @ApiNotFoundResponse({ description: 'Template not found.' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async deleteTemplate(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Admin deleting template ID: ${id}`);
    return this.templatesService.deleteTemplate(id);
  }

  @Get(':id/calculate')
  @ApiOperation({ summary: 'Calculate template total value and breakdown' })
  @ApiOkResponse({
    description: 'Template calculation result.',
    type: TemplateCalculationResultDto,
  })
  @ApiNotFoundResponse({ description: 'Template not found.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async calculateTemplate(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<TemplateCalculationResultDto> {
    this.logger.log(`Admin calculating template total for ID: ${id}`);
    return this.templatesService.calculateTemplateTotal(id);
  }

  @Get(':id/calculate/summary')
  @ApiOperation({ summary: 'Get template calculation summary' })
  @ApiOkResponse({
    description: 'Template calculation summary.',
    type: TemplateCalculationSummaryDto,
  })
  @ApiNotFoundResponse({ description: 'Template not found.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async getTemplateCalculationSummary(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<TemplateCalculationSummaryDto> {
    this.logger.log(`Admin getting template calculation summary for ID: ${id}`);
    return this.templatesService.getTemplateCalculationSummary(id);
  }
}
