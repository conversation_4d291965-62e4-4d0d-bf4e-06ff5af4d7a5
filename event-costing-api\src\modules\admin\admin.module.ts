import { Module } from '@nestjs/common';
import { AdminUsersModule } from './users/admin-users.module';
import { AdminDashboardService } from './services/admin-dashboard.service';
import { AdminDashboardController } from './controllers/admin-dashboard.controller';
import { AuthModule } from '../auth/auth.module'; // For guards
import { CategoriesModule } from '../categories/categories.module'; // For dashboard service
import { CitiesModule } from '../cities/cities.module'; // For dashboard service
import { DivisionsModule } from '../divisions/divisions.module'; // For dashboard service

@Module({
  imports: [
    AdminUsersModule,
    AuthModule, // For JwtAuthGuard and AdminRoleGuard
    CategoriesModule, // For dashboard service dependencies
    CitiesModule, // For dashboard service dependencies
    DivisionsModule, // For dashboard service dependencies
  ],
  controllers: [
    AdminDashboardController, // New consolidated dashboard controller
  ],
  providers: [
    AdminDashboardService, // New consolidated dashboard service
  ],
  exports: [
    AdminUsersModule,
    AdminDashboardService, // Export for use in other modules
  ],
})
export class AdminModule {}
