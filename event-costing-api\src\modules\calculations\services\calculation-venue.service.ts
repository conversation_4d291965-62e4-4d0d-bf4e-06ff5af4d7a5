import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { VenueReferenceDto } from '../../venues/dto/venue-reference.dto';

/**
 * Service responsible for managing venue associations with calculations
 * Extracted from the main CalculationsService for better separation of concerns
 */
@Injectable()
export class CalculationVenueService {
  private readonly logger = new Logger(CalculationVenueService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Add venues to a calculation during creation
   */
  async addVenuesToCalculation(
    calculationId: string,
    venueIds: string[],
  ): Promise<void> {
    if (!venueIds || venueIds.length === 0) {
      return;
    }

    this.logger.log(
      `Adding venues to calculation ${calculationId}: ${venueIds.join(', ')}`,
    );

    const supabase = this.supabaseService.getClient();

    const venueEntries = venueIds.map(venueId => ({
      calculation_id: calculationId,
      venue_id: venueId,
    }));

    const { error: venueError } = await supabase
      .from('calculation_venues')
      .insert(venueEntries);

    if (venueError) {
      this.logger.error(
        `Failed to add venues to calculation ${calculationId}: ${venueError.message}`,
        venueError.stack,
      );
      // We don't throw here to avoid failing the whole creation process
      // The calculation is created, but venues might not be associated
    }
  }

  /**
   * Update venues for a calculation
   */
  async updateCalculationVenues(
    calculationId: string,
    venueIds: string[],
  ): Promise<void> {
    this.logger.log(
      `Updating venues for calculation ${calculationId}: ${venueIds.join(', ')}`,
    );

    const supabase = this.supabaseService.getClient();

    try {
      // First, delete existing venue associations
      await supabase
        .from('calculation_venues')
        .delete()
        .eq('calculation_id', calculationId);

      // Then, add new venue associations if any are provided
      if (venueIds && venueIds.length > 0) {
        const venueEntries = venueIds.map(venueId => ({
          calculation_id: calculationId,
          venue_id: venueId,
        }));

        const { error: venueError } = await supabase
          .from('calculation_venues')
          .insert(venueEntries);

        if (venueError) {
          this.logger.error(
            `Failed to update venues for calculation ${calculationId}: ${venueError.message}`,
            venueError.stack,
          );
          // We don't throw here to avoid failing the whole update process
        }
      }
    } catch (venueError) {
      this.logger.error(
        `Error handling venue updates for calculation ${calculationId}: ${venueError}`,
      );
      // We don't throw here to avoid failing the whole update process
    }
  }

  /**
   * Fetch venues associated with a calculation
   */
  async fetchCalculationVenues(
    calculationId: string,
  ): Promise<VenueReferenceDto[]> {
    this.logger.log(`Fetching venues for calculation ID: ${calculationId}`);
    const supabase = this.supabaseService.getClient();

    // First, get the venue IDs from the junction table
    const { data: venueRelations, error: relationsError } = await supabase
      .from('calculation_venues')
      .select('venue_id')
      .eq('calculation_id', calculationId);

    if (relationsError) {
      this.logger.error(
        `Error fetching venue relations for calculation ${calculationId}: ${relationsError.message}`,
      );
      return []; // Return empty array on error instead of failing
    }

    if (!venueRelations || venueRelations.length === 0) {
      return []; // No venues associated with this calculation
    }

    // Extract venue IDs
    const venueIds = venueRelations.map(relation => relation.venue_id);

    // Fetch venue details
    const { data: venues, error: venuesError } = await supabase
      .from('venues')
      .select(
        `
        id,
        name,
        address,
        city_id,
        cities (name)
      `,
      )
      .in('id', venueIds)
      .eq('is_deleted', false);

    if (venuesError) {
      this.logger.error(
        `Error fetching venues for calculation ${calculationId}: ${venuesError.message}`,
      );
      return []; // Return empty array on error instead of failing
    }

    // Transform the data to match the VenueReferenceDto
    return venues.map(venue => ({
      id: venue.id,
      name: venue.name,
      address: venue.address,
      city_id: venue.city_id,
      city_name: (venue.cities && venue.cities[0]?.name) || null,
    }));
  }

  /**
   * Remove all venues from a calculation
   */
  async removeAllVenuesFromCalculation(calculationId: string): Promise<void> {
    this.logger.log(`Removing all venues from calculation ${calculationId}`);
    
    const supabase = this.supabaseService.getClient();

    const { error } = await supabase
      .from('calculation_venues')
      .delete()
      .eq('calculation_id', calculationId);

    if (error) {
      this.logger.error(
        `Failed to remove venues from calculation ${calculationId}: ${error.message}`,
        error.stack,
      );
      // We don't throw here to avoid failing other operations
    }
  }

  /**
   * Check if a calculation has any venues associated
   */
  async hasVenues(calculationId: string): Promise<boolean> {
    const supabase = this.supabaseService.getClient();

    const { count, error } = await supabase
      .from('calculation_venues')
      .select('*', { count: 'exact', head: true })
      .eq('calculation_id', calculationId);

    if (error) {
      this.logger.error(
        `Error checking venues for calculation ${calculationId}: ${error.message}`,
      );
      return false;
    }

    return (count ?? 0) > 0;
  }

  /**
   * Get venue count for a calculation
   */
  async getVenueCount(calculationId: string): Promise<number> {
    const supabase = this.supabaseService.getClient();

    const { count, error } = await supabase
      .from('calculation_venues')
      .select('*', { count: 'exact', head: true })
      .eq('calculation_id', calculationId);

    if (error) {
      this.logger.error(
        `Error counting venues for calculation ${calculationId}: ${error.message}`,
      );
      return 0;
    }

    return count ?? 0;
  }
}
