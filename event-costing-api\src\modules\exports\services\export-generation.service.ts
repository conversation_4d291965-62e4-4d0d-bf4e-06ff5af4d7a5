import { Injectable, Logger } from '@nestjs/common';
import { Workbook } from 'exceljs';
import * as PDFDocument from 'pdfkit';
import * as fs from 'fs';
import {
  CalculationDetailDto,
  CalculationLineItemDto,
  CalculationCustomItemDto,
} from '../../calculations/dto/calculation-detail.dto';
import {
  TransformedExportData,
  TransformedExportItem,
} from '../interfaces/transformed-export-data.interface';

@Injectable()
export class ExportGenerationService {
  private readonly logger = new Logger(ExportGenerationService.name);

  // Inject dependencies if needed (e.g., FormattingUtil)
  constructor() {}

  transformCalculationData(
    calculationData: CalculationDetailDto,
  ): TransformedExportData {
    // Implementation moved from ExportsProcessor._transformCalculationDataForExport
    const items: TransformedExportItem[] = [];

    calculationData.line_items.forEach((item: CalculationLineItemDto) => {
      // Add safety checks for undefined values
      const itemQuantity = item.item_quantity || 0;
      const durationDays = item.duration_days || 1;
      const lineItemQuantity = itemQuantity * durationDays;

      const totalPrice = item.calculated_line_total || 0;
      const unitBaseCost = item.unit_base_cost_snapshot || 0;
      const optionsCost = item.options_total_cost_snapshot || 0;
      const unitCost = unitBaseCost + optionsCost;
      const totalCost = item.calculated_line_cost || 0;
      const margin = totalPrice - totalCost;

      items.push({
        name:
          (item.item_name_snapshot || 'Unknown Item') +
          (item.option_summary_snapshot
            ? ` (${item.option_summary_snapshot})`
            : ''),
        quantity: lineItemQuantity,
        unitCost: unitCost,
        totalCost: totalCost,
        unitPrice: lineItemQuantity !== 0 ? totalPrice / lineItemQuantity : 0,
        totalPrice: totalPrice,
        margin: margin,
      });
    });

    calculationData.custom_items.forEach((item: CalculationCustomItemDto) => {
      // Add safety checks for undefined values
      const quantity = item.quantity || 0;
      const unitPrice = item.unit_price || 0;
      const unitCost = item.unit_cost || 0;

      const totalPrice = quantity * unitPrice;
      const totalCost = quantity * unitCost;
      const margin = totalPrice - totalCost;

      items.push({
        name:
          (item.item_name || 'Unknown Custom Item') +
          (item.description ? ` (${item.description})` : ''),
        quantity: quantity,
        unitCost: unitCost,
        totalCost: totalCost,
        unitPrice: unitPrice,
        totalPrice: totalPrice,
        margin: margin,
      });
    });

    return {
      name: calculationData.name,
      clientName: calculationData.client?.client_name,
      totalCost: calculationData.total_cost,
      totalPrice: calculationData.total,
      totalMargin: calculationData.estimated_profit,
      items: items,
    };
  }

  async generateXlsxBuffer(data: TransformedExportData): Promise<Buffer> {
    // Create a new workbook
    const workbook = new Workbook();

    // Add Summary worksheet
    const summaryWorksheet = workbook.addWorksheet('Summary');

    // Add data to Summary worksheet
    summaryWorksheet.addRow(['Calculation Name:', data.name]);
    summaryWorksheet.addRow(['Client:', data.clientName || 'N/A']);
    summaryWorksheet.addRow([]);
    summaryWorksheet.addRow(['Total Cost:', data.totalCost]);
    summaryWorksheet.addRow(['Total Price:', data.totalPrice]);
    summaryWorksheet.addRow(['Total Margin:', data.totalMargin]);

    // Format currency cells in Summary worksheet
    const currencyFormat = '"Rp"#,##0.00';
    summaryWorksheet.getCell('B4').numFmt = currencyFormat;
    summaryWorksheet.getCell('B5').numFmt = currencyFormat;
    summaryWorksheet.getCell('B6').numFmt = currencyFormat;

    // Add Items worksheet
    const itemsWorksheet = workbook.addWorksheet('Items');

    // Add header row
    itemsWorksheet.addRow([
      'Item Name',
      'Quantity',
      'Unit Cost',
      'Total Cost',
      'Unit Price',
      'Total Price',
      'Margin',
    ]);

    // Add data rows
    data.items.forEach(item => {
      itemsWorksheet.addRow([
        item.name,
        item.quantity,
        item.unitCost,
        item.totalCost,
        item.unitPrice,
        item.totalPrice,
        item.margin,
      ]);
    });

    // Set column widths
    itemsWorksheet.columns = [
      { width: 40 }, // Item Name
      { width: 10 }, // Quantity
      { width: 15 }, // Unit Cost
      { width: 15 }, // Total Cost
      { width: 15 }, // Unit Price
      { width: 15 }, // Total Price
      { width: 15 }, // Margin
    ];

    // Format number and currency cells
    for (let rowIndex = 2; rowIndex <= data.items.length + 1; rowIndex++) {
      // Format quantity column as number
      itemsWorksheet.getCell(`B${rowIndex}`).numFmt = '#,##0';

      // Format currency columns
      ['C', 'D', 'E', 'F', 'G'].forEach(col => {
        itemsWorksheet.getCell(`${col}${rowIndex}`).numFmt = currencyFormat;
      });
    }

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async generateCsvBuffer(data: TransformedExportData): Promise<Buffer> {
    // Create CSV content
    const csvLines: string[] = [];

    // Add header
    csvLines.push(
      'Item Name,Quantity,Unit Cost,Total Cost,Unit Price,Total Price,Margin',
    );

    // Add data rows
    data.items.forEach(item => {
      const row = [
        `"${item.name.replace(/"/g, '""')}"`, // Escape quotes in item name
        item.quantity.toString(),
        item.unitCost.toFixed(2),
        item.totalCost.toFixed(2),
        item.unitPrice.toFixed(2),
        item.totalPrice.toFixed(2),
        item.margin.toFixed(2),
      ];
      csvLines.push(row.join(','));
    });

    // Add summary section
    csvLines.push(''); // Empty line
    csvLines.push('Summary');
    csvLines.push(`Calculation Name,"${data.name.replace(/"/g, '""')}"`);
    csvLines.push(`Client,"${(data.clientName || 'N/A').replace(/"/g, '""')}"`);
    csvLines.push(`Total Cost,${data.totalCost.toFixed(2)}`);
    csvLines.push(`Total Price,${data.totalPrice.toFixed(2)}`);
    csvLines.push(`Total Margin,${data.totalMargin.toFixed(2)}`);

    // Convert to buffer
    const csvContent = csvLines.join('\n');
    return Buffer.from(csvContent, 'utf8');
  }

  async generatePdfToFile(
    data: TransformedExportData,
    filePath: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const doc = new PDFDocument({
        margin: 50,
        layout: 'portrait',
        autoFirstPage: false,
        bufferPages: true,
      });
      const writeStream = fs.createWriteStream(filePath);

      doc.pipe(writeStream);

      const drawPageHeader = (pageDoc: typeof PDFDocument) => {
        pageDoc.fontSize(18).text(`Calculation: ${data.name}`, {
          align: 'center',
        });
        pageDoc.moveDown(0.5);
        pageDoc
          .fontSize(10)
          .text(
            `Client: ${data.clientName || 'N/A'}`,
            pageDoc.page.margins.left,
            pageDoc.y,
            { align: 'left' },
          );
        pageDoc.moveDown(2);
        pageDoc
          .moveTo(pageDoc.page.margins.left, pageDoc.y)
          .lineTo(pageDoc.page.width - pageDoc.page.margins.right, pageDoc.y)
          .stroke()
          .moveDown();
      };

      const drawPageFooter = (pageDoc: typeof PDFDocument) => {
        const range = pageDoc.bufferedPageRange();
        for (let i = range.start; i < range.count; i++) {
          pageDoc.switchToPage(i);
          pageDoc
            .fontSize(8)
            .text(
              `Page ${i + 1} of ${range.count}`,
              pageDoc.page.margins.left,
              pageDoc.page.height - pageDoc.page.margins.bottom + 10,
              {
                align: 'center',
                width:
                  pageDoc.page.width -
                  pageDoc.page.margins.left -
                  pageDoc.page.margins.right,
              },
            );
        }
      };

      const drawTableHeader = (currentDoc: typeof PDFDocument) => {
        const tableTop = currentDoc.y;
        const itemCol = 50;
        const qtyCol = 280;
        const unitCostCol = 320;
        const totalCostCol = 375;
        const unitPriceCol = 430;
        const totalPriceCol = 485;
        const tableEnd = totalPriceCol + 60;

        currentDoc.fontSize(9).font('Helvetica-Bold');
        currentDoc
          .text('Item Name', itemCol, tableTop)
          .text('Qty', qtyCol, tableTop, {
            width: unitCostCol - qtyCol - 5,
            align: 'right',
          })
          .text('Unit Cost', unitCostCol, tableTop, {
            width: totalCostCol - unitCostCol - 5,
            align: 'right',
          })
          .text('Total Cost', totalCostCol, tableTop, {
            width: unitPriceCol - totalCostCol - 5,
            align: 'right',
          })
          .text('Unit Price', unitPriceCol, tableTop, {
            width: totalPriceCol - unitPriceCol - 5,
            align: 'right',
          })
          .text('Total Price', totalPriceCol, tableTop, {
            width: tableEnd - totalPriceCol - 5,
            align: 'right',
          })
          .moveDown(0.5);
        currentDoc.font('Helvetica');
        currentDoc
          .moveTo(itemCol, currentDoc.y)
          .lineTo(tableEnd, currentDoc.y)
          .stroke()
          .moveDown(0.5);
        return {
          itemCol,
          qtyCol,
          unitCostCol,
          totalCostCol,
          unitPriceCol,
          totalPriceCol,
          tableEnd,
        };
      };

      doc.addPage();
      drawPageHeader(doc);

      doc.fontSize(12).text('Summary', { underline: true }).moveDown(0.5);
      doc
        .fontSize(10)
        .text(`Total Cost: ${data.totalCost.toFixed(2)}`)
        .text(`Total Price: ${data.totalPrice.toFixed(2)}`)
        .text(`Total Margin: ${data.totalMargin.toFixed(2)}`)
        .moveDown(2);

      doc.fontSize(12).text('Line Items', { underline: true }).moveDown(0.5);
      let tableCols = drawTableHeader(doc);

      data.items.forEach(item => {
        const currentY = doc.y;
        const nameWidth = tableCols.qtyCol - tableCols.itemCol - 5;
        const estimatedHeight =
          doc.heightOfString(item.name, { width: nameWidth }) + 5;

        if (
          currentY + estimatedHeight >
          doc.page.height - doc.page.margins.bottom - 30
        ) {
          doc.addPage();
          drawPageHeader(doc);
          tableCols = drawTableHeader(doc);
        }

        const rowY = doc.y;
        doc.fontSize(8);
        doc.text(item.name, tableCols.itemCol, rowY, {
          width: nameWidth,
          align: 'left',
        });
        doc.text((item.quantity || 0).toString(), tableCols.qtyCol, rowY, {
          width: tableCols.unitCostCol - tableCols.qtyCol - 5,
          align: 'right',
        });
        doc.text((item.unitCost || 0).toFixed(2), tableCols.unitCostCol, rowY, {
          width: tableCols.totalCostCol - tableCols.unitCostCol - 5,
          align: 'right',
        });
        doc.text(
          (item.totalCost || 0).toFixed(2),
          tableCols.totalCostCol,
          rowY,
          {
            width: tableCols.unitPriceCol - tableCols.totalCostCol - 5,
            align: 'right',
          },
        );
        doc.text(
          (item.unitPrice || 0).toFixed(2),
          tableCols.unitPriceCol,
          rowY,
          {
            width: tableCols.totalPriceCol - tableCols.unitPriceCol - 5,
            align: 'right',
          },
        );
        doc.text(
          (item.totalPrice || 0).toFixed(2),
          tableCols.totalPriceCol,
          rowY,
          {
            width: tableCols.tableEnd - tableCols.totalPriceCol - 5,
            align: 'right',
          },
        );

        const actualNameHeight = doc.heightOfString(item.name, {
          width: nameWidth,
        });
        doc.y = rowY + actualNameHeight + 5;
      });

      drawPageFooter(doc);
      doc.end();
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });
  }

  // Add formatCalculationToCsv method here if needed for specific formats
  // formatCalculationToCsv(calc: CalculationDetailDto): string { ... }
}
