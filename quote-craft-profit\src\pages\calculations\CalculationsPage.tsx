import React, { useState, useMemo } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PlusCircle } from "lucide-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";

// Import components
import { ExportButton } from "./components/list/ExportButton";
import { StatusBadge } from "@/components/ui/StatusBadge";

// Import services
import { getAllCalculations } from "../../services/calculations";

// Import utilities
import { formatCurrency, formatDate } from "./utils/formatUtils";

// Import types
import { ApiCalculation } from "./types/calculations";

const CalculationsPage: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Function to clear cache and refresh data
  const refreshCalculations = () => {
    console.log("🔄 Clearing calculations cache and refreshing...");
    queryClient.removeQueries({ queryKey: ["calculations"] });
    queryClient.invalidateQueries({ queryKey: ["calculations"] });
    toast.success("Calculations refreshed");
  };

  // Fetch calculations data
  const {
    data: calculations,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["calculations"],
    queryFn: getAllCalculations,
    staleTime: 30 * 1000, // Consider data fresh for 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    meta: {
      onError: () => {
        toast.error("Failed to load calculations");
      },
    },
  });

  // Memoized search filtering to prevent unnecessary recalculations
  const filteredCalculations = useMemo(() => {
    // Return early if no calculations data
    if (!calculations) return [];

    // Return all calculations if no search query
    if (!searchQuery || searchQuery.trim() === "") {
      return calculations;
    }

    // Optimize search by converting query to lowercase once
    const lowerQuery = searchQuery.toLowerCase().trim();

    // Filter calculations based on search query
    return calculations.filter((calc) => {
      // Search in calculation name
      const nameMatch = calc.name.toLowerCase().includes(lowerQuery);

      // Could extend to search in other fields like venue names, client names, etc.
      // const venueMatch = calc.venues?.some(venue =>
      //   venue.name.toLowerCase().includes(lowerQuery)
      // );

      return nameMatch;
    });
  }, [calculations, searchQuery]); // Only recalculate when calculations or searchQuery changes

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold tracking-tight">
            Event Calculations
          </h1>
          <Button
            onClick={() => navigate("/calculations/new")}
            className="flex items-center gap-2"
          >
            <PlusCircle className="h-4 w-4" />
            Create New Calculation
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Calculations</CardTitle>
            <CardDescription>
              View and manage all your event cost calculations
            </CardDescription>
          </CardHeader>

          <CardContent>
            <div className="mb-4">
              <Input
                placeholder="Search calculations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>

            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="p-4 border rounded-lg">
                    <Skeleton className="h-6 w-1/3 mb-2" />
                    <Skeleton className="h-4 w-1/4 mb-1" />
                    <Skeleton className="h-4 w-1/5" />
                  </div>
                ))}
              </div>
            ) : isError ? (
              <div className="p-4 border border-red-200 bg-red-50 rounded-lg text-red-700">
                Error loading calculations. Please try again.
              </div>
            ) : filteredCalculations.length === 0 ? (
              <div className="p-8 text-center border rounded-lg">
                <p className="text-gray-500 mb-4">No calculations found</p>
                <Button
                  onClick={() => navigate("/calculations/new")}
                  variant="outline"
                  className="flex items-center gap-2 mx-auto"
                >
                  <PlusCircle className="h-4 w-4" />
                  <span>Create your first calculation</span>
                </Button>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Venue(s)</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                      <TableHead className="text-right">Profit</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCalculations.map((calculation: ApiCalculation) => (
                      <TableRow key={calculation.id}>
                        <TableCell className="font-medium">
                          {calculation.name}
                        </TableCell>
                        <TableCell>
                          {formatDate(calculation.event_start_date)}
                        </TableCell>
                        <TableCell>
                          {calculation.venues &&
                          calculation.venues.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {calculation.venues.map((venue, index) => (
                                <span
                                  key={venue.id}
                                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200"
                                >
                                  {venue.name}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">
                              No venue
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          <StatusBadge
                            status={calculation.status as any}
                            size="sm"
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(calculation.total)}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(calculation.estimated_profit)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                navigate(`/calculations/${calculation.id}`)
                              }
                            >
                              View
                            </Button>
                            <ExportButton
                              calculationId={calculation.id}
                              calculationName={calculation.name}
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>

          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              {!isLoading && !isError && (
                <>Showing {filteredCalculations.length} calculation(s)</>
              )}
            </div>
          </CardFooter>
        </Card>
      </div>
    </MainLayout>
  );
};

export default CalculationsPage;
