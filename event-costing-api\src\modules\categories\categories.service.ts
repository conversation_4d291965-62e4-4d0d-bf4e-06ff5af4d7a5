// src/modules/categories/categories.service.ts
import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { CategoryDto } from './dto/category.dto';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryOrderItemDto } from './dto/update-category-order.dto';
import { CategoryOrderResponseDto } from './dto/category-order-response.dto';

@Injectable()
export class CategoriesService {
  private readonly logger = new Logger(CategoriesService.name);
  private readonly tableName = 'categories';
  private readonly selectFields =
    'id, code, name, description, icon, display_order, created_at, updated_at';
  private readonly uniqueConstraint = 'categories_code_key';

  constructor(private readonly supabaseService: SupabaseService) {}

  async findAll(): Promise<CategoryDto[]> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.tableName)
      .select(this.selectFields)
      .order('display_order', { ascending: true });

    if (error) {
      this.logger.error(
        `Failed to fetch categories: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not retrieve categories.');
    }

    return (data as CategoryDto[]) || [];
  }

  // --- Admin Methods ---

  async findOneById(id: string): Promise<CategoryDto> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.tableName)
      .select(this.selectFields)
      .eq('id', id)
      .single<CategoryDto>();

    if (error || !data) {
      throw new NotFoundException(`Category with ID ${id} not found.`);
    }
    return data;
  }

  async createCategory(createDto: CreateCategoryDto): Promise<CategoryDto> {
    this.logger.debug(
      `Creating category: ${createDto.code} - ${createDto.name}`,
    );
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .insert({
        code: createDto.code,
        name: createDto.name,
        description: createDto.description,
        icon: createDto.icon,
      })
      .select(this.selectFields)
      .single<CategoryDto>();

    if (error) {
      if (
        error.code === '23505' &&
        error.message.includes(this.uniqueConstraint)
      ) {
        this.logger.warn(
          `Attempted to create duplicate category code: ${createDto.code}`,
        );
        throw new ConflictException(
          `A category with the code "${createDto.code}" already exists.`,
        );
      }
      this.logger.error(
        `Failed to create category: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not create category.');
    }

    if (!data) {
      this.logger.error('Category insert succeeded but returned no data.');
      throw new InternalServerErrorException(
        'Failed to retrieve newly created category.',
      );
    }

    this.logger.log(
      `Successfully created category ID: ${data.id}, Code: ${data.code}`,
    );
    return data;
  }

  async updateCategory(
    id: string,
    updateDto: UpdateCategoryDto,
  ): Promise<CategoryDto> {
    this.logger.debug(`Updating category ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const updateData: Partial<UpdateCategoryDto> = {};
    if (updateDto.name !== undefined) {
      updateData.name = updateDto.name;
    }
    if (updateDto.description !== undefined) {
      updateData.description = updateDto.description;
    }
    if (updateDto.icon !== undefined) {
      updateData.icon = updateDto.icon;
    }

    if (Object.keys(updateData).length === 0) {
      return this.findOneById(id);
    }

    const { data, error } = await supabase
      .from(this.tableName)
      .update(updateData)
      .eq('id', id)
      .select(this.selectFields)
      .single<CategoryDto>();

    if (error) {
      if (error.code === 'PGRST116') {
        this.logger.warn(`Category not found for update: ID ${id}`);
        throw new NotFoundException(`Category with ID ${id} not found.`);
      }
      this.logger.error(
        `Failed to update category ${id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not update category.');
    }

    if (!data) {
      throw new NotFoundException(`Category with ID ${id} not found.`);
    }

    this.logger.log(`Successfully updated category ID: ${id}`);
    return data;
  }

  async deleteCategory(id: string): Promise<void> {
    this.logger.debug(`Deleting category ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { error, count } = await supabase
      .from(this.tableName)
      .delete()
      .eq('id', id);

    if (error) {
      this.logger.error(
        `Failed to delete category ${id}: ${error.message}`,
        error.stack,
      );
      if (error.code === '23503') {
        this.logger.warn(
          `Attempted to delete category ${id} which is still referenced.`,
        );
        throw new ConflictException(
          `Cannot delete category because it is referenced by other records (e.g., packages).`,
        );
      }
      throw new InternalServerErrorException('Could not delete category.');
    }

    if (count === 0) {
      this.logger.warn(`Category not found for deletion: ID ${id}`);
      throw new NotFoundException(`Category with ID ${id} not found.`);
    }

    this.logger.log(`Successfully deleted category ID: ${id}`);
  }

  /**
   * Update the display order of multiple categories
   * @param categories - Array of category order items with id and display_order
   * @returns Response with success status, message, and updated categories
   */
  async updateCategoryOrder(
    categories: CategoryOrderItemDto[],
  ): Promise<CategoryOrderResponseDto> {
    this.logger.log(
      `Updating display order for ${categories.length} categories`,
    );
    const supabase = this.supabaseService.getClient();

    try {
      // Start a transaction
      const { error: txError } = await supabase.rpc('begin_transaction');
      if (txError) {
        this.logger.error(
          `Failed to start transaction: ${txError.message}`,
          txError.stack,
        );
        throw txError;
      }

      // Update each category's display_order
      for (const category of categories) {
        const { error } = await supabase
          .from(this.tableName)
          .update({
            display_order: category.display_order,
            updated_at: new Date().toISOString(),
          })
          .eq('id', category.id);

        if (error) {
          // Rollback on error
          await supabase.rpc('rollback_transaction');
          this.logger.error(
            `Failed to update category ${category.id}: ${error.message}`,
            error.stack,
          );
          throw error;
        }
      }

      // Commit the transaction
      const { error: commitError } = await supabase.rpc('commit_transaction');
      if (commitError) {
        this.logger.error(
          `Failed to commit transaction: ${commitError.message}`,
          commitError.stack,
        );
        throw commitError;
      }

      // Fetch the updated categories
      const { data, error } = await supabase
        .from(this.tableName)
        .select(this.selectFields)
        .order('display_order', { ascending: true });

      if (error) {
        this.logger.error(
          `Failed to fetch updated categories: ${error.message}`,
          error.stack,
        );
        throw error;
      }

      this.logger.log('Category order updated successfully');

      return {
        success: true,
        message: 'Category order updated successfully',
        categories: data as CategoryDto[],
      };
    } catch (error) {
      this.logger.error(
        `Failed to update category order: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed to update category order');
    }
  }
}
