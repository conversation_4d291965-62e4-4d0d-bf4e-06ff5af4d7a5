import { Module } from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { TemplatesController } from './templates.controller';
import { AdminModule } from '../auth/admin.module'; // Provides AdminRoleGuard
import { AdminTemplatesController } from './admin-templates.controller'; // Will be created
import { CalculationsModule } from '../calculations/calculations.module'; // Provides CalculationItemsService
import { AuthModule } from '../auth/auth.module'; // Provides JwtAuthGuard
import { PackagesModule } from '../packages/packages.module'; // For consolidated service
import { CategoriesModule } from '../categories/categories.module'; // For consolidated service
import {
  TemplateQueryService,
  TemplateCreationService,
  TemplateDetailService,
  TemplateAdminService,
  TemplateVenueService,
  TemplateCalculationService,
} from './services';
import { TemplateConsolidatedService } from './services/template-consolidated.service';
import { TemplateManagementController } from './controllers/template-management.controller';

@Module({
  imports: [
    AuthModule,
    AdminModule,
    CalculationsModule,
    PackagesModule, // For consolidated service dependencies
    CategoriesModule, // For consolidated service dependencies
  ],
  controllers: [
    TemplatesController,
    AdminTemplatesController,
    TemplateManagementController, // New consolidated management controller
  ],
  providers: [
    TemplatesService,
    TemplateQueryService,
    TemplateCreationService,
    TemplateDetailService,
    TemplateAdminService,
    TemplateVenueService,
    TemplateCalculationService,
    TemplateConsolidatedService, // New consolidated service
  ],
  exports: [
    TemplatesService,
    TemplateConsolidatedService, // Export for use in other modules
  ],
})
export class TemplatesModule {}
