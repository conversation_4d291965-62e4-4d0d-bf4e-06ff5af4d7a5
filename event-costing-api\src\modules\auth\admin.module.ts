import { Module } from '@nestjs/common';
import { AdminRoleGuard } from './guards/admin-role.guard';
import { PackageCitiesModule } from '../admin/package-cities/package-cities.module'; // Added import
// import { CoreModule } from '../../core/core.module'; // Assume SupabaseService provided globally or otherwise accessible to guard
@Module({
  imports: [
    PackageCitiesModule, // Added Module
    /* CoreModule */
  ], // Removed CoreModule import
  providers: [AdminRoleGuard],
  exports: [AdminRoleGuard], // Export the guard so other modules can import AdminModule and use it
})
export class AdminModule {}
