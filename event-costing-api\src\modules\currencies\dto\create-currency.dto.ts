import { IsNotEmpty, IsString, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCurrencyDto {
  @ApiProperty({
    description: 'The ISO 4217 currency code (e.g., USD, EUR, IDR).',
    example: 'IDR',
    maxLength: 3,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(3)
  code: string;

  @ApiProperty({
    description: 'The description of the currency.',
    example: 'Indonesian Rupiah',
  })
  @IsNotEmpty()
  @IsString()
  description: string;
}
