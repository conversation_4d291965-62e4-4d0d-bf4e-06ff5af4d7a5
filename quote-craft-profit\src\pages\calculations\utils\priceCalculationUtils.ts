/**
 * Price calculation utilities for packages and line items
 */
import { QuantityBasisEnum } from "@/types/calculation";
import { debug } from "@/lib/debugUtils";

/**
 * Calculate total price based on quantity, item_quantity_basis, base price, and selected options
 * @param basePrice - Base price of the package
 * @param quantity - Quantity of the package
 * @param itemQuantityBasis - Number of days/units (replaces days)
 * @param quantityBasis - Basis for quantity calculation
 * @param options - Selected options with price adjustments
 * @returns Total price
 */
export const calculateTotalPrice = (
  basePrice: string | number,
  quantity: number = 1,
  itemQuantityBasis: number = 1,
  quantityBasis?: QuantityBasisEnum,
  options: Array<{ price_adjustment?: number }> = []
) => {
  const basePriceNum = Number(basePrice) || 0;

  // CORRECTED CALCULATION: First add options to base price, then multiply by quantities
  // Step 1: Calculate the effective unit price (base price + options)
  let effectiveUnitPrice = basePriceNum;

  // Add option price adjustments to the base price
  if (options && options.length > 0) {
    const totalOptionsAdjustment = options.reduce((sum, option) => {
      return sum + (option.price_adjustment || 0);
    }, 0);
    effectiveUnitPrice += totalOptionsAdjustment;

    debug(
      `[calculateTotalPrice] Base price: ${basePriceNum}, Options adjustment: ${totalOptionsAdjustment}, Effective unit price: ${effectiveUnitPrice}`
    );
  }

  // Step 2: Multiply effective unit price by quantity and item quantity basis
  // ALL quantity basis types use the same calculation: (basePrice + options) × quantity × itemQuantityBasis
  const totalPrice = effectiveUnitPrice * quantity * itemQuantityBasis;

  debug(
    `[calculateTotalPrice] ${effectiveUnitPrice} × ${quantity} × ${itemQuantityBasis} = ${totalPrice}`
  );

  return totalPrice;
};

/**
 * Generate a price formula string based on quantity basis
 * @param basePriceNum - Base price of the package as a number
 * @param quantity - Quantity of the package
 * @param itemQuantityBasis - Number of days/units (replaces days)
 * @param quantityBasis - Basis for quantity calculation
 * @param currencySymbol - Currency symbol to use in the formula
 * @returns Formula string (e.g., "Rp 500,000 × 5 × 3")
 */
export const generatePriceFormula = (
  basePriceNum: number,
  quantity: number,
  itemQuantityBasis: number,
  quantityBasis?: QuantityBasisEnum,
  currencySymbol: string = "Rp"
): string => {
  // Handle undefined quantityBasis
  const basis = quantityBasis || QuantityBasisEnum.PER_DAY;

  // ALL quantity basis types now show the same formula structure: price × quantity × units
  // This matches the calculation logic where all types multiply by itemQuantityBasis
  const baseFormula = `${currencySymbol} ${basePriceNum.toLocaleString()} × ${quantity}`;

  // Add the units/days part for all types to match the calculation
  if (itemQuantityBasis !== 1) {
    // Determine the unit label based on the quantity basis type
    let unitLabel = "units";
    switch (basis) {
      case QuantityBasisEnum.PER_DAY:
      case QuantityBasisEnum.PER_ITEM_PER_DAY:
      case QuantityBasisEnum.PER_ATTENDEE_PER_DAY:
        unitLabel = "days";
        break;
      case QuantityBasisEnum.PER_EVENT:
        unitLabel = "events";
        break;
      case QuantityBasisEnum.PER_ATTENDEE:
        unitLabel = "attendees";
        break;
      case QuantityBasisEnum.PER_ITEM:
        unitLabel = "items";
        break;
      default:
        unitLabel = "units";
    }

    return `${baseFormula} × ${itemQuantityBasis} ${unitLabel}`;
  }

  return baseFormula;
};

/**
 * Calculate total price and generate formula for a package
 * @param basePrice - Base price of the package
 * @param quantity - Quantity of the package
 * @param itemQuantityBasis - Number of days/units (replaces days)
 * @param quantityBasis - Basis for quantity calculation
 * @param options - Selected options with price adjustments
 * @param currencySymbol - Currency symbol to use in the formula
 * @returns Object containing total price and formula
 */
export const calculatePackagePrice = (
  basePrice: string | number,
  quantity: number = 1,
  itemQuantityBasis: number = 1,
  quantityBasis?: QuantityBasisEnum,
  options: Array<{
    price_adjustment?: number;
    id?: string;
    option_name?: string;
  }> = [],
  currencySymbol: string = "Rp"
): { totalPrice: number; formula: string; optionsFormula: string } => {
  const basePriceNum = Number(basePrice) || 0;

  // Calculate the total price using corrected logic
  const totalPrice = calculateTotalPrice(
    basePrice,
    quantity,
    itemQuantityBasis,
    quantityBasis,
    options
  );

  // Calculate effective unit price (base + options) for formula display
  let effectiveUnitPrice = basePriceNum;
  let optionsFormula = "";

  if (options && options.length > 0) {
    const totalOptionsAdjustment = options.reduce((sum, option) => {
      return sum + (option.price_adjustment || 0);
    }, 0);
    effectiveUnitPrice += totalOptionsAdjustment;

    // Generate options breakdown for display
    if (totalOptionsAdjustment !== 0) {
      const optionStrings = options
        .filter(
          (option) => option.price_adjustment && option.price_adjustment !== 0
        )
        .map((option) => {
          const adjustment = option.price_adjustment || 0;
          const sign = adjustment >= 0 ? " + " : " - ";
          return `${sign}${currencySymbol} ${Math.abs(
            adjustment
          ).toLocaleString()}`;
        });

      if (optionStrings.length > 0) {
        optionsFormula = ` (${currencySymbol} ${basePriceNum.toLocaleString()}${optionStrings.join(
          ""
        )})`;
      }
    }
  }

  // Generate the corrected formula: (basePrice + options) × quantity × itemQuantityBasis
  let formula: string;
  if (optionsFormula) {
    // Show the breakdown when options are present: (base + options) × quantity × itemQuantityBasis
    formula = `${optionsFormula} × ${quantity} × ${itemQuantityBasis}`;
  } else {
    // Use the standard formula when no options
    formula = generatePriceFormula(
      basePriceNum,
      quantity,
      itemQuantityBasis,
      quantityBasis,
      currencySymbol
    );
  }

  return {
    totalPrice,
    formula,
    optionsFormula,
  };
};
