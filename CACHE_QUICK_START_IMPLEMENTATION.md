# Cache Issues - Quick Start Implementation Guide

## 🚨 Immediate Actions (Today)

### 1. Stop the Bleeding - Critical Query Key Fix

**Problem**: Your app has conflicting query key definitions causing cache misses.

**Files to check immediately**:
- `src/lib/queryKeys.ts` (Global definitions)
- `src/pages/calculations/utils/queryKeys.ts` (Conflicting definitions)
- `src/pages/admin/packages/constants/index.ts` (Different patterns)

**Quick Fix** (30 minutes):

1. **Backup current files**:
```bash
cp src/lib/queryKeys.ts src/lib/queryKeys.ts.backup
cp src/pages/calculations/utils/queryKeys.ts src/pages/calculations/utils/queryKeys.ts.backup
```

2. **Update all imports to use global QUERY_KEYS**:
   - Search for `CALCULATION_QUERY_KEYS` imports
   - Replace with `QUERY_KEYS` from `@/lib/queryKeys`
   - Remove the local query keys file

3. **Test immediately**: Check calculation page for cache consistency

### 2. Fix Race Conditions in Cache Invalidation

**Problem**: Manual delays and uncoordinated invalidations.

**Files to update**:
- `src/hooks/useEntityCreationCallbacks.ts` (Remove 500ms delays)
- `src/pages/calculations/hooks/data/useLineItemMutations.ts` (Coordinate invalidations)

**Quick Fix** (45 minutes):

Replace manual delays with proper coordination:

```typescript
// BEFORE (❌ Bad)
await new Promise(resolve => setTimeout(resolve, 500));
await queryClient.refetchQueries({ queryKey });

// AFTER (✅ Good)
await queryClient.invalidateQueries({ queryKey });
// React Query handles timing automatically
```

### 3. Reduce Calculation Cache Stale Time

**Problem**: 5-minute stale time too long for active editing.

**Quick Fix** (15 minutes):

In calculation-related queries, change:
```typescript
// BEFORE
staleTime: 5 * 60 * 1000, // 5 minutes

// AFTER  
staleTime: 30 * 1000, // 30 seconds for active editing
```

## 🔧 Priority Fixes (This Week)

### Day 1: Query Key Consolidation

**Goal**: Single source of truth for all query keys

**Tasks**:
1. Create unified query key structure
2. Update all components to use unified keys
3. Remove duplicate query key definitions
4. Test cache invalidation works correctly

**Files to modify**:
- `src/lib/queryKeys.ts` (expand with unified structure)
- All components using `CALCULATION_QUERY_KEYS`
- All components using local query key definitions

### Day 2: Cache Invalidation Coordinator

**Goal**: Eliminate race conditions and coordinate related invalidations

**Tasks**:
1. Create `CacheCoordinator` class
2. Replace manual invalidation logic
3. Implement relationship-aware invalidation
4. Test with calculation mutations

**New files**:
- `src/services/cache/cacheCoordinator.ts`
- `src/hooks/useCacheCoordinator.ts`

### Day 3: Performance Monitoring

**Goal**: Visibility into cache performance

**Tasks**:
1. Implement cache analytics hook
2. Add performance monitoring to key components
3. Identify cache hotspots
4. Document baseline metrics

**New files**:
- `src/hooks/useCacheAnalytics.ts`
- `src/lib/cacheMetrics.ts`

### Day 4: Backend Cache Optimization

**Goal**: Improve server-side caching

**Tasks**:
1. Review backend cache TTL settings
2. Add cache invalidation triggers
3. Implement cache warming for frequently accessed data
4. Monitor memory usage

**Files to modify**:
- `event-costing-api/src/core/cache/cache.service.ts`
- Package service cache implementations

### Day 5: Testing and Validation

**Goal**: Ensure improvements work correctly

**Tasks**:
1. Performance testing
2. Cache hit rate measurement
3. Memory usage analysis
4. User experience validation

## 🎯 Quick Wins (2 Hours)

### 1. Remove Render Tracking Overhead (30 min)

**Problem**: Extensive debugging infrastructure may be causing memory issues.

**Action**: Disable render tracking in production:

```typescript
// In src/lib/renderTracker.ts
const enabled = process.env.NODE_ENV === 'development' && false; // Disable temporarily
```

### 2. Optimize Financial Calculations Cache (45 min)

**Problem**: Financial calculations cached separately causing inconsistency.

**Action**: Coordinate financial calculation cache with line items:

```typescript
// When line items change, also invalidate financial calculations
await queryClient.invalidateQueries({ 
  queryKey: ['financialCalculations', calculationId] 
});
```

### 3. Fix Package Form State Cleanup (30 min)

**Problem**: Package form state not cleaned up after line item deletion.

**Action**: Add cleanup to line item deletion mutations:

```typescript
// In line item deletion success handler
onSuccess: () => {
  // Clear package form state
  setPackageForms({});
  // Invalidate caches
  queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lineItems(calculationId) });
}
```

### 4. Reduce Cache Memory Usage (15 min)

**Problem**: No cache size limits.

**Action**: Add cache size limits to React Query config:

```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: 10 * 60 * 1000, // 10 minutes (reduce from current)
      // Add cache size limit
      cacheTime: 5 * 60 * 1000, // 5 minutes for older queries
    },
  },
});
```

## 🚀 Expected Results

After implementing these fixes, you should see:

**Immediate (Today)**:
- Reduced cache-related UI flickering
- More consistent data display
- Fewer unnecessary API calls

**This Week**:
- 50% reduction in API calls
- 30% faster page loads
- Elimination of race conditions
- Better memory usage

**Metrics to Track**:
- Cache hit rate (target: >80%)
- API call frequency (target: 50% reduction)
- Page load times (target: 30% improvement)
- Memory usage (target: 40% reduction)

## 🆘 If Issues Persist

**Debugging Steps**:

1. **Check browser console** for cache invalidation loops
2. **Monitor network tab** for duplicate API calls
3. **Use React DevTools** to identify re-render causes
4. **Check memory usage** in browser dev tools

**Emergency Rollback**:
If cache changes cause issues, you can quickly rollback:

```bash
# Restore backup files
cp src/lib/queryKeys.ts.backup src/lib/queryKeys.ts
# Restart development server
npm run dev
```

**Get Help**:
- Check the comprehensive analysis document for detailed explanations
- Look for specific error patterns in the browser console
- Monitor cache invalidation frequency (should be <5 per user action)

## 📞 Support

If you encounter issues during implementation:

1. **Document the specific problem** (error messages, unexpected behavior)
2. **Note which fix you were implementing** when the issue occurred
3. **Check browser console** for cache-related errors
4. **Verify query key consistency** across components

This quick start guide focuses on the highest-impact fixes that can be implemented immediately to resolve your cache frustrations.
