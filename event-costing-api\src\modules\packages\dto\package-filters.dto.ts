import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsNumber, IsArray, IsEnum, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Sort order enum
 */
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Sort by options enum
 */
export enum PackageSortBy {
  NAME = 'name',
  PRICE = 'price',
  CATEGORY = 'category',
  DIVISION = 'division',
  CREATED_AT = 'created_at',
  UPDATED_AT = 'updated_at',
}

/**
 * DTO for package filtering and pagination
 */
export class PackageFiltersDto {
  @ApiPropertyOptional({
    description: 'Search term for package name',
    example: 'sound system',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by category ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Filter by division ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  divisionId?: string;

  @ApiPropertyOptional({
    description: 'Filter by city ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  cityId?: string;

  @ApiPropertyOptional({
    description: 'Filter by venue IDs',
    type: [String],
    example: ['venue-1', 'venue-2'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  venueIds?: string[];

  @ApiPropertyOptional({
    description: 'Filter by currency ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  currencyId?: string;

  @ApiPropertyOptional({
    description: 'Minimum price filter',
    example: 100000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minPrice?: number;

  @ApiPropertyOptional({
    description: 'Maximum price filter',
    example: 5000000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxPrice?: number;

  @ApiPropertyOptional({
    description: 'Filter by quantity basis',
    example: 'per_day',
  })
  @IsOptional()
  @IsString()
  quantityBasis?: string;

  @ApiPropertyOptional({
    description: 'Include deleted packages',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  showDeleted?: boolean;

  @ApiPropertyOptional({
    description: 'Include packages with options',
    example: true,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  hasOptions?: boolean;

  @ApiPropertyOptional({
    description: 'Include packages available in specific venues',
    example: true,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  venueExclusive?: boolean;

  @ApiPropertyOptional({
    description: 'Exclude package with specific ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  excludeId?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  pageSize?: number;

  @ApiPropertyOptional({
    description: 'Sort by field',
    enum: PackageSortBy,
    example: PackageSortBy.NAME,
    default: PackageSortBy.NAME,
  })
  @IsOptional()
  @IsEnum(PackageSortBy)
  sortBy?: PackageSortBy;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: SortOrder,
    example: SortOrder.ASC,
    default: SortOrder.ASC,
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder;

  @ApiPropertyOptional({
    description: 'Include package options in response',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  includeOptions?: boolean;

  @ApiPropertyOptional({
    description: 'Include package dependencies in response',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  includeDependencies?: boolean;

  @ApiPropertyOptional({
    description: 'Include package availability information',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  includeAvailability?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by tags',
    type: [String],
    example: ['premium', 'outdoor'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Filter by availability status',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  isAvailable?: boolean;
}
