import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  createClient,
  updateClient,
  ClientFormData,
} from "@/services/shared/entities/clients";
import { Client } from "@/types/types";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { showSuccess, showError } from "@/lib/notifications";

// Import schema from centralized location
import { clientFormSchema, type ClientFormValues } from "../schemas";

interface ClientFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  client?: Client;
  mode: "create" | "edit";
  onClientCreated?: (client: Client) => void; // Callback for when client is created
}

const ClientFormDialog: React.FC<ClientFormDialogProps> = ({
  open,
  onOpenChange,
  client,
  mode,
  onClientCreated,
}) => {
  const queryClient = useQueryClient();

  // Initialize form with default values or existing client data
  const form = useForm<ClientFormValues>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      client_name: client?.name || "",
      contact_person: client?.name || "",
      email: client?.email || "",
      phone: client?.phone || "",
      address: client?.address || "",
      company_name: client?.company || "",
    },
  });

  // Create client mutation
  const createMutation = useMutation({
    mutationFn: (data: ClientFormData) => createClient(data),
    onSuccess: (newClient) => {
      showSuccess("Client created successfully", {
        category: "client",
        description: `${newClient.client_name} has been added to your client list.`,
      });
      queryClient.invalidateQueries({ queryKey: ["clients"] });

      // Call the callback if provided (for auto-selection in calculation flow)
      if (onClientCreated) {
        onClientCreated(newClient);
      }

      onOpenChange(false);
      form.reset();
    },
    onError: (error: Error) => {
      showError("Failed to create client", {
        category: "client",
        description: "Please check your information and try again.",
      });
      console.error("Error creating client:", error);
    },
  });

  // Update client mutation
  const updateMutation = useMutation({
    mutationFn: (data: ClientFormData) => updateClient(client?.id || "", data),
    onSuccess: () => {
      showSuccess("Client updated successfully", {
        category: "client",
        description: "Client information has been saved.",
      });
      // Invalidate both the clients list and the specific client
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      if (client?.id) {
        queryClient.invalidateQueries({ queryKey: ["client", client.id] });
      }
      onOpenChange(false);
    },
    onError: (error: Error) => {
      showError("Failed to update client", {
        category: "client",
        description: "Please check your information and try again.",
      });
      console.error("Error updating client:", error);
    },
  });

  // Form submission handler
  const onSubmit = (data: ClientFormValues) => {
    // Clean up empty string values to be undefined (except required fields)
    const formData: ClientFormData = {
      client_name: data.client_name,
      contact_person: data.contact_person || undefined,
      email: data.email || undefined,
      phone: data.phone, // Required field, don't convert to undefined
      address: data.address || undefined,
      company_name: data.company_name, // Required field, don't convert to undefined
    };

    if (mode === "create") {
      createMutation.mutate(formData);
    } else {
      updateMutation.mutate(formData);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[500px]"
        aria-describedby="client-form-description"
      >
        <DialogHeader>
          <DialogTitle>
            {mode === "create" ? "Add New Client" : "Edit Client"}
          </DialogTitle>
          <DialogDescription id="client-form-description">
            {mode === "create"
              ? "Fill in the details to create a new client."
              : "Update the client information."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="client_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client Name*</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter client name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="contact_person"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contact Person</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter contact person name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter email address"
                        type="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="company_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter company name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                {createMutation.isPending || updateMutation.isPending
                  ? "Saving..."
                  : mode === "create"
                  ? "Create Client"
                  : "Update Client"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ClientFormDialog;
