import { z } from 'zod';
import { commonValidations, formFieldPatterns } from '@/schemas/common';

/**
 * User management validation schemas
 * Centralized schemas for all user forms and components
 */

// User form schema (extracted from UserForm.tsx)
export const userSchema = z.object({
  email: commonValidations.email(),
  full_name: formFieldPatterns.name('Full name'),
  username: formFieldPatterns.name('Username'),
  role_id: z.string(),
  password: commonValidations.optionalString(),
});

// Type exports for TypeScript inference
export type UserFormValues = z.infer<typeof userSchema>;
