/**
 * Currency API Service
 *
 * This service provides methods for interacting with the currencies API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import { apiClient, getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';

// Interface for currency data
export interface Currency {
  id: string;
  code: string;
  description: string;
}

/**
 * Get all currencies from the backend API
 * @returns Promise resolving to an array of currencies
 */
export const getAllCurrenciesFromApi = async (): Promise<Currency[]> => {
  try {
    console.log('Fetching currencies from backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.CURRENCIES.GET_ALL);

    console.log('Currencies fetched successfully from backend API');

    // Check if the response data is in the expected format
    const currencies = Array.isArray(response.data)
      ? response.data
      : response.data.data || [];

    return currencies;
  } catch (error) {
    console.error('Error fetching currencies from backend API:', error);
    throw error;
  }
};

/**
 * Get a currency by ID from the backend API
 * @param id - The currency ID
 * @returns Promise resolving to a currency
 */
export const getCurrencyByIdFromApi = async (id: string): Promise<Currency> => {
  try {
    console.log(`Fetching currency with ID ${id} from backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.CURRENCIES.GET_BY_ID(id));

    console.log('Currency fetched successfully from backend API');

    // Get the currency data from the response
    const currencyData = response.data.data || response.data;

    return currencyData;
  } catch (error) {
    console.error(`Error fetching currency with ID ${id} from backend API:`, error);
    throw error;
  }
};
