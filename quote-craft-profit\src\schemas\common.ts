import { z } from 'zod';

/**
 * Common validation patterns and utilities for Zod schemas
 * These are shared across multiple features and provide consistent validation rules
 */

// Common string validations
export const commonValidations = {
  // Required string with minimum length
  requiredString: (minLength: number = 1, fieldName: string = 'Field') =>
    z.string().min(minLength, `${fieldName} must be at least ${minLength} character${minLength > 1 ? 's' : ''}`),

  // Optional string
  optionalString: () => z.string().optional(),

  // Email validation
  email: (required: boolean = true) => {
    const emailSchema = z.string().email('Invalid email address');
    return required ? emailSchema : emailSchema.optional().or(z.literal(''));
  },

  // UUID validation
  uuid: (fieldName: string = 'ID') =>
    z.string().uuid(`${fieldName} must be a valid UUID`),

  // Required UUID
  requiredUuid: (fieldName: string = 'ID') =>
    z.string().min(1, `${fieldName} is required`).uuid(`${fieldName} must be a valid UUID`),

  // Positive number validation
  positiveNumber: (fieldName: string = 'Value') =>
    z.number().min(0, `${fieldName} cannot be negative`),

  // Required positive number
  requiredPositiveNumber: (fieldName: string = 'Value') =>
    z.number().min(1, `${fieldName} must be at least 1`),

  // String that represents a number (for form inputs)
  numericString: (fieldName: string = 'Value', minValue: number = 0) =>
    z.string()
      .min(1, `${fieldName} is required`)
      .refine((val) => !isNaN(Number(val)) && Number(val) >= minValue, {
        message: `${fieldName} must be a valid number${minValue > 0 ? ` greater than ${minValue - 1}` : ''}`,
      }),

  // Array with minimum length
  requiredArray: <T>(schema: z.ZodType<T>, minLength: number = 1, fieldName: string = 'Items') =>
    z.array(schema).min(minLength, `At least ${minLength} ${fieldName.toLowerCase()} must be selected`),

  // Date validation
  requiredDate: (fieldName: string = 'Date') =>
    z.date({ required_error: `${fieldName} is required` }),

  // Optional date
  optionalDate: () => z.date().optional(),

  // Boolean with default
  booleanWithDefault: (defaultValue: boolean = false) =>
    z.boolean().default(defaultValue),
};

// Common date range validation
export const dateRangeSchema = z.object({
  from: commonValidations.requiredDate('Start date'),
  to: commonValidations.requiredDate('End date'),
}).refine((data) => data.from <= data.to, {
  message: 'Start date must be before or equal to end date',
  path: ['to'],
});

// Optional date range validation
export const optionalDateRangeSchema = z.object({
  from: commonValidations.optionalDate(),
  to: commonValidations.optionalDate(),
}).optional().refine((data) => {
  if (data?.from && data?.to) {
    return data.from <= data.to;
  }
  return true;
}, {
  message: 'Start date must be before or equal to end date',
  path: ['to'],
});

// Common form field patterns
export const formFieldPatterns = {
  // Name field (2+ characters)
  name: (fieldName: string = 'Name') =>
    commonValidations.requiredString(2, fieldName),

  // Code field (1+ characters, uppercase)
  code: (fieldName: string = 'Code') =>
    z.string()
      .min(1, `${fieldName} is required`)
      .transform((val) => val.toUpperCase()),

  // Description field (optional)
  description: () => commonValidations.optionalString(),

  // Notes field with character limit
  notes: (maxLength: number = 1000) =>
    z.string()
      .max(maxLength, `Notes must be less than ${maxLength} characters`)
      .optional(),

  // Phone number (optional, basic validation)
  phone: () => z.string().optional(),

  // Address (optional)
  address: () => z.string().optional(),

  // Quantity field
  quantity: () =>
    z.coerce.number().min(1, 'Quantity must be at least 1'),

  // Price field (as string for form inputs)
  price: (fieldName: string = 'Price', required: boolean = true) => {
    if (required) {
      return z.string()
        .min(1, `${fieldName} is required`)
        .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
          message: `${fieldName} must be a valid number greater than 0`,
        });
    } else {
      return z.string()
        .optional()
        .refine((val) => !val || (!isNaN(Number(val)) && Number(val) > 0), {
          message: `${fieldName} must be a valid number greater than 0`,
        });
    }
  },

  // Cost field (as string for form inputs, can be 0)
  cost: (fieldName: string = 'Cost', required: boolean = true) => {
    if (required) {
      return z.string()
        .min(1, `${fieldName} is required`)
        .refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
          message: `${fieldName} must be a valid number`,
        });
    } else {
      return z.string()
        .optional()
        .refine((val) => !val || (!isNaN(Number(val)) && Number(val) >= 0), {
          message: `${fieldName} must be a valid number`,
        });
    }
  },
};

// Type inference helpers
export type InferSchema<T extends z.ZodType> = z.infer<T>;
