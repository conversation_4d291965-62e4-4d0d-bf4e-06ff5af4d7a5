import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { Loader2, Settings } from "lucide-react";
import { useAuth } from "@/contexts/useAuth";
import { useNavigate } from "react-router-dom";
import { getUserProfile } from "@/services/shared/users";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";

// Import profile component sections
import {
  ProfilePictureSection,
  PersonalInfoSection,
  PasswordChangeSection,
} from "./components";

const ProfilePage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [formVersion, setFormVersion] = useState(0);
  // Define the profile data type with an optional _timestamp field for re-rendering
  interface ProfileData {
    full_name: string;
    username: string;
    role_name: string;
    phone_number: string;
    address: string;
    city: string;
    company_name: string;
    profile_picture_url: string | null;
    _timestamp?: number; // Optional timestamp for forcing re-renders
  }

  const [profileData, setProfileData] = useState<ProfileData | null>(null);

  // Fetch user profile data when component mounts
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      setIsLoading(true);
      try {
        const data = await getUserProfile();

        if (data) {
          const newProfileData: ProfileData = {
            full_name: data.fullName || "",
            username: data.username || "",
            role_name: data.role || "user",
            phone_number: data.phoneNumber || "",
            address: data.address || "",
            city: data.city || "",
            company_name: data.companyName || "",
            profile_picture_url: data.profilePictureUrl,
            _timestamp: Date.now(), // Add timestamp to ensure fresh rendering
          };

          console.log(
            "Setting profile data with timestamp:",
            newProfileData._timestamp
          );
          setProfileData(newProfileData);
        }
      } catch (error) {
        console.error("Error fetching profile:", error);
        toast.error("Failed to load profile data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [user]);

  // Handle profile picture update
  const handleProfilePictureUpdate = (url: string) => {
    if (profileData) {
      // Create a completely new object to ensure React detects the change
      // Add a timestamp to force a re-render
      setProfileData({
        ...profileData,
        profile_picture_url: url,
        _timestamp: Date.now(), // This is just for forcing a re-render
      });

      console.log("Profile picture updated with new URL:", url);

      // Force a re-fetch of the profile data to ensure we have the latest
      const refreshUserProfile = async () => {
        try {
          const data = await getUserProfile();

          if (data) {
            console.log("Refreshed profile data after picture update:", data);

            // Update the profile data with the refreshed data
            const refreshedProfileData: ProfileData = {
              full_name: data.fullName || "",
              username: data.username || "",
              role_name: data.role || "user",
              phone_number: data.phoneNumber || "",
              address: data.address || "",
              city: data.city || "",
              company_name: data.companyName || "",
              profile_picture_url: data.profilePictureUrl,
              _timestamp: Date.now(), // New timestamp for this update
            };

            // Update the state with the refreshed data
            setProfileData(refreshedProfileData);
          }
        } catch (error) {
          console.error(
            "Error refreshing profile after picture update:",
            error
          );
        }
      };

      // Refresh the profile data after a short delay
      setTimeout(refreshUserProfile, 500);
    }
  };

  // Handle profile data update with loading state
  const handleProfileUpdate = (updatedData: any) => {
    console.log("Profile data updated in parent component:", updatedData);

    // Log the current profile data for comparison
    console.log("Current profile data before update:", profileData);

    // Set loading state to true to show loading indicator
    setIsLoading(true);

    // Increment form version first to ensure a fresh component instance
    const newVersion = formVersion + 1;
    setFormVersion(newVersion);
    console.log(`Incremented form version to ${newVersion}`);

    // Update profile data with a completely new object to ensure React detects the change
    const newProfileData = {
      ...updatedData,
    };

    console.log("Setting new profile data:", newProfileData);
    setProfileData(newProfileData);

    // Use a timeout to ensure the state updates have time to propagate
    setTimeout(() => {
      // Reset loading state
      setIsLoading(false);
      console.log("Profile page loading state reset");

      // Log the final state for debugging
      console.log("Profile data updated in ProfilePage.tsx:", {
        formVersion: newVersion,
        profileData: newProfileData,
      });
    }, 300);
  };

  return (
    <MainLayout>
      <div className="container max-w-3xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Profile</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/profile/settings")}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Advanced Settings
          </Button>
        </div>

        {isLoading && !profileData ? (
          <div className="flex justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-eventcost-primary" />
          </div>
        ) : (
          <>
            {/* Profile Picture Section */}
            {profileData && (
              <ProfilePictureSection
                user={user}
                profilePictureUrl={profileData.profile_picture_url}
                fullName={profileData.full_name}
                onPictureUpdated={handleProfilePictureUpdate}
              />
            )}

            {/* Personal Information Section */}
            {profileData && (
              <PersonalInfoSection
                key={`profile-info-v${formVersion}`}
                user={user}
                profileData={profileData}
                onProfileUpdated={handleProfileUpdate}
              />
            )}

            {/* Password Change Section */}
            <PasswordChangeSection user={user} />
          </>
        )}
      </div>
    </MainLayout>
  );
};

export default ProfilePage;
