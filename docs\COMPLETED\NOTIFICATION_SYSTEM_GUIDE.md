# 🔔 Notification System Developer Guide

## Overview

This guide covers the standardized notification system implemented across the Quote Craft Profit application. All notifications now use a consistent API built on top of Son<PERSON> for optimal performance and user experience.

## Quick Start

### Basic Usage

```typescript
import { showSuccess, showError, showInfo, showWarning } from '@/lib/notifications';

// Simple notifications
showSuccess("Operation completed!");
showError("Something went wrong");
showInfo("Here's some information");
showWarning("Please be careful");
```

### Rich Notifications

```typescript
import { showSuccess, showError } from '@/lib/notifications';

// With descriptions
showSuccess("Package created", {
  description: "Your package is now available for use"
});

// With action buttons
showSuccess("Item deleted", {
  description: "The item has been removed",
  action: {
    label: "Undo",
    onClick: () => restoreItem()
  }
});
```

## API Reference

### Core Functions

#### `showSuccess(message, options?)`
Display a success notification with green styling.

```typescript
showSuccess("Success!", {
  description?: string;
  duration?: number;
  action?: { label: string; onClick: () => void };
  id?: string;
});
```

#### `showError(message, options?)`
Display an error notification with red styling.

```typescript
showError("Error occurred", {
  description?: string;
  duration?: number;
  action?: { label: string; onClick: () => void };
  id?: string;
});
```

#### `showInfo(message, options?)`
Display an informational notification with blue styling.

#### `showWarning(message, options?)`
Display a warning notification with yellow styling.

#### `showLoading(message, options?)`
Display a loading notification that persists until dismissed.

```typescript
const loadingId = showLoading("Processing...");
// Later...
dismissToast(loadingId);
```

### Advanced Functions

#### `promiseToast(promise, messages, options?)`
Automatically handle promise states with appropriate notifications.

```typescript
promiseToast(
  saveData(),
  {
    loading: "Saving...",
    success: "Data saved successfully!",
    error: "Failed to save data"
  }
);
```

#### `showUndoableSuccess(message, undoAction, options?)`
Show a success notification with an undo button.

```typescript
showUndoableSuccess(
  "Item deleted",
  () => restoreItem(),
  { description: "Click undo to restore" }
);
```

#### `showBatchSuccess(count, itemType, options?)`
Show a notification for batch operations.

```typescript
showBatchSuccess(5, "packages"); // "5 packages processed successfully"
```

### Utility Functions

#### `dismissToast(id)`
Dismiss a specific notification by ID.

#### `updateToast(id, options)`
Update an existing notification.

## Best Practices

### When to Use Each Type

- **Success**: Completed operations, successful saves, confirmations
- **Error**: Failed operations, validation errors, network issues
- **Info**: General information, status updates, tips
- **Warning**: Potential issues, confirmations needed, deprecation notices
- **Loading**: Long-running operations, async processes

### Message Guidelines

1. **Be Concise**: Keep messages short and clear
2. **Be Specific**: "Package created" vs "Operation completed"
3. **Use Action Words**: "Saved", "Deleted", "Updated"
4. **Provide Context**: Use descriptions for additional details

### Performance Considerations

1. **Avoid Spam**: Don't show notifications for every minor action
2. **Use Debouncing**: For high-frequency operations, use optimized notifications
3. **Batch Operations**: Use `showBatchSuccess` for multiple items
4. **Loading States**: Always provide feedback for async operations

## Migration from Legacy Systems

### From useToast Hook

```typescript
// ❌ OLD (deprecated)
const { toast } = useToast();
toast({
  title: "Success",
  description: "Operation completed",
  variant: "destructive"
});

// ✅ NEW (recommended)
import { showSuccess, showError } from '@/lib/notifications';
showSuccess("Success", { description: "Operation completed" });
showError("Error", { description: "Operation failed" });
```

### From Direct Sonner

```typescript
// ❌ OLD (inconsistent)
import { toast } from 'sonner';
toast.success("Message");

// ✅ NEW (standardized)
import { showSuccess } from '@/lib/notifications';
showSuccess("Message");
```

## Common Patterns

### Form Submissions

```typescript
const handleSubmit = async (data) => {
  const loadingId = showLoading("Saving...");
  
  try {
    await saveData(data);
    dismissToast(loadingId);
    showSuccess("Data saved successfully");
  } catch (error) {
    dismissToast(loadingId);
    showError("Failed to save data", {
      description: error.message
    });
  }
};
```

### Delete Operations

```typescript
const handleDelete = async (item) => {
  try {
    await deleteItem(item.id);
    showUndoableSuccess(
      "Item deleted",
      () => restoreItem(item),
      { description: `${item.name} has been removed` }
    );
  } catch (error) {
    showError("Failed to delete item");
  }
};
```

### Bulk Operations

```typescript
const handleBulkDelete = async (items) => {
  try {
    await Promise.all(items.map(item => deleteItem(item.id)));
    showBatchSuccess(items.length, "items");
  } catch (error) {
    showError("Some items could not be deleted");
  }
};
```

## Configuration

The notification system is pre-configured with optimal defaults:

- **Position**: Top-right corner
- **Duration**: 5 seconds (customizable per notification)
- **Close Button**: Enabled
- **Theme**: Follows system theme
- **Accessibility**: Full screen reader support

## Troubleshooting

### Common Issues

1. **Notifications not appearing**: Check that Sonner is imported in App.tsx
2. **Styling issues**: Verify Tailwind classes are available
3. **TypeScript errors**: Ensure proper import paths

### Debug Mode

Enable debug logging by setting:

```typescript
// In development
if (process.env.NODE_ENV === 'development') {
  console.log('Notification:', message, options);
}
```

## Related Files

- `src/lib/notifications.ts` - Main notification functions
- `src/lib/optimized-notifications.ts` - High-frequency operations
- `src/components/ui/sonner.tsx` - Sonner configuration
- `src/contexts/NotificationContext.tsx` - Persistent notifications

## Support

For questions or issues with the notification system:

1. Check this guide first
2. Review the implementation in `src/lib/notifications.ts`
3. See migration examples in `NOTIFICATION_STANDARDIZATION_PLAN.md`
4. Test with the examples in this guide
