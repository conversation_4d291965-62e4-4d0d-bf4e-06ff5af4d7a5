import { useState, useCallback, useMemo } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  AlertCircle,
  Download,
  FileX,
  FileText,
  Files,
  Loader2,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import { showSuccess, showError } from "@/lib/notifications";
import { useCalculationExports } from "@/hooks/use-calculation-exports";
import { debounce } from "lodash-es";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

type ExportFormat = "pdf" | "xlsx" | "csv";

interface ExportPopupProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  calculationId: string;
  calculationName: string;
}

export function ExportPopup({
  open,
  onOpenChange,
  calculationId,
  calculationName,
}: ExportPopupProps) {
  const { formatForDisplay } = useTimezoneAwareDates();
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>("pdf");

  // Use our enhanced hook with dialog state awareness
  const { exports, isLoading, generateExport, isGenerating, refetch, error } =
    useCalculationExports(calculationId, open);

  // Debounced export generation to prevent duplicate requests
  const handleGenerateExport = useCallback(() => {
    if (!isGenerating) {
      generateExport(selectedFormat);
    }
  }, [generateExport, selectedFormat, isGenerating]);

  // Create debounced version using useMemo to avoid recreating the debounced function
  const debouncedHandleGenerateExport = useMemo(
    () => debounce(handleGenerateExport, 1000),
    [handleGenerateExport]
  );

  const handleDownload = async (exportItem: {
    id: string;
    format: string;
    status: string;
    file_url?: string;
    download_url?: string;
  }) => {
    if (
      exportItem.status !== "completed" ||
      (!exportItem.file_url && !exportItem.download_url)
    ) {
      showError("Cannot download", {
        description: "This export is not ready for download.",
      });
      return;
    }

    try {
      // Use download_url if available, otherwise fall back to file_url
      const downloadUrl = exportItem.download_url || exportItem.file_url;

      if (downloadUrl) {
        // Create a temporary link element and trigger download
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = `${calculationName}_${exportItem.format || "export"}_${
          new Date().toISOString().split("T")[0]
        }.${exportItem.format || "file"}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showSuccess("Download started", {
          description: `Your ${(
            exportItem.format || "export"
          ).toUpperCase()} file is downloading.`,
          category: "export",
          replace: true,
        });
      } else {
        throw new Error("No download URL available");
      }
    } catch (error) {
      console.error("Download error:", error);
      showError("Download failed", {
        description:
          "There was an error downloading the file. Please try again.",
        category: "export",
        replace: true,
      });
    }
  };

  const getFormatIcon = (format: string | undefined) => {
    switch (format) {
      case "pdf":
        return <FileText className="h-4 w-4" />;
      case "csv":
        return <Files className="h-4 w-4" />;
      case "xlsx":
        return <Download className="h-4 w-4" />;
      default:
        return <FileX className="h-4 w-4" />;
    }
  };

  // Enhanced status badge with animations
  const getStatusBadge = (status: string) => {
    const baseClasses =
      "px-2 py-1 text-xs rounded-full font-medium transition-all duration-200";

    switch (status) {
      case "completed":
        return (
          <span
            className={`${baseClasses} bg-green-100 text-green-800 border border-green-200`}
          >
            <CheckCircle className="inline w-3 h-3 mr-1" />
            Completed
          </span>
        );
      case "failed":
        return (
          <span
            className={`${baseClasses} bg-red-100 text-red-800 border border-red-200`}
          >
            <XCircle className="inline w-3 h-3 mr-1" />
            Failed
          </span>
        );
      case "processing":
        return (
          <span
            className={`${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`}
          >
            <Loader2 className="inline w-3 h-3 mr-1 animate-spin" />
            Processing
          </span>
        );
      case "pending":
        return (
          <span
            className={`${baseClasses} bg-yellow-100 text-yellow-800 border border-yellow-200`}
          >
            <Clock className="inline w-3 h-3 mr-1" />
            Pending
          </span>
        );
      default:
        return (
          <span
            className={`${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`}
          >
            Unknown
          </span>
        );
    }
  };

  // Enhanced loading state for the entire dialog
  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="w-full max-w-4xl max-h-[85vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="text-xl">Export Calculation</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center p-8 space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Loading export history...</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Error state
  if (error && !exports?.length) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[550px] max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="text-xl">Export Calculation</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center p-8 space-y-4">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <p className="text-destructive">Failed to load export history</p>
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-[95vw] sm:max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl">Export Calculation</DialogTitle>
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none" />
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Initiation Block */}
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="font-medium mb-3">Generate New Export</h3>
            <div className="flex flex-col sm:flex-row gap-3">
              <Select
                value={selectedFormat}
                onValueChange={(value) =>
                  setSelectedFormat(value as ExportFormat)
                }
              >
                <SelectTrigger className="w-full sm:w-[180px] md:w-[200px]">
                  <SelectValue placeholder="Select format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF Document</SelectItem>
                  <SelectItem value="xlsx">Excel Spreadsheet</SelectItem>
                  <SelectItem value="csv">CSV File</SelectItem>
                </SelectContent>
              </Select>
              <Button
                className="w-full sm:flex-1 sm:min-w-[140px] md:w-auto md:flex-none"
                onClick={debouncedHandleGenerateExport}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    <span className="hidden sm:inline">Generating...</span>
                    <span className="sm:hidden">Generating</span>
                  </>
                ) : (
                  <>
                    <span className="hidden sm:inline">Generate Export</span>
                    <span className="sm:hidden">Generate</span>
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Export History Block */}
          <div>
            <h3 className="font-medium mb-3">Export History</h3>
            {isLoading ? (
              <div className="flex justify-center p-6">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900" />
              </div>
            ) : exports && exports.length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <ScrollArea className="h-[350px] w-full">
                  <div className="min-w-[600px]">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[100px] sm:w-[120px]">
                            Format
                          </TableHead>
                          <TableHead className="w-[140px] sm:w-[180px]">
                            Date
                          </TableHead>
                          <TableHead className="w-[100px] sm:w-[140px]">
                            Status
                          </TableHead>
                          <TableHead className="w-[120px] sm:w-[140px]">
                            Action
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {exports.map((exportItem) => (
                          <TableRow key={exportItem.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                {getFormatIcon(exportItem.format)}
                                <span className="truncate">
                                  {exportItem.format?.toUpperCase() ||
                                    "Unknown"}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-sm">
                              <div className="flex flex-col">
                                <span>
                                  {formatForDisplay(
                                    exportItem.created_at,
                                    "MMM d, yyyy"
                                  )}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {formatForDisplay(
                                    exportItem.created_at,
                                    "h:mm a"
                                  )}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(exportItem.status)}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDownload(exportItem)}
                                disabled={exportItem.status !== "completed"}
                                className="flex gap-1 items-center w-full min-w-[80px]"
                              >
                                {exportItem.status === "completed" ? (
                                  <>
                                    <Download className="h-4 w-4" />
                                    <span className="hidden sm:inline">
                                      Download
                                    </span>
                                    <span className="sm:hidden">DL</span>
                                  </>
                                ) : (
                                  <>
                                    <AlertCircle className="h-4 w-4" />
                                    <span className="hidden sm:inline">
                                      Unavailable
                                    </span>
                                    <span className="sm:hidden">N/A</span>
                                  </>
                                )}
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </ScrollArea>
              </div>
            ) : (
              <div className="text-center py-6 text-muted-foreground bg-muted/30 border rounded-md">
                No exports generated yet for this calculation.
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
