import React from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { MapPin, FileText, CheckCircle } from "lucide-react";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";

interface DashboardStats {
  totalCalculations: number;
  completedCalculations: number;
  mostUsedCity: {
    name: string;
    count: number;
  };
}

/**
 * Fetch dashboard statistics from the real API
 */
const fetchDashboardStats = async (): Promise<DashboardStats> => {
  try {
    const apiClient = await getAuthenticatedApiClient();

    // Fetch calculations with pagination to get total count
    const calculationsResponse = await apiClient.get(
      API_ENDPOINTS.CALCULATIONS.GET_ALL,
      {
        params: {
          page: 1,
          pageSize: 1, // We only need the count, not the data
        },
      }
    );

    // Fetch completed calculations
    const completedResponse = await apiClient.get(
      API_ENDPOINTS.CALCULATIONS.GET_ALL,
      {
        params: {
          page: 1,
          pageSize: 1,
          status: "completed",
        },
      }
    );

    // For now, simulate most used city until we have a dedicated endpoint
    // TODO: Create a dedicated dashboard stats endpoint in the backend
    const cities = ["Jakarta", "Bali", "Bandung", "Surabaya", "Yogyakarta"];
    const randomIndex = Math.floor(Math.random() * cities.length);

    return {
      totalCalculations: calculationsResponse.data.totalCount || 0,
      completedCalculations: completedResponse.data.totalCount || 0,
      mostUsedCity: {
        name: cities[randomIndex],
        count: Math.floor(Math.random() * 20) + 5,
      },
    };
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    // Return default values on error
    return {
      totalCalculations: 0,
      completedCalculations: 0,
      mostUsedCity: {
        name: "Jakarta",
        count: 0,
      },
    };
  }
};

const QuickStats: React.FC = () => {
  const {
    data: dashboardStats,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["dashboard-stats"],
    queryFn: fetchDashboardStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="flex justify-between">
              <div>
                <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
              <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
            </div>
            <div className="mt-2 flex justify-between items-center">
              <div className="h-4 bg-gray-200 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Use real data or default values if error
  const stats = [
    {
      name: "Total Quotes",
      value: dashboardStats?.totalCalculations || 0,
      change: "+12%", // TODO: Calculate real change percentage
      positive: true,
      icon: <FileText className="h-8 w-8 text-blue-100" />,
    },
    {
      name: "Completed",
      value: dashboardStats?.completedCalculations || 0,
      change: "+25%", // TODO: Calculate real change percentage
      positive: true,
      icon: <CheckCircle className="h-8 w-8 text-green-100" />,
    },
    {
      name: "Most Used City",
      value: dashboardStats?.mostUsedCity?.name || "No data",
      change: dashboardStats?.mostUsedCity
        ? `${dashboardStats.mostUsedCity.count} events`
        : "No events",
      positive: true,
      icon: <MapPin className="h-8 w-8 text-orange-100" />,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {stats.map((stat, i) => (
        <div
          key={i}
          className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 flex-1 hover-scale transition-all"
          style={{ animationDelay: `${i * 0.1}s` }}
        >
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {stat.name}
              </p>
              <p className="text-2xl font-semibold mt-1 dark:text-white">
                {stat.value}
              </p>
            </div>
            <div className="h-12 w-12 bg-blue-500 rounded-lg flex items-center justify-center">
              {stat.icon}
            </div>
          </div>
          <div className="mt-2 flex justify-between items-center">
            <div>
              <span
                className={`text-sm ${
                  stat.positive ? "text-green-600" : "text-red-600"
                }`}
              >
                {stat.change}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                vs last month
              </span>
            </div>
            <Link
              to={
                stat.name === "Total Quotes"
                  ? "/calculations"
                  : stat.name === "Completed"
                  ? "/calculations?status=completed"
                  : "/events"
              }
              className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
            >
              View details →
            </Link>
          </div>
        </div>
      ))}
    </div>
  );
};

export default QuickStats;
