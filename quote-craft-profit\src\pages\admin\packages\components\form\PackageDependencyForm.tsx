import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import {
  PackageDependencyFormValues,
  packageDependencySchema,
  DependencyType,
  getDependencyTypeLabel,
} from '../../types/packageDependencies';
import {
  savePackageDependency,
  checkDependencyExists,
  getAllPackages,
} from '@/services/admin/packages';
import { getAllCities } from '@/services/shared/entities/cities';
import { Package } from '../../types/package';
import { City } from '@/types/types';
import { Loader2, Search, X } from 'lucide-react';

interface PackageDependencyFormProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  packageId: string;
  dependencyId?: string | null;
  initialData?: Partial<PackageDependencyFormValues>;
}

export const PackageDependencyForm: React.FC<PackageDependencyFormProps> = ({
  isOpen,
  onClose,
  packageId,
  dependencyId,
  initialData,
}) => {
  const isEditMode = !!dependencyId;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCityId, setSelectedCityId] = useState<string>('all');
  const [filteredPackages, setFilteredPackages] = useState<Package[]>([]);

  const form = useForm<PackageDependencyFormValues>({
    resolver: zodResolver(packageDependencySchema),
    defaultValues: {
      dependent_package_id: initialData?.dependent_package_id || '',
      dependency_type: initialData?.dependency_type || 'REQUIRES',
      description: initialData?.description || '',
    },
  });

  // Fetch all packages for the dropdown
  const { data: packagesResult, isLoading: isLoadingPackages } = useQuery({
    queryKey: ['packages-for-dependencies', selectedCityId],
    queryFn: () =>
      getAllPackages({
        excludeId: packageId,
        cityId: selectedCityId !== 'all' ? selectedCityId : undefined,
      }),
    meta: {
      onError: () => {
        toast.error('Failed to load packages');
      },
    },
  });

  // Extract the packages array from the paginated result using useMemo
  const packages = React.useMemo(
    () => packagesResult?.data || [],
    [packagesResult?.data],
  );

  // Fetch cities for filtering
  const { data: cities = [], isLoading: isLoadingCities } = useQuery({
    queryKey: ['cities-for-dependencies'],
    queryFn: getAllCities,
    meta: {
      onError: () => {
        console.error('Failed to load cities');
      },
    },
  });

  // Filter packages based on search term and exclude current package
  useEffect(() => {
    if (packages) {
      const filtered = packages.filter(
        (pkg) =>
          // Exclude the current package to prevent self-dependency
          pkg.id !== packageId &&
          (pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (pkg.description &&
              pkg.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
            pkg.categoryName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            pkg.divisionName.toLowerCase().includes(searchTerm.toLowerCase())),
      );
      setFilteredPackages(filtered);
    }
  }, [packages, searchTerm, packageId]);

  // Clear search
  const handleClearSearch = () => {
    setSearchTerm('');
  };

  const handleSubmit = async (values: PackageDependencyFormValues) => {
    try {
      setIsSubmitting(true);

      // Prevent self-dependency
      if (values.dependent_package_id === packageId) {
        toast.error('A package cannot depend on itself');
        setIsSubmitting(false);
        return;
      }

      // Check if this dependency already exists (only for new dependencies)
      if (!isEditMode) {
        const exists = await checkDependencyExists(
          packageId,
          values.dependent_package_id,
        );
        if (exists) {
          toast.error('A dependency with this package already exists');
          setIsSubmitting(false);
          return;
        }
      }

      await savePackageDependency({
        id: dependencyId || undefined,
        package_id: packageId,
        dependent_package_id: values.dependent_package_id,
        dependency_type: values.dependency_type as DependencyType,
        description: values.description || null,
      });

      toast.success(
        `Package dependency ${isEditMode ? 'updated' : 'created'} successfully`,
      );
      onClose(true); // Refresh the dependencies list
    } catch (error) {
      toast.error(`Failed to ${isEditMode ? 'update' : 'create'} package dependency`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='sm:max-w-[550px]'>
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? 'Edit Dependency' : 'Add New Dependency'}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? 'Update the details of this package dependency.'
              : 'Define a relationship between this package and another package.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='dependent_package_id'
              render={({ field, fieldState }) => (
                <FormItem className={fieldState.error ? 'form-field-error' : ''}>
                  <FormLabel>Dependent Package</FormLabel>
                  <div className='space-y-2'>
                    {/* City filter */}
                    <div className='mb-2'>
                      <label className='text-sm text-muted-foreground mb-1 block'>
                        Filter by City
                      </label>
                      <Select
                        onValueChange={setSelectedCityId}
                        value={selectedCityId}
                        disabled={isLoadingCities || isSubmitting}
                      >
                        <SelectTrigger className='h-8'>
                          <SelectValue placeholder='All Cities' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='all'>All Cities</SelectItem>
                          {cities.map((city: City) => (
                            <SelectItem key={city.id} value={city.id}>
                              {city.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Package search and selection */}
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                      disabled={isLoadingPackages || isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select a package' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* Search input */}
                        <div className='px-2 py-2 sticky top-0 bg-background z-10 border-b'>
                          <div className='relative'>
                            <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
                            <Input
                              placeholder='Search packages...'
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className='pl-8 h-9'
                            />
                            {searchTerm && (
                              <button
                                onClick={handleClearSearch}
                                className='absolute right-2 top-2.5'
                                type='button'
                              >
                                <X className='h-4 w-4 text-muted-foreground' />
                              </button>
                            )}
                          </div>
                        </div>

                        <ScrollArea className='h-[200px]'>
                          {isLoadingPackages ? (
                            <div className='flex items-center justify-center p-4'>
                              <Loader2 className='h-4 w-4 animate-spin mr-2' />
                              Loading packages...
                            </div>
                          ) : filteredPackages.length === 0 ? (
                            <div className='p-4 text-center text-muted-foreground'>
                              {packages.length === 0
                                ? 'No packages available'
                                : 'No packages match your search'}
                            </div>
                          ) : (
                            filteredPackages.map((pkg: Package) => (
                              <SelectItem key={pkg.id} value={pkg.id} className='py-2'>
                                <div className='flex flex-col'>
                                  <span className='font-medium'>{pkg.name}</span>
                                  <div className='flex flex-wrap gap-1 mt-1'>
                                    <Badge
                                      variant='outline'
                                      className='text-xs bg-slate-100'
                                    >
                                      {pkg.categoryName}
                                    </Badge>
                                    {pkg.cityNames && pkg.cityNames.length > 0 && (
                                      <Badge
                                        variant='outline'
                                        className='text-xs bg-blue-50 text-blue-700'
                                      >
                                        {pkg.cityNames.length > 1
                                          ? `${pkg.cityNames[0]} +${
                                              pkg.cityNames.length - 1
                                            }`
                                          : pkg.cityNames[0]}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </SelectItem>
                            ))
                          )}
                        </ScrollArea>
                      </SelectContent>
                    </Select>
                  </div>
                  <FormDescription>
                    The package that this dependency relates to (current package excluded)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='dependency_type'
              render={({ field, fieldState }) => (
                <FormItem className={fieldState.error ? 'form-field-error' : ''}>
                  <FormLabel>Dependency Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select dependency type' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='REQUIRES'>
                        {getDependencyTypeLabel('REQUIRES')}
                      </SelectItem>
                      <SelectItem value='CONFLICTS'>
                        {getDependencyTypeLabel('CONFLICTS')}
                      </SelectItem>
                      <SelectItem value='RECOMMENDS'>
                        {getDependencyTypeLabel('RECOMMENDS')}
                      </SelectItem>
                      <SelectItem value='OPTIONAL'>
                        {getDependencyTypeLabel('OPTIONAL')}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Defines the relationship between the packages
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Explain the dependency relationship'
                      className='min-h-[80px]'
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className='mt-6'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onClose()}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
                {isEditMode ? 'Update Dependency' : 'Create Dependency'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default PackageDependencyForm;
