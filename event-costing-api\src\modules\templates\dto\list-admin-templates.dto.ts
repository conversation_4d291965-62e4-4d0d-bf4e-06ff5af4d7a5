import { IsOptional, IsString, IsBoolean, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';
import { PaginationQueryDto } from '../../../shared/dtos/pagination-query.dto'; // Adjust path if needed
import { ApiPropertyOptional } from '@nestjs/swagger';

// Helper function to transform string ('true'/'false') to boolean
const ToBoolean = (): ((target: any, key: string) => void) => {
  return Transform(({ value }: { value: string | boolean }) => {
    if (typeof value === 'string') {
      if (value.toLowerCase() === 'true') return true;
      if (value.toLowerCase() === 'false') return false;
    }
    return value; // Return boolean as-is or original value if not transformable string
  });
};

export class ListAdminTemplatesQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by template name (case-insensitive search)',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Filter by category ID',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Filter by city ID',
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiPropertyOptional({
    description: 'Filter by public status',
    type: Boolean,
  })
  @IsOptional()
  @IsBoolean()
  @ToBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by deleted status (defaults to false)',
    type: Boolean,
  })
  @IsOptional()
  @IsBoolean()
  @ToBoolean()
  isDeleted?: boolean = false;
}
