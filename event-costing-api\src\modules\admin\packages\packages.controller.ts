import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { PackagesService } from './packages.service.js';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { UpdatePackageStatusDto } from './dto/update-package-status.dto';
import { PackageDto } from './dto/package.dto';
import { PackageListQueryDto } from './dto/package-list-query.dto';
import { BatchUpdatePackagesDto } from './dto/batch-update-packages.dto';
// Assuming these DTOs are still relevant or will be adapted/moved
import { PaginatedResponseDto } from 'src/shared/dtos/paginated-response.dto';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from 'src/modules/auth/guards/admin-role.guard';

@ApiTags('Admin | Packages')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AdminRoleGuard)
@Controller('admin/packages')
export class PackagesController {
  constructor(private readonly packagesService: PackagesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new package' })
  @ApiResponse({
    status: 201,
    description: 'The package has been successfully created.',
    type: PackageDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  create(@Body() createPackageDto: CreatePackageDto): Promise<PackageDto> {
    // Assuming the service method will be named 'create'
    return this.packagesService.create(createPackageDto);
  }

  @Get()
  @ApiOperation({ summary: 'List all packages with filtering and pagination' })
  @ApiQuery({ type: PackageListQueryDto }) // Use the specific DTO for query params
  @ApiResponse({
    status: 200,
    description: 'List of packages.',
    type: () => PaginatedResponseDto<PackageDto>, // Use function for generic type in Swagger
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  findAll(
    @Query() queryDto: PackageListQueryDto,
  ): Promise<PaginatedResponseDto<PackageDto>> {
    // Assuming the service method will be named 'findAll'
    return this.packagesService.findAll(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a package by ID' })
  @ApiParam({ name: 'id', description: 'Package UUID', type: String })
  @ApiResponse({
    status: 200,
    description: 'The package details.',
    type: PackageDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Package not found.' })
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<PackageDto> {
    // Assuming the service method will be named 'findOne'
    return this.packagesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a package by ID' })
  @ApiParam({ name: 'id', description: 'Package UUID', type: String })
  @ApiResponse({
    status: 200,
    description: 'The package has been successfully updated.',
    type: PackageDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Package not found.' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePackageDto: UpdatePackageDto,
  ): Promise<PackageDto> {
    // Assuming the service method will be named 'update'
    return this.packagesService.update(id, updatePackageDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT) // Standard practice for DELETE
  @ApiOperation({ summary: 'Delete a package by ID' })
  @ApiParam({ name: 'id', description: 'Package UUID', type: String })
  @ApiResponse({
    status: 204,
    description: 'The package has been successfully deleted.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Package not found.' })
  remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    // Assuming the service method will be named 'remove'
    return this.packagesService.remove(id);
  }

  @Post('batch-update')
  @ApiOperation({ summary: 'Batch update multiple packages' })
  @ApiResponse({
    status: 200,
    description: 'The packages have been successfully updated.',
    schema: {
      type: 'object',
      properties: {
        updatedCount: {
          type: 'number',
          description: 'Number of packages updated',
        },
        errors: {
          type: 'array',
          description: 'Errors encountered during update',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', description: 'Package ID' },
              error: { type: 'string', description: 'Error message' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  batchUpdate(
    @Body() batchUpdateDto: BatchUpdatePackagesDto,
  ): Promise<{ updatedCount: number; errors: any[] }> {
    return this.packagesService.batchUpdate(batchUpdateDto);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update package status (active/inactive)' })
  @ApiParam({ name: 'id', description: 'Package UUID', type: String })
  @ApiResponse({
    status: 200,
    description: 'The package status has been successfully updated.',
    type: PackageDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Package not found.' })
  updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateStatusDto: UpdatePackageStatusDto,
  ): Promise<PackageDto> {
    return this.packagesService.updateStatus(id, updateStatusDto.isActive);
  }
}
