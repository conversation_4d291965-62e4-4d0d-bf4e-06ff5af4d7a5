# Quote Craft Profit - Project Structure

This document provides a comprehensive overview of the Quote Craft Profit project structure, covering both the frontend and backend components, their organization, and the relationships between them.

## Project Overview

Quote Craft Profit is a comprehensive event planning and cost calculation platform consisting of two main components:

1. **Frontend Application (quote-craft-profit)**

   - React-based web application with TypeScript
   - Located in the `quote-craft-profit` directory

2. **Backend API (event-costing-api)**
   - NestJS-based RESTful API
   - Located in the `event-costing-api` directory

## Frontend Structure

The frontend follows a feature-based organization structure for better code organization and maintainability. The project uses a comprehensive technology stack including React 18, TypeScript 5, Vite, Tailwind CSS, shadcn/ui, React Query, and additional libraries for data visualization, date utilities, icons, notifications, image editing, carousels, and theme management.

### Recent Updates

The admin packages module has been fully refactored to follow the feature-first architecture pattern. This includes:

1. Organizing all package-related code within the `src/pages/admin/packages/` directory
2. Structuring components by their purpose (list, detail, form, shared)
3. Implementing dedicated hooks, services, and types for the feature
4. Using barrel exports (index.ts files) to simplify imports
5. Setting default sorting to last modified date (newest first) for better user experience
6. Removing redundant components and files from the old structure

### Current Structure (Feature-Based Architecture)

```
quote-craft-profit/
├── public/              # Static assets
└── src/
    ├── components/      # Shared UI components
    │   ├── ui/          # Base UI components from shadcn
    │   ├── layout/      # Layout components (Navbar, MainLayout, etc.)
    │   └── notifications/ # Notification components
    ├── contexts/        # React Context providers
    ├── data/            # Mock data and constants
    ├── hooks/           # Shared custom React hooks
    ├── integrations/    # Third-party integrations
    │   ├── supabase/    # Supabase client and types
    │   └── api/         # External API client and configuration
    ├── lib/             # Utility functions and helpers
    ├── pages/           # Feature-based page organization
    │   ├── admin/       # Admin features
    │   ├── auth/        # Authentication features
    │   ├── calculations/# Calculation features
    │   ├── clients/     # Client management features
    │   ├── dashboard/   # Dashboard features
    │   ├── events/      # Event management features
    │   ├── profile/     # User profile features
    │   ├── reports/     # Reporting features
    │   └── templates/   # Template features
    ├── services/        # Global API service functions
    │   ├── admin/       # Admin-specific services
    │   ├── calculations/# Calculation services
    │   └── shared/      # Cross-feature services
    ├── styles/          # Global styles
    ├── types/           # Shared TypeScript type definitions
    ├── App.tsx          # Main application component
    ├── main.tsx         # Application entry point
    └── vite-env.d.ts    # Vite environment type definitions
```

### Feature-Based Structure (New Approach)

```
quote-craft-profit/
├── public/              # Static assets
└── src/
    ├── pages/           # Feature-based organization
    │   ├── calculations/# Calculation feature
    │   │   ├── components/  # Calculation-specific components
    │   │   │   ├── detail/  # Components for detail page
    │   │   │   ├── new/     # Components for new calculation page
    │   │   │   ├── list/    # Components for calculations list page
    │   │   │   └── shared/  # Shared calculation components
    │   │   ├── hooks/       # Calculation-specific hooks
    │   │   ├── utils/       # Calculation-specific utility functions
    │   │   ├── services/    # Calculation-specific services
    │   │   ├── types/       # Calculation-specific types
    │   │   ├── constants/   # Calculation-specific constants
    │   │   ├── CalculationDetailPage.tsx  # Main detail page
    │   │   ├── NewCalculationPage.tsx     # New calculation page
    │   │   └── CalculationsPage.tsx       # List page
    │   ├── admin/       # Admin features
    │   │   ├── packages/    # Package management feature
    │   │   │   ├── components/  # Package-specific components
    │   │   │   │   ├── list/    # Components for packages list page
    │   │   │   │   ├── detail/  # Components for package detail page
    │   │   │   │   ├── form/    # Form components for creating/editing packages
    │   │   │   │   └── shared/  # Shared package components
    │   │   │   ├── hooks/       # Package-specific hooks
    │   │   │   ├── services/    # Package-specific services
    │   │   │   ├── types/       # Package-specific types
    │   │   │   ├── utils/       # Package-specific utility functions
    │   │   │   ├── constants/   # Package-specific constants
    │   │   │   ├── index.ts     # Barrel exports for the feature
    │   │   │   ├── PackagesPage.tsx    # Package list page
    │   │   │   └── EditPackagePage.tsx # Package edit page
    │   │   └── ...          # Other admin features
    │   ├── clients/     # Client management feature (similar structure)
    │   ├── events/      # Event management feature (similar structure)
    │   └── ...          # Other features
    ├── components/      # Shared components used across features
    ├── hooks/           # Shared hooks used across features
    ├── lib/             # Shared utility functions
    ├── contexts/        # Application-wide contexts
    ├── integrations/    # Third-party integrations
    ├── types/           # Shared TypeScript type definitions
    ├── App.tsx          # Main application component
    ├── main.tsx         # Application entry point
    └── vite-env.d.ts    # Vite environment type definitions
```

### Key Frontend Files and Directories

#### Core Files

- `src/main.tsx`: The entry point of the application
- `src/App.tsx`: The main application component that sets up routing and global providers
- `src/vite-env.d.ts`: Type definitions for Vite environment variables

#### Components

- `src/components/ui/`: Base UI components from shadcn/ui
- `src/components/layout/`: Layout components like Navbar, Sidebar, and MainLayout
- `src/pages/calculations/components/`: Calculation-specific components
- `src/pages/admin/packages/components/`: Package-specific components organized by purpose (list, detail, form)

#### Hooks

- `src/hooks/`: Shared hooks used across features
- `src/pages/calculations/hooks/`: Calculation-specific hooks
- `src/pages/admin/packages/hooks/`: Package-specific hooks (e.g., usePackages, usePackageDetail)

#### Services

Our service layer follows a **feature-based architecture with shared services** pattern:

- **Shared Services** (`src/services/shared/`): Cross-feature functionality
  - `entities/` - Core business entities (cities, clients, currencies, events, venues)
  - `users/` - User profile and authentication services
  - Used across admin, calculations, and user-facing features

- **Feature Services** (`src/services/admin/`, `src/services/calculations/`):
  - `admin/` - Admin-only management operations with complex business logic
  - `calculations/` - Calculation-specific logic with direct Supabase integration
  - Feature-specific workflows and validation rules

#### Integrations

- `src/integrations/supabase/`: Supabase client and types
- `src/integrations/api/`: External API client and configuration

## Backend Structure

The backend follows a modular architecture based on NestJS best practices, organizing code by domain entities.

```
event-costing-api/
├── docs/               # Planning & reference docs
├── src/
│   ├── main.ts         # Application entry point
│   ├── app.module.ts   # Root module
│   ├── core/           # Core functionality
│   │   ├── filters/    # Global exception filters
│   │   ├── supabase/   # Supabase integration
│   │   └── database/   # Database configuration
│   ├── modules/        # Feature modules
│   │   ├── auth/       # Authentication module
│   │   ├── users/      # User management module
│   │   ├── calculations/   # Calculation module
│   │   ├── calculation-items/ # Calculation line-item logic
│   │   ├── templates/  # Templates module
│   │   ├── clients/    # Client management module
│   │   ├── packages/   # Package catalog module
│   │   ├── categories/ # Category management module
│   │   ├── cities/     # City location module
│   │   ├── currencies/ # Currency support module
│   │   └── events/     # Event types module
│   └── shared/         # Shared DTOs, interfaces, pipes
├── test/               # E2E tests
├── .env                # Environment variables (not committed)
├── package.json        # Dependencies & scripts
└── tsconfig.json       # TypeScript configuration
```

### Key Backend Files and Directories

#### Core Files

- `src/main.ts`: The entry point of the application
- `src/app.module.ts`: The root module that imports all other modules

#### Core Functionality

- `src/core/filters/`: Global exception filters for consistent error handling
- `src/core/supabase/`: Supabase integration with the SupabaseService
- `src/core/database/`: Database configuration and migrations

#### Feature Modules

Each feature module follows a similar structure:

```
modules/feature-name/
├── dto/                # Data Transfer Objects
├── entities/           # Entity definitions
├── interfaces/         # TypeScript interfaces
├── feature-name.controller.ts  # HTTP request handlers
├── feature-name.service.ts     # Business logic
├── feature-name.module.ts      # Module definition
└── feature-name.types.ts       # Type definitions
```

## Database Structure

The application uses Supabase (PostgreSQL) as its database. Here's a simplified overview of the main tables:

```
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│ calculations    │       │ calculation_    │       │ packages        │
│                 │       │ line_items      │       │                 │
│ id              │◄──────┤ calculation_id  │       │ id              │
│ name            │       │ package_id      ├───────┤ name            │
│ status          │       │ quantity        │       │ description     │
│ event_start_date│       │ item_quantity_  │       │ quantity_basis  │
│ event_end_date  │       │ basis           │       │ category_id     │
│ client_id       │       │ unit_price      │       │ is_deleted      │
│ ...             │       │ ...             │       │ ...             │
└─────────────────┘       └─────────────────┘       └─────────────────┘
        ▲                        │                          ▲
        │                        │                          │
        │                        ▼                          │
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│ clients         │       │ calculation_    │       │ package_options │
│                 │       │ line_item_      │       │                 │
│ id              │       │ options         │       │ id              │
│ name            │       │ line_item_id    │       │ applicable_     │
│ email           │       │ option_id       │       │ package_id      │
│ phone           │       │ price_adjustment│       │ option_name     │
│ ...             │       │ ...             │       │ price_adjustment│
└─────────────────┘       └─────────────────┘       └─────────────────┘
```

## Integration Points

### Frontend to Backend Communication

The frontend communicates with the backend through two main channels:

1. **Direct Supabase Integration**:

   - Authentication using Supabase Auth
   - Some database operations using the Supabase client

2. **External API Integration**:
   - RESTful API communication using a custom Axios-based client
   - The API client is configured to add authentication tokens, handle errors, and transform data

### Authentication Flow

1. User authentication is handled by Supabase Auth
2. JWT tokens are used for secure communication
3. The frontend stores tokens in localStorage and the Supabase client
4. The backend validates tokens for protected endpoints

## Build and Deployment

### Frontend Build Process

1. Vite is used as the build tool
2. TypeScript files are transpiled to JavaScript
3. CSS is processed with PostCSS and Tailwind
4. Assets are optimized and bundled
5. The output is a static site that can be deployed to any hosting service

### Backend Build Process

1. TypeScript files are transpiled to JavaScript using the TypeScript compiler
2. NestJS CLI is used to build the application
3. The output is a Node.js application that can be deployed to any hosting service

### Deployment

- Frontend is deployed to Vercel
- Backend is deployed to Railway
- Database is hosted on Supabase

## Development Workflow

1. **Backend First Approach**:

   - Implement backend API changes before frontend implementation
   - Test API endpoints before beginning frontend work

2. **Feature Implementation Flow**:

   - Start with database schema changes if needed
   - Implement backend API endpoints and services
   - Implement frontend components and services
   - Test integration

3. **Feature-Based Organization**:

   - Organize code by feature rather than by technical concerns
   - Place all feature-specific code in the feature's folder
   - Within feature folders, organize components by their purpose (list, detail, form)
   - Use barrel exports (index.ts files) to simplify imports

4. **Package Management Implementation**:
   - The admin packages module is fully implemented using the feature-first architecture
   - Components are organized by purpose (list, detail, form, shared)
   - Default sorting is by last modified date (newest first) for better user experience
   - API integration is handled through dedicated service files
   - State management is implemented with React Query and custom hooks

## Configuration Files

### Frontend Configuration

- `package.json`: Dependencies and scripts
- `tsconfig.json`: TypeScript configuration
- `vite.config.ts`: Vite build configuration
- `.env.example`: Example environment variables
- `.eslintrc.json`: ESLint configuration
- `.prettierrc`: Prettier configuration

### Backend Configuration

- `package.json`: Dependencies and scripts
- `tsconfig.json`: TypeScript configuration
- `nest-cli.json`: NestJS CLI configuration
- `.env.example`: Example environment variables
- `.eslintrc.js`: ESLint configuration
- `.prettierrc`: Prettier configuration

## Testing Structure

### Frontend Testing

```
quote-craft-profit/
└── src/
    ├── components/
    │   └── __tests__/  # Component tests
    ├── hooks/
    │   └── __tests__/  # Hook tests
    └── services/
        └── __tests__/  # Service tests
```

### Backend Testing

```
event-costing-api/
├── src/
│   └── modules/
│       └── feature-name/
│           └── __tests__/  # Unit tests
└── test/                   # E2E tests
```

## State Management

### Frontend State Management

The application uses a combination of state management approaches:

1. **React Query**: For server state (data fetching, caching, synchronization)
2. **React Context**: For global application state
3. **React useState/useReducer**: For component-local state

Key state management files:

- `src/contexts/AuthContext.tsx`: Authentication state management
- `src/hooks/useCalculationDetail.ts`: Calculation state management
- `src/hooks/useFinancialCalculations.ts`: Financial calculations state
- `src/pages/admin/packages/hooks/usePackages.ts`: Package list state management
- `src/pages/admin/packages/hooks/usePackageDetail.ts`: Package detail state management

### Backend State Management

The backend is stateless, with all state stored in the database. Session state is managed through JWT tokens.

## Data Flow

### Frontend Data Flow

1. **User Interaction**: User interacts with a component
2. **Component Handler**: Component calls a handler function
3. **Service Function**: Handler calls a service function
4. **API Request**: Service function makes an API request
5. **State Update**: Response updates local or global state
6. **Re-render**: Component re-renders with new data

Example data flow for adding a package to a calculation:

```
User clicks "Add to Calculation" button
↓
PackageCard.onAddToCalculation() is called
↓
useCalculationDetail.handleAddToCalculation() is called
↓
calculationService.addLineItem() is called
↓
API request to POST /calculations/{id}/line-items
↓
React Query invalidates and refetches line items
↓
CalculationLineItems component re-renders with new line item
```

Example data flow for updating a package in the admin interface:

```
User edits package details and clicks "Save Changes" button
↓
EditPackagePage.handleSubmit() is called
↓
packageService.savePackage() is called
↓
packageApiService.savePackageWithApi() is called
↓
API request to PUT /packages/{id}
↓
React Query invalidates the package cache
↓
Toast notification shows success message
↓
User can continue editing or navigate back to package list
```

### Backend Data Flow

1. **HTTP Request**: Client sends a request to an endpoint
2. **Controller**: NestJS controller receives the request
3. **Validation**: DTOs validate the request data
4. **Service**: Controller calls a service method
5. **Database**: Service interacts with the database via Supabase
6. **Response**: Controller returns a response to the client

Example data flow for creating a calculation:

```
POST /calculations
↓
CalculationsController.create() is called
↓
CreateCalculationDto validates the request data
↓
CalculationsService.create() is called
↓
SupabaseService interacts with the database
↓
Response with the new calculation is returned
```

## Code Style and Standards

The project follows a consistent code style enforced by ESLint and Prettier:

- TypeScript for type safety
- Functional components with hooks in React
- NestJS decorators and dependency injection in the backend
- Consistent naming conventions
- Comprehensive documentation

## Conclusion

This document provides a high-level overview of the Quote Craft Profit project structure. For more detailed information, please refer to the following resources:

- [Developer Guide](docs/developer-guide.md)
- [Frontend Documentation](quote-craft-profit/README.md)
- [Backend Documentation](event-costing-api/README.md)
- [API Documentation](event-costing-api/docs/API.md)
