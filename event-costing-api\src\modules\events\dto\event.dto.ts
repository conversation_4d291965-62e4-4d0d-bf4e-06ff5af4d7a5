import { ApiProperty } from '@nestjs/swagger';

export class EventDto {
  @ApiProperty({ type: String, format: 'uuid' })
  id: string;

  @ApiProperty()
  event_name: string;

  @ApiProperty({ nullable: true, format: 'date-time' })
  event_start_datetime: string | null;

  @ApiProperty({ nullable: true, format: 'date-time' })
  event_end_datetime: string | null;

  @ApiProperty({ nullable: true })
  location: string | null;

  @ApiProperty({ nullable: true })
  notes: string | null;

  // Add other relevant fields from the 'events' table if needed
  @ApiProperty({ type: String, format: 'date-time' })
  created_at: string;
}
