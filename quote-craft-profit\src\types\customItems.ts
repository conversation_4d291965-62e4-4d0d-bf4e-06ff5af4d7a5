/**
 * Custom item in a calculation
 */
export interface CustomItem {
  id: string;
  calculation_id: string;
  item_name: string;
  description: string | null;
  item_quantity: number;
  item_quantity_basis: number | null;
  quantity_basis: string | null;
  unit_price: number;
  unit_cost: number;
  calculated_total: number;
  category_id: string | null;
  currency_id: string;
  city_id: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Input data for creating or updating a custom item
 */
export interface CustomItemInput {
  itemName: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  unitCost?: number;
  itemQuantityBasis?: number;
  quantityBasis?: string;
  categoryId?: string;
  cityId?: string;
}

// Import the centralized quantity basis enum
import { QuantityBasisEnum } from './types';

/**
 * @deprecated Use QuantityBasisEnum from './types' instead
 * This alias is kept for backward compatibility
 */
export const CustomItemQuantityBasis = QuantityBasisEnum;

/**
 * Custom item form data
 */
export interface CustomItemFormData {
  name: string;
  description?: string;
  quantity: number;
  item_quantity_basis: number;
  quantity_basis: QuantityBasisEnum;
  unit_price: number;
  category_id: string;
}

/**
 * Custom item with calculated fields for display
 */
export interface CustomItemDisplay extends CustomItem {
  categoryName?: string;
  formattedTotal: string;
  formattedUnitPrice: string;
}
