import {
  Controller,
  Get,
  Logger,
  NotFoundException,
  Param,
  ParseUUIDPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { VenuesService } from './venues.service';
import {
  ApiTags,
  ApiOkResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { VenueReferenceDto } from './dto/venue-reference.dto';

@ApiTags('Venues')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('venues')
export class VenuesController {
  private readonly logger = new Logger(VenuesController.name);

  constructor(private readonly venuesService: VenuesService) {}

  @Get()
  @ApiQuery({
    name: 'cityId',
    required: false,
    type: String,
    description: 'Filter venues by city ID',
  })
  @ApiQuery({
    name: 'active',
    required: false,
    type: Boolean,
    description: 'Filter venues by active status',
  })
  @ApiQuery({
    name: 'classification',
    required: false,
    type: String,
    description:
      'Filter venues by classification (outdoor, hotel, indoor, premium, luxury)',
    enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
  })
  @ApiQuery({
    name: 'minCapacity',
    required: false,
    type: Number,
    description: 'Filter venues by minimum capacity',
  })
  @ApiQuery({
    name: 'maxCapacity',
    required: false,
    type: Number,
    description: 'Filter venues by maximum capacity',
  })
  @ApiOkResponse({ type: [VenueReferenceDto] })
  async getVenues(
    @Query('cityId') cityId?: string,
    @Query('active') active?: boolean,
    @Query('classification') classification?: string,
    @Query('minCapacity') minCapacity?: number,
    @Query('maxCapacity') maxCapacity?: number,
  ): Promise<VenueReferenceDto[]> {
    this.logger.log(
      `Fetching venues with filters: cityId=${cityId}, active=${active}, classification=${classification}, minCapacity=${minCapacity}, maxCapacity=${maxCapacity}`,
    );

    // Use enhanced filtering if any of the new parameters are provided
    if (
      classification ||
      minCapacity !== undefined ||
      maxCapacity !== undefined
    ) {
      return this.venuesService.findWithEnhancedFilters(
        cityId,
        active,
        classification,
        minCapacity,
        maxCapacity,
      );
    }

    // Use original method for backward compatibility
    return this.venuesService.findAllWithFilters(cityId, active);
  }

  @Get(':id')
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({ type: VenueReferenceDto })
  async getVenueById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<VenueReferenceDto> {
    this.logger.log(`Fetching venue with ID: ${id}`);
    const venues = await this.venuesService.findByIds([id]);
    if (venues.length === 0) {
      throw new NotFoundException(`Venue with ID ${id} not found`);
    }
    return venues[0];
  }
}
