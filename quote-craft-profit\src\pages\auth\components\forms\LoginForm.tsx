import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { useAuth } from '../../hooks/useAuth';

interface LoginFormProps {
  onSuccess?: () => void;
  onToggleMode?: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onToggleMode }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { signIn } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { error } = await signIn(email, password);
      if (error) throw error;

      toast.success('Logged in successfully!');
      onSuccess?.();
    } catch (error) {
      console.error('Login error:', error);
      toast.error(error.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='mt-6 text-center text-3xl font-extrabold text-gray-900'>
          Sign in to your account
        </h2>
        <p className='mt-2 text-center text-sm text-gray-600'>
          Don't have an account?{' '}
          <button
            onClick={onToggleMode}
            className='font-medium text-eventcost-primary hover:text-eventcost-primary/90'
          >
            Sign up
          </button>
        </p>
      </div>

      <form className='space-y-6' onSubmit={handleSubmit}>
        <div>
          <Label htmlFor='email'>Email address</Label>
          <div className='mt-1'>
            <Input
              id='email'
              name='email'
              type='email'
              autoComplete='email'
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className='block w-full'
            />
          </div>
        </div>

        <div>
          <Label htmlFor='password'>Password</Label>
          <div className='mt-1'>
            <Input
              id='password'
              name='password'
              type='password'
              autoComplete='current-password'
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className='block w-full'
            />
          </div>
        </div>

        <div>
          <Button type='submit' className='w-full' disabled={isLoading}>
            {isLoading ? 'Processing...' : 'Sign In'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default LoginForm;
