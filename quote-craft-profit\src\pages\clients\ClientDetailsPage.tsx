import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { getClientById } from '@/services/shared/entities/clients';
import { ClientFormDialog, DeleteClientDialog } from './components';
import { ArrowLeftIcon, PencilIcon, TrashIcon } from 'lucide-react';

const ClientDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State for dialogs
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Fetch client data
  const {
    data: client,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['client', id],
    queryFn: () => getClientById(id || ''),
    enabled: !!id,
  });

  // Handle back button click
  const handleBack = () => {
    navigate('/clients');
  };

  // Handle edit button click
  const handleEdit = () => {
    setEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  return (
    <MainLayout>
      <div className='mb-6 flex justify-between items-center'>
        <div className='flex items-center'>
          <Button variant='ghost' onClick={handleBack} className='mr-4'>
            <ArrowLeftIcon className='h-5 w-5 mr-2' />
            Back to Clients
          </Button>
          <h1 className='text-2xl font-bold text-gray-800'>
            {isLoading ? (
              <Skeleton className='h-8 w-48' />
            ) : (
              client?.name || 'Client Details'
            )}
          </h1>
        </div>

        {!isLoading && !isError && client && (
          <div className='flex space-x-2'>
            <Button variant='outline' onClick={handleEdit}>
              <PencilIcon className='h-4 w-4 mr-2' />
              Edit Client
            </Button>
            <Button
              variant='outline'
              onClick={handleDelete}
              className='text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700'
            >
              <TrashIcon className='h-4 w-4 mr-2' />
              Delete Client
            </Button>
          </div>
        )}
      </div>

      {isLoading ? (
        // Loading state
        <Card>
          <CardHeader>
            <Skeleton className='h-8 w-48 mb-2' />
            <Skeleton className='h-4 w-64' />
          </CardHeader>
          <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={`skeleton-${index}`}>
                <Skeleton className='h-4 w-24 mb-2' />
                <Skeleton className='h-8 w-full' />
              </div>
            ))}
          </CardContent>
        </Card>
      ) : isError ? (
        // Error state
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>
              There was an error loading the client details. Please try again.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/clients')}>Return to Clients</Button>
          </CardContent>
        </Card>
      ) : client ? (
        // Client details
        <Card>
          <CardHeader>
            <CardTitle>{client.name}</CardTitle>
            <CardDescription>Client details and contact information</CardDescription>
          </CardHeader>
          <CardContent className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
              <h3 className='text-sm font-medium text-gray-500 mb-1'>Contact Person</h3>
              <p className='text-lg'>{client.name || '-'}</p>
            </div>

            <div>
              <h3 className='text-sm font-medium text-gray-500 mb-1'>Company</h3>
              <p className='text-lg'>{client.company || '-'}</p>
            </div>

            <div>
              <h3 className='text-sm font-medium text-gray-500 mb-1'>Email</h3>
              <p className='text-lg'>
                {client.email ? (
                  <a
                    href={`mailto:${client.email}`}
                    className='text-blue-600 hover:underline'
                  >
                    {client.email}
                  </a>
                ) : (
                  '-'
                )}
              </p>
            </div>

            <div>
              <h3 className='text-sm font-medium text-gray-500 mb-1'>Phone</h3>
              <p className='text-lg'>
                {client.phone ? (
                  <a
                    href={`tel:${client.phone}`}
                    className='text-blue-600 hover:underline'
                  >
                    {client.phone}
                  </a>
                ) : (
                  '-'
                )}
              </p>
            </div>

            <div className='md:col-span-2'>
              <h3 className='text-sm font-medium text-gray-500 mb-1'>Address</h3>
              <p className='text-lg'>{client.address || '-'}</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        // Client not found
        <Card>
          <CardHeader>
            <CardTitle>Client Not Found</CardTitle>
            <CardDescription>
              The client you are looking for does not exist or has been deleted.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/clients')}>Return to Clients</Button>
          </CardContent>
        </Card>
      )}

      {/* Edit Client Dialog */}
      {client && (
        <ClientFormDialog
          open={editDialogOpen}
          onOpenChange={(open) => {
            setEditDialogOpen(open);
            if (!open) {
              // Refetch client data when dialog is closed
              refetch();
            }
          }}
          client={client}
          mode='edit'
        />
      )}

      {/* Delete Client Dialog */}
      {client && (
        <DeleteClientDialog
          open={deleteDialogOpen}
          onOpenChange={(open) => {
            setDeleteDialogOpen(open);
            // If dialog was closed and client was deleted, navigate back to clients list
            if (!open && !deleteDialogOpen) {
              navigate('/clients');
            }
          }}
          client={client}
        />
      )}
    </MainLayout>
  );
};

export default ClientDetailsPage;
