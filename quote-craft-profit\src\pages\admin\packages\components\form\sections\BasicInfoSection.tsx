import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { PackageFormValues } from '../../../types/package';
import { FormSection } from '../../shared';

interface BasicInfoSectionProps {
  form: UseFormReturn<PackageFormValues>;
}

export const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({ form }) => {
  return (
    <FormSection title='Basic Information' stepNumber={1}>
        <FormField
          control={form.control}
          name='name'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='font-medium'>Package Name *</FormLabel>
              <FormControl>
                <Input
                  placeholder='Enter package name'
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='description'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='font-medium'>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder='Describe the package'
                  className='min-h-[100px] resize-y'
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormDescription>
                Provide a detailed description of what this package includes
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
    </FormSection>
  );
};
