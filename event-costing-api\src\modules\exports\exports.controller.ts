import {
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { GetCurrentUser } from '../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ExportsService } from './exports.service';
import { CreateExportDto } from './dto/create-export.dto';
import { ExportResponseDto } from './dto/export-response.dto';
import { ExportStatusResponseDto } from './dto/export-status-response.dto';

@ApiTags('Exports')
@ApiBearerAuth()
@Controller('exports')
@UseGuards(JwtAuthGuard)
export class ExportsController {
  private readonly logger = new Logger(ExportsController.name);

  constructor(private readonly exportsService: ExportsService) {}

  @Post()
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({
    summary: 'Initiate Calculation Export',
    description:
      'Starts an asynchronous job to generate an export file (xlsx or PDF) for a specific calculation. Returns the ID of the export record.',
  })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'Export process successfully initiated.',
    type: ExportResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input (e.g., missing fields, invalid format).',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or not accessible by the user.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to initiate export process.',
  })
  async initiateExport(
    @Body() createExportDto: CreateExportDto,
    @GetCurrentUser() user: User,
  ): Promise<ExportResponseDto> {
    this.logger.log(
      `User ${user.id} initiating export for calc ${createExportDto.calculationId} as ${createExportDto.format}`,
    );
    try {
      return await this.exportsService.initiateExport(createExportDto, user);
    } catch (error) {
      this.logger.error(
        `Failed to initiate export for user ${user.id}, calc ${createExportDto.calculationId}: ${error instanceof Error ? error.message : error}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  @Get('calculation/:calculationId')
  @ApiOperation({
    summary: 'Get Exports by Calculation ID',
    description: 'Retrieves all export records for a specific calculation.',
  })
  @ApiParam({
    name: 'calculationId',
    type: 'string',
    format: 'uuid',
    description: 'Calculation ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Export history retrieved successfully.',
    type: [ExportStatusResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  async getExportsByCalculation(
    @Param('calculationId', ParseUUIDPipe) calculationId: string,
    @GetCurrentUser() user: User,
  ): Promise<ExportStatusResponseDto[]> {
    this.logger.log(
      `User ${user.id} fetching exports for calculation ${calculationId}`,
    );
    try {
      return await this.exportsService.getExportsByCalculation(
        calculationId,
        user.id,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get exports for calculation ${calculationId}, user ${user.id}: ${error instanceof Error ? error.message : error}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  @Get(':id/status')
  @ApiOperation({
    summary: 'Get Export Status',
    description:
      'Retrieves the status and details (like download URL if completed) of a specific export job.',
  })
  @ApiParam({ name: 'id', description: 'The UUID of the export record.' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Export status retrieved successfully.',
    type: ExportStatusResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Export record not found or not accessible by the user.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to retrieve export status.',
  })
  async getExportStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @GetCurrentUser() user: User,
  ): Promise<ExportStatusResponseDto> {
    this.logger.log(`User ${user.id} checking status for export ${id}`);
    try {
      return await this.exportsService.getExportStatus(id, user.id);
    } catch (error) {
      this.logger.error(
        `Failed to get status for export ${id}, user ${user.id}: ${error instanceof Error ? error.message : error}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }
}
