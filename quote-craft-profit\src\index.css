/* Import custom scrollbar styles */
@import './styles/scrollbar.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 252 99% 64%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 6% 97%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 240 6% 97%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 240 6% 97%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 252 99% 64%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 252 99% 64%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Override browser default validation styling */
  input:invalid,
  select:invalid,
  textarea:invalid {
    box-shadow: none;
    border-color: hsl(var(--input));
  }

  /* Override aria-invalid styling */
  [aria-invalid="true"] {
    box-shadow: none;
  }

  /* Ensure form controls maintain consistent styling */
  [data-radix-select-trigger][aria-invalid="true"] {
    border-color: hsl(var(--input)) !important;
    box-shadow: none !important;
  }

  /* Override any red border on select triggers */
  [data-radix-select-trigger] {
    border-color: hsl(var(--input)) !important;
  }

  /* Focus state for select triggers */
  [data-radix-select-trigger]:focus {
    border-color: hsl(var(--ring)) !important;
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2) !important;
  }

  /* Only apply error styling when explicitly marked with error class */
  .form-field-error [data-radix-select-trigger] {
    border-color: hsl(var(--destructive)) !important;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .animate-slideIn {
    animation: slideIn 0.4s ease-out forwards;
  }

  .animate-pulse-subtle {
    animation: pulse 2s infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .hover-scale {
    transition: transform 0.2s ease-in-out;
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }
}
