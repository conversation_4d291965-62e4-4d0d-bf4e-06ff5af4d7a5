/**
 * Unified Line Item Deletion Hook
 *
 * This hook provides a centralized approach to deleting line items,
 * handling both package items and custom items with proper state cleanup
 * and cache management.
 */

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { optimizedNotifications } from "@/lib/optimized-notifications";
import { LineItem } from "@/types/calculation";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { calculationCacheService } from "../../services/calculationCacheService";
import { recalculateCalculationTotals } from "@/services/calculations";
import { deleteLineItemFromApi } from "@/services/calculations/calculationApiService";
import { useCustomItems } from "@/hooks/useCustomItems";

interface DeleteLineItemParams {
  lineItemId: string;
  isCustom: boolean;
  packageId?: string;
  lineItemName?: string;
}

interface DeleteLineItemContext {
  previousData?: LineItem[];
  deletingItem?: LineItem;
}

/**
 * Hook for unified line item deletion with proper state management
 * @param calculationId - The calculation ID
 * @param onPackageFormCleanup - Callback to clean up package form state
 * @returns Deletion mutation and state
 */
export function useLineItemDeletion(
  calculationId: string,
  onPackageFormCleanup?: (packageId: string) => void
) {
  const queryClient = useQueryClient();
  const { deleteCustomItem } = useCustomItems(calculationId);

  const deleteLineItemMutation = useMutation<
    boolean,
    Error,
    DeleteLineItemParams,
    DeleteLineItemContext
  >({
    mutationFn: async ({ lineItemId, isCustom }: DeleteLineItemParams) => {
      if (isCustom) {
        // Use custom items service - this handles its own state management
        deleteCustomItem(lineItemId);
        return true;
      } else {
        // Use Backend API for package line item deletion
        await deleteLineItemFromApi(calculationId, lineItemId);
        return true;
      }
    },

    onMutate: async ({ lineItemId, packageId, lineItemName }) => {
      try {
        // Get the line item being deleted to clean up related state
        const lineItems = queryClient.getQueryData<LineItem[]>(
          QUERY_KEYS.lineItems(calculationId)
        );

        const deletingItem = lineItems?.find((item) => item.id === lineItemId);

        // Optimistic update using centralized cache service
        const previousData = calculationCacheService.optimisticLineItemRemoval(
          calculationId,
          lineItemId,
          queryClient
        );

        // Clean up package form state if it's a package item
        const packageIdToCleanup = packageId || deletingItem?.package_id;
        if (packageIdToCleanup && onPackageFormCleanup) {
          onPackageFormCleanup(packageIdToCleanup);
        }

        // Show optimistic success message
        if (lineItemName || deletingItem?.name) {
          optimizedNotifications.lineItem.success(
            `Removing "${lineItemName || deletingItem?.name}"...`
          );
        }

        return { previousData, deletingItem };
      } catch (error) {
        return { previousData: undefined, deletingItem: undefined };
      }
    },

    onSuccess: async (_, { lineItemName, isCustom }) => {
      try {
        // Show success message
        const itemName = lineItemName || "Item";
        optimizedNotifications.lineItem.success(
          `${itemName} removed successfully`
        );

        // For package items, we need to recalculate totals and invalidate cache
        if (!isCustom) {
          // Comprehensive cache invalidation using centralized service
          await calculationCacheService.invalidateCalculationData(
            calculationId,
            queryClient,
            {
              calculation: true,
              lineItems: true,
              packagesByCategory: false, // Don't invalidate packages as they haven't changed
              customItems: false, // Only invalidate if it was a custom item
              financialCalculations: true,
              waitForInvalidation: true,
            }
          );

          // Recalculate totals to ensure backend consistency
          try {
            await recalculateCalculationTotals(calculationId);
          } catch (recalcError) {
            // Don't throw here as the deletion was successful
          }
        } else {
          // For custom items, just invalidate the relevant caches
          await calculationCacheService.invalidateCalculationData(
            calculationId,
            queryClient,
            {
              calculation: true,
              lineItems: true,
              packagesByCategory: false,
              customItems: true,
              financialCalculations: true,
              waitForInvalidation: true,
            }
          );
        }
      } catch (error) {
        // Don't throw here as the deletion was successful
      }
    },

    onError: (error, { lineItemName }, context) => {
      // Show error message
      const itemName = lineItemName || "item";
      optimizedNotifications.lineItem.error(
        `Failed to remove ${itemName}. Please try again.`
      );

      // Rollback optimistic updates using centralized service
      if (context?.previousData) {
        calculationCacheService.rollbackOptimisticUpdate(
          calculationId,
          context.previousData,
          queryClient
        );
      }

      // Note: Package form state cleanup is not rolled back as it's a UI state
      // and the user can re-select the package if needed
    },

    onSettled: () => {
      // Ensure data consistency regardless of success or failure
      calculationCacheService
        .ensureDataConsistency(calculationId, queryClient)
        .catch(() => {
          // Silently handle consistency errors
        });
    },
  });

  return {
    deleteLineItem: deleteLineItemMutation.mutate,
    deleteLineItemAsync: deleteLineItemMutation.mutateAsync,
    isDeleting: deleteLineItemMutation.isPending,
    error: deleteLineItemMutation.error,
    reset: deleteLineItemMutation.reset,
  };
}

/**
 * Helper function to extract line item deletion parameters
 * @param lineItem - The line item to delete
 * @returns Deletion parameters
 */
export function getLineItemDeletionParams(
  lineItem: LineItem
): DeleteLineItemParams {
  return {
    lineItemId: lineItem.id,
    isCustom: lineItem.is_custom || false,
    packageId: lineItem.package_id || undefined,
    lineItemName: lineItem.name,
  };
}
