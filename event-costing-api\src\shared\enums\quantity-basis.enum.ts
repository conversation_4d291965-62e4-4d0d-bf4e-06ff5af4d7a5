/**
 * Defines how the quantity and price interact for calculation.
 * Based on the database enum: public.package_quantity_basis
 */
export enum QuantityBasisEnum {
  PER_EVENT = 'PER_EVENT', // Price is for the whole item/service once.
  PER_DAY = 'PER_DAY', // Price is per day (item is implicit).
  PER_ATTENDEE = 'PER_ATTENDEE', // Price is per attendee (duration is implicit).
  PER_ITEM = 'PER_ITEM', // Price is per item (duration is implicit).
  PER_ITEM_PER_DAY = 'PER_ITEM_PER_DAY', // Price is per item per day (e.g., chairs, staff).
  PER_ATTENDEE_PER_DAY = 'PER_ATTENDEE_PER_DAY', // Price is per attendee per day.
}
