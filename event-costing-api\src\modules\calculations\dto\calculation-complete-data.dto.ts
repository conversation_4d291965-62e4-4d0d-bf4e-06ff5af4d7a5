import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CalculationDetailDto } from './calculation-detail.dto';
import { LineItemDto } from '../../calculation-items/dto/line-item.dto';
import { CategoryWithPackagesDto } from '../../packages/dto/packages-by-category-response.dto';
import { CategoryDto } from '../../categories/dto/category.dto';

/**
 * Metadata for the consolidated calculation data response
 */
export class CalculationCompleteDataMetadataDto {
  @ApiProperty({
    description: 'Time taken to load the data in milliseconds',
    example: 250,
  })
  loadTime: number;

  @ApiProperty({
    description: 'Cache version for the response',
    example: '1.0',
  })
  cacheVersion: string;

  @ApiProperty({
    description: 'User ID who requested the data',
    type: String,
    format: 'uuid',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Any errors encountered during data loading',
    type: [String],
    example: [],
  })
  errors?: string[];

  @ApiProperty({
    description: 'Timestamp when the data was loaded',
    type: String,
    format: 'date-time',
  })
  timestamp: string;
}

/**
 * Complete calculation data response DTO
 * Consolidates all calculation-related data in a single response
 * Implements the consolidated endpoint pattern from the API Architecture Migration Plan
 */
export class CalculationCompleteDataDto {
  @ApiProperty({
    description: 'Calculation details',
    type: CalculationDetailDto,
  })
  calculation: CalculationDetailDto;

  @ApiProperty({
    description: 'All line items for the calculation',
    type: [LineItemDto],
  })
  lineItems: LineItemDto[];

  @ApiProperty({
    description: 'Available packages organized by category',
    type: [CategoryWithPackagesDto],
  })
  packages: CategoryWithPackagesDto[];

  @ApiProperty({
    description: 'All available categories',
    type: [CategoryDto],
  })
  categories: CategoryDto[];

  @ApiProperty({
    description: 'Metadata about the response',
    type: CalculationCompleteDataMetadataDto,
  })
  metadata: CalculationCompleteDataMetadataDto;
}
