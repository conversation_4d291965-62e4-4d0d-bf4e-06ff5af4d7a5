/**
 * Error component for calculation detail page
 * Shows error state when calculation cannot be loaded
 */
import React from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

interface CalculationDetailErrorProps {
  message: string;
  onNavigateBack?: () => void;
}

const CalculationDetailError: React.FC<CalculationDetailErrorProps> = ({
  message,
  onNavigateBack,
}) => {
  return (
    <>
      {onNavigateBack && (
        <div className="mb-6">
          <Button variant="ghost" size="sm" onClick={onNavigateBack}>
            <ChevronLeft size={16} />
            <span>Back to Calculations</span>
          </Button>
        </div>
      )}
      <div className="p-8 text-center border dark:border-gray-700 rounded-lg">
        <p className="text-red-500 dark:text-red-400 mb-4">{message}</p>
        {onNavigateBack && (
          <Button onClick={onNavigateBack} variant="outline">
            Return to Calculations
          </Button>
        )}
      </div>
    </>
  );
};

export default CalculationDetailError;
