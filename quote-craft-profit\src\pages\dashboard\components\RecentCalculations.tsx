import React from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";
import { formatRupiah } from "@/lib/utils";
import { useEventType } from "@/components/ui/event-type-selector";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

// Component to display event type name
const EventTypeDisplay: React.FC<{ eventTypeId?: string | null }> = ({
  eventTypeId,
}) => {
  const { eventTypeName } = useEventType(eventTypeId);
  return <span>{eventTypeName || "General Event"}</span>;
};

interface RecentCalculation {
  id: string;
  name: string;
  event_type_id?: string | null; // Updated to use event_type_id
  client_name?: string;
  event_start_date?: string;
  total: number;
  status: "draft" | "completed" | "cancelled";
  updated_at: string;
}

/**
 * Fetch recent calculations from the real API
 */
const fetchRecentCalculations = async (): Promise<RecentCalculation[]> => {
  try {
    const apiClient = await getAuthenticatedApiClient();

    // Fetch recent calculations with pagination
    const response = await apiClient.get(API_ENDPOINTS.CALCULATIONS.GET_ALL, {
      params: {
        page: 1,
        pageSize: 5, // Get 5 most recent calculations
        // TODO: Add sorting by updated_at desc when backend supports it
      },
    });

    return response.data.data || [];
  } catch (error) {
    console.error("Error fetching recent calculations:", error);
    // Return empty array on error
    return [];
  }
};

const RecentCalculations: React.FC = () => {
  const { formatForDisplay } = useTimezoneAwareDates();

  const {
    data: recentCalcs = [],
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["recentCalculations"],
    queryFn: fetchRecentCalculations,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Recent Calculations
          </h2>
          <Button variant="ghost" size="sm" disabled>
            View All
          </Button>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left border-b border-gray-200 dark:border-gray-700">
                <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                  Event
                </th>
                <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                  Client
                </th>
                <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                  Date
                </th>
                <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                  Total
                </th>
                <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                  Status
                </th>
                <th className="pb-3 font-medium text-gray-600 dark:text-gray-300"></th>
              </tr>
            </thead>
            <tbody>
              {[1, 2, 3].map((i) => (
                <tr
                  key={i}
                  className="border-b border-gray-100 dark:border-gray-700"
                >
                  <td className="py-4 pr-4">
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-3 w-20" />
                  </td>
                  <td className="py-4">
                    <Skeleton className="h-4 w-24" />
                  </td>
                  <td className="py-4">
                    <Skeleton className="h-4 w-20" />
                  </td>
                  <td className="py-4">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="py-4">
                    <Skeleton className="h-6 w-16 rounded-full" />
                  </td>
                  <td className="py-4">
                    <Skeleton className="h-8 w-12" />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Recent Calculations
        </h2>
        <Link to="/calculations">
          <Button variant="ghost" size="sm">
            View All
          </Button>
        </Link>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="text-left border-b border-gray-200 dark:border-gray-700">
              <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                Event
              </th>
              <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                Client
              </th>
              <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                Date
              </th>
              <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                Total
              </th>
              <th className="pb-3 font-medium text-gray-600 dark:text-gray-300">
                Status
              </th>
              <th className="pb-3 font-medium text-gray-600 dark:text-gray-300"></th>
            </tr>
          </thead>
          <tbody>
            {recentCalcs.map((calc) => (
              <tr
                key={calc.id}
                className="border-b border-gray-100 dark:border-gray-700"
              >
                <td className="py-4 pr-4">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {calc.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    <EventTypeDisplay eventTypeId={calc.event_type_id} />
                  </div>
                </td>
                <td className="py-4 text-gray-900 dark:text-gray-300">
                  {calc.client_name || "No client"}
                </td>
                <td className="py-4 text-gray-900 dark:text-gray-300">
                  {calc.event_start_date
                    ? formatForDisplay(calc.event_start_date, "MMM d, yyyy")
                    : formatForDisplay(calc.updated_at, "MMM d, yyyy")}
                </td>
                <td className="py-4 text-gray-900 dark:text-gray-300">
                  {formatRupiah(calc.total || 0)}
                </td>
                <td className="py-4">
                  <Badge
                    className={
                      calc.status === "draft"
                        ? "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 hover:bg-yellow-200 dark:hover:bg-yellow-900/30"
                        : calc.status === "completed"
                        ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/30"
                        : "bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                    }
                  >
                    {calc.status === "draft"
                      ? "Draft"
                      : calc.status === "completed"
                      ? "Complete"
                      : "Cancelled"}
                  </Badge>
                </td>
                <td className="py-4">
                  <Link to={`/calculations/${calc.id}`}>
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </Link>
                </td>
              </tr>
            ))}

            {recentCalcs.length === 0 && !isLoading && (
              <tr>
                <td
                  colSpan={6}
                  className="py-8 text-center text-gray-500 dark:text-gray-400"
                >
                  {isError
                    ? "Failed to load calculations."
                    : "No calculations found. Create your first calculation!"}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <div className="mt-6 text-center">
        <Link to="/calculations/new">
          <Button>Create New Calculation</Button>
        </Link>
      </div>
    </div>
  );
};

export default RecentCalculations;
