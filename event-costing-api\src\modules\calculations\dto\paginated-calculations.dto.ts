import { ApiProperty } from '@nestjs/swagger'; // Optional for Swagger documentation
import { CalculationStatus } from '../enums/calculation-status.enum';

// DTO representing the summary data for a single calculation in a list
export class CalculationSummaryDto {
  @ApiProperty({ type: String, format: 'uuid' })
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ enum: CalculationStatus })
  status: CalculationStatus;

  @ApiProperty({ type: String, format: 'date', nullable: true })
  event_start_date: string | null;

  @ApiProperty({ type: String, format: 'date', nullable: true })
  event_end_date: string | null;

  @ApiProperty({
    type: Number,
    description: 'Pre-calculated total from calculation_history',
  })
  total: number;

  @ApiProperty({ type: String, format: 'uuid' })
  currency_id: string;

  @ApiProperty({ type: String, format: 'date-time' })
  created_at: string;

  @ApiProperty({ type: String, format: 'date-time' })
  updated_at: string;

  @ApiProperty({ type: String, format: 'uuid', nullable: true })
  client_id?: string;

  @ApiProperty({ type: String, format: 'uuid', nullable: true })
  event_id?: string;
}

// Remove the specific paginated response DTO as it's replaced by the generic one in shared/dtos
// export class PaginatedCalculationsResponse {
//   @ApiProperty({ type: [CalculationSummaryDto] })
//   data: CalculationSummaryDto[];
//
//   @ApiProperty({ type: Number, description: 'Total count of matching records' })
//   count: number;
// }
