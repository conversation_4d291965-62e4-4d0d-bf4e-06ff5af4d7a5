import {
  IsString,
  IsOptional,
  IsUUID,
  IsInt,
  Min,
  IsEnum,
  IsObject,
  IsNotEmpty,
  ValidateNested,
  IsArray,
  MaxLength,
  IsISO8601,
  IsNumber,
  IsIn,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CalculationStatus } from '../enums/calculation-status.enum';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';

// --- Define specific structures for Tax/Discount CORRECTLY --- //
class TaxDetailItemDto {
  @ApiProperty({ description: 'Name of the tax', example: 'PPN' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Tax rate (percentage)',
    example: 11,
    type: 'number',
  })
  @IsNumber()
  rate: number;

  @ApiProperty({
    description: 'Type of tax',
    example: 'percentage',
    enum: ['percentage', 'fixed'], // Example enum
  })
  @IsString()
  @IsIn(['percentage', 'fixed']) // Example validation
  type: string; // Consider using an enum if types are fixed

  @ApiProperty({
    description: 'Basis for percentage calculation',
    example: 'subtotal',
    enum: ['subtotal', 'total'], // Example enum
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['subtotal', 'total']) // Example validation
  basis?: string; // Basis might only apply to percentage type
}

class DiscountDetailDto {
  @ApiProperty({
    description: 'Name or description of the discount',
    example: 'Early Bird',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Discount amount (fixed value)',
    example: 100.0,
    type: 'number',
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Reason for the discount',
    example: 'Promotion',
    required: false,
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

// --- Main Update DTO ---

export class UpdateCalculationDto {
  @ApiPropertyOptional({
    required: false,
    nullable: true,
    example: 'Calculation Name',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  // Cannot change currency or creator after creation
  @ApiPropertyOptional({
    description: 'City ID',
    example: '029f586d-70da-4637-b58a-176470d3e528',
    format: 'uuid',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  city_id?: string | null; // Allow setting back to null

  @ApiPropertyOptional({
    description: 'Array of venue IDs',
    type: [String],
    format: 'uuid',
    example: ['a1b2c3d4-e5f6-7890-1234-567890abcdef'],
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  venue_ids?: string[] | null;

  @ApiPropertyOptional({
    description: 'Optional: Start date and time of the event (ISO 8601 format)', // Updated description
    example: '2025-12-31T10:00:00.000Z', // Example in ISO 8601 UTC
    // Or example with offset: '2025-12-31T17:00:00+07:00' (7 AM UTC)
    format: 'date-time', // Specify format for Swagger UI
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsISO8601({ strict: true, strictSeparator: true }) // Validate as ISO 8601 string
  event_start_date?: string | null; // Keep as string if input is string, or use Date if transformed

  @ApiPropertyOptional({
    description: 'Optional: End date and time of the event (ISO 8601 format)', // Updated description
    example: '2026-01-01T10:00:00.000Z', // Example in ISO 8601 UTC
    format: 'date-time', // Specify format for Swagger UI
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsISO8601({ strict: true, strictSeparator: true }) // Validate as ISO 8601 string
  event_end_date?: string | null; // Keep as string if input is string, or use Date if transformed

  @ApiPropertyOptional({
    // Changed from @ApiProperty to @ApiPropertyOptional
    description: 'Optional: Number of attendees expected',
    example: 150,
    minimum: 0,
    nullable: true,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  attendees?: number | null;

  @ApiPropertyOptional({
    description:
      'Optional: Event type ID (UUID reference to event_types table)',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    format: 'uuid',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  event_type_id?: string | null;

  @ApiPropertyOptional({
    description: 'Optional: General notes about the calculation',
    example: 'Final guest list confirmed. Pending catering finalization.',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  notes?: string | null;

  @ApiPropertyOptional({
    description:
      'Optional: Notes specific to this version/update of the calculation',
    example: 'Increased attendee count from 120 to 150 based on client update.',
    maxLength: 2000,
    nullable: true,
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  version_notes?: string | null;

  @ApiPropertyOptional({
    description: 'Optional: ID of the client associated with this calculation',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    format: 'uuid',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  client_id?: string | null;

  @ApiPropertyOptional({
    description: 'Optional: ID of the event this calculation belongs to',
    example: 'b2c3d4e5-f6a7-8901-2345-67890abcdef1',
    format: 'uuid',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  event_id?: string | null;

  @ApiPropertyOptional({
    description: 'Optional: The current status of the calculation',
    enum: CalculationStatus,
    example: CalculationStatus.COMPLETED, // Use an actual enum member
  })
  @IsOptional()
  @IsEnum(CalculationStatus)
  status?: CalculationStatus;

  @ApiPropertyOptional({
    description:
      'Optional: Array of tax objects to apply. Replaces the existing taxes.',
    type: [TaxDetailItemDto],
    example: [{ name: 'PPN', rate: 12, type: 'percentage', basis: 'subtotal' }],
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaxDetailItemDto)
  taxes?: TaxDetailItemDto[] | null;

  @ApiPropertyOptional({
    description:
      'Optional: Discount object to apply. Replaces the existing discount.',
    type: DiscountDetailDto,
    example: { name: 'Early Bird', amount: 100.0, reason: 'Promotion' },
    nullable: true,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DiscountDetailDto)
  @IsObject()
  discount?: DiscountDetailDto | null;
}
