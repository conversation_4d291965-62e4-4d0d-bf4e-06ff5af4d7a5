import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  ParseU<PERSON><PERSON>ipe,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ClientsService } from './clients.service';
import { ClientDto } from './dto/client.dto';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiParam,
  ApiResponse,
} from '@nestjs/swagger';

@ApiTags('Clients')
@ApiBearerAuth() // Indicate JWT is needed for this controller
@Controller('clients')
@UseGuards(JwtAuthGuard) // Protect all routes in this controller
export class ClientsController {
  private readonly logger = new Logger(ClientsController.name);

  constructor(private readonly clientsService: ClientsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new client' })
  @ApiCreatedResponse({
    description: 'Client created successfully.',
    type: ClientDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request (Validation Error)' })
  @ApiResponse({ status: 409, description: 'Conflict (e.g., duplicate email)' })
  async create(@Body() createClientDto: CreateClientDto): Promise<ClientDto> {
    this.logger.log(
      `Received request to create client: ${createClientDto.client_name}`,
    );
    return this.clientsService.create(createClientDto);
  }

  @Get()
  @ApiOperation({ summary: 'List all clients with optional search' })
  @ApiOkResponse({ description: 'List of clients.', type: [ClientDto] })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for client name, company, or email',
  })
  async findAll(@Query('search') search?: string): Promise<ClientDto[]> {
    this.logger.log(
      `Received request to list clients ${search ? `with search: '${search}'` : ''}`,
    );
    return this.clientsService.findAll(search);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific client by ID' })
  @ApiOkResponse({ description: 'Client details.', type: ClientDto })
  @ApiResponse({ status: 404, description: 'Client not found.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ClientDto> {
    this.logger.log(`Received request to get client ID: ${id}`);
    return this.clientsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a client' })
  @ApiOkResponse({
    description: 'Client updated successfully.',
    type: ClientDto,
  })
  @ApiResponse({ status: 404, description: 'Client not found.' })
  @ApiResponse({ status: 400, description: 'Bad Request (Validation Error)' })
  @ApiResponse({ status: 409, description: 'Conflict (e.g., duplicate email)' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateClientDto: UpdateClientDto,
  ): Promise<ClientDto> {
    this.logger.log(`Received request to update client ID: ${id}`);
    return this.clientsService.update(id, updateClientDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a client' })
  @ApiNoContentResponse({ description: 'Client deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Client not found.' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Received request to delete client ID: ${id}`);
    await this.clientsService.remove(id);
  }
}
