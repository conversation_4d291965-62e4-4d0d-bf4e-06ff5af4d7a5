import {
  Controller,
  Post,
  Put,
  Delete,
  Body,
  Param,
  ParseUUI<PERSON>ipe,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
  Inject,
  forwardRef,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiParam,
  ApiOkResponse,
  ApiResponse,
  ApiOperation,
  ApiNoContentResponse,
} from '@nestjs/swagger';
import { CalculationItemsService } from '../../calculation-items/calculation-items.service';
import { CalculationCrudService } from '../services/calculation-crud.service';
import { AddPackageLineItemDto } from '../../calculation-items/dto/add-package-line-item.dto';
import { AddCustomLineItemDto } from '../../calculation-items/dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from '../../calculation-items/dto/update-line-item.dto';
import { LineItemDto } from '../../calculation-items/dto/line-item.dto';
import { ItemIdResponse } from '../../calculation-items/dto/item-id-response.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';

/**
 * Controller for calculation line item operations
 * Implements the new line item API endpoints from the API Architecture Migration Plan
 * Provides CRUD operations for line items with proper authentication and validation
 */
@ApiTags('Calculation Line Items')
@ApiBearerAuth()
@Controller('calculations/:calculationId/line-items')
@UseGuards(JwtAuthGuard)
export class CalculationLineItemsController {
  private readonly logger = new Logger(CalculationLineItemsController.name);

  constructor(
    private readonly calculationItemsService: CalculationItemsService,
    @Inject(forwardRef(() => CalculationCrudService))
    private readonly calculationCrudService: CalculationCrudService,
  ) {}

  /**
   * Validate calculation ownership
   */
  private async validateCalculationOwnership(
    calculationId: string,
    user: User,
  ): Promise<void> {
    this.logger.debug(
      `Validating ownership for calculation: ${calculationId}, user: ${user.id}`,
    );

    try {
      // This will throw NotFoundException if calculation doesn't exist or user doesn't own it
      await this.calculationCrudService.findCalculationRawById(
        calculationId,
        user,
      );
      this.logger.debug(
        `Ownership validated for calculation: ${calculationId}`,
      );
    } catch (error) {
      this.logger.warn(
        `Ownership validation failed for calculation: ${calculationId}, user: ${user.id}`,
      );
      throw error;
    }
  }

  /**
   * Add a package line item to the calculation
   */
  @Post('package')
  @ApiOperation({
    summary: 'Add a package line item to a calculation',
    description: 'Adds a package-based line item and triggers recalculation',
  })
  @ApiParam({ name: 'calculationId', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Package line item added successfully',
    type: ItemIdResponse,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid package data provided.',
  })
  async addPackageLineItem(
    @Param('calculationId', ParseUUIDPipe) calculationId: string,
    @Body() addPackageDto: AddPackageLineItemDto,
    @GetCurrentUser() user: User,
  ): Promise<ItemIdResponse> {
    this.logger.log(
      `User ${user.email} adding package line item to calculation ${calculationId}`,
    );

    await this.validateCalculationOwnership(calculationId, user);

    const result = await this.calculationItemsService.addPackageLineItem(
      calculationId,
      addPackageDto,
      user,
    );

    // Trigger recalculation after adding the item
    await this.calculationCrudService.triggerRecalculation(calculationId);

    this.logger.log(
      `Package line item added successfully to calculation ${calculationId}`,
    );

    return result;
  }

  /**
   * Add a custom line item to the calculation
   */
  @Post('custom')
  @ApiOperation({
    summary: 'Add a custom line item to a calculation',
    description: 'Adds a custom line item and triggers recalculation',
  })
  @ApiParam({ name: 'calculationId', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Custom line item added successfully',
    type: ItemIdResponse,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid custom item data provided.',
  })
  async addCustomLineItem(
    @Param('calculationId', ParseUUIDPipe) calculationId: string,
    @Body() addCustomDto: AddCustomLineItemDto,
    @GetCurrentUser() user: User,
  ): Promise<ItemIdResponse> {
    this.logger.log(
      `User ${user.email} adding custom line item to calculation ${calculationId}`,
    );

    await this.validateCalculationOwnership(calculationId, user);

    const result = await this.calculationItemsService.addCustomLineItem(
      calculationId,
      addCustomDto,
      user,
    );

    // Trigger recalculation after adding the item
    await this.calculationCrudService.triggerRecalculation(calculationId);

    this.logger.log(
      `Custom line item added successfully to calculation ${calculationId}`,
    );

    return result;
  }

  /**
   * Update a line item
   */
  @Put(':lineItemId')
  @ApiOperation({
    summary: 'Update a line item',
    description:
      'Updates a line item and triggers recalculation if quantity or price changed',
  })
  @ApiParam({ name: 'calculationId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'lineItemId', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Line item updated successfully',
    type: LineItemDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation or line item not found or access denied.',
  })
  async updateLineItem(
    @Param('calculationId', ParseUUIDPipe) calculationId: string,
    @Param('lineItemId', ParseUUIDPipe) lineItemId: string,
    @Body() updateDto: UpdateLineItemDto,
    @GetCurrentUser() user: User,
  ): Promise<LineItemDto> {
    this.logger.log(
      `User ${user.email} updating line item ${lineItemId} in calculation ${calculationId}`,
    );

    await this.validateCalculationOwnership(calculationId, user);

    const updatedItem = await this.calculationItemsService.updateLineItem(
      calculationId,
      lineItemId,
      updateDto,
      user,
    );

    // Smart recalculation - only if quantity or price changed
    const shouldRecalculate =
      updateDto.quantity !== undefined ||
      updateDto.unitPrice !== undefined ||
      updateDto.itemQuantityBasis !== undefined;

    if (shouldRecalculate) {
      this.logger.debug(
        `Triggering recalculation for calculation ${calculationId} after line item update`,
      );
      await this.calculationCrudService.triggerRecalculation(calculationId);
    }

    this.logger.log(
      `Line item ${lineItemId} updated successfully in calculation ${calculationId}`,
    );

    return updatedItem;
  }

  /**
   * Delete a line item
   */
  @Delete(':lineItemId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete a line item',
    description: 'Deletes a line item and triggers recalculation',
  })
  @ApiParam({ name: 'calculationId', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'lineItemId', type: 'string', format: 'uuid' })
  @ApiNoContentResponse({ description: 'Line item deleted successfully' })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation or line item not found or access denied.',
  })
  async deleteLineItem(
    @Param('calculationId', ParseUUIDPipe) calculationId: string,
    @Param('lineItemId', ParseUUIDPipe) lineItemId: string,
    @GetCurrentUser() user: User,
  ): Promise<void> {
    this.logger.log(
      `User ${user.email} deleting line item ${lineItemId} from calculation ${calculationId}`,
    );

    await this.validateCalculationOwnership(calculationId, user);

    // First check if the line item exists and belongs to this calculation
    await this.calculationItemsService.getLineItemById(
      calculationId,
      lineItemId,
    );

    // Delete the line item (this will handle both package and custom items)
    await this.calculationItemsService.deleteLineItem(
      calculationId,
      lineItemId,
      user,
    );

    // Trigger recalculation after deletion
    await this.calculationCrudService.triggerRecalculation(calculationId);

    this.logger.log(
      `Line item ${lineItemId} deleted successfully from calculation ${calculationId}`,
    );
  }
}
