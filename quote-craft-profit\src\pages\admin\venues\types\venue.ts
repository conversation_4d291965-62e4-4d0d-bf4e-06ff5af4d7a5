/**
 * Venue types for the admin venues feature
 */

import {
  Venue as GlobalVenue,
  SaveVenueData,
  VenueFormValues,
} from "@/types/venues";

// Re-export the global venue types
export type Venue = GlobalVenue;
export interface VenueDisplay extends GlobalVenue {
  city_name: string;
}

// Re-export form and save data types
export type { SaveVenueData, VenueFormValues };

/**
 * API response for venue operations
 */
export interface VenueApiResponse {
  data: Venue | Venue[];
  error?: string;
  message?: string;
}
