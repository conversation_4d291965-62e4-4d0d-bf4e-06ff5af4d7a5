import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Form } from '@/components/ui/form';
import { PackageFormValues } from '../../types/package';
import { Category } from '@/types/types';
import {
  BasicInfoSection,
  ClassificationSection,
  PricingSection,
  VenueSelectionSection,
  StatusSection,
} from './sections';

// Define interfaces for props
interface Division {
  id: string;
  name: string;
}

interface City {
  id: string;
  name: string;
}

interface Currency {
  id: string;
  code: string;
  description: string;
}

interface PackageDetailsFormProps {
  form: UseFormReturn<PackageFormValues>;
  categories: Category[];
  divisions: Division[];
  cities: City[];
  currencies?: Currency[];
  isLoading?: boolean;
  hideCitySelection?: boolean;
}

export const PackageDetailsForm: React.FC<PackageDetailsFormProps> = ({
  form,
  categories,
  divisions,
  cities,
  currencies = [],
  isLoading = false,
  hideCitySelection = false,
}) => {

  return (
    <Form {...form}>
      <div className='space-y-6'>
        <BasicInfoSection form={form} />

        <ClassificationSection
          form={form}
          categories={categories}
          divisions={divisions}
          cities={cities}
          isLoading={isLoading}
          hideCitySelection={hideCitySelection}
        />

        <PricingSection
          form={form}
          currencies={currencies}
          isLoading={isLoading}
        />

        <VenueSelectionSection form={form} cities={cities} />

        <StatusSection form={form} />
      </div>
    </Form>
  );
};
