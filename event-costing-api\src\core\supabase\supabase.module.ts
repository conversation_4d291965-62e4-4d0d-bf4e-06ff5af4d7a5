import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SupabaseService } from './supabase.service';

@Global() // Make the service available globally without importing SupabaseModule everywhere
@Module({
  imports: [ConfigModule], // Import ConfigModule to make ConfigService available
  providers: [SupabaseService],
  exports: [SupabaseService], // Export SupabaseService so other modules can inject it
})
export class SupabaseModule {}
