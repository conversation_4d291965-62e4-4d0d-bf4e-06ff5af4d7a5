import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { EventTypeSelector } from "./EventTypeSelector";
import { AttendeeCounter } from "./AttendeeCounter";
import { CitySelector } from "./CitySelector";
import { VenueSelector } from "./VenueSelector";
import { TemplateRecommendations } from "./TemplateRecommendations";

export interface WizardState {
  eventType: string;
  attendeeCount: number;
  cityId: string;
  venueId?: string;
}

const WIZARD_STEPS = [
  { id: "event-type", title: "Event Type", component: EventTypeSelector },
  { id: "attendees", title: "Attendees", component: AttendeeCounter },
  { id: "city", title: "City", component: CitySelector },
  { id: "venue", title: "Venue", component: VenueSelector },
  { id: "templates", title: "Templates", component: TemplateRecommendations },
];

export const WizardContainer: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [wizardState, setWizardState] = useState<WizardState>({
    eventType: "",
    attendeeCount: 50,
    cityId: "",
    venueId: "",
  });

  const updateWizardState = (updates: Partial<WizardState>) => {
    setWizardState((prev) => ({ ...prev, ...updates }));
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return wizardState.eventType !== "";
      case 1:
        return wizardState.attendeeCount > 0;
      case 2:
        return wizardState.cityId !== "";
      case 3:
        return true; // Venue is optional
      case 4:
        return true; // Final step
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (currentStep < WIZARD_STEPS.length - 1 && canProceed()) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const handleWizardComplete = () => {
    try {
      // Build template filters based on wizard selections
      const templateFilters = {
        eventType: wizardState.eventType,
        cityId: wizardState.cityId,
        attendeeCount: wizardState.attendeeCount,
        venueId: wizardState.venueId,
      };

      // Navigate to templates page with filters applied
      navigate("/templates", {
        state: {
          filters: templateFilters,
          fromWizard: true,
        },
      });

      // Show success message
      toast.success(
        "Event setup completed! Browse templates that match your requirements."
      );
    } catch (error) {
      console.error("Error completing wizard:", error);
      toast.error("Something went wrong. Please try again.");
    }
  };

  const CurrentStepComponent = WIZARD_STEPS[currentStep].component;

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="flex items-center justify-between mb-8">
        {WIZARD_STEPS.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                index <= currentStep
                  ? "bg-blue-600 text-white"
                  : "bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
              }`}
            >
              {index + 1}
            </div>
            <span
              className={`ml-2 text-sm font-medium ${
                index <= currentStep
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-gray-500 dark:text-gray-400"
              }`}
            >
              {step.title}
            </span>
            {index < WIZARD_STEPS.length - 1 && (
              <div
                className={`w-12 h-0.5 mx-4 ${
                  index < currentStep
                    ? "bg-blue-600"
                    : "bg-gray-200 dark:bg-gray-700"
                }`}
              />
            )}
          </div>
        ))}
      </div>

      {/* Current Step Content */}
      <Card className="min-h-[400px]">
        <CardContent className="p-8">
          <CurrentStepComponent
            wizardState={wizardState}
            updateWizardState={updateWizardState}
            onNext={nextStep}
          />
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 0}
          className="flex items-center gap-2"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>

        {currentStep < WIZARD_STEPS.length - 1 ? (
          <Button
            onClick={nextStep}
            disabled={!canProceed()}
            className="flex items-center gap-2"
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        ) : (
          <Button
            onClick={handleWizardComplete}
            className="flex items-center gap-2"
          >
            Browse Templates
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};
