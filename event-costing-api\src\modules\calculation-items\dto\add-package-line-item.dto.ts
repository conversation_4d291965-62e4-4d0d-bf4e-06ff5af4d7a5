import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsUUID,
  IsArray,
  IsOptional,
  Min,
  IsNumber,
  IsString,
} from 'class-validator';

export class AddPackageLineItemDto {
  @ApiProperty({ description: 'ID of the package to add', format: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  packageId: string;

  @ApiProperty({
    description: 'Array of selected option IDs for this package',
    type: [String],
    format: 'uuid',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  optionIds?: string[];

  // Optional overrides for quantity/duration if the package allows it or context demands it
  // Validation might depend on package.quantity_basis (handled in service)
  @ApiPropertyOptional({
    description: 'Override the default quantity (if applicable)',
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  quantity?: number;

  @ApiPropertyOptional({
    description: 'Override the default duration in days (if applicable)',
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  duration?: number;

  @ApiPropertyOptional({
    description: 'Optional notes for this specific line item',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
