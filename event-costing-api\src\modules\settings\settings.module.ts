import { Module } from '@nestjs/common';
import { SettingsService } from './settings.service';
import { AdminSettingsController } from './admin-settings.controller';
// import { AdminModule } from '../admin/admin.module'; // Temporarily commented out
import { AuthModule } from '../auth/auth.module';
// Assuming CoreModule/SupabaseService is handled globally

@Module({
  imports: [
    AuthModule, // Still need JwtAuthGuard
    // AdminModule, // Temporarily commented out
  ],
  controllers: [AdminSettingsController],
  providers: [SettingsService],
  exports: [SettingsService],
})
export class SettingsModule {}
