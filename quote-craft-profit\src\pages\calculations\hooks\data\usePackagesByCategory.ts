/**
 * Hook for fetching packages by category
 */
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { getPackagesByCategoryForCalculation } from "../../../../services/calculations";
import { useCalculation } from "../core/useCalculation";

/**
 * Hook for fetching packages organized by category for a calculation
 *
 * @param calculationId - The ID of the calculation
 * @returns Query result with packages organized by category
 */
export function usePackagesByCategory(calculationId: string) {
  // Use the calculation data to determine when to fetch packages
  const { data: calculation } = useCalculation(calculationId);

  return useQuery({
    queryKey: QUERY_KEYS.calculations.packagesByCategory(calculationId),
    queryFn: async () => {
      const result = await getPackagesByCategoryForCalculation(
        calculationId,
        true
      );

      // Ensure we always return an array, even if Supabase returns null or undefined
      if (!result || !Array.isArray(result)) {
        return [];
      }

      return result;
    },
    enabled: !!calculation?.id,
    meta: {
      onError: (error: Error) => {
        console.error("Error fetching packages by category:", error);
        toast.error("Failed to load packages. Please try again later.");
      },
    },
    retry: 2, // Retry failed requests up to 2 times
  });
}
