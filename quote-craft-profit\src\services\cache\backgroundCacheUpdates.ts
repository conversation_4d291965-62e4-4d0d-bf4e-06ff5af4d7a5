/**
 * Background Cache Updates Service
 *
 * PHASE 2: Implements seamless background cache refresh without blocking UI
 * Provides stale-while-revalidate pattern and intelligent update scheduling
 */

import { QueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/lib/queryKeys";

export interface BackgroundUpdateConfig {
  queryKey: readonly any[];
  queryFn: () => Promise<any>;
  staleTime: number;
  backgroundRefreshInterval?: number;
  priority: "high" | "medium" | "low";
  enabled: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

export interface BackgroundUpdateStats {
  totalUpdates: number;
  successfulUpdates: number;
  failedUpdates: number;
  lastUpdateTime: Date | null;
  activeUpdates: number;
  queuedUpdates: number;
}

class BackgroundCacheUpdatesService {
  private queryClient: QueryClient;
  private updateConfigs = new Map<string, BackgroundUpdateConfig>();
  private updateQueue: string[] = [];
  private activeUpdates = new Set<string>();
  private updateIntervals = new Map<string, NodeJS.Timeout>();
  private stats: BackgroundUpdateStats = {
    totalUpdates: 0,
    successfulUpdates: 0,
    failedUpdates: 0,
    lastUpdateTime: null,
    activeUpdates: 0,
    queuedUpdates: 0,
  };

  private readonly maxConcurrentUpdates = 3;
  private readonly updateDelayMs = 100; // Delay between updates to prevent overwhelming

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
    this.initializeDefaultConfigs();
    this.startUpdateProcessor();
  }

  /**
   * Initialize default background update configurations
   */
  private initializeDefaultConfigs(): void {
    const defaultConfigs: BackgroundUpdateConfig[] = [
      // High priority - frequently changing data
      {
        queryKey: QUERY_KEYS.calculations.lists(),
        queryFn: () => this.fetchCalculationsList(),
        staleTime: 30 * 1000, // 30 seconds
        backgroundRefreshInterval: 60 * 1000, // 1 minute
        priority: "high",
        enabled: true,
      },

      // Medium priority - moderately changing data
      {
        queryKey: QUERY_KEYS.packages.lists(),
        queryFn: () => this.fetchPackagesList(),
        staleTime: 2 * 60 * 1000, // 2 minutes
        backgroundRefreshInterval: 5 * 60 * 1000, // 5 minutes
        priority: "medium",
        enabled: true,
      },

      {
        queryKey: QUERY_KEYS.clients.all(),
        queryFn: () => this.fetchClientsList(),
        staleTime: 5 * 60 * 1000, // 5 minutes
        backgroundRefreshInterval: 10 * 60 * 1000, // 10 minutes
        priority: "medium",
        enabled: true,
      },

      // Low priority - rarely changing data
      {
        queryKey: QUERY_KEYS.categories.all(),
        queryFn: () => this.fetchCategoriesList(),
        staleTime: 15 * 60 * 1000, // 15 minutes
        backgroundRefreshInterval: 30 * 60 * 1000, // 30 minutes
        priority: "low",
        enabled: true,
      },

      {
        queryKey: QUERY_KEYS.cities.all(),
        queryFn: () => this.fetchCitiesList(),
        staleTime: 30 * 60 * 1000, // 30 minutes
        backgroundRefreshInterval: 60 * 60 * 1000, // 1 hour
        priority: "low",
        enabled: true,
      },
    ];

    defaultConfigs.forEach((config) => {
      const key = this.generateConfigKey(config.queryKey);
      this.updateConfigs.set(key, config);
      this.scheduleBackgroundUpdate(key, config);
    });

    console.debug(
      "🔄 Background cache updates initialized with",
      defaultConfigs.length,
      "configurations"
    );
  }

  /**
   * Generate unique key for update configuration
   */
  private generateConfigKey(queryKey: readonly any[]): string {
    return JSON.stringify(queryKey);
  }

  /**
   * Schedule background update for a configuration
   */
  private scheduleBackgroundUpdate(
    configKey: string,
    config: BackgroundUpdateConfig
  ): void {
    if (!config.enabled || !config.backgroundRefreshInterval) return;

    // Clear existing interval if any
    const existingInterval = this.updateIntervals.get(configKey);
    if (existingInterval) {
      clearInterval(existingInterval);
    }

    // Set new interval
    const interval = setInterval(() => {
      this.queueUpdate(configKey);
    }, config.backgroundRefreshInterval);

    this.updateIntervals.set(configKey, interval);
  }

  /**
   * Queue an update for processing
   */
  private queueUpdate(configKey: string): void {
    if (
      !this.updateQueue.includes(configKey) &&
      !this.activeUpdates.has(configKey)
    ) {
      // Insert based on priority
      const config = this.updateConfigs.get(configKey);
      if (!config || !config.enabled) return;

      const priorityOrder = { high: 0, medium: 1, low: 2 };
      const insertIndex = this.updateQueue.findIndex((key) => {
        const queuedConfig = this.updateConfigs.get(key);
        return (
          queuedConfig &&
          priorityOrder[queuedConfig.priority] > priorityOrder[config.priority]
        );
      });

      if (insertIndex === -1) {
        this.updateQueue.push(configKey);
      } else {
        this.updateQueue.splice(insertIndex, 0, configKey);
      }

      this.stats.queuedUpdates = this.updateQueue.length;
      console.debug(
        `📋 Queued background update: ${configKey} (priority: ${config.priority})`
      );
    }
  }

  /**
   * Start the update processor
   */
  private startUpdateProcessor(): void {
    const processUpdates = async () => {
      while (
        this.updateQueue.length > 0 &&
        this.activeUpdates.size < this.maxConcurrentUpdates
      ) {
        const configKey = this.updateQueue.shift();
        if (configKey) {
          this.stats.queuedUpdates = this.updateQueue.length;
          await this.processUpdate(configKey);
        }
      }

      this.stats.activeUpdates = this.activeUpdates.size;

      // Schedule next processing cycle
      setTimeout(processUpdates, this.updateDelayMs);
    };

    processUpdates();
  }

  /**
   * Process a single background update
   */
  private async processUpdate(configKey: string): Promise<void> {
    const config = this.updateConfigs.get(configKey);
    if (!config || !config.enabled) return;

    this.activeUpdates.add(configKey);
    this.stats.totalUpdates++;
    this.stats.activeUpdates = this.activeUpdates.size;

    try {
      console.debug(`🔄 Starting background update: ${configKey}`);

      // Check if data is actually stale
      const queryState = this.queryClient.getQueryState(config.queryKey);
      const isStale =
        !queryState ||
        !queryState.dataUpdatedAt ||
        Date.now() - queryState.dataUpdatedAt > config.staleTime;

      if (!isStale) {
        console.debug(`✅ Data still fresh, skipping update: ${configKey}`);
        return;
      }

      // Perform background fetch
      const data = await config.queryFn();

      // Update cache with new data
      this.queryClient.setQueryData(config.queryKey, data);

      // Call success callback if provided
      if (config.onSuccess) {
        config.onSuccess(data);
      }

      this.stats.successfulUpdates++;
      this.stats.lastUpdateTime = new Date();

      console.debug(`✅ Background update completed: ${configKey}`);
    } catch (error) {
      console.error(`❌ Background update failed: ${configKey}`, error);

      // Call error callback if provided
      if (config.onError) {
        config.onError(error);
      }

      this.stats.failedUpdates++;
    } finally {
      this.activeUpdates.delete(configKey);
      this.stats.activeUpdates = this.activeUpdates.size;
    }
  }

  /**
   * Trigger immediate background update for specific query
   */
  async triggerUpdate(queryKey: readonly any[]): Promise<void> {
    const configKey = this.generateConfigKey(queryKey);
    const config = this.updateConfigs.get(configKey);

    if (config) {
      this.queueUpdate(configKey);
    } else {
      console.warn(
        `No background update configuration found for query: ${JSON.stringify(
          queryKey
        )}`
      );
    }
  }

  /**
   * Add or update background update configuration
   */
  addUpdateConfig(config: BackgroundUpdateConfig): void {
    const configKey = this.generateConfigKey(config.queryKey);
    this.updateConfigs.set(configKey, config);
    this.scheduleBackgroundUpdate(configKey, config);

    console.debug(`🔧 Added background update config: ${configKey}`);
  }

  /**
   * Remove background update configuration
   */
  removeUpdateConfig(queryKey: readonly any[]): void {
    const configKey = this.generateConfigKey(queryKey);

    // Clear interval
    const interval = this.updateIntervals.get(configKey);
    if (interval) {
      clearInterval(interval);
      this.updateIntervals.delete(configKey);
    }

    // Remove from configs
    this.updateConfigs.delete(configKey);

    // Remove from queue if present
    const queueIndex = this.updateQueue.indexOf(configKey);
    if (queueIndex !== -1) {
      this.updateQueue.splice(queueIndex, 1);
      this.stats.queuedUpdates = this.updateQueue.length;
    }

    console.debug(`🗑️ Removed background update config: ${configKey}`);
  }

  /**
   * Enable/disable background updates globally
   */
  setEnabled(enabled: boolean): void {
    for (const [configKey, config] of this.updateConfigs.entries()) {
      config.enabled = enabled;
      if (enabled) {
        this.scheduleBackgroundUpdate(configKey, config);
      } else {
        const interval = this.updateIntervals.get(configKey);
        if (interval) {
          clearInterval(interval);
          this.updateIntervals.delete(configKey);
        }
      }
    }

    console.debug(`🔄 Background updates ${enabled ? "enabled" : "disabled"}`);
  }

  /**
   * Get background update statistics
   */
  getStats(): BackgroundUpdateStats {
    return { ...this.stats };
  }

  /**
   * Get current configurations
   */
  getConfigurations(): BackgroundUpdateConfig[] {
    return Array.from(this.updateConfigs.values());
  }

  /**
   * Clear all pending updates
   */
  clearQueue(): void {
    this.updateQueue.length = 0;
    this.stats.queuedUpdates = 0;
    console.debug("🧹 Background update queue cleared");
  }

  /**
   * Placeholder fetch functions (to be replaced with actual API calls)
   */
  private async fetchCalculationsList(): Promise<any> {
    // This would be replaced with actual API call
    return [];
  }

  private async fetchPackagesList(): Promise<any> {
    // This would be replaced with actual API call
    return [];
  }

  private async fetchClientsList(): Promise<any> {
    // This would be replaced with actual API call
    return [];
  }

  private async fetchCategoriesList(): Promise<any> {
    // This would be replaced with actual API call
    return [];
  }

  private async fetchCitiesList(): Promise<any> {
    // This would be replaced with actual API call
    return [];
  }

  /**
   * Cleanup on service destruction
   */
  destroy(): void {
    // Clear all intervals
    for (const interval of this.updateIntervals.values()) {
      clearInterval(interval);
    }
    this.updateIntervals.clear();
    this.updateConfigs.clear();
    this.updateQueue.length = 0;
    this.activeUpdates.clear();

    console.debug("🧹 Background cache updates service destroyed");
  }
}

// Export singleton instance
let backgroundCacheUpdatesInstance: BackgroundCacheUpdatesService | null = null;

export const createBackgroundCacheUpdates = (
  queryClient: QueryClient
): BackgroundCacheUpdatesService => {
  if (!backgroundCacheUpdatesInstance) {
    backgroundCacheUpdatesInstance = new BackgroundCacheUpdatesService(
      queryClient
    );
  }
  return backgroundCacheUpdatesInstance;
};

export const getBackgroundCacheUpdates =
  (): BackgroundCacheUpdatesService | null => {
    return backgroundCacheUpdatesInstance;
  };
