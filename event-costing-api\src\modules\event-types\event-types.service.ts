import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import {
  EventTypeDto,
  CreateEventTypeDto,
  UpdateEventTypeDto,
} from './dto/event-type.dto';

@Injectable()
export class EventTypesService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async findAll(): Promise<EventTypeDto[]> {
    const { data, error } = await this.supabaseService
      .getClient()
      .from('event_types')
      .select('*')
      .eq('is_active', true)
      .order('display_order', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch event types: ${error.message}`);
    }

    return data || [];
  }

  async findAllAdmin(): Promise<EventTypeDto[]> {
    const { data, error } = await this.supabaseService
      .getClient()
      .from('event_types')
      .select('*')
      .order('display_order', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch event types: ${error.message}`);
    }

    return data || [];
  }

  async findOne(id: string): Promise<EventTypeDto> {
    const { data, error } = await this.supabaseService
      .getClient()
      .from('event_types')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw new NotFoundException(`Event type with ID ${id} not found`);
    }

    return data;
  }

  async create(createEventTypeDto: CreateEventTypeDto): Promise<EventTypeDto> {
    // Check if name or code already exists
    const { data: existing } = await this.supabaseService
      .getClient()
      .from('event_types')
      .select('id, name, code')
      .or(
        `name.eq.${createEventTypeDto.name},code.eq.${createEventTypeDto.code}`,
      );

    if (existing && existing.length > 0) {
      const existingItem = existing[0];
      if (existingItem.name === createEventTypeDto.name) {
        throw new ConflictException(
          `Event type with name "${createEventTypeDto.name}" already exists`,
        );
      }
      if (existingItem.code === createEventTypeDto.code) {
        throw new ConflictException(
          `Event type with code "${createEventTypeDto.code}" already exists`,
        );
      }
    }

    const eventTypeData = {
      ...createEventTypeDto,
      color: createEventTypeDto.color || 'blue',
      display_order: createEventTypeDto.display_order || 9999,
    };

    const { data, error } = await this.supabaseService
      .getClient()
      .from('event_types')
      .insert(eventTypeData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create event type: ${error.message}`);
    }

    return data;
  }

  async update(
    id: string,
    updateEventTypeDto: UpdateEventTypeDto,
  ): Promise<EventTypeDto> {
    // Check if the event type exists
    await this.findOne(id);

    // Check for conflicts if name or code is being updated
    if (updateEventTypeDto.name || updateEventTypeDto.code) {
      const conditions: string[] = [];
      if (updateEventTypeDto.name) {
        conditions.push(`name.eq.${updateEventTypeDto.name}`);
      }
      if (updateEventTypeDto.code) {
        conditions.push(`code.eq.${updateEventTypeDto.code}`);
      }

      const { data: existing } = await this.supabaseService
        .getClient()
        .from('event_types')
        .select('id, name, code')
        .neq('id', id)
        .or(conditions.join(','));

      if (existing && existing.length > 0) {
        const existingItem = existing[0];
        if (
          updateEventTypeDto.name &&
          existingItem.name === updateEventTypeDto.name
        ) {
          throw new ConflictException(
            `Event type with name "${updateEventTypeDto.name}" already exists`,
          );
        }
        if (
          updateEventTypeDto.code &&
          existingItem.code === updateEventTypeDto.code
        ) {
          throw new ConflictException(
            `Event type with code "${updateEventTypeDto.code}" already exists`,
          );
        }
      }
    }

    const updateData = {
      ...updateEventTypeDto,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.supabaseService
      .getClient()
      .from('event_types')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update event type: ${error.message}`);
    }

    return data;
  }

  async remove(id: string): Promise<void> {
    // Check if the event type exists
    await this.findOne(id);

    // Soft delete by setting is_active to false
    const { error } = await this.supabaseService
      .getClient()
      .from('event_types')
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete event type: ${error.message}`);
    }
  }

  async getEventTypeByCode(code: string): Promise<EventTypeDto | null> {
    const { data, error } = await this.supabaseService
      .getClient()
      .from('event_types')
      .select('*')
      .eq('code', code)
      .eq('is_active', true)
      .single();

    if (error) {
      return null;
    }

    return data;
  }
}
