import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';

/**
 * Event types for authentication events
 */
export enum AuthEventType {
  LOGIN_ATTEMPT = 'LOGIN_ATTEMPT',
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  REGISTRATION = 'REGISTRATION',
  TOKEN_REFRESH = 'TOKEN_REFRESH',
  TOKEN_REFRESH_FAILURE = 'TOKEN_REFRESH_FAILURE',
  PASSWORD_RESET_REQUEST = 'PASSWORD_RESET_REQUEST',
  PASSWORD_RESET = 'PASSWORD_RESET',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
}

/**
 * Severity levels for authentication events
 */
export enum AuthEventSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL',
}

/**
 * Interface for authentication event data
 */
export interface AuthEventData {
  userId?: string;
  email?: string;
  ip?: string;
  userAgent?: string;
  fingerprint?: string;
  result?: 'success' | 'failure';
  reason?: string;
  metadata?: Record<string, any>;
}

/**
 * Service for logging authentication events
 */
@Injectable()
export class AuthEventLoggerService {
  private readonly logger = new Logger(AuthEventLoggerService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Log an authentication event
   * @param eventType The type of authentication event
   * @param severity The severity of the event
   * @param data Additional data about the event
   * @param correlationId Optional correlation ID for tracking related events
   * @returns The correlation ID for the event
   */
  logEvent(
    eventType: AuthEventType,
    severity: AuthEventSeverity,
    data: AuthEventData,
    correlationId?: string,
  ): string {
    // Generate a correlation ID if not provided
    const eventCorrelationId = correlationId || uuidv4();

    // Create the event object
    const event = {
      timestamp: new Date().toISOString(),
      eventType,
      severity,
      correlationId: eventCorrelationId,
      ...data,
    };

    // Log the event based on severity
    switch (severity) {
      case AuthEventSeverity.INFO:
        this.logger.log(`Auth Event [${eventType}]: ${JSON.stringify(event)}`);
        break;
      case AuthEventSeverity.WARNING:
        this.logger.warn(`Auth Event [${eventType}]: ${JSON.stringify(event)}`);
        break;
      case AuthEventSeverity.ERROR:
      case AuthEventSeverity.CRITICAL:
        this.logger.error(
          `Auth Event [${eventType}]: ${JSON.stringify(event)}`,
        );
        break;
      default:
        this.logger.log(`Auth Event [${eventType}]: ${JSON.stringify(event)}`);
    }

    // In a production environment, you might want to store events in a database
    // or send them to a monitoring service
    this.storeEvent(event);

    return eventCorrelationId;
  }

  /**
   * Log a login attempt
   * @param data Data about the login attempt
   * @param correlationId Optional correlation ID
   * @returns The correlation ID for the event
   */
  logLoginAttempt(data: AuthEventData, correlationId?: string): string {
    return this.logEvent(
      AuthEventType.LOGIN_ATTEMPT,
      AuthEventSeverity.INFO,
      data,
      correlationId,
    );
  }

  /**
   * Log a successful login
   * @param data Data about the successful login
   * @param correlationId Optional correlation ID
   * @returns The correlation ID for the event
   */
  logLoginSuccess(data: AuthEventData, correlationId?: string): string {
    return this.logEvent(
      AuthEventType.LOGIN_SUCCESS,
      AuthEventSeverity.INFO,
      {
        ...data,
        result: 'success',
      },
      correlationId,
    );
  }

  /**
   * Log a failed login
   * @param data Data about the failed login
   * @param correlationId Optional correlation ID
   * @returns The correlation ID for the event
   */
  logLoginFailure(data: AuthEventData, correlationId?: string): string {
    return this.logEvent(
      AuthEventType.LOGIN_FAILURE,
      AuthEventSeverity.WARNING,
      {
        ...data,
        result: 'failure',
      },
      correlationId,
    );
  }

  /**
   * Log a logout event
   * @param data Data about the logout
   * @param correlationId Optional correlation ID
   * @returns The correlation ID for the event
   */
  logLogout(data: AuthEventData, correlationId?: string): string {
    return this.logEvent(
      AuthEventType.LOGOUT,
      AuthEventSeverity.INFO,
      data,
      correlationId,
    );
  }

  /**
   * Log a token refresh event
   * @param data Data about the token refresh
   * @param correlationId Optional correlation ID
   * @returns The correlation ID for the event
   */
  logTokenRefresh(data: AuthEventData, correlationId?: string): string {
    return this.logEvent(
      AuthEventType.TOKEN_REFRESH,
      AuthEventSeverity.INFO,
      {
        ...data,
        result: 'success',
      },
      correlationId,
    );
  }

  /**
   * Log a failed token refresh
   * @param data Data about the failed token refresh
   * @param correlationId Optional correlation ID
   * @returns The correlation ID for the event
   */
  logTokenRefreshFailure(data: AuthEventData, correlationId?: string): string {
    return this.logEvent(
      AuthEventType.TOKEN_REFRESH_FAILURE,
      AuthEventSeverity.WARNING,
      {
        ...data,
        result: 'failure',
      },
      correlationId,
    );
  }

  /**
   * Store an event in a persistent storage
   * @param event The event to store
   * @private
   */
  private storeEvent(event: any): void {
    // In a production environment, you would store the event in a database
    // or send it to a monitoring service
    // For now, we'll just log it
    if (this.configService.get<string>('NODE_ENV') === 'production') {
      // TODO: Implement event storage
    }
  }
}
