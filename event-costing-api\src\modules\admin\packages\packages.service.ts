import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { PackageDto } from './dto/package.dto';
import { PackageListQueryDto } from './dto/package-list-query.dto';
import { BatchUpdatePackagesDto } from './dto/batch-update-packages.dto';
import { PaginatedResponseDto } from 'src/shared/dtos/paginated-response.dto';

// Import specialized services
import { PackageCrudService } from './services/package-crud.service';
import { PackageQueryService } from './services/package-query.service';
import { PackageRelationsService } from './services/package-relations.service';
import { PackageBatchService } from './services/package-batch.service';
import { PackageTransformerUtil } from './utils/package-transformer.util';

@Injectable()
export class PackagesService {
  private readonly logger = new Logger(PackagesService.name);

  constructor(
    private readonly packageCrudService: PackageCrudService,
    private readonly packageQueryService: PackageQueryService,
    private readonly packageRelationsService: PackageRelationsService,
    private readonly packageBatchService: PackageBatchService,
  ) {}

  /**
   * Create a new package with all related data
   * @param createPackageDto - Package creation data
   * @returns Created package with all relations
   */
  async create(createPackageDto: CreatePackageDto): Promise<PackageDto> {
    this.logger.log(
      `Attempting to create package: ${JSON.stringify(createPackageDto)}`,
    );

    try {
      // 1. Create the base package
      const packageId = await this.packageCrudService.createPackage(createPackageDto);

      // 2. Handle city associations
      await this.packageRelationsService.createCityAssociations(
        packageId,
        createPackageDto.city_ids || [],
      );

      // 3. Handle venue associations
      await this.packageRelationsService.createVenueAssociations(
        packageId,
        createPackageDto.enable_venues || false,
        createPackageDto.venue_ids,
      );

      // 4. Handle price information
      await this.packageRelationsService.createPriceInformation(
        packageId,
        createPackageDto,
      );

      // 5. Return the complete package data
      return this.findOne(packageId);
    } catch (error) {
      this.logger.error(
        `Transaction failed during package creation: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Find all packages with filtering, pagination, and related data
   * @param queryDto - Query parameters
   * @returns Paginated list of packages with related data
   */
  async findAll(
    queryDto: PackageListQueryDto,
  ): Promise<PaginatedResponseDto<PackageDto>> {
    this.logger.log(
      `Finding all packages with query: ${JSON.stringify(queryDto)}`,
    );

    // 1. Get paginated packages
    const paginatedResult = await this.packageQueryService.findAllPackages(queryDto);

    if (!paginatedResult.data || paginatedResult.data.length === 0) {
      return paginatedResult;
    }

    // 2. Get package IDs for related data queries
    const packageIds = this.packageQueryService.getPackageIds(paginatedResult.data);

    // 3. Fetch all related data in parallel
    const [categoriesMap, divisionsMap, pricesMap, citiesMap] = await Promise.all([
      this.packageQueryService.fetchCategoriesMap(
        PackageTransformerUtil.extractUniqueIds(paginatedResult.data, 'category_id'),
      ),
      this.packageQueryService.fetchDivisionsMap(
        PackageTransformerUtil.extractUniqueIds(paginatedResult.data, 'division_id'),
      ),
      this.packageQueryService.fetchPackagePricesMap(packageIds),
      this.packageQueryService.fetchPackageCitiesMap(packageIds),
    ]);

    // 4. Transform packages with related data
    const transformedData = PackageTransformerUtil.transformPackagesWithRelations(
      paginatedResult.data,
      categoriesMap,
      divisionsMap,
      pricesMap,
      citiesMap,
    );

    return {
      data: transformedData,
      count: paginatedResult.count,
      limit: paginatedResult.limit,
      offset: paginatedResult.offset,
    };
  }

  /**
   * Find a single package by ID with all related data
   * @param id - Package ID
   * @returns Package with all related data
   */
  async findOne(id: string): Promise<PackageDto> {
    this.logger.log(`Attempting to fetch package with ID: ${id}`);

    // 1. Get the base package data
    const packageData = await this.packageQueryService.findPackageById(id);

    // 2. Fetch related data in parallel
    const [categoriesMap, divisionsMap, pricesMap, citiesMap] = await Promise.all([
      packageData.category_id
        ? this.packageQueryService.fetchCategoriesMap([packageData.category_id])
        : Promise.resolve(new Map()),
      packageData.division_id
        ? this.packageQueryService.fetchDivisionsMap([packageData.division_id])
        : Promise.resolve(new Map()),
      this.packageQueryService.fetchPackagePricesMap([id]),
      this.packageQueryService.fetchPackageCitiesMap([id]),
    ]);

    // 3. Transform the package with related data
    const transformedPackage = PackageTransformerUtil.transformSinglePackageWithRelations(
      packageData,
      packageData.category_id ? categoriesMap.get(packageData.category_id) : undefined,
      packageData.division_id ? divisionsMap.get(packageData.division_id) : undefined,
      pricesMap.get(id),
      citiesMap.get(id),
    );

    this.logger.log(
      `Package with ID ${id} fetched successfully with related data.`,
    );

    return transformedPackage;
  }

  /**
   * Update a package with all related data
   * @param id - Package ID
   * @param updatePackageDto - Update data
   * @returns Updated package with all relations
   */
  async update(
    id: string,
    updatePackageDto: UpdatePackageDto,
  ): Promise<PackageDto> {
    this.logger.log(
      `Attempting to update package ID ${id} with data: ${JSON.stringify(
        updatePackageDto,
      )}`,
    );

    try {
      // 1. Update the base package data
      await this.packageCrudService.updatePackage(id, updatePackageDto);

      // 2. Update city associations
      await this.packageRelationsService.updateCityAssociations(
        id,
        updatePackageDto.city_ids,
      );

      // 3. Update venue associations
      await this.packageRelationsService.updateVenueAssociations(
        id,
        updatePackageDto.enable_venues,
        updatePackageDto.venue_ids,
      );

      // 4. Update price information
      await this.packageRelationsService.updatePriceInformation(
        id,
        updatePackageDto,
      );

      // 5. Return the complete updated package data
      return this.findOne(id);
    } catch (error) {
      this.logger.error(
        `Transaction failed during package update: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Remove a package (soft delete)
   * @param id - Package ID
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`Attempting to soft delete package with ID: ${id}`);
    await this.packageCrudService.deletePackage(id);
  }

  /**
   * Update package status (active/inactive)
   * @param id - Package ID
   * @param isActive - Whether the package should be active
   * @returns Updated package data
   */
  async updateStatus(id: string, isActive: boolean): Promise<PackageDto> {
    this.logger.log(
      `Attempting to update status for package ID ${id} to ${isActive ? 'active' : 'inactive'}`,
    );

    const updatedPackage = await this.packageCrudService.updatePackageStatus(id, isActive);
    return updatedPackage;
  }

  /**
   * Batch update multiple packages
   * @param batchUpdateDto - Batch update data
   * @returns Summary of the batch operation
   */
  async batchUpdate(
    batchUpdateDto: BatchUpdatePackagesDto,
  ): Promise<{ updatedCount: number; errors: any[] }> {
    this.logger.log(
      `Attempting to batch update ${batchUpdateDto.packages.length} packages`,
    );

    return this.packageBatchService.batchUpdatePackages(batchUpdateDto);
  }
}
