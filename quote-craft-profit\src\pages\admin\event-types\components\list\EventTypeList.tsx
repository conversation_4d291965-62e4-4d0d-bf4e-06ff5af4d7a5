import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Trash2, <PERSON>ert<PERSON><PERSON>cle, Loader2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { deleteEventType, EventType } from "@/services/admin/event-types";

interface EventTypeListProps {
  eventTypes: EventType[];
  isLoading: boolean;
  isError: boolean;
  onEdit: (eventTypeId: string) => void;
  onRefresh: () => void;
}

const EventTypeList: React.FC<EventTypeListProps> = ({
  eventTypes,
  isLoading,
  isError,
  onEdit,
  onRefresh,
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [eventTypeToDelete, setEventTypeToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteClick = (eventTypeId: string) => {
    setEventTypeToDelete(eventTypeId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!eventTypeToDelete) return;

    setIsDeleting(true);
    try {
      await deleteEventType(eventTypeToDelete);
      toast.success("Event type deleted successfully");
      onRefresh();
    } catch (error) {
      console.error("Error deleting event type:", error);
      toast.error("Failed to delete event type");
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setEventTypeToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setEventTypeToDelete(null);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">Loading event types...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h3 className="text-lg font-semibold mb-2">Error Loading Event Types</h3>
        <p className="text-muted-foreground mb-4">
          There was a problem loading the event types. Please try again.
        </p>
        <Button onClick={onRefresh} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (eventTypes.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <h3 className="text-lg font-semibold mb-2">No Event Types Found</h3>
        <p className="text-muted-foreground">
          Get started by creating your first event type.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-16">#</TableHead>
              <TableHead className="w-[25%]">Name</TableHead>
              <TableHead className="w-[15%]">Code</TableHead>
              <TableHead className="w-[30%]">Description</TableHead>
              <TableHead className="w-[10%]">Color</TableHead>
              <TableHead className="w-[10%]">Order</TableHead>
              <TableHead className="w-[10%]">Status</TableHead>
              <TableHead className="w-[15%] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {eventTypes.map((eventType, index) => (
              <TableRow key={eventType.id}>
                <TableCell className="text-muted-foreground">{index + 1}</TableCell>
                <TableCell className="font-medium">
                  <div className="flex items-center space-x-2">
                    {eventType.icon && (
                      <span className="text-lg">{eventType.icon}</span>
                    )}
                    <span>{eventType.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <code className="bg-muted px-2 py-1 rounded text-sm">
                    {eventType.code}
                  </code>
                </TableCell>
                <TableCell className="truncate max-w-[200px]">
                  {eventType.description || "-"}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: eventType.color }}
                    />
                    <span className="text-sm capitalize">{eventType.color}</span>
                  </div>
                </TableCell>
                <TableCell>{eventType.display_order}</TableCell>
                <TableCell>
                  <Badge variant={eventType.is_active ? "default" : "secondary"}>
                    {eventType.is_active ? "Active" : "Inactive"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(eventType.id)}
                    >
                      <Pencil className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteClick(eventType.id)}
                      disabled={isDeleting}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Event Type</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this event type? This action will deactivate
              the event type and it will no longer be available for new events.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default EventTypeList;
