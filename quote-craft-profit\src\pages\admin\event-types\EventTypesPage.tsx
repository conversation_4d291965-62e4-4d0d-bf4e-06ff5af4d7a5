import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import AdminLayout from "@/components/layout/AdminLayout";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { getAllEventTypesAdmin } from "@/services/admin/event-types";
import EventTypeList from "./components/list/EventTypeList";
import EventTypeFormDialog from "./components/form/EventTypeFormDialog";

const EventTypesPage: React.FC = () => {
  const [isEventTypeFormOpen, setIsEventTypeFormOpen] = useState(false);
  const [editingEventTypeId, setEditingEventTypeId] = useState<string | null>(null);

  // Fetch event types using the admin service
  const {
    data: eventTypes,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["admin-event-types"],
    queryFn: getAllEventTypesAdmin,
    meta: {
      onError: () => {
        toast.error("Failed to load event types");
      },
    },
  });

  const handleOpenNewEventTypeForm = () => {
    setEditingEventTypeId(null);
    setIsEventTypeFormOpen(true);
  };

  const handleEditEventType = (eventTypeId: string) => {
    setEditingEventTypeId(eventTypeId);
    setIsEventTypeFormOpen(true);
  };

  const handleEventTypeFormClose = (shouldRefresh: boolean = false) => {
    setIsEventTypeFormOpen(false);
    setEditingEventTypeId(null);

    if (shouldRefresh) {
      refetch();
    }
  };

  return (
    <AdminLayout title="Manage Event Types">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Event Types</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="mb-6 text-muted-foreground">
        Create and manage event types to categorize different kinds of events in your system.
        Event types help users quickly identify and filter events in the Dashboard V2 wizard.
      </div>

      {/* Action Bar Block */}
      <div className="mb-6">
        <Button onClick={handleOpenNewEventTypeForm}>
          <Plus className="w-4 h-4 mr-2" /> Add New Event Type
        </Button>
      </div>

      {/* Event Types List Block */}
      <EventTypeList
        eventTypes={eventTypes || []}
        isLoading={isLoading}
        isError={isError}
        onEdit={handleEditEventType}
        onRefresh={refetch}
      />

      {/* Add/Edit Event Type Dialog */}
      <EventTypeFormDialog
        isOpen={isEventTypeFormOpen}
        onClose={handleEventTypeFormClose}
        eventTypeId={editingEventTypeId}
      />
    </AdminLayout>
  );
};

export default EventTypesPage;
