import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { PlusCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { FormValues } from '../../hooks/core/useCalculationForm';

interface AdditionalDetailsStepProps {
  form: UseFormReturn<FormValues>;
  events: {
    id: string;
    name: string;
  }[];
  cities: {
    id: string;
    name: string;
  }[];
  venues: {
    id: string;
    name: string;
    address?: string;
  }[];
  selectedCityId: string;
  onOpenEventForm: () => void;
  onCityChange: (cityId: string) => void;
}

const AdditionalDetailsStep: React.FC<AdditionalDetailsStepProps> = ({
  form,
  events,
  cities,
  venues,
  selectedCityId,
  onOpenEventForm,
  onCityChange,
}) => {
  return (
    <>
      <FormField
        control={form.control}
        name='event_id'
        render={({ field }) => (
          <FormItem>
            <div className='flex justify-between items-center'>
              <FormLabel>Associated Event (Optional)</FormLabel>
              <Button
                type='button'
                variant='ghost'
                size='sm'
                onClick={onOpenEventForm}
                className='h-8 px-2 text-xs'
              >
                <PlusCircle className='h-3.5 w-3.5 mr-1' />
                Add New
              </Button>
            </div>
            <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder='Select an event' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value='none'>None</SelectItem>
                {events
                  .filter((event) => event.id && event.id.trim() !== '') // Filter out events with empty IDs
                  .map((event) => (
                    <SelectItem key={event.id} value={event.id}>
                      {event.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='city_id'
        render={({ field }) => (
          <FormItem>
            <FormLabel>City</FormLabel>
            <Select
              onValueChange={(value) => {
                field.onChange(value);
                onCityChange(value);
              }}
              value={field.value}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder='Select a city' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {cities
                  .filter((city) => city.id && city.id.trim() !== '') // Filter out cities with empty IDs
                  .map((city) => (
                    <SelectItem key={city.id} value={city.id}>
                      {city.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='venue_ids'
        render={({ field }) => (
          <FormItem>
            <div className='flex justify-between items-center'>
              <FormLabel>Venues (Required)</FormLabel>
              {field.value.length > 0 && (
                <span className='text-xs text-muted-foreground'>
                  {field.value.length} venue
                  {field.value.length !== 1 ? 's' : ''} selected
                </span>
              )}
            </div>
            <div className='text-xs text-muted-foreground mb-2'>
              One city can have multiple venues. Select all that apply.
            </div>
            <div className='space-y-2 border rounded-md p-3 max-h-60 overflow-y-auto'>
              {venues.length > 0 ? (
                venues.map((venue) => (
                  <div
                    key={venue.id}
                    className='flex items-center space-x-2 py-1 px-2 hover:bg-muted/50 rounded-sm'
                  >
                    <Checkbox
                      id={`venue-${venue.id}`}
                      checked={field.value.includes(venue.id)}
                      onCheckedChange={(checked: boolean) => {
                        if (checked) {
                          field.onChange([...field.value, venue.id]);
                        } else {
                          field.onChange(field.value.filter((id) => id !== venue.id));
                        }
                      }}
                    />
                    <label
                      htmlFor={`venue-${venue.id}`}
                      className='text-sm font-medium text-gray-700 cursor-pointer flex-1'
                    >
                      {venue.name}
                      {venue.address && (
                        <span className='block text-xs text-muted-foreground'>
                          {venue.address}
                        </span>
                      )}
                    </label>
                  </div>
                ))
              ) : selectedCityId ? (
                <div className='text-sm text-gray-500 p-2'>
                  No venues available for this city
                </div>
              ) : (
                <div className='text-sm text-gray-500 p-2'>
                  Select a city first to see available venues
                </div>
              )}
            </div>
            <FormMessage />
            {field.value.length === 0 && (
              <p className='text-sm text-red-500 mt-1'>
                Please select at least one venue
              </p>
            )}
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='attendees'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Number of Attendees *</FormLabel>
            <FormControl>
              <Input
                type='number'
                min='1'
                placeholder='Enter number of attendees'
                {...field}
              />
            </FormControl>
            <FormMessage />
            <p className="text-xs text-muted-foreground">
              Enter the expected number of attendees for this event
            </p>
          </FormItem>
        )}
      />
    </>
  );
};

export default AdditionalDetailsStep;
