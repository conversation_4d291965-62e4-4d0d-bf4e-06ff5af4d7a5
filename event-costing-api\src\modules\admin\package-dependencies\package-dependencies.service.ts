import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PostgrestError } from '@supabase/supabase-js';
import { CreatePackageDependencyDto } from './dto/create-package-dependency.dto';
import { PackageDependencyDto } from './dto/package-dependency.dto';

@Injectable()
export class PackageDependenciesService {
  private readonly logger = new Logger(PackageDependenciesService.name);
  private readonly TABLE_NAME = 'package_dependencies';

  constructor(private readonly supabaseService: SupabaseService) {}

  // Centralized error handler for PostgREST errors specific to this service
  private handlePostgrestError(error: PostgrestError, context?: string): never {
    this.logger.error(
      `Postgrest error in PackageDependenciesService (${context}): ${error.message}`,
      error.details,
    );

    const FK_VIOLATION = '23503';
    const UNIQUE_VIOLATION = '23505';
    const CHECK_VIOLATION = '23514'; // Common code for check constraints

    if (error.code === FK_VIOLATION) {
      // Determine which FK failed if possible from error.details
      if (error.details?.includes('package_id')) {
        throw new NotFoundException(
          `The package specified (${context === 'create' ? 'package_id' : 'dependent_package_id'}) does not exist. Details: ${error.details}`,
        );
      } else if (error.details?.includes('dependent_package_id')) {
        throw new NotFoundException(
          `The dependent package specified (${context === 'create' ? 'dependent_package_id' : 'package_id'}) does not exist. Details: ${error.details}`,
        );
      } else {
        throw new NotFoundException(
          `A related package was not found. Details: ${error.details}`,
        );
      }
    } else if (error.code === UNIQUE_VIOLATION) {
      // Assuming a unique constraint on (package_id, dependent_package_id, dependency_type)
      throw new ConflictException(
        `This dependency relationship already exists. Details: ${error.details}`,
      );
    } else if (error.code === CHECK_VIOLATION) {
      // Example: check constraint preventing package_id == dependent_package_id
      if (error.message.includes('package_cannot_depend_on_itself')) {
        // Adjust if constraint name differs
        throw new BadRequestException('A package cannot depend on itself.');
      }
      // Add more specific check constraint handling if needed
    }

    // Default fallback
    throw new InternalServerErrorException(
      `An unexpected database error occurred (${context}). Details: ${error.message}`,
    );
  }

  async create(
    packageId: string,
    createDto: CreatePackageDependencyDto,
  ): Promise<PackageDependencyDto> {
    this.logger.log(
      `Attempting to create dependency from package ${packageId} to ${createDto.dependent_package_id} of type ${createDto.dependency_type}`,
    );

    // Self-dependency check (could also be handled by DB constraint + handlePostgrestError)
    if (packageId === createDto.dependent_package_id) {
      this.logger.warn(
        `Attempt to create self-dependency for package ${packageId} blocked.`,
      );
      throw new BadRequestException('A package cannot depend on itself.');
    }

    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .insert([
        {
          package_id: packageId,
          dependent_package_id: createDto.dependent_package_id,
          dependency_type: createDto.dependency_type,
          description: createDto.description, // Include description if provided
        },
      ])
      .select('*')
      .single<PackageDependencyDto>();

    if (error) {
      this.handlePostgrestError(error, 'create'); // Pass context
    }

    if (!data) {
      this.logger.error(
        'Failed to create package dependency, insert operation returned no data.',
      );
      throw new InternalServerErrorException(
        'Failed to create package dependency due to an unexpected error.',
      );
    }

    this.logger.log(
      `Successfully created dependency ${data.id} for package ${packageId}`,
    );
    // Map DB response to DTO explicitly if needed, otherwise assume direct compatibility
    return data;
  }

  async findAllByPackage(packageId: string): Promise<PackageDependencyDto[]> {
    this.logger.log(`Finding all dependencies for package ${packageId}`);

    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select(`
        *,
        dependent_package:packages!dependent_package_id(
          name
        )
      `)
      .eq('package_id', packageId)
      .returns<any[]>();

    if (error) {
      this.handlePostgrestError(error, 'findAllByPackage'); // Pass context
    }

    this.logger.log(
      `Found ${data?.length ?? 0} dependencies for package ${packageId}`,
    );

    // Map the response to include the dependent package name
    const mappedData = data?.map(item => ({
      ...item,
      dependent_package: item.dependent_package ? {
        name: item.dependent_package.name
      } : null
    })) ?? [];

    return mappedData;
  }

  async remove(dependencyId: string): Promise<void> {
    this.logger.log(`Attempting to remove dependency with ID ${dependencyId}`);

    const supabase = this.supabaseService.getClient();
    const { error, count } = await supabase
      .from(this.TABLE_NAME)
      .delete({ count: 'exact' }) // Ensure we get the count
      .eq('id', dependencyId);

    if (error) {
      this.handlePostgrestError(error, 'remove'); // Pass context
    }

    // Check if exactly one row was deleted
    if (count === 0) {
      this.logger.warn(
        `Dependency with ID ${dependencyId} not found for deletion.`,
      );
      throw new NotFoundException(
        `Dependency with ID ${dependencyId} not found.`,
      );
    }

    if (count === null || count > 1) {
      // Should not happen with a primary key 'id', but good to log
      this.logger.error(
        `Unexpected number of rows deleted (${count}) for dependency ID ${dependencyId}. Potential data integrity issue?`,
      );
      // Decide if this should be an InternalServerError or still proceed if count > 1?
      // Sticking with InternalServerError for safety.
      throw new InternalServerErrorException(
        'An unexpected error occurred during dependency deletion.',
      );
    }

    this.logger.log(`Dependency with ID ${dependencyId} removed successfully.`);
  }
}
