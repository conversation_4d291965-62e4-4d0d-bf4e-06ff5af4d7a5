import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';

interface NavigationCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  to: string;
}

const NavigationCard: React.FC<NavigationCardProps> = ({
  title,
  description,
  icon,
  to,
}) => {
  console.log(`NavigationCard - Rendering card for "${title}" with link to "${to}"`);

  return (
    <Link
      to={to}
      onClick={() =>
        console.log(`NavigationCard - Clicked on "${title}" card, navigating to "${to}"`)
      }
    >
      <Card className='hover:shadow-md transition-shadow h-full'>
        <CardContent className='p-6 flex flex-col h-full'>
          <div className='rounded-full p-3 bg-primary/10 text-primary w-12 h-12 flex items-center justify-center mb-4'>
            {icon}
          </div>
          <h3 className='text-lg font-medium mb-2'>{title}</h3>
          <p className='text-sm text-muted-foreground'>{description}</p>
        </CardContent>
      </Card>
    </Link>
  );
};

export default NavigationCard;
