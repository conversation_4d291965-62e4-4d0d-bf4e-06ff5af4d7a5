import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  MaxLength,
} from 'class-validator';

export class CreateClientDto {
  @ApiProperty({ description: 'The name of the client contact or company' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255) // Assuming a reasonable max length
  client_name: string;

  @ApiPropertyOptional({ description: 'The primary contact person' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  contact_person?: string;

  @ApiPropertyOptional({ description: 'Client email address' })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiProperty({ description: 'Client phone number' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50) // Assuming a reasonable max length for phone
  phone: string;

  @ApiPropertyOptional({ description: 'Client address' })
  @IsOptional()
  @IsString()
  address?: string; // Text type in DB, no strict length

  @ApiProperty({ description: 'Associated company name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  company_name: string;
}
