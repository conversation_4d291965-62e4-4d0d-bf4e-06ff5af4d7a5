import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  Package,
  Calendar,
  Users,
  Tag,
  MapPin,
  DollarSign,
} from "lucide-react";
import { toast } from "sonner";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";
import { useQuery } from "@tanstack/react-query";
import {
  getTemplateDetailsById,
  calculateTemplateTotal,
  formatCurrency,
} from "@/services/admin/templates";
import { getVenueById } from "@/services/shared/entities/venues";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface TemplateDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  templateId: string | null;
}

const TemplateDetailsDialog: React.FC<TemplateDetailsDialogProps> = ({
  isOpen,
  onClose,
  templateId,
}) => {
  // State to store venue names
  const [venueNames, setVenueNames] = useState<Record<string, string>>({});
  const { formatForDisplay } = useTimezoneAwareDates();

  // Fetch template details when dialog is opened
  const {
    data: template,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["templateDetails", templateId],
    queryFn: () => getTemplateDetailsById(templateId!),
    enabled: isOpen && !!templateId,
    meta: {
      onError: (error: Error) => {
        toast.error(`Failed to load template details: ${error.message}`);
      },
    },
  });

  // Calculate template total value
  const {
    data: calculationResult,
    isLoading: isCalculating,
    isError: isCalculationError,
  } = useQuery({
    queryKey: ["templateCalculation", templateId],
    queryFn: () => calculateTemplateTotal(template!),
    enabled:
      !!template &&
      template.package_selections &&
      template.package_selections.length > 0,
    meta: {
      onError: (error: Error) => {
        console.error("Failed to calculate template total:", error);
        // Don't show toast for calculation errors as it's not critical
      },
    },
  });

  // Fetch venue names when template is loaded
  useEffect(() => {
    if (template?.venue_ids && template.venue_ids.length > 0) {
      const fetchVenueNames = async () => {
        const venueNamesMap: Record<string, string> = {};

        for (const venueId of template.venue_ids) {
          try {
            const venue = await getVenueById(venueId);
            venueNamesMap[venueId] = venue.name;
          } catch (error) {
            console.error(`Error fetching venue ${venueId}:`, error);
            venueNamesMap[venueId] = "Unknown Venue";
          }
        }

        setVenueNames(venueNamesMap);
      };

      fetchVenueNames();
    }
  }, [template?.venue_ids]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Template Details</DialogTitle>
          <DialogDescription>
            View detailed information about this template.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading template details...</span>
          </div>
        ) : isError ? (
          <div className="text-center p-4 text-destructive">
            <p>Error loading template details.</p>
            <p className="text-sm">
              {(error as Error)?.message || "Unknown error"}
            </p>
          </div>
        ) : template ? (
          <ScrollArea className="flex-1 pr-4">
            <div className="space-y-6">
              {/* Basic Info */}
              <div>
                <h3 className="text-lg font-semibold">{template.name}</h3>
                <p className="text-muted-foreground mt-1">
                  {template.description || "No description provided"}
                </p>
                <div className="flex items-center mt-2">
                  <Badge variant={template.is_public ? "default" : "outline"}>
                    {template.is_public ? "Public" : "Private"}
                  </Badge>
                  {template.event_type && (
                    <Badge variant="secondary" className="ml-2">
                      {template.event_type}
                    </Badge>
                  )}
                </div>
              </div>

              <Separator />

              {/* Details */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Start Date</p>
                    <p className="text-sm text-muted-foreground">
                      {template.template_start_date
                        ? formatForDisplay(
                            template.template_start_date,
                            "MMM d, yyyy"
                          )
                        : "N/A"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">End Date</p>
                    <p className="text-sm text-muted-foreground">
                      {template.template_end_date
                        ? formatForDisplay(
                            template.template_end_date,
                            "MMM d, yyyy"
                          )
                        : "N/A"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Attendees</p>
                    <p className="text-sm text-muted-foreground">
                      {template.attendees || "Not specified"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Tag className="h-4 w-4 mr-2 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Created</p>
                    <p className="text-sm text-muted-foreground">
                      {template.created_at
                        ? formatForDisplay(template.created_at, "MMM d, yyyy")
                        : "N/A"}
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Estimated Total Value */}
              {template.package_selections &&
                template.package_selections.length > 0 && (
                  <>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center">
                          <DollarSign className="h-4 w-4 mr-2" />
                          Estimated Total Value
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {isCalculating ? (
                          <div className="flex items-center justify-center py-4">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span className="text-sm text-muted-foreground">
                              Calculating...
                            </span>
                          </div>
                        ) : isCalculationError ? (
                          <div className="text-center py-4">
                            <p className="text-sm text-destructive">
                              Failed to calculate total value
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Package prices may not be available
                            </p>
                          </div>
                        ) : calculationResult ? (
                          <div className="space-y-3">
                            {/* Summary */}
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">
                                Packages Total:
                              </span>
                              <span className="text-sm">
                                {formatCurrency(
                                  calculationResult.packagesTotal,
                                  calculationResult.currency
                                )}
                              </span>
                            </div>
                            {calculationResult.customItemsTotal > 0 && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">
                                  Custom Items:
                                </span>
                                <span className="text-sm">
                                  {formatCurrency(
                                    calculationResult.customItemsTotal,
                                    calculationResult.currency
                                  )}
                                </span>
                              </div>
                            )}
                            <Separator />
                            <div className="flex justify-between items-center">
                              <span className="font-semibold">
                                Grand Total:
                              </span>
                              <span className="font-semibold text-lg">
                                {formatCurrency(
                                  calculationResult.grandTotal,
                                  calculationResult.currency
                                )}
                              </span>
                            </div>

                            {/* Breakdown */}
                            {calculationResult.breakdown.length > 0 && (
                              <div className="mt-4">
                                <p className="text-xs font-medium text-muted-foreground mb-2">
                                  Package Breakdown:
                                </p>
                                <div className="space-y-2">
                                  {calculationResult.breakdown.map(
                                    (item, index) => (
                                      <div
                                        key={index}
                                        className="text-xs bg-muted/50 rounded p-2"
                                      >
                                        <div className="flex justify-between items-start">
                                          <div className="flex-1">
                                            <p className="font-medium">
                                              {item.packageName}
                                            </p>
                                            <p className="text-muted-foreground">
                                              {item.quantity} ×{" "}
                                              {formatCurrency(
                                                item.unitPrice,
                                                item.currency
                                              )}
                                            </p>
                                          </div>
                                          <span className="font-medium">
                                            {formatCurrency(
                                              item.totalPrice,
                                              item.currency
                                            )}
                                          </span>
                                        </div>
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Warnings */}
                            {!calculationResult.hasValidPrices && (
                              <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                                <p className="text-xs text-yellow-800 font-medium">
                                  ⚠️ Incomplete Pricing
                                </p>
                                <p className="text-xs text-yellow-700">
                                  Some packages may not have prices set for the
                                  selected currency.
                                </p>
                                {calculationResult.missingPrices.length > 0 && (
                                  <ul className="text-xs text-yellow-700 mt-1 list-disc list-inside">
                                    {calculationResult.missingPrices
                                      .slice(0, 3)
                                      .map((missing, index) => (
                                        <li key={index}>{missing}</li>
                                      ))}
                                    {calculationResult.missingPrices.length >
                                      3 && (
                                      <li>
                                        ... and{" "}
                                        {calculationResult.missingPrices
                                          .length - 3}{" "}
                                        more
                                      </li>
                                    )}
                                  </ul>
                                )}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-4">
                            <p className="text-sm text-muted-foreground">
                              No calculation data available
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    <Separator />
                  </>
                )}

              {/* Venues */}
              <div>
                <h4 className="text-sm font-semibold mb-2">Venues</h4>
                {template.venue_ids && template.venue_ids.length > 0 ? (
                  <div className="space-y-2">
                    {template.venue_ids.map((venueId) => (
                      <div key={venueId} className="flex items-center">
                        <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                        <p className="text-sm">
                          {venueNames[venueId] || "Loading venue..."}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No venues associated
                  </p>
                )}
              </div>

              <Separator />

              {/* Package Selections */}
              <div>
                <h4 className="text-sm font-semibold mb-2">
                  Package Selections
                </h4>
                {template.package_selections &&
                template.package_selections.length > 0 ? (
                  <div className="space-y-3">
                    {template.package_selections.map((selection, index) => (
                      <div
                        key={`${selection.package_id}-${index}`}
                        className="border rounded-md p-3"
                      >
                        <div className="flex items-center">
                          <Package className="h-4 w-4 mr-2 text-muted-foreground" />
                          <p className="text-sm font-medium">
                            Package ID: {selection.package_id}
                          </p>
                        </div>
                        {selection.option_ids &&
                          selection.option_ids.length > 0 && (
                            <div className="mt-2">
                              <p className="text-xs text-muted-foreground mb-1">
                                Selected Options:
                              </p>
                              <div className="flex flex-wrap gap-1">
                                {selection.option_ids.map((optionId) => (
                                  <Badge
                                    key={optionId}
                                    variant="outline"
                                    className="text-xs"
                                  >
                                    {optionId}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No packages selected
                  </p>
                )}
              </div>
            </div>
          </ScrollArea>
        ) : (
          <div className="text-center p-4">
            <p>No template data available</p>
          </div>
        )}

        <DialogFooter className="mt-4">
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TemplateDetailsDialog;
