import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  IsEnum,
  MaxLength,
  IsArray,
  IsBoolean,
  IsNumber,
  Validate<PERSON>f,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
// Assuming enum definition lives here or is imported correctly elsewhere
// import { PackageQuantityBasis } from 'src/shared/enums/package-quantity-basis.enum';

// Temporary enum definition for validation - replace with actual import
enum PackageQuantityBasis {
  PER_EVENT = 'PER_EVENT',
  PER_DAY = 'PER_DAY',
  PER_ATTENDEE = 'PER_ATTENDEE',
  PER_ITEM = 'PER_ITEM',
  PER_ITEM_PER_DAY = 'PER_ITEM_PER_DAY',
  PER_ATTENDEE_PER_DAY = 'PER_ATTENDEE_PER_DAY',
}

export class CreatePackageDto {
  @ApiProperty({
    description: 'Name of the package',
    example: 'Standard Wedding Photography',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255) // Example length constraint
  name: string;

  @ApiPropertyOptional({
    description: 'Detailed description of the package',
    example: 'Includes 8 hours coverage, 2 photographers...',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Category ID for the package',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsOptional()
  @IsUUID()
  category_id?: string;

  @ApiPropertyOptional({
    description: 'Division ID for the package',
    example: 'e47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsOptional()
  @IsUUID()
  division_id?: string;

  @ApiPropertyOptional({
    description: 'Variation group code for related package variations',
    example: 'WEDDING_PHOTO_V1',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100) // Example length constraint
  variation_group_code?: string;

  @ApiProperty({
    description: 'Defines how quantity and price interact for calculation',
    enum: PackageQuantityBasis,
    example: PackageQuantityBasis.PER_ATTENDEE,
  })
  @IsEnum(PackageQuantityBasis)
  @IsNotEmpty()
  quantity_basis: PackageQuantityBasis;

  @ApiPropertyOptional({
    description: 'Whether the package is deleted/inactive',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  is_deleted?: boolean;

  @ApiPropertyOptional({
    description: 'Array of city IDs where this package is available',
    example: [
      'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      'e47ac10b-58cc-4372-a567-0e02b2c3d479',
    ],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  city_ids?: string[];

  @ApiPropertyOptional({
    description: 'Whether to enable venue-specific availability',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  enable_venues?: boolean;

  @ApiPropertyOptional({
    description:
      'Array of venue IDs where this package is available (if enable_venues is true)',
    example: [
      'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      'e47ac10b-58cc-4372-a567-0e02b2c3d479',
    ],
    type: [String],
  })
  @ValidateIf(o => o.enable_venues === true)
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  venue_ids?: string[];

  @ApiPropertyOptional({
    description: 'Base price of the package',
    example: 1500000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  price?: number;

  @ApiPropertyOptional({
    description: 'Base cost of the package (internal)',
    example: 1000000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  unit_base_cost?: number;

  @ApiPropertyOptional({
    description: 'Currency ID for the price',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @ValidateIf(o => o.price !== undefined || o.unit_base_cost !== undefined)
  @IsUUID()
  currency_id?: string;
}
