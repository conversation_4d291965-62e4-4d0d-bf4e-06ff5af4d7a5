import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import { deleteLineItemFromApi } from "@/services/calculations/calculationApiService";
import { useCustomItems } from "@/hooks/useCustomItems";

interface LineItemDeleteButtonProps {
  /**
   * The ID of the calculation containing the line item
   */
  calculationId: string;

  /**
   * The ID of the line item to delete
   */
  lineItemId: string;

  /**
   * The name of the line item (for confirmation message)
   */
  lineItemName: string;

  /**
   * Whether this is a custom item or package item
   */
  isCustom?: boolean;

  /**
   * Optional callback after successful deletion
   */
  onDeleted?: () => void;

  /**
   * Optional variant for the button
   * @default "ghost"
   */
  variant?: "outline" | "destructive" | "ghost";

  /**
   * Optional size for the button
   * @default "icon"
   */
  size?: "sm" | "default" | "lg" | "icon";
}

/**
 * A button component that triggers a confirmation dialog before deleting a line item
 *
 * @example
 * ```tsx
 * <LineItemDeleteButton
 *   calculationId={calculationId}
 *   lineItemId={lineItem.id}
 *   lineItemName={lineItem.name}
 * />
 * ```
 */
export function LineItemDeleteButton({
  calculationId,
  lineItemId,
  lineItemName,
  isCustom = false,
  onDeleted,
  variant = "ghost",
  size = "icon",
}: LineItemDeleteButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  // Get custom items hook for custom item deletion
  const { deleteCustomItem, isDeletingCustomItem } =
    useCustomItems(calculationId);

  // Optimistic update helpers
  const previousLineItems = queryClient.getQueryData([
    "calculation",
    calculationId,
    "lineItems",
  ]);

  // Delete line item mutation
  const deleteMutation = useMutation({
    mutationFn: async () => {
      if (isCustom) {
        // Use the custom items service for custom items
        deleteCustomItem(lineItemId);
      } else {
        // Use the Backend API for package items
        await deleteLineItemFromApi(calculationId, lineItemId);
      }
    },
    onMutate: async () => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ["calculation", calculationId, "lineItems"],
      });

      // Optimistically update the UI
      queryClient.setQueryData(
        ["calculation", calculationId, "lineItems"],
        (old: any) => {
          if (!old) return old;
          return old.filter((item: any) => item.id !== lineItemId);
        }
      );

      // Return previous data for rollback
      return { previousLineItems };
    },
    onSuccess: () => {
      toast.success(`Item "${lineItemName}" removed successfully`);
      queryClient.invalidateQueries({
        queryKey: ["calculation", calculationId],
      });
      setIsDialogOpen(false);
      if (onDeleted) {
        onDeleted();
      }
    },
    onError: (error, _, context) => {
      console.error("Error removing line item:", error);
      toast.error(`Failed to remove item "${lineItemName}"`);

      // Rollback to previous state
      if (context?.previousLineItems) {
        queryClient.setQueryData(
          ["calculation", calculationId, "lineItems"],
          context.previousLineItems
        );
      }
    },
    onSettled: () => {
      // Always refetch after error or success to ensure data consistency
      queryClient.invalidateQueries({
        queryKey: ["calculation", calculationId, "lineItems"],
      });
    },
  });

  return (
    <ConfirmationDialog
      title="Remove Item"
      description={`Are you sure you want to remove "${lineItemName}" from this calculation? This action cannot be undone.`}
      confirmText="Remove"
      confirmVariant="destructive"
      cancelText="Cancel"
      open={isDialogOpen}
      onOpenChange={setIsDialogOpen}
      onConfirm={() => deleteMutation.mutate()}
      isLoading={isCustom ? isDeletingCustomItem : deleteMutation.isPending}
      trigger={
        <Button
          variant={variant}
          size={size}
          className="text-muted-foreground hover:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
          <span className="sr-only">Delete {lineItemName}</span>
        </Button>
      }
    />
  );
}
