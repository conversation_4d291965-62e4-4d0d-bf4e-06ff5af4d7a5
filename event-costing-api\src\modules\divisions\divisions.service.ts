import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { DivisionDto } from './dto/division.dto';
import { CreateDivisionDto } from './dto/create-division.dto';
import { UpdateDivisionDto } from './dto/update-division.dto';

@Injectable()
export class DivisionsService {
  private readonly logger = new Logger(DivisionsService.name);
  private readonly tableName = 'divisions';
  private readonly selectFields = 'id, code, name, description, is_active, created_at, updated_at';
  private readonly uniqueConstraint = 'uk_division_code';

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Find all divisions with optional filtering by active status
   * @param active - Optional filter for active status
   * @returns Array of divisions
   */
  async findAll(active?: boolean): Promise<DivisionDto[]> {
    this.logger.debug('Finding all divisions');
    const supabase = this.supabaseService.getClient();

    let query = supabase
      .from(this.tableName)
      .select(this.selectFields)
      .order('name');

    // Apply active filter if provided
    if (active !== undefined) {
      query = query.eq('is_active', active);
    }

    const { data, error } = await query;

    if (error) {
      this.logger.error(
        `Failed to fetch divisions: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed to fetch divisions');
    }

    return data as DivisionDto[];
  }

  // --- Admin Methods ---

  async findOneById(id: string): Promise<DivisionDto> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.tableName)
      .select(this.selectFields)
      .eq('id', id)
      .single<DivisionDto>();

    if (error || !data) {
      throw new NotFoundException(`Division with ID ${id} not found.`);
    }
    return data;
  }

  async createDivision(createDto: CreateDivisionDto): Promise<DivisionDto> {
    this.logger.debug(
      `Creating division: ${createDto.code} - ${createDto.name}`,
    );
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .insert({
        code: createDto.code,
        name: createDto.name,
        description: createDto.description,
        is_active: createDto.is_active,
      })
      .select(this.selectFields)
      .single<DivisionDto>();

    if (error) {
      if (
        error.code === '23505' &&
        error.message.includes(this.uniqueConstraint)
      ) {
        this.logger.warn(
          `Attempted to create duplicate division code: ${createDto.code}`,
        );
        throw new ConflictException(
          `A division with the code "${createDto.code}" already exists.`,
        );
      }
      this.logger.error(
        `Failed to create division: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not create division.');
    }

    if (!data) {
      this.logger.error('Division insert succeeded but returned no data.');
      throw new InternalServerErrorException(
        'Failed to retrieve newly created division.',
      );
    }

    this.logger.log(
      `Successfully created division ID: ${data.id}, Code: ${data.code}`,
    );
    return data;
  }

  async updateDivision(
    id: string,
    updateDto: UpdateDivisionDto,
  ): Promise<DivisionDto> {
    this.logger.debug(`Updating division ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const updateData: Partial<UpdateDivisionDto> = {};
    if (updateDto.name !== undefined) {
      updateData.name = updateDto.name;
    }
    if (updateDto.description !== undefined) {
      updateData.description = updateDto.description;
    }
    if (updateDto.is_active !== undefined) {
      updateData.is_active = updateDto.is_active;
    }

    if (Object.keys(updateData).length === 0) {
      return this.findOneById(id);
    }

    const { data, error } = await supabase
      .from(this.tableName)
      .update(updateData)
      .eq('id', id)
      .select(this.selectFields)
      .single<DivisionDto>();

    if (error) {
      if (error.code === 'PGRST116') {
        this.logger.warn(`Division not found for update: ID ${id}`);
        throw new NotFoundException(`Division with ID ${id} not found.`);
      }
      this.logger.error(
        `Failed to update division ${id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not update division.');
    }

    if (!data) {
      throw new NotFoundException(`Division with ID ${id} not found.`);
    }

    this.logger.log(`Successfully updated division ID: ${id}`);
    return data;
  }

  async deleteDivision(id: string): Promise<void> {
    this.logger.debug(`Deleting division ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { error, count } = await supabase
      .from(this.tableName)
      .delete()
      .eq('id', id);

    if (error) {
      this.logger.error(
        `Failed to delete division ${id}: ${error.message}`,
        error.stack,
      );
      if (error.code === '23503') {
        this.logger.warn(
          `Attempted to delete division ${id} which is still referenced.`,
        );
        throw new ConflictException(
          `Cannot delete division because it is referenced by other records.`,
        );
      }
      throw new InternalServerErrorException('Could not delete division.');
    }

    if (count === 0) {
      this.logger.warn(`Division not found for deletion: ID ${id}`);
      throw new NotFoundException(`Division with ID ${id} not found.`);
    }

    this.logger.log(`Successfully deleted division ID: ${id}`);
  }
}
