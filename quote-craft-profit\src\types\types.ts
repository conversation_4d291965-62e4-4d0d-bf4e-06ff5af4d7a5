export interface City {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface Division {
  id: string;
  name: string;
  code: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  name: string;
  code: string;
  description?: string;
  icon?: string;
  created_at: string;
  updated_at: string;
  display_order: number;
}

export interface Export {
  id: string;
  format: 'pdf' | 'xlsx' | 'csv';
  created_at: string;
  status: 'processing' | 'completed' | 'failed';
  file_url?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'planner';
  avatarUrl?: string;
}

export interface Client {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  address?: string;
  city?: string;
  notes?: string;
}

export interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  services: Service[];
}

export interface Service {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  pricePerDay: boolean;
  categoryId: string;
}

export interface ServiceOption {
  id: string;
  name: string;
  price: number;
  serviceId: string;
}

export interface CalculationItem {
  id: string;
  serviceId: string;
  serviceName: string;
  description: string;
  unitPrice: number;
  quantity: number;
  days: number;
  options: SelectedOption[];
  totalPrice: number;
}

export interface SelectedOption {
  id: string;
  name: string;
  price: number;
}

export interface TaxFee {
  id: string;
  name: string;
  type: 'percentage' | 'fixed';
  value: number;
  isDefault?: boolean;
}

export interface Calculation {
  id: string;
  status: 'draft' | 'complete';
  createdAt: string;
  updatedAt: string;
  eventName: string;
  eventDate: string;
  eventType: string;
  clientId: string;
  client: Client;
  city: string;
  attendees: number;
  items: CalculationItem[];
  notes?: string;
  subtotal: number;
  taxes: AppliedTaxFee[];
  discountAmount: number;
  total: number;
  profit: number;
  createdBy: string;
  lastEditedAt?: string;
}

export interface AppliedTaxFee {
  id: string;
  name: string;
  type: 'percentage' | 'fixed';
  value: number;
  amount: number;
}

export interface EventType {
  id: string;
  name: string;
}

export type Currency = 'USD' | 'EUR' | 'GBP';

/**
 * Centralized Quantity Basis Enum
 *
 * This enum defines how prices are calculated based on quantity and duration.
 * ALL quantity basis types multiply by: unitPrice × quantity × itemQuantityBasis
 * The difference is only in the display formula and semantic meaning.
 *
 * Database enum: package_quantity_basis
 * Used across: packages, custom items, calculations, formulas
 */
export enum QuantityBasisEnum {
  PER_EVENT = 'PER_EVENT',
  PER_DAY = 'PER_DAY',
  PER_ATTENDEE = 'PER_ATTENDEE',
  PER_ITEM = 'PER_ITEM',
  PER_ITEM_PER_DAY = 'PER_ITEM_PER_DAY',
  PER_ATTENDEE_PER_DAY = 'PER_ATTENDEE_PER_DAY',
}

/**
 * Display labels for quantity basis types
 * Used in forms and UI components
 */
export const QuantityBasisLabels: Record<QuantityBasisEnum, string> = {
  [QuantityBasisEnum.PER_EVENT]: 'Per Event',
  [QuantityBasisEnum.PER_DAY]: 'Per Day',
  [QuantityBasisEnum.PER_ATTENDEE]: 'Per Attendee',
  [QuantityBasisEnum.PER_ITEM]: 'Per Item',
  [QuantityBasisEnum.PER_ITEM_PER_DAY]: 'Per Item Per Day',
  [QuantityBasisEnum.PER_ATTENDEE_PER_DAY]: 'Per Attendee Per Day',
};

// Type alias for backward compatibility
export type QuantityBasis = keyof typeof QuantityBasisEnum;

export interface Package {
  id: string;
  name: string;
  description: string;
  categoryId?: string;
  categoryName: string;
  divisionId?: string;
  divisionName: string;
  cityIds?: string[];
  cityNames?: string[];
  venueIds?: string[];
  venueNames?: string[];
  quantityBasis: QuantityBasisEnum;
  isDeleted: boolean;
  hasPricing: boolean;
  price?: string;
  unitBaseCost?: string;
  currencyId?: string;
  currencySymbol?: string;
  variationGroupCode?: string;
  hasOptions?: boolean;
  hasDependencies?: boolean;
}

export interface EventData {
  id: string;
  name: string;
  clientName: string;
  startDate: string;
  endDate: string;
  location: string;
  status: string;
  // Removed attendees field - this information is tracked in calculation history
  primaryContact: string;
  notes?: string;
}
