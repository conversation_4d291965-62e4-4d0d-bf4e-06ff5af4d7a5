import React from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { TaxItem } from "./TaxItem";
import { TaxForm } from "./TaxForm";
import { Tax } from "../../../utils/calculationUtils";

interface TaxManagementProps {
  taxes: Tax[];
  calculatedTaxes: { tax: Tax; amount: number }[];
  isAddingTax: boolean;
  newTaxName: string;
  newTaxPercentage: string;
  formatCurrency: (amount: number) => string;
  onTaxNameChange: (value: string) => void;
  onTaxPercentageChange: (value: string) => void;
  onAddTax: () => void;
  onEditTax: (id: string, name: string, rate: number) => void; // New prop for editing
  onRemoveTax: (id: string) => void;
  onStartAddingTax: () => void;
  onCancelAddingTax: () => void;
}

/**
 * Tax management component
 * Orchestrates tax list display and tax addition form
 */
export const TaxManagement: React.FC<TaxManagementProps> = ({
  taxes,
  calculatedTaxes,
  isAddingTax,
  newTaxName,
  newTaxPercentage,
  formatCurrency,
  onTaxNameChange,
  onTaxPercentageChange,
  onAddTax,
  onEditTax,
  onRemoveTax,
  onStartAddingTax,
  onCancelAddingTax,
}) => {
  return (
    <>
      {/* Taxes List */}
      {taxes.length > 0 && (
        <div className="space-y-2 pb-2 border-b dark:border-gray-700">
          {calculatedTaxes.map(({ tax, amount }) => (
            <TaxItem
              key={tax.id}
              tax={tax}
              amount={amount}
              formatCurrency={formatCurrency}
              onEdit={onEditTax}
              onRemove={onRemoveTax}
            />
          ))}
        </div>
      )}

      {/* Add Tax Form or Button */}
      {isAddingTax ? (
        <TaxForm
          newTaxName={newTaxName}
          newTaxPercentage={newTaxPercentage}
          onTaxNameChange={onTaxNameChange}
          onTaxPercentageChange={onTaxPercentageChange}
          onAdd={onAddTax}
          onCancel={onCancelAddingTax}
        />
      ) : (
        <div className="pb-2 border-b dark:border-gray-700">
          <Button
            variant="outline"
            size="sm"
            onClick={onStartAddingTax}
            className="w-full"
          >
            <Plus size={16} className="mr-1" /> Add Tax
          </Button>
        </div>
      )}
    </>
  );
};
