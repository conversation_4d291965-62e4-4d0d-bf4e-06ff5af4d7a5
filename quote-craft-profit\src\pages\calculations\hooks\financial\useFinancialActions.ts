import { useState } from "react";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { Tax, Discount } from "../../utils/calculationUtils";

interface UseFinancialActionsProps {
  calculationId?: string;
  onStatusChange?: (
    status: "draft" | "completed" | "canceled",
    taxes: Tax[],
    discount: Discount
  ) => Promise<void>;
  onDelete?: () => Promise<void>;
  onNavigateToList?: () => void;
}

/**
 * Custom hook for financial actions (status changes, delete)
 * Handles the business logic for calculation status management and deletion
 */
export const useFinancialActions = ({
  calculationId,
  onStatusChange,
  onDelete,
  onNavigateToList,
}: UseFinancialActionsProps) => {
  const queryClient = useQueryClient();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

  // Handle status change
  const handleStatusChange = async (
    newStatus: "draft" | "completed" | "canceled",
    taxes: Tax[],
    discount: Discount
  ) => {
    if (!onStatusChange || !calculationId) return;

    try {
      setIsProcessing(true);

      // Save taxes and discount regardless of status change
      await onStatusChange(newStatus, taxes, discount);

      // Invalidate both the calculation and calculations list queries
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.detail(calculationId),
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.all(),
      });

      // Show success message
      toast.success(
        newStatus === "completed"
          ? "Calculation completed"
          : "Calculation saved as draft"
      );

      // Navigate to the calculation list page if saving as draft
      if (onNavigateToList && newStatus === "draft") {
        // Force a refetch before navigating away
        await queryClient.refetchQueries({
          queryKey: QUERY_KEYS.calculations.detail(calculationId),
        });

        // Use a short delay to allow the toast to be seen and data to be refetched
        setTimeout(() => {
          onNavigateToList();
        }, 500);
      }
    } catch (error) {
      console.error(`Error changing status to ${newStatus}:`, error);
      toast.error(
        `Failed to ${
          newStatus === "completed" ? "complete" : "save"
        } calculation`
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!onDelete || !calculationId) return;

    try {
      setIsProcessing(true);
      await onDelete();

      // Invalidate the calculations list query
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.all(),
      });

      toast.success("Calculation deleted");
    } catch (error) {
      console.error("Error deleting calculation:", error);
      toast.error("Failed to delete calculation");
    } finally {
      setIsProcessing(false);
      setIsConfirmingDelete(false);
    }
  };

  return {
    isProcessing,
    isConfirmingDelete,
    setIsConfirmingDelete,
    handleStatusChange,
    handleDelete,
  };
};
