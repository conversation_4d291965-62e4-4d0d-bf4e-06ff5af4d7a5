import React, { useState, useEffect } from "react";
import { showSuccess, showError } from "@/lib/notifications";
import { CheckCircle, AlertCircle, Clock, Wifi, WifiOff } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface SmartNotificationsProps {
  isSaving?: boolean;
  lastSaved?: Date | null;
  hasUnsavedChanges?: boolean;
  isOnline?: boolean;
  onSaveNow?: () => void;
}

export const SmartNotifications: React.FC<SmartNotificationsProps> = ({
  isSaving = false,
  lastSaved = null,
  hasUnsavedChanges = false,
  isOnline = true,
  onSaveNow,
}) => {
  const [showOfflineWarning, setShowOfflineWarning] = useState(false);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => {
      if (showOfflineWarning) {
        showSuccess("Connection restored", {
          description: "Your changes will be saved automatically.",
        });
        setShowOfflineWarning(false);
      }
    };

    const handleOffline = () => {
      setShowOfflineWarning(true);
      showError("Connection lost", {
        description: "Changes will be saved when connection is restored.",
        duration: Infinity, // Keep until connection is restored
      });
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [showOfflineWarning]);

  // Show save status
  const getSaveStatus = () => {
    if (!isOnline) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <WifiOff className="h-3 w-3" />
          Offline
        </Badge>
      );
    }

    if (isSaving) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="h-3 w-3 animate-spin" />
          Saving...
        </Badge>
      );
    }

    if (hasUnsavedChanges) {
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Unsaved changes
          {onSaveNow && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 px-1 ml-1"
              onClick={onSaveNow}
            >
              Save now
            </Button>
          )}
        </Badge>
      );
    }

    if (lastSaved) {
      const timeAgo = getTimeAgo(lastSaved);
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Saved {timeAgo}
        </Badge>
      );
    }

    return null;
  };

  const getTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return "just now";
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}d ago`;
    }
  };

  const saveStatus = getSaveStatus();

  if (!saveStatus) return null;

  return <div className="fixed bottom-4 right-4 z-50">{saveStatus}</div>;
};

// Export only named exports to avoid Fast Refresh issues
