import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { showSuccess } from "@/lib/notifications";
import { optimizedNotifications } from "@/lib/optimized-notifications";
import { LineItem, LineItemInput } from "@/types/calculation";
import { updateLineItemFromApi } from "@/services/calculations/calculationApiService";
import { QUERY_KEYS } from "@/lib/queryKeys";

/**
 * Hook for handling inline field updates for line items
 * Provides optimistic updates with proper rollback on errors
 */
export function useInlineLineItemUpdates(calculationId: string) {
  const queryClient = useQueryClient();

  // Helper function for preparing optimistic updates
  const prepareOptimisticUpdate = async () => {
    // Cancel any outgoing refetches
    await queryClient.cancelQueries({
      queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
    });

    // Get current line items
    return queryClient.getQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId)
    );
  };

  // Mutation for updating individual fields
  const updateFieldMutation = useMutation({
    mutationFn: async ({
      lineItemId,
      field,
      value,
    }: {
      lineItemId: string;
      field: string;
      value: string | number;
    }) => {
      // Prepare the update payload based on the field
      const updates: Partial<LineItemInput> = {};

      switch (field) {
        case "quantity":
          updates.quantity = Number(value);
          break;
        case "notes":
        case "description":
          updates.description = value.toString();
          break;
        case "item_quantity_basis":
          updates.item_quantity_basis = Number(value);
          break;
        case "name":
          updates.name = value.toString();
          break;
        case "unit_price":
          updates.unit_price = Number(value);
          break;
        default:
          throw new Error(`Unknown field: ${field}`);
      }

      // Use the existing API service
      return updateLineItemFromApi(calculationId, lineItemId, updates);
    },
    onMutate: async ({ lineItemId, field, value }) => {
      const previousLineItems = await prepareOptimisticUpdate();

      // Optimistically update the UI
      queryClient.setQueryData<LineItem[]>(
        QUERY_KEYS.calculations.lineItems(calculationId),
        (old) => {
          if (!old) return old;

          return old.map((item) => {
            if (item.id === lineItemId) {
              const updatedItem = { ...item };

              // Update the specific field
              switch (field) {
                case "quantity":
                  updatedItem.quantity = Number(value);
                  break;
                case "notes":
                case "description":
                  updatedItem.description = value.toString();
                  break;
                case "item_quantity_basis":
                  updatedItem.item_quantity_basis = Number(value);
                  break;
                case "name":
                  updatedItem.name = value.toString();
                  break;
                case "unit_price":
                  updatedItem.unit_price = Number(value);
                  break;
              }

              // Recalculate total price for quantity, unit_price, or item_quantity_basis changes
              if (
                ["quantity", "unit_price", "item_quantity_basis"].includes(
                  field
                )
              ) {
                const quantity = updatedItem.quantity;
                const unitPrice = updatedItem.unit_price || 0;
                const itemQuantityBasis = updatedItem.item_quantity_basis || 1;
                updatedItem.total_price =
                  quantity * unitPrice * itemQuantityBasis;
              }

              updatedItem.updated_at = new Date().toISOString();
              return updatedItem;
            }
            return item;
          });
        }
      );

      // Return previous data for rollback
      return { previousLineItems };
    },
    onSuccess: (updatedLineItem, { field }) => {
      // Show success toast for significant changes
      if (["quantity", "unit_price", "name"].includes(field)) {
        toast.success(`Updated ${updatedLineItem.name} successfully`);
      }

      // Invalidate calculation totals to ensure they're recalculated
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.detail(calculationId),
      });
    },
    onError: (error, { field }, context) => {
      console.error(`Error updating field ${field}:`, error);

      // Show error toast
      const fieldName = field === "item_quantity_basis" ? "days/units" : field;
      optimizedNotifications.lineItem.error(
        `Failed to update ${fieldName}. Please try again.`
      );

      // Rollback to previous state
      if (context?.previousLineItems) {
        queryClient.setQueryData(
          QUERY_KEYS.calculations.lineItems(calculationId),
          context.previousLineItems
        );
      }
    },
    onSettled: () => {
      // Always refetch after error or success to ensure data consistency
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
      });
    },
  });

  // Convenience function for updating a specific field
  const updateField = async (
    lineItemId: string,
    field: string,
    value: string | number
  ) => {
    return updateFieldMutation.mutateAsync({ lineItemId, field, value });
  };

  return {
    updateField,
    isUpdating: updateFieldMutation.isPending,
    error: updateFieldMutation.error,
  };
}

/**
 * Hook for batch updates (multiple fields at once)
 * Useful for more complex updates that need to be atomic
 */
export function useBatchLineItemUpdates(calculationId: string) {
  const queryClient = useQueryClient();

  const batchUpdateMutation = useMutation({
    mutationFn: async ({
      lineItemId,
      updates,
    }: {
      lineItemId: string;
      updates: Partial<LineItemInput>;
    }) => {
      return updateLineItemFromApi(calculationId, lineItemId, updates);
    },
    onSuccess: (updatedLineItem) => {
      showSuccess(`Updated ${updatedLineItem.name}`, {
        category: "line-item",
        description: "Line item has been updated successfully.",
      });

      // Invalidate both line items and calculation totals
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.detail(calculationId),
      });
    },
    onError: (error) => {
      console.error("Error in batch update:", error);
      toast.error("Failed to update item. Please try again.");
    },
  });

  return {
    batchUpdate: batchUpdateMutation.mutateAsync,
    isBatchUpdating: batchUpdateMutation.isPending,
    batchError: batchUpdateMutation.error,
  };
}
