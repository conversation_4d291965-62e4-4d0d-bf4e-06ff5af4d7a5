import {
  Body,
  Controller,
  Get,
  Post,
  Delete,
  Logger,
  Patch,
  UseGuards,
  Param,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { CategoriesService } from './categories.service';
import { CategoryDto } from './dto/category.dto';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import {
  ApiTags,
  ApiOkResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiCreatedResponse,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard as AdminGuard } from '../auth/guards/admin-role.guard';
import { UpdateCategoryOrderDto } from './dto/update-category-order.dto';
import { CategoryOrderResponseDto } from './dto/category-order-response.dto';

@ApiTags('Categories')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('categories')
export class CategoriesController {
  private readonly logger = new Logger(CategoriesController.name);

  constructor(private readonly categoriesService: CategoriesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all categories' })
  @ApiOkResponse({ type: [CategoryDto] })
  async getCategories(): Promise<CategoryDto[]> {
    this.logger.log('Fetching all categories');
    return this.categoriesService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a category by ID' })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: 'Category ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Category details',
    type: CategoryDto,
  })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getCategoryById(
    @Param('id', ParseUUIDPipe) id: string): Promise<CategoryDto> {
    this.logger.log(`Fetching category with ID: ${id}`);
    return this.categoriesService.findOneById(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new category' })
  @ApiBody({ type: CreateCategoryDto })
  @ApiResponse({
    status: 201,
    description: 'Category created successfully',
    type: CategoryDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 409, description: 'Category name already exists' })
  async createCategory(
    @Body() createCategoryDto: CreateCategoryDto): Promise<CategoryDto> {
    this.logger.log(`Creating category: ${createCategoryDto.name}`);
    return this.categoriesService.createCategory(createCategoryDto);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing category' })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: 'Category ID',
  })
  @ApiBody({ type: UpdateCategoryDto })
  @ApiResponse({
    status: 200,
    description: 'Category updated successfully',
    type: CategoryDto,
  })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 409, description: 'Category name already exists' })
  async updateCategory(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<CategoryDto> {
    this.logger.log(`Updating category with ID: ${id}`);
    return this.categoriesService.updateCategory(id, updateCategoryDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a category' })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: 'Category ID',
  })
  @ApiResponse({ status: 204, description: 'Category deleted successfully' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCategory(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Deleting category with ID: ${id}`);
    await this.categoriesService.deleteCategory(id);
  }

  @Patch('order')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Update category display order',
    description:
      'Updates the display order of multiple categories. Requires admin privileges.',
  })
  @ApiCreatedResponse({
    type: CategoryOrderResponseDto,
    description: 'The categories have been successfully reordered.',
  })
  async updateCategoryOrder(
    @Body() updateOrderDto: UpdateCategoryOrderDto,
  ): Promise<CategoryOrderResponseDto> {
    this.logger.log(
      `Updating category order for ${updateOrderDto.categories.length} categories`,
    );
    return this.categoriesService.updateCategoryOrder(
      updateOrderDto.categories,
    );
  }
}
