import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Use environment variables for Supabase configuration
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Simple storage provider that uses localStorage directly
// This is the standard approach for Supabase auth
class StandardSessionStorage {
  // Store the session in localStorage
  setItem(key: string, value: string): void {
    try {
      // Store directly in localStorage
      localStorage.setItem(key, value);
      console.log('Session stored in localStorage');
    } catch (error) {
      console.error('Error storing session:', error);
    }
  }

  // Retrieve the session from localStorage
  getItem(key: string): string | null {
    return localStorage.getItem(key);
  }

  // Remove the session from localStorage
  removeItem(key: string): void {
    localStorage.removeItem(key);
    console.log('Session removed from localStorage');
  }
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Create the Supabase client with the standard storage provider
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    storage: new StandardSessionStorage(),
    persistSession: true,
    autoRefreshToken: true,
  },
});
