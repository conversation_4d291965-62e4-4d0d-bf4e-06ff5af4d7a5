/**
 * Unified Template Calculation Service
 *
 * This service consolidates all template calculation functionality:
 * - Template calculation logic (from admin/templates/templateCalculationService.ts)
 * - Template-to-calculation conversion (from calculations/templateCalculationService.ts)
 * - Legacy wrapper functions (from admin/templates/templateCalculations.ts)
 */

import { apiClient, API_ENDPOINTS, getAuthenticatedApiClient } from '@/integrations/api';
import { Template } from '@/pages/admin/templates/types';

// ===== INTERFACES =====

export interface CalculationBreakdown {
  packageId: string;
  packageName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  currency: string;
  unitCost?: number;
  totalCost?: number;
}

export interface TemplateCalculationResult {
  packagesTotal: number;
  customItemsTotal: number;
  grandTotal: number;
  breakdown: CalculationBreakdown[];
  currency: string;
  hasValidPrices: boolean;
  missingPrices: string[];
  totalCost?: number;
  estimatedProfit?: number;
}

export interface TemplateCalculationSummary {
  totalPackages: number;
  totalValue: number;
  currency: string;
  hasValidPrices: boolean;
  missingPricesCount: number;
  averagePackageValue: number;
  totalCost?: number;
  profitMarginPercentage?: number;
}

export interface CreateCalculationFromTemplateRequest {
  name: string;
  eventStartDate?: string;
  eventEndDate?: string;
  attendees?: number;
  clientId?: string;
  eventId?: string;
  cityId?: string;
  venueIds?: string[];
  notes?: string;
}

export interface CreateCalculationFromTemplateResponse {
  id: string;
  name: string;
  created_at: string;
  message?: string;
}

// ===== TEMPLATE CALCULATION FUNCTIONS =====

/**
 * Calculate the total value of a template based on its package selections
 * @param templateId - The template ID
 * @returns The calculation result with breakdown
 */
export const calculateTemplateTotal = async (
  templateId: string
): Promise<TemplateCalculationResult> => {
  try {
    console.log('Calculating template total for:', templateId);

    // Use the authenticated API client
    const authClient = await getAuthenticatedApiClient();
    const response = await authClient.get(API_ENDPOINTS.TEMPLATES.CALCULATE(templateId));

    console.log('Template calculation result:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error calculating template total:', error);
    throw error;
  }
};

/**
 * Calculate template total using Template object (legacy wrapper)
 * @param template - The template to calculate
 * @returns The calculation result with breakdown
 */
export const calculateTemplateFromObject = async (
  template: Template
): Promise<TemplateCalculationResult> => {
  try {
    console.log('Calculating template total for:', template.name);

    // Use the main calculation function
    const apiResult = await calculateTemplateTotal(template.id);

    // Transform API result to match the legacy interface (remove optional fields)
    const result: TemplateCalculationResult = {
      packagesTotal: apiResult.packagesTotal,
      customItemsTotal: apiResult.customItemsTotal,
      grandTotal: apiResult.grandTotal,
      breakdown: apiResult.breakdown.map(item => ({
        packageId: item.packageId,
        packageName: item.packageName,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        currency: item.currency,
      })),
      currency: apiResult.currency,
      hasValidPrices: apiResult.hasValidPrices,
      missingPrices: apiResult.missingPrices,
    };

    console.log('Template calculation result:', result);
    return result;
  } catch (error) {
    console.error('Error calculating template total:', error);
    throw error;
  }
};

/**
 * Get a summary of the template calculation
 * @param templateId - The template ID
 * @returns A summary object with key metrics
 */
export const getTemplateCalculationSummary = async (
  templateId: string
): Promise<TemplateCalculationSummary> => {
  try {
    console.log('Getting template calculation summary for:', templateId);

    // Use the authenticated API client
    const authClient = await getAuthenticatedApiClient();
    const response = await authClient.get(API_ENDPOINTS.TEMPLATES.CALCULATE_SUMMARY(templateId));

    console.log('Template calculation summary:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error getting template calculation summary:', error);
    throw error;
  }
};

/**
 * Get calculation summary from full calculation result
 * @param result - The full calculation result
 * @returns A summary object with key metrics
 */
export const getTemplateCalculationSummaryFromResult = (
  result: TemplateCalculationResult
): TemplateCalculationSummary => {
  return {
    totalPackages: result.breakdown.length,
    totalValue: result.grandTotal,
    currency: result.currency,
    hasValidPrices: result.hasValidPrices,
    missingPricesCount: result.missingPrices.length,
    averagePackageValue: result.breakdown.length > 0
      ? result.packagesTotal / result.breakdown.length
      : 0,
    totalCost: result.totalCost,
    profitMarginPercentage: result.grandTotal > 0 && result.estimatedProfit !== undefined
      ? Math.round((result.estimatedProfit / result.grandTotal) * 100)
      : undefined,
  };
};

// ===== TEMPLATE-TO-CALCULATION CONVERSION =====

/**
 * Create a new calculation from an existing template
 * @param templateId - The ID of the template to use
 * @param customization - Customization options for the new calculation
 * @returns The created calculation details
 */
export const createCalculationFromTemplate = async (
  templateId: string,
  customization: CreateCalculationFromTemplateRequest
): Promise<CreateCalculationFromTemplateResponse> => {
  try {
    console.log('Creating calculation from template:', {
      templateId,
      customization,
    });

    // Use the authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make the API call
    const response = await authClient.post(
      API_ENDPOINTS.CALCULATIONS.FROM_TEMPLATE(templateId),
      customization
    );

    console.log('Created calculation from template:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error creating calculation from template:', error);
    throw error;
  }
};

// ===== UTILITY FUNCTIONS =====

/**
 * Format currency value for display
 * @param amount - The amount to format
 * @param currency - The currency code
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currency: string = 'IDR'): string => {
  try {
    const formatter = new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
    return formatter.format(amount).replace('IDR', 'Rp');
  } catch (error) {
    // Fallback formatting if currency is not supported
    const displayCurrency = currency === 'IDR' ? 'Rp' : currency;
    return `${displayCurrency} ${amount.toLocaleString()}`;
  }
};

/**
 * Validate if a template has valid pricing data
 * @param templateId - The template ID
 * @returns Whether the template has valid pricing
 */
export const validateTemplatePricing = async (templateId: string): Promise<boolean> => {
  try {
    const summary = await getTemplateCalculationSummary(templateId);
    return summary.hasValidPrices && summary.missingPricesCount === 0;
  } catch (error) {
    console.error('Error validating template pricing:', error);
    return false;
  }
};

/**
 * Get template pricing issues
 * @param templateId - The template ID
 * @returns List of pricing issues
 */
export const getTemplatePricingIssues = async (templateId: string): Promise<string[]> => {
  try {
    const result = await calculateTemplateTotal(templateId);
    return result.missingPrices;
  } catch (error) {
    console.error('Error getting template pricing issues:', error);
    return ['Failed to fetch pricing information'];
  }
};

/**
 * Get template calculation limits for the current user
 * @returns Calculation limits
 */
export const getTemplateCalculationLimits = async () => {
  try {
    // This would typically fetch user-specific limits from the API
    // For now, return default limits
    return {
      maxCalculationsPerDay: 50,
      maxCalculationsPerMonth: 500,
      canCreateFromPublicTemplates: true,
      canCreateFromPrivateTemplates: true,
    };
  } catch (error) {
    console.error('Error fetching template calculation limits:', error);
    return {
      maxCalculationsPerDay: 10,
      maxCalculationsPerMonth: 100,
      canCreateFromPublicTemplates: true,
      canCreateFromPrivateTemplates: false,
    };
  }
};

// ===== UTILITY FUNCTIONS FOR TEMPLATE-TO-CALCULATION CONVERSION =====

/**
 * Get calculation suggestions based on a template
 * @param template - The template to get suggestions from
 * @returns Suggested calculation data
 */
export const getCalculationSuggestions = (template: any) => {
  return {
    name: `${template.name} - ${new Date().toLocaleDateString()}`,
    attendees: template.attendees || undefined,
    notes: `Created from template: ${template.name}`,
  };
};

/**
 * Validate template customization data
 * @param data - The customization data to validate
 * @returns Validation result
 */
export const validateTemplateCustomization = (data: CreateCalculationFromTemplateRequest) => {
  const errors: string[] = [];

  if (!data.name || data.name.trim().length < 2) {
    errors.push('Calculation name must be at least 2 characters');
  }

  if (data.attendees !== undefined && data.attendees < 1) {
    errors.push('Attendees must be at least 1');
  }

  if (data.eventStartDate && data.eventEndDate) {
    const startDate = new Date(data.eventStartDate);
    const endDate = new Date(data.eventEndDate);
    if (startDate > endDate) {
      errors.push('Event start date must be before end date');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
