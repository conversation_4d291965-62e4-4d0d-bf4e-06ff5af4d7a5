import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO for line item data
 */
export class LineItemDto {
  @ApiProperty({
    description: 'Line item ID',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Calculation ID',
    type: String,
    format: 'uuid',
  })
  calculation_id: string;

  @ApiPropertyOptional({
    description: 'Package ID (null for custom items)',
    type: String,
    format: 'uuid',
    nullable: true,
  })
  package_id: string | null;

  @ApiProperty({
    description: 'Item name',
    type: String,
  })
  item_name: string;

  @ApiPropertyOptional({
    description: 'Item name snapshot (for package items)',
    type: String,
    nullable: true,
  })
  item_name_snapshot: string | null;

  @ApiPropertyOptional({
    description: 'Description (for custom items)',
    type: String,
    nullable: true,
  })
  description: string | null;

  @ApiPropertyOptional({
    description: 'Notes (for package items)',
    type: String,
    nullable: true,
  })
  notes: string | null;

  @ApiProperty({
    description: 'Quantity',
    type: Number,
  })
  quantity: number;

  @ApiPropertyOptional({
    description: 'Item quantity (for package items)',
    type: Number,
    nullable: true,
  })
  item_quantity: number | null;

  @ApiPropertyOptional({
    description: 'Item quantity basis (number of days/units)',
    type: Number,
    nullable: true,
  })
  item_quantity_basis: number | null;

  @ApiPropertyOptional({
    description: 'Duration in days (for package items)',
    type: Number,
    nullable: true,
  })
  duration_days: number | null;

  @ApiPropertyOptional({
    description: 'Quantity basis type (PER_EVENT, PER_DAY, etc.)',
    type: String,
    nullable: true,
  })
  quantity_basis: string | null;

  @ApiPropertyOptional({
    description: 'Unit price (for custom items)',
    type: Number,
    nullable: true,
  })
  unit_price: number | null;

  @ApiPropertyOptional({
    description: 'Unit base price (for package items)',
    type: Number,
    nullable: true,
  })
  unit_base_price: number | null;

  @ApiPropertyOptional({
    description: 'Calculated line total',
    type: Number,
    nullable: true,
  })
  calculated_line_total: number | null;

  @ApiPropertyOptional({
    description: 'Category ID',
    type: String,
    format: 'uuid',
    nullable: true,
  })
  category_id: string | null;

  @ApiProperty({
    description: 'Whether this is a custom item',
    type: Boolean,
  })
  is_custom: boolean;

  @ApiPropertyOptional({
    description: 'Selected options',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        option_id: { type: 'string', format: 'uuid' },
        option_name: { type: 'string', nullable: true },
      },
    },
    nullable: true,
  })
  options?: { option_id: string; option_name?: string }[] | null;

  @ApiProperty({
    description: 'Creation timestamp',
    type: String,
    format: 'date-time',
  })
  created_at: string;

  @ApiProperty({
    description: 'Last update timestamp',
    type: String,
    format: 'date-time',
  })
  updated_at: string;
}
