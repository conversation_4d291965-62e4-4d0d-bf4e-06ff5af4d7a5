import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { CalculationStatus } from '../enums/calculation-status.enum';

export class UpdateCalculationStatusDto {
  @ApiProperty({
    description: 'The new status for the calculation',
    enum: CalculationStatus,
    example: CalculationStatus.COMPLETED,
  })
  @IsEnum(CalculationStatus)
  @IsNotEmpty()
  status: CalculationStatus;
}
