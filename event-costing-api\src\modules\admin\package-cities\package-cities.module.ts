import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/auth.module';
import { PackageCitiesController } from './package-cities.controller';
import { PackageCitiesService } from './package-cities.service';

@Module({
  imports: [AuthModule],
  controllers: [PackageCitiesController],
  providers: [PackageCitiesService],
  exports: [PackageCitiesService],
})
export class PackageCitiesModule {}
