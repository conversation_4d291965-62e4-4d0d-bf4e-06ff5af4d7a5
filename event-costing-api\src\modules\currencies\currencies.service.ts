import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { CurrencyDto } from './dto/currency.dto';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';

@Injectable()
export class CurrenciesService {
  private readonly logger = new Logger(CurrenciesService.name);
  private readonly tableName = 'currencies';
  private readonly selectFields = 'id, code, description';

  constructor(private readonly supabaseService: SupabaseService) {}

  async findAll(): Promise<CurrencyDto[]> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.tableName)
      .select(this.selectFields)
      .order('code', { ascending: true });

    if (error) {
      this.logger.error(
        `Failed to fetch currencies: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not retrieve currencies.');
    }

    return (data as CurrencyDto[]) || [];
  }

  // --- Admin Methods ---

  async findOneById(id: string): Promise<CurrencyDto> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.tableName)
      .select(this.selectFields)
      .eq('id', id)
      .single<CurrencyDto>();

    if (error || !data) {
      throw new NotFoundException(`Currency with ID ${id} not found.`);
    }
    return data;
  }

  async createCurrency(createDto: CreateCurrencyDto): Promise<CurrencyDto> {
    this.logger.debug(
      `Creating currency: ${createDto.code} - ${createDto.description}`,
    );
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .insert({
        code: createDto.code.toUpperCase(),
        description: createDto.description,
      })
      .select(this.selectFields)
      .single<CurrencyDto>();

    if (error) {
      if (error.code === '23505') {
        this.logger.warn(
          `Attempted to create duplicate currency code: ${createDto.code}`,
        );
        throw new ConflictException(
          `A currency with the code "${createDto.code}" already exists.`,
        );
      }
      this.logger.error(
        `Failed to create currency: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not create currency.');
    }

    if (!data) {
      this.logger.error('Currency insert succeeded but returned no data.');
      throw new InternalServerErrorException(
        'Failed to retrieve newly created currency.',
      );
    }

    this.logger.log(
      `Successfully created currency ID: ${data.id}, Code: ${data.code}`,
    );
    return data;
  }

  async updateCurrency(
    id: string,
    updateDto: UpdateCurrencyDto,
  ): Promise<CurrencyDto> {
    this.logger.debug(`Updating currency ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const updateData: Partial<UpdateCurrencyDto> = {};
    if (updateDto.description !== undefined) {
      updateData.description = updateDto.description;
    }

    if (Object.keys(updateData).length === 0) {
      return this.findOneById(id);
    }

    const { data, error } = await supabase
      .from(this.tableName)
      .update(updateData)
      .eq('id', id)
      .select(this.selectFields)
      .single<CurrencyDto>();

    if (error) {
      if (error.code === 'PGRST116') {
        this.logger.warn(`Currency not found for update: ID ${id}`);
        throw new NotFoundException(`Currency with ID ${id} not found.`);
      }
      this.logger.error(
        `Failed to update currency ${id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not update currency.');
    }

    if (!data) {
      throw new NotFoundException(`Currency with ID ${id} not found.`);
    }

    this.logger.log(`Successfully updated currency ID: ${id}`);
    return data;
  }

  async deleteCurrency(id: string): Promise<void> {
    this.logger.debug(`Deleting currency ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { error, count } = await supabase
      .from(this.tableName)
      .delete()
      .eq('id', id);

    if (error) {
      this.logger.error(
        `Failed to delete currency ${id}: ${error.message}`,
        error.stack,
      );
      if (error.code === '23503') {
        this.logger.warn(
          `Attempted to delete currency ${id} which is still referenced.`,
        );
        throw new ConflictException(
          `Cannot delete currency because it is referenced by other records (e.g., calculations, package prices).`,
        );
      }
      throw new InternalServerErrorException('Could not delete currency.');
    }

    if (count === 0) {
      this.logger.warn(`Currency not found for deletion: ID ${id}`);
      throw new NotFoundException(`Currency with ID ${id} not found.`);
    }

    this.logger.log(`Successfully deleted currency ID: ${id}`);
  }
}
