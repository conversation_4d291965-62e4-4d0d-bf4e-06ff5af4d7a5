import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Loader2,
  Users,
  MapPin,
  Package,
  FileText,
  DollarSign,
  Tag,
  Layers,
} from "lucide-react";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import { EventTypeSelector } from "@/components/ui/event-type-selector";
import { getAllCities } from "@/services/shared/entities/cities";
import {
  getAllCalculations,
  getCalculationById,
} from "@/services/calculations";
import { getCalculationSummary } from "@/services/calculations/core";
import { createTemplateFromCalculation } from "@/services/admin/templates";
import { CreateTemplateFromCalculationRequest } from "@/pages/admin/templates/types";
import { z } from "zod";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Calculation } from "@/types/types";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";

interface CreateTemplateFromCalculationDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
}

// Form values type (simplified)
type FormValues = {
  name: string;
  description: string;
  eventTypeId: string;
};

// Define a step type for the dialog
type DialogStep = "select-calculation" | "create-template";

const CreateTemplateFromCalculationDialog: React.FC<
  CreateTemplateFromCalculationDialogProps
> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] =
    useState<DialogStep>("select-calculation");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCalculation, setSelectedCalculation] =
    useState<Calculation | null>(null);
  const [selectedCalculationId, setSelectedCalculationId] = useState<
    string | null
  >(null);

  // Fetch calculations for selection
  const { data: calculations = [], isLoading: isLoadingCalculations } =
    useQuery({
      queryKey: ["calculations"],
      queryFn: getAllCalculations,
      enabled: isOpen && currentStep === "select-calculation",
    });

  // Fetch detailed calculation information when a calculation is selected
  const { data: calculationDetails, isLoading: isLoadingDetails } = useQuery({
    queryKey: ["calculationDetails", selectedCalculationId],
    queryFn: () => getCalculationById(selectedCalculationId!),
    enabled:
      isOpen && !!selectedCalculationId && currentStep === "create-template",
  });

  // Fetch calculation summary for the packages display
  const { data: calculationSummary, isLoading: isLoadingSummary } = useQuery({
    queryKey: ["calculationSummary", selectedCalculationId],
    queryFn: () => getCalculationSummary(selectedCalculationId!),
    enabled:
      isOpen && !!selectedCalculationId && currentStep === "create-template",
  });

  // Fetch cities for dropdown
  const { data: cities = [] } = useQuery({
    queryKey: ["cities"],
    queryFn: getAllCities,
    enabled: isOpen && currentStep === "create-template",
  });

  // Create a simplified schema for the form
  const formSchema = z.object({
    name: z.string().min(2, "Template name must be at least 2 characters"),
    description: z.string().optional(),
    eventTypeId: z.string().uuid("Invalid event type").optional(),
  });

  // Initialize form with default values (simplified)
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: selectedCalculation
        ? `Template from ${selectedCalculation.eventName}`
        : "",
      description: "",
      eventTypeId: "",
    },
  });

  // Reset the form when the selected calculation changes
  useEffect(() => {
    if (selectedCalculation) {
      // Basic reset with selected calculation data
      form.reset({
        name: `Template from ${selectedCalculation.eventName}`,
        description: "",
        eventTypeId: "",
      });
    }
  }, [selectedCalculation, form]);

  // Update form with more detailed information when calculation details are loaded
  useEffect(() => {
    if (calculationDetails) {
      // Update the event type from the detailed calculation
      form.setValue("eventTypeId", calculationDetails.event_type || "");
    }
  }, [calculationDetails, form]);

  // Reset the dialog state when it's closed
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep("select-calculation");
      setSelectedCalculation(null);
      setSelectedCalculationId(null);
    }
  }, [isOpen]);

  const handleSelectCalculation = (calculation: any) => {
    // Transform ApiCalculation to Calculation format
    const transformedCalculation: Calculation = {
      id: calculation.id,
      status: calculation.status === "completed" ? "complete" : "draft",
      createdAt: calculation.created_at,
      updatedAt: calculation.updated_at,
      eventName: calculation.name,
      eventDate: calculation.event_start_date,
      eventType: calculation.event_type || "",
      clientId: "",
      client: { id: "", name: "", email: "" },
      city: "",
      attendees: calculation.attendees || 0,
      items: [],
      notes: "",
      subtotal: 0,
      taxes: [],
      discountAmount: 0,
      total: calculation.total,
      profit: calculation.estimated_profit || 0,
      createdBy: "",
    };

    setSelectedCalculation(transformedCalculation);
    setSelectedCalculationId(calculation.id);
    setCurrentStep("create-template");
  };

  const handleBack = () => {
    setCurrentStep("select-calculation");
  };

  const onSubmit = async (values: FormValues) => {
    if (!selectedCalculation) {
      toast.error("Please select a calculation first");
      setCurrentStep("select-calculation");
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare the template data using values from calculationDetails for city and attendees
      const templateData: CreateTemplateFromCalculationRequest = {
        calculationId: selectedCalculation.id,
        name: values.name,
        description: values.description || "",
        eventTypeId: values.eventTypeId || "", // Use native event type ID
        cityId: calculationDetails?.city?.id || "",
        currencyId: calculationDetails?.currency?.id || "",
        attendees: calculationDetails?.attendees || 0,
        // Include venue IDs if available
        ...(calculationDetails?.venues && calculationDetails.venues.length > 0
          ? { venueIds: calculationDetails.venues.map((venue) => venue.id) }
          : {}),
      };

      console.log("Submitting template data:", templateData);

      await createTemplateFromCalculation(templateData);
      toast.success("Template created successfully");
      onClose(true); // Close and refresh
    } catch (error) {
      console.error("Error creating template:", error);
      toast.error(`Failed to create template: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Create Template from Calculation</DialogTitle>
          <DialogDescription>
            {currentStep === "select-calculation"
              ? "Select a calculation to create a template from."
              : "Create a reusable template based on this calculation for future events."}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 h-full w-full">
          {currentStep === "select-calculation" ? (
            <>
              {isLoadingCalculations ? (
                <div className="flex justify-center items-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Loading calculations...</span>
                </div>
              ) : calculations.length === 0 ? (
                <div className="text-center p-8">
                  <p className="text-muted-foreground">
                    No calculations found.
                  </p>
                  <p className="text-sm mt-2">
                    Create a calculation first before creating a template.
                  </p>
                </div>
              ) : (
                <div className="space-y-4 p-1">
                  {calculations.map((calculation) => (
                    <Card
                      key={calculation.id}
                      className="cursor-pointer hover:bg-muted/50 transition-colors"
                      onClick={() => handleSelectCalculation(calculation)}
                    >
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{calculation.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              Event • {calculation.attendees || 0} attendees
                            </p>
                            <p className="text-sm mt-1">
                              Created:{" "}
                              {new Date(
                                calculation.created_at
                              ).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">
                              Rp {calculation.total.toLocaleString()}
                            </p>
                            <span
                              className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${
                                calculation.status === "completed"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {calculation.status === "completed"
                                ? "Complete"
                                : "Draft"}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              <div className="sticky bottom-0 bg-background pt-2 border-t mt-4">
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onClose()}
                  >
                    Cancel
                  </Button>
                </DialogFooter>
              </div>
            </>
          ) : (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4 pb-4"
              >
                {isLoadingDetails ? (
                  <div className="flex items-center justify-center p-6">
                    <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                    <span>Loading calculation details...</span>
                  </div>
                ) : calculationDetails ? (
                  <Card className="mb-6 border-primary/20">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg flex items-center">
                        <FileText className="h-5 w-5 mr-2 text-primary" />
                        Calculation Details
                      </CardTitle>
                      <CardDescription>
                        This information will be used to create your template
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-medium text-sm mb-2">
                          Basic Information
                        </h4>
                        <div className="grid grid-cols-2 gap-3">
                          <div className="flex items-start">
                            <FileText className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                            <div>
                              <p className="text-xs font-medium">Event Name</p>
                              <p className="text-sm">
                                {calculationDetails.name}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start">
                            <Tag className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                            <div>
                              <p className="text-xs font-medium">Event Type</p>
                              <p className="text-sm">
                                {calculationDetails.event_type || "N/A"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start">
                            <MapPin className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                            <div>
                              <p className="text-xs font-medium">City</p>
                              <p className="text-sm">
                                {cities.find(
                                  (c) => c.id === calculationDetails.city?.id
                                )?.name || "N/A"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start">
                            <Users className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                            <div>
                              <p className="text-xs font-medium">Attendees</p>
                              <p className="text-sm">
                                {calculationDetails.attendees}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {calculationDetails.venues &&
                        calculationDetails.venues.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm mb-2">Venues</h4>
                            <div className="space-y-1">
                              {calculationDetails.venues.map((venue) => (
                                <div
                                  key={venue.id}
                                  className="flex items-center"
                                >
                                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                                  <span className="text-sm">{venue.name}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                      <div>
                        <h4 className="font-medium text-sm mb-2">
                          Packages Summary
                        </h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Package className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span className="text-sm">Standard Packages</span>
                            </div>
                            <Badge variant="outline">
                              {isLoadingSummary
                                ? "..."
                                : calculationSummary?.standardPackagesCount ||
                                  0}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Layers className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span className="text-sm">Custom Items</span>
                            </div>
                            <Badge variant="outline">
                              {isLoadingSummary
                                ? "..."
                                : calculationSummary?.customItemsCount || 0}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span className="text-sm font-medium">
                                Total Value
                              </span>
                            </div>
                            <span className="font-medium">
                              {isLoadingSummary
                                ? "Loading..."
                                : `Rp ${(
                                    calculationSummary?.total || 0
                                  ).toLocaleString()}`}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-muted/20 p-3 rounded-md text-sm">
                        <p className="text-muted-foreground">
                          This template will include all packages, options, and
                          custom items from the calculation. Venues will be
                          associated but dates will be set to null.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  selectedCalculation && (
                    <div className="bg-muted/50 p-3 rounded-md mb-4">
                      <p className="font-medium">Selected Calculation:</p>
                      <p className="text-sm">{selectedCalculation.eventName}</p>
                      <p className="text-sm">
                        Total: Rp {selectedCalculation.total.toLocaleString()}
                      </p>
                    </div>
                  )
                )}

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter template name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter template description"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="eventTypeId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Event Type (Optional)</FormLabel>
                      <FormControl>
                        <EventTypeSelector
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Select event type"
                          allowEmpty={true}
                          emptyLabel="None"
                        />
                      </FormControl>
                      <FormDescription>
                        Select an event type for this template
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="sticky bottom-0 bg-background pt-2 border-t">
                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleBack}
                      className="mr-auto"
                    >
                      Back
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => onClose()}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating Template...
                        </>
                      ) : (
                        "Create Template from Calculation"
                      )}
                    </Button>
                  </DialogFooter>
                </div>
              </form>
            </Form>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTemplateFromCalculationDialog;
