/**
 * Loading component for calculation detail page
 * Shows skeleton loading state while data is being fetched
 */
import React from 'react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronLeft } from 'lucide-react';

interface CalculationDetailLoadingProps {
  onNavigateBack: () => void;
}

const CalculationDetailLoading: React.FC<CalculationDetailLoadingProps> = ({
  onNavigateBack,
}) => {
  return (
    <>
      <div className="mb-6 flex items-center gap-2">
        <Button variant="ghost" size="sm" onClick={onNavigateBack}>
          <ChevronLeft size={16} />
          <span>Back</span>
        </Button>
        <Skeleton className="h-8 w-1/3" />
      </div>
      <div className="space-y-4">
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    </>
  );
};

export default CalculationDetailLoading;
