import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';

export interface Column<T> {
  id: string;
  header: React.ReactNode;
  cell: (item: T) => React.ReactNode;
  className?: string;
  enableSorting?: boolean;
  meta?: {
    [key: string]: any;
  };
}

export interface ResponsiveTableProps<T> {
  data: T[];
  columns: Column<T>[];
  keyField: keyof T;
  isLoading?: boolean;
  emptyState?: React.ReactNode;
  onRowClick?: (item: T) => void;
  className?: string;
  cardView?: boolean;
  cardViewBreakpoint?: 'always' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  renderCard?: (item: T, columns: Column<T>[]) => React.ReactNode;
}

export function ResponsiveTable<T>({
  data,
  columns,
  keyField,
  isLoading = false,
  emptyState,
  onRowClick,
  className,
  cardView = true,
  cardViewBreakpoint = 'md',
  renderCard,
}: ResponsiveTableProps<T>) {
  // Determine the CSS class for the breakpoint
  const getBreakpointClass = () => {
    if (cardViewBreakpoint === 'always') return '';
    if (cardViewBreakpoint === 'sm') return 'sm:hidden';
    if (cardViewBreakpoint === 'md') return 'md:hidden';
    if (cardViewBreakpoint === 'lg') return 'lg:hidden';
    if (cardViewBreakpoint === 'xl') return 'xl:hidden';
    if (cardViewBreakpoint === '2xl') return '2xl:hidden';
    return 'md:hidden'; // Default to md
  };

  const getTableBreakpointClass = () => {
    if (cardViewBreakpoint === 'always') return 'hidden';
    if (cardViewBreakpoint === 'sm') return 'hidden sm:table';
    if (cardViewBreakpoint === 'md') return 'hidden md:table';
    if (cardViewBreakpoint === 'lg') return 'hidden lg:table';
    if (cardViewBreakpoint === 'xl') return 'hidden xl:table';
    if (cardViewBreakpoint === '2xl') return 'hidden 2xl:table';
    return 'hidden md:table'; // Default to md
  };

  // Default card renderer
  const defaultCardRenderer = (item: T, columns: Column<T>[]) => {
    return (
      <Card className="mb-4">
        <CardContent className="p-4">
          {columns.map((column) => (
            <div key={column.id} className="mb-2">
              <div className="text-sm font-medium text-muted-foreground">
                {column.header}
              </div>
              <div>{column.cell(item)}</div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full py-10 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <div className="w-full py-10 flex flex-col items-center justify-center text-center">
        {emptyState || (
          <>
            <p className="text-muted-foreground">No data available</p>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Card view for mobile */}
      {cardView && (
        <div className={cn('space-y-4', getBreakpointClass())}>
          {data.map((item) => (
            <div
              key={String(item[keyField])}
              onClick={onRowClick ? () => onRowClick(item) : undefined}
              className={cn(onRowClick && 'cursor-pointer')}
            >
              {renderCard
                ? renderCard(item, columns)
                : defaultCardRenderer(item, columns)}
            </div>
          ))}
        </div>
      )}

      {/* Table view for desktop */}
      <div className={cn('w-full overflow-auto', cardView && getTableBreakpointClass())}>
        <Table className={className}>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.id} className={column.className}>
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((item) => (
              <TableRow
                key={String(item[keyField])}
                onClick={onRowClick ? () => onRowClick(item) : undefined}
                className={cn(onRowClick && 'cursor-pointer')}
              >
                {columns.map((column) => (
                  <TableCell key={column.id} className={column.className}>
                    {column.cell(item)}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export default ResponsiveTable;
