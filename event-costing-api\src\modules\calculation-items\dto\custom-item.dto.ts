import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CustomItemDto {
  @ApiProperty({
    description: 'Unique identifier for the custom item',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'ID of the calculation this custom item belongs to',
    type: String,
    format: 'uuid',
  })
  calculation_id: string;

  @ApiProperty({
    description: 'Name of the custom item',
    type: String,
  })
  item_name: string;

  @ApiPropertyOptional({
    description: 'Description of the custom item',
    type: String,
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'Quantity of the custom item',
    type: Number,
  })
  item_quantity: number;

  @ApiPropertyOptional({
    description: 'Number of days/units this item applies for',
    type: Number,
    nullable: true,
  })
  item_quantity_basis: number | null;

  @ApiPropertyOptional({
    description: 'Quantity basis for calculation',
    type: String,
    enum: ['PER_EVENT', 'PER_DAY', 'PER_ATTENDEE', 'PER_ITEM', 'PER_ITEM_PER_DAY', 'PER_ATTENDEE_PER_DAY'],
    nullable: true,
  })
  quantity_basis: string | null;

  @ApiProperty({
    description: 'Unit price for the custom item',
    type: Number,
  })
  unit_price: number;

  @ApiProperty({
    description: 'Unit cost for the custom item',
    type: Number,
  })
  unit_cost: number;

  @ApiProperty({
    description: 'Calculated total price (quantity * unit_price)',
    type: Number,
  })
  calculated_total: number;

  @ApiPropertyOptional({
    description: 'Category ID',
    type: String,
    format: 'uuid',
    nullable: true,
  })
  category_id: string | null;

  @ApiProperty({
    description: 'Currency ID',
    type: String,
    format: 'uuid',
  })
  currency_id: string;

  @ApiPropertyOptional({
    description: 'City ID',
    type: String,
    format: 'uuid',
    nullable: true,
  })
  city_id: string | null;

  @ApiProperty({
    description: 'Creation timestamp',
    type: String,
    format: 'date-time',
  })
  created_at: string;

  @ApiProperty({
    description: 'Last update timestamp',
    type: String,
    format: 'date-time',
  })
  updated_at: string;
}
