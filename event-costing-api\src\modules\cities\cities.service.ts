import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { CityDto } from './dto/city.dto';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';

@Injectable()
export class CitiesService {
  private readonly logger = new Logger(CitiesService.name);
  private readonly tableName = 'cities';

  constructor(private readonly supabaseService: SupabaseService) {}

  async findAll(): Promise<CityDto[]> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from('cities')
      .select('id, name')
      .order('name', { ascending: true });

    if (error) {
      this.logger.error(
        `Failed to fetch cities: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not retrieve cities.');
    }

    return data || [];
  }

  // --- Admin Methods ---

  async createCity(createCityDto: CreateCityDto): Promise<CityDto> {
    this.logger.debug(`Creating city: ${createCityDto.name}`);
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .insert({
        name: createCityDto.name,
      })
      .select('id, name')
      .single<CityDto>();

    if (error) {
      // Check for unique constraint violation (example code, adjust based on actual constraint name)
      if (error.code === '23505') {
        // PostgreSQL unique violation code
        this.logger.warn(
          `Attempted to create duplicate city name: ${createCityDto.name}`,
        );
        throw new ConflictException(
          `A city with the name "${createCityDto.name}" already exists.`,
        );
      }
      this.logger.error(`Failed to create city: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Could not create city.');
    }

    if (!data) {
      this.logger.error('City insert succeeded but returned no data.');
      throw new InternalServerErrorException(
        'Failed to retrieve newly created city.',
      );
    }

    this.logger.log(
      `Successfully created city ID: ${data.id}, Name: ${data.name}`,
    );
    return data;
  }

  async updateCity(id: string, updateCityDto: UpdateCityDto): Promise<CityDto> {
    this.logger.debug(`Updating city ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    // Only include name in update if it's provided in the DTO
    const updateData: Partial<CityDto> = {};
    if (updateCityDto.name) {
      updateData.name = updateCityDto.name;
    }

    // If updateData is empty, no fields were provided to update
    if (Object.keys(updateData).length === 0) {
      // Optionally, fetch and return the existing record or throw an error
      // Fetching existing seems more user-friendly for a PUT
      return this.findOne(id); // Assuming findOne exists or needs to be added
      // OR: throw new BadRequestException('No valid fields provided for update.');
    }

    const { data, error } = await supabase
      .from(this.tableName)
      .update(updateData)
      .eq('id', id)
      .select('id, name')
      .single<CityDto>();

    if (error) {
      if (error.code === 'PGRST116') {
        // Not found
        this.logger.warn(`City not found for update: ID ${id}`);
        throw new NotFoundException(`City with ID ${id} not found.`);
      }
      if (error.code === '23505') {
        // Unique constraint violation
        this.logger.warn(
          `Attempted duplicate city name during update: ${updateData.name}`,
        );
        throw new ConflictException(
          `A city with the name "${updateData.name}" already exists.`,
        );
      }
      this.logger.error(
        `Failed to update city ${id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not update city.');
    }

    if (!data) {
      // Should be caught by single() error, but double-check
      throw new NotFoundException(`City with ID ${id} not found.`);
    }

    this.logger.log(`Successfully updated city ID: ${id}`);
    return data;
  }

  async deleteCity(id: string): Promise<void> {
    this.logger.debug(`Deleting city ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { error, count } = await supabase
      .from(this.tableName)
      .delete()
      .eq('id', id);

    if (error) {
      this.logger.error(
        `Failed to delete city ${id}: ${error.message}`,
        error.stack,
      );
      // Check if it's due to foreign key constraint
      if (error.code === '23503') {
        // Foreign key violation
        this.logger.warn(
          `Attempted to delete city ${id} which is still referenced.`,
        );
        throw new ConflictException(
          `Cannot delete city because it is referenced by other records (e.g., calculations, packages).`,
        );
      }
      throw new InternalServerErrorException('Could not delete city.');
    }

    if (count === 0) {
      this.logger.warn(`City not found for deletion: ID ${id}`);
      throw new NotFoundException(`City with ID ${id} not found.`);
    }

    this.logger.log(`Successfully deleted city ID: ${id}`);
  }

  // Helper method needed for updateCity if no fields are provided
  async findOne(id: string): Promise<CityDto> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.tableName)
      .select('id, name')
      .eq('id', id)
      .single<CityDto>();

    if (error || !data) {
      throw new NotFoundException(`City with ID ${id} not found.`);
    }
    return data;
  }
}
