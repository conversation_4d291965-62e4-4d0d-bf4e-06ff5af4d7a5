import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { EventTypeSelector } from "@/components/ui/event-type-selector";
import { useAuth } from "@/contexts/useAuth";
import {
  updateTemplate,
  getTemplateDetailsById,
  createTemplate,
  CreateTemplateRequest,
} from "@/services/admin/templates";
import { UpdateTemplateRequest } from "@/pages/admin/templates/types";

interface TemplateFormDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  templateId: string | null;
}

const formSchema = z.object({
  name: z.string().min(2, "Template name must be at least 2 characters"),
  description: z.string().optional(),
  event_type_id: z.string().uuid("Invalid event type").optional(),
  attendees: z.number().int().min(1, "Attendees must be at least 1").optional(),
  is_public: z.boolean().default(false),
});

type FormValues = z.infer<typeof formSchema>;

const TemplateFormDialog: React.FC<TemplateFormDialogProps> = ({
  isOpen,
  onClose,
  templateId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!templateId;
  const { user } = useAuth();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      event_type_id: "",
      attendees: undefined,
      is_public: false,
    },
  });

  useEffect(() => {
    const loadTemplateDetails = async () => {
      if (isEditMode && isOpen) {
        setIsLoading(true);
        try {
          // Use the backend API service to get template details
          const template = await getTemplateDetailsById(templateId);

          if (template) {
            form.reset({
              name: template.name,
              description: template.description || "",
              event_type_id: template.event_type_id || "", // Use native event_type_id
              attendees: template.attendees,
              is_public: template.is_public,
            });
          } else {
            throw new Error(
              "Template not found or you don't have access to it"
            );
          }
        } catch (error) {
          toast.error("Failed to load template details");
          console.error("Error loading template:", error);
          onClose();
        } finally {
          setIsLoading(false);
        }
      }
    };

    if (isOpen) {
      form.reset({
        name: "",
        description: "",
        event_type_id: "",
        attendees: undefined,
        is_public: false,
      }); // Reset form on open

      if (isEditMode) {
        loadTemplateDetails();
      }
    }
  }, [isOpen, templateId, isEditMode, form, onClose, user]);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      if (isEditMode) {
        // Update existing template using the service layer
        const templateData: UpdateTemplateRequest = {
          name: values.name,
          description: values.description,
          event_type_id: values.event_type_id || null, // Use native event type ID
          attendees: values.attendees,
          is_public: values.is_public,
        };

        await updateTemplate(templateId, templateData);
        toast.success("Template updated successfully");
      } else {
        // Create new template using the service layer
        const templateData: CreateTemplateRequest = {
          name: values.name,
          description: values.description,
          event_type_id: values.event_type_id || null, // Use native event type ID
          attendees: values.attendees,
          is_public: values.is_public,
        };

        await createTemplate(templateData, user?.id || "");
        toast.success("Template created successfully");
      }

      onClose(true); // Close and refresh
    } catch (error) {
      toast.error(
        `Failed to ${isEditMode ? "update" : "create"} template: ${
          (error as Error).message
        }`
      );
      console.error("Error saving template:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit Template" : "Create New Template"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Update the details of this template."
              : "Create a new template for your events."}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Loading template details...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Template Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter template name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a brief description of this template"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="event_type_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Event Type (Optional)</FormLabel>
                    <FormControl>
                      <EventTypeSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select event type"
                        allowEmpty={true}
                        emptyLabel="None"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="attendees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Attendees (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="Enter number of attendees"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value) || undefined)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_public"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Make template public</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Public templates can be seen and used by all users
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <DialogFooter className="pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onClose()}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isEditMode ? "Updating..." : "Creating..."}
                    </>
                  ) : isEditMode ? (
                    "Update Template"
                  ) : (
                    "Create Template"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default TemplateFormDialog;
