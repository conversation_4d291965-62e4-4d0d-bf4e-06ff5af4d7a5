import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { ExportPopup } from "../shared/ExportPopup";

interface ExportButtonProps {
  calculationId: string;
  calculationName: string;
}

export function ExportButton({
  calculationId,
  calculationName,
}: ExportButtonProps) {
  // Use local state for dialog management to avoid triggering API calls on mount
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1"
        onClick={() => setIsDialogOpen(true)}
      >
        <Download className="h-4 w-4" />
        Export
      </Button>

      <ExportPopup
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        calculationId={calculationId}
        calculationName={calculationName}
      />
    </>
  );
}
