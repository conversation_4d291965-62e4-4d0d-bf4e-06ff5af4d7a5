import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { JwtValidationService } from '../services/jwt-validation.service';

/**
 * JWT payload interface
 * This represents the data stored in the JWT token
 */
export interface JwtPayload {
  sub: string; // Subject (user ID)
  email: string;
  iat: number; // Issued at
  exp: number; // Expiration time
  aud: string; // Audience
  role?: string; // Optional role information
  [key: string]: any; // Allow for additional fields
}

/**
 * JWT Strategy for Passport
 * This strategy validates JWT tokens and extracts user information
 */
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly jwtValidationService: JwtValidationService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET') || 'your-secret-key',
    });
  }

  /**
   * Validate the JWT payload and return the user
   * This method is called by Passport after the token is verified
   * @param payload The decoded JWT payload
   * @returns The user object or throws an exception
   */
  async validate(payload: JwtPayload) {
    try {
      this.logger.debug(`Validating JWT payload for user: ${payload.email}`);

      // Use the validateUserFromJwtPayload method from the JwtValidationService
      const user =
        await this.jwtValidationService.validateUserFromJwtPayload(payload);

      if (!user) {
        this.logger.warn(`JWT validation failed for user: ${payload.email}`);
        throw new UnauthorizedException('User validation failed');
      }

      this.logger.debug(`JWT validation successful for user: ${payload.email}`);
      return user;
    } catch (error) {
      this.logger.error(`JWT validation error: ${error.message}`, error.stack);
      throw new UnauthorizedException(
        'Invalid token or authentication failure',
      );
    }
  }
}
