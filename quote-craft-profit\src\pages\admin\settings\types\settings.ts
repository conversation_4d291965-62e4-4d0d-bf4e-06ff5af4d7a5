export interface TableColumnSettings {
  number: boolean;
  name: boolean;
  category: boolean;
  division: boolean;
  cities: boolean;
  quantityBasis: boolean;
  price: boolean;
  lastModified: boolean;
  status: boolean;
  actions: boolean;
}

export interface AdminSettings {
  packageTable: TableColumnSettings;
  // Add other settings categories here as needed
}

export interface SettingsContextType {
  settings: AdminSettings;
  updateSettings: (
    category: keyof AdminSettings,
    values: Partial<AdminSettings[keyof AdminSettings]>,
  ) => void;
  resetSettings: () => void;
}

export const defaultSettings: AdminSettings = {
  packageTable: {
    number: true,
    name: true,
    category: true,
    division: true,
    cities: true,
    quantityBasis: true,
    price: true,
    lastModified: true,
    status: true,
    actions: true,
  },
};
