import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const EventsHeader: React.FC = () => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
        Events
      </h1>

      <div className="flex items-center gap-4">
        <Button asChild>
          <Link to="/events/new">
            <Plus className="h-4 w-4 mr-1" />
            Add New Event
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default EventsHeader;
