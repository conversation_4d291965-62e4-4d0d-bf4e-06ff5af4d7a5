import React, { useState } from "react";
import { Trash2, Edit, Check, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tax } from "../../../utils/calculationUtils";

interface TaxItemProps {
  tax: Tax;
  amount: number;
  formatCurrency: (amount: number) => string;
  onRemove: (id: string) => void;
  onEdit?: (id: string, name: string, rate: number) => void; // New prop for editing
}

/**
 * Individual tax item display with edit and remove functionality
 * Shows tax name, rate, amount and provides edit/remove functionality
 */
export const TaxItem: React.FC<TaxItemProps> = ({
  tax,
  amount,
  formatCurrency,
  onRemove,
  onEdit,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(tax.name);
  const [editRate, setEditRate] = useState(tax.rate.toString());

  const handleSave = () => {
    const rate = parseFloat(editRate);
    if (!editName.trim() || isNaN(rate) || rate < 0) {
      // Reset to original values if invalid
      setEditName(tax.name);
      setEditRate(tax.rate.toString());
      setIsEditing(false);
      return;
    }

    if (onEdit) {
      onEdit(tax.id!, editName.trim(), rate);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditName(tax.name);
    setEditRate(tax.rate.toString());
    setIsEditing(false);
  };

  if (isEditing) {
    return (
      <div className="flex justify-between items-center gap-2">
        <div className="flex items-center gap-2 flex-1">
          <Input
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            placeholder="Tax name"
            className="h-8 text-sm"
          />
          <div className="flex items-center gap-1">
            <Input
              type="number"
              value={editRate}
              onChange={(e) => setEditRate(e.target.value)}
              placeholder="Rate"
              min="0"
              step="0.01"
              className="h-8 w-20 text-sm"
            />
            <span className="text-sm text-gray-500 dark:text-gray-400">%</span>
          </div>
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleSave}
              className="h-8 w-8 p-0 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-300"
            >
              <Check size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCancel}
              className="h-8 w-8 p-0 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-300"
            >
              <X size={14} />
            </Button>
          </div>
        </div>
        <span className="text-sm dark:text-gray-200">
          {formatCurrency(amount)}
        </span>
      </div>
    );
  }

  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center gap-2">
        <span className="text-gray-600 dark:text-gray-300">
          {tax.name} ({tax.rate}%)
        </span>
        <div className="flex items-center gap-1">
          {onEdit && (
            <button
              onClick={() => setIsEditing(true)}
              className="text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1"
              title="Edit tax"
            >
              <Edit size={14} />
            </button>
          )}
          <button
            onClick={() => onRemove(tax.id!)}
            className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1"
            title="Remove tax"
          >
            <Trash2 size={14} />
          </button>
        </div>
      </div>
      <span className="dark:text-gray-200">{formatCurrency(amount)}</span>
    </div>
  );
};
