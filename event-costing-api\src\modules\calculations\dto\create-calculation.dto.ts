import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>otE<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsISO<PERSON>01,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  MaxLength,
  IsA<PERSON>y,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCalculationDto {
  @ApiProperty({
    description: 'Name of the calculation',
    example: 'Corporate Event 2025',
  })
  @IsString({ message: 'Name must be a string.' })
  @IsNotEmpty({ message: 'Name should not be empty.' })
  name: string;

  @ApiProperty({
    description: 'UUID of the currency',
    format: 'uuid',
    example: '685860b9-257f-41eb-b223-b3e1fad8f3b9',
  })
  @IsUUID()
  @IsNotEmpty()
  currency_id: string;

  @ApiPropertyOptional({
    description: 'UUID of the city',
    format: 'uuid',
    example: '029f586d-70da-4637-b58a-176470d3e528',
  })
  @IsUUID()
  @IsOptional() // City might be optional initially
  city_id?: string;

  @ApiPropertyOptional({
    description: 'Array of venue IDs',
    type: [String],
    format: 'uuid',
    example: ['a1b2c3d4-e5f6-7890-1234-567890abcdef'],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  venue_ids?: string[];

  @ApiPropertyOptional({
    description: 'Event start datetime (ISO 8601 format)',
    example: '2025-06-01T00:00:00.000Z',
    format: 'date-time',
  })
  @IsISO8601()
  @IsOptional()
  event_start_date?: string; // ISO 8601 format

  @ApiPropertyOptional({
    description: 'Event end datetime (ISO 8601 format)',
    example: '2025-06-03T23:59:59.999Z',
    format: 'date-time',
  })
  @IsISO8601()
  @IsOptional()
  event_end_date?: string; // ISO 8601 format

  @IsInt()
  @Min(1)
  @IsOptional() // Attendees might be optional initially
  attendees?: number;

  @ApiPropertyOptional({
    description: 'Event type ID (UUID reference to event_types table)',
    format: 'uuid',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsUUID()
  @IsOptional()
  event_type_id?: string;

  @IsString()
  @IsOptional()
  @MaxLength(500)
  notes?: string | null;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  version_notes?: string | null;

  @IsOptional()
  @IsUUID()
  client_id?: string | null;

  @IsUUID()
  @IsOptional()
  event_id?: string; // Link to an existing event

  // Tax and Discount are handled in the PUT endpoint, not usually set on creation
  // @IsObject()
  // @IsOptional()
  // taxes?: object;

  // @IsObject()
  // @IsOptional()
  // discount?: object;
}
