# Event Costing API

A NestJS v11 + Supabase backend for managing event cost calculations and quote generation.

## Overview

This API supports:

- Service catalog management (packages, options, dependencies)
- Dynamic event cost calculations (attendees, duration, location)
- Multi-currency support and price/cost snapshotting
- Profitability tracking
- Calculation templates and line-item operations
- Role-based user management (Ad<PERSON> vs. Planner)

## Folder Structure & Module Breakdown

```
event-costing-api/
├── docs/               # Planning & reference docs (ProjectBrief, BACKEND_PLAN, BackendTECH, Schema, TEST_POSTMAN)
├── src/
│   ├── main.ts         # Application entry point
│   ├── app.module.ts   # Root module
│   ├── core/
│   │   ├── filters/    # Global exception filters
│   │   ├── supabase/   # SupabaseModule & service wiring
│   │   └── database/   # Database config & migrations
│   ├── modules/        # Feature modules
│   │   ├── auth/       # /auth login & logout
│   │   ├── users/      # /users profiles & management
│   │   ├── calculations/   # /calculations endpoints
│   │   ├── calculation-items/ # Calculation line-item logic
│   │   ├── templates/  # /templates blueprint operations
│   │   ├── clients/    # /clients data management
│   │   ├── packages/   # /packages catalog
│   │   ├── categories/ # /categories package categories
│   │   ├── cities/     # /cities location data
│   │   ├── currencies/ # /currencies currency support
│   │   └── events/     # /events event types metadata
│   └── shared/         # Shared DTOs, interfaces, pipes
├── test/               # E2E tests
├── .env                # Environment variables (DO NOT COMMIT)
├── package.json        # Dependencies & scripts
├── tsconfig.json       # TypeScript config
└── README.md           # Project overview & instructions
```

## Core Features

- **Service Catalog Management**: Define packages with variations, configurable options, and pricing rules
- **Dynamic Calculations**: Create cost calculations based on event parameters (attendees, duration, location)
- **Dependency Resolution**: Handle package dependencies (requires/conflicts)
- **Multi-Currency Support**: Handle different currencies and location-specific pricing
- **Price Locking**: Snapshot prices and costs when calculations are saved
- **Profitability Tracking**: Calculate estimated profit based on costs and prices
- **User Management**: Role-based access control (Admin vs. Planner)
- **Export Functionality**: Generate calculation summaries in various formats

## Core Technologies

- **Framework:** [NestJS](https://nestjs.com/) v11 (TypeScript)
- **Database & Backend Services:** [Supabase](https://supabase.com/)
  - PostgreSQL Database
  - Authentication (with JWT)
  - Storage (for exports/documents)
- **Validation:** [class-validator](https://github.com/typestack/class-validator) & [class-transformer](https://github.com/typestack/class-transformer)
- **Configuration:** [@nestjs/config](https://docs.nestjs.com/techniques/configuration)
- **Logging:** [Winston](https://github.com/winstonjs/winston) via nest-winston
- **Authentication:** [@nestjs/passport](https://docs.nestjs.com/security/authentication) with JWT
- **Testing:** Jest, Supertest

## API Endpoints

Base URL: `http://localhost:<PORT>` (default 5000)

### Authentication (`/auth`)

- POST `/auth/login`: Authenticate with email & password
- POST `/auth/logout`: Invalidate session (204 No Content)

### Users (`/users`)

- GET `/users/me`: Retrieve current user profile

### Calculations (`/calculations`)

- POST `/calculations`: Create a new calculation
- GET `/calculations`: List calculations (filter, sort, paginate)
- GET `/calculations/{id}`: Get calculation details
- PUT `/calculations/{id}`: Update a calculation
- DELETE `/calculations/{id}`: Soft-delete a calculation
- POST `/calculations/from-template/{templateId}`: Create from template
- POST `/calculations/{calcId}/line-items/package`: Add package line-item
- POST `/calculations/{calcId}/line-items/custom`: Add custom line-item
- DELETE `/calculations/{calcId}/line-items/{itemId}`: Remove package item
- DELETE `/calculations/{calcId}/custom-items/{itemId}`: Remove custom item

### Clients (`/clients`)

- GET `/clients`: List clients
- GET `/clients/{id}`: Get client details
- POST `/clients`: Create client
- PUT `/clients/{id}`: Update client
- DELETE `/clients/{id}`: Delete client

### Categories (`/categories`)

- CRUD endpoints similar to /clients

### Cities (`/cities`)

- CRUD endpoints similar to /clients

### Currencies (`/currencies`)

- CRUD endpoints similar to /clients

### Events (`/events`)

- CRUD endpoints similar to /clients

### Packages (`/packages`)

- CRUD endpoints similar to /clients

### Templates (`/templates`)

- CRUD endpoints similar to /clients

Full request/response examples are in `docs/TEST_POSTMAN.md`.

## Environment Setup & Running

1. Install dependencies:
   ```bash
   npm install
   ```
2. Copy `.env.example` to `.env` and set:
   ```
   PORT=5000
   JWT_SECRET=YOUR_JWT_SECRET
   SUPABASE_URL=YOUR_SUPABASE_URL
   SUPABASE_KEY=YOUR_SUPABASE_KEY
   SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
   SUPABASE_SERVICE_ROLE_KEY=YOUR_SERVICE_ROLE_KEY
   ```
3. Start server:
   ```bash
   npm run start:dev   # watch mode
   npm run build && npm run start:prod
   ```

Server listens on `http://localhost:${process.env.PORT}`.

## Testing (Manual with Postman)

Import the collection defined in `docs/TEST_POSTMAN.md` and follow the listed endpoints.

## Contribution & Coding Guidelines

Please follow our [Project Rules and Guidelines](.windsurfrules) for code style, architecture, and PR process.

## License

This project is MIT licensed. See [LICENSE](LICENSE) for details.
