import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface EnhancedTooltipProps {
  /** The content to display in the tooltip */
  content: React.ReactNode;
  /** The element that triggers the tooltip */
  children: React.ReactNode;
  /** Optional keyboard shortcut to display */
  shortcut?: string;
  /** Optional delay before showing the tooltip (in ms) */
  delayDuration?: number;
  /** Optional side to display the tooltip */
  side?: 'top' | 'right' | 'bottom' | 'left';
  /** Optional alignment of the tooltip */
  align?: 'start' | 'center' | 'end';
  /** Whether the tooltip is disabled */
  disabled?: boolean;
}

/**
 * Enhanced tooltip component that can display keyboard shortcuts
 * and provides better accessibility
 */
export function EnhancedTooltip({
  content,
  children,
  shortcut,
  delayDuration = 300,
  side = 'top',
  align = 'center',
  disabled = false,
}: EnhancedTooltipProps) {
  if (disabled) {
    return <>{children}</>;
  }

  return (
    <TooltipProvider>
      <Tooltip delayDuration={delayDuration}>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent side={side} align={align} className="max-w-xs">
          <div className="text-sm">
            {content}
            {shortcut && (
              <div className="mt-1 font-mono text-xs bg-muted px-1.5 py-0.5 rounded inline-block">
                {shortcut}
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
