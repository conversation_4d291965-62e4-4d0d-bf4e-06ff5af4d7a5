/**
 * MIGRATED: Consolidated calculation data hook
 * Uses the new consolidated endpoint to replace 4 separate API calls with 1
 * Provides backward compatibility with the existing interface
 */
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import {
  useConsolidatedCalculationData,
  useConsolidatedCalculationSelectors,
  isConsolidatedDataLoaded,
} from "../data/useConsolidatedCalculationData";
import { getCompleteCalculationData } from "@/services/calculations/calculationCompleteDataService";
// Legacy imports for fallback
import {
  getCalculationById,
  getCalculationLineItems,
  getPackagesByCategoryForCalculation,
} from "../../../../services/calculations";
import { getAllCategories } from "@/services/admin/categories";
import { QUERY_KEYS } from "@/lib/queryKeys";

/**
 * Configuration for parallel data fetching
 */
interface ParallelDataConfig {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  retryDelay?: number;
  maxRetries?: number;
}

/**
 * MIGRATED: Hook for consolidated calculation data fetching
 * Uses the new consolidated endpoint to replace 4 separate API calls with 1
 * Maintains backward compatibility with the existing interface
 */
export const useParallelCalculationData = (
  calculationId: string,
  config: ParallelDataConfig = {}
) => {
  const {
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    gcTime = 10 * 60 * 1000, // 10 minutes
    retryDelay = 1000,
    maxRetries = 3,
  } = config;

  // Use consolidated endpoint (always available now)
  const consolidatedQuery = useConsolidatedCalculationData(calculationId, {
    enabled: enabled && !!calculationId,
    staleTime,
    cacheTime: gcTime,
    retry: maxRetries,
    onError: (error: Error) => {
      console.error(
        `[CONSOLIDATED] Failed to fetch calculation data for ${calculationId}:`,
        error
      );
    },
  });

  // Legacy fallback queries (only if consolidated endpoint fails)
  const shouldUseLegacy = consolidatedQuery.isError;

  const legacyCalculationQuery = useQuery({
    queryKey: QUERY_KEYS.calculation(calculationId),
    queryFn: () => getCalculationById(calculationId),
    enabled: enabled && !!calculationId && shouldUseLegacy,
    staleTime,
    gcTime,
    retry: maxRetries,
  });

  const legacyPackagesQuery = useQuery({
    queryKey: QUERY_KEYS.packagesByCategory(calculationId),
    queryFn: () => getPackagesByCategoryForCalculation(calculationId, true),
    enabled: enabled && !!calculationId && shouldUseLegacy,
    staleTime,
    gcTime,
    retry: maxRetries,
  });

  const legacyLineItemsQuery = useQuery({
    queryKey: QUERY_KEYS.lineItems(calculationId),
    queryFn: () => getCalculationLineItems(calculationId),
    enabled: enabled && !!calculationId && shouldUseLegacy,
    staleTime,
    gcTime,
    retry: maxRetries,
  });

  const legacyCategoriesQuery = useQuery({
    queryKey: ["categories"],
    queryFn: getAllCategories,
    enabled: enabled && shouldUseLegacy,
    staleTime: 10 * 60 * 1000,
    gcTime: 20 * 60 * 1000,
    retry: maxRetries,
  });

  // Combine consolidated and legacy data with intelligent selection
  const combinedResult = useMemo(() => {
    // Use consolidated data if successful
    if (!consolidatedQuery.isError && consolidatedQuery.data) {
      // Extract data from consolidated response
      const calculation = consolidatedQuery.data.calculation;
      const lineItems = consolidatedQuery.data.lineItems;
      const packages = consolidatedQuery.data.packages;
      const categories = consolidatedQuery.data.categories;

      return {
        // Core data (transformed from consolidated response)
        calculation,
        packagesByCategory: packages,
        lineItems,
        categories,

        // Aggregate states
        isLoading: consolidatedQuery.isLoading,
        isError: consolidatedQuery.isError,
        isFetching: consolidatedQuery.isFetching,
        isSuccess: consolidatedQuery.isSuccess,

        // Individual loading states (all same for consolidated)
        isLoadingCalculation: consolidatedQuery.isLoading,
        isLoadingPackages: consolidatedQuery.isLoading,
        isLoadingLineItems: consolidatedQuery.isLoading,
        isLoadingCategories: consolidatedQuery.isLoading,

        // Individual error states (all same for consolidated)
        isErrorCalculation: consolidatedQuery.isError,
        isPackagesError: consolidatedQuery.isError,
        isErrorLineItems: consolidatedQuery.isError,
        isErrorCategories: consolidatedQuery.isError,

        // Performance metrics
        loadingStates: {
          calculation: consolidatedQuery.isLoading,
          packages: consolidatedQuery.isLoading,
          lineItems: consolidatedQuery.isLoading,
          categories: consolidatedQuery.isLoading,
        },
        errorStates: {
          calculation: consolidatedQuery.isError,
          packages: consolidatedQuery.isError,
          lineItems: consolidatedQuery.isError,
          categories: consolidatedQuery.isError,
        },

        // Migration metadata
        usingConsolidated: true,
        usingLegacy: false,
        performanceMetrics: consolidatedQuery.data?.metadata,
      };
    }

    // Fall back to legacy queries
    const isLoading =
      legacyCalculationQuery.isLoading ||
      legacyPackagesQuery.isLoading ||
      legacyLineItemsQuery.isLoading ||
      legacyCategoriesQuery.isLoading;
    const isError =
      legacyCalculationQuery.isError ||
      legacyPackagesQuery.isError ||
      legacyLineItemsQuery.isError ||
      legacyCategoriesQuery.isError;
    const isFetching =
      legacyCalculationQuery.isFetching ||
      legacyPackagesQuery.isFetching ||
      legacyLineItemsQuery.isFetching ||
      legacyCategoriesQuery.isFetching;
    const isSuccess =
      legacyCalculationQuery.isSuccess &&
      legacyPackagesQuery.isSuccess &&
      legacyLineItemsQuery.isSuccess &&
      legacyCategoriesQuery.isSuccess;

    return {
      // Core data (from legacy queries)
      calculation: legacyCalculationQuery.data || null,
      packagesByCategory: legacyPackagesQuery.data || [],
      lineItems: legacyLineItemsQuery.data || [],
      categories: legacyCategoriesQuery.data || [],

      // Aggregate states
      isLoading,
      isError,
      isFetching,
      isSuccess,

      // Individual loading states
      isLoadingCalculation: legacyCalculationQuery.isLoading,
      isLoadingPackages: legacyPackagesQuery.isLoading,
      isLoadingLineItems: legacyLineItemsQuery.isLoading,
      isLoadingCategories: legacyCategoriesQuery.isLoading,

      // Individual error states
      isErrorCalculation: legacyCalculationQuery.isError,
      isPackagesError: legacyPackagesQuery.isError,
      isErrorLineItems: legacyLineItemsQuery.isError,
      isErrorCategories: legacyCategoriesQuery.isError,

      // Performance metrics
      loadingStates: {
        calculation: legacyCalculationQuery.isLoading,
        packages: legacyPackagesQuery.isLoading,
        lineItems: legacyLineItemsQuery.isLoading,
        categories: legacyCategoriesQuery.isLoading,
      },
      errorStates: {
        calculation: legacyCalculationQuery.isError,
        packages: legacyPackagesQuery.isError,
        lineItems: legacyLineItemsQuery.isError,
        categories: legacyCategoriesQuery.isError,
      },

      // Migration metadata
      usingConsolidated: false,
      usingLegacy: true,
      performanceMetrics: null,
    };
  }, [
    // CRITICAL FIX: Drastically reduce dependencies to prevent excessive re-renders
    // Only depend on the most essential state changes

    // Primary data availability (consolidated takes priority)
    consolidatedQuery.isSuccess,
    consolidatedQuery.isError,
    consolidatedQuery.data?.calculation?.id, // Only depend on calculation ID

    // Fallback to legacy only if consolidated fails
    legacyCalculationQuery.isSuccess,
    legacyCalculationQuery.data?.id, // Only depend on calculation ID

    // Loading state (simplified)
    consolidatedQuery.isLoading || legacyCalculationQuery.isLoading,
  ]);

  // Log migration status for debugging
  console.log(`[MIGRATION] Calculation ${calculationId}:`, {
    usingConsolidated: combinedResult.usingConsolidated,
    usingLegacy: combinedResult.usingLegacy,
    loadTime: combinedResult.performanceMetrics?.loadTime,
  });

  return combinedResult;
};

/**
 * MIGRATED: Hook for optimized calculation detail data with intelligent fallbacks
 * Now uses consolidated endpoint with automatic fallback to legacy endpoints
 * Provides the same interface as original hook but with improved performance
 */
export const useOptimizedCalculationDetailCore = (
  calculationId: string,
  config: ParallelDataConfig = {}
) => {
  const parallelData = useParallelCalculationData(calculationId, config);

  // Memoize the result to prevent unnecessary re-renders
  const result = useMemo(() => {
    const returnValue = {
      // Core data (same interface as original hook)
      calculation: parallelData.calculation,
      packagesByCategory: parallelData.packagesByCategory,
      lineItems: parallelData.lineItems,
      categories: parallelData.categories,

      // Loading and error states (same interface as original hook)
      isLoading: parallelData.isLoading,
      isLoadingCalculation: parallelData.isLoadingCalculation,
      isLoadingPackages: parallelData.isLoadingPackages,
      isLoadingLineItems: parallelData.isLoadingLineItems,
      isLoadingCategories: parallelData.isLoadingCategories,
      isError: parallelData.isError,
      isErrorCalculation: parallelData.isErrorCalculation,
      isErrorLineItems: parallelData.isErrorLineItems,
      isErrorCategories: parallelData.isErrorCategories,
      isPackagesError: parallelData.isPackagesError,

      // Additional performance data
      isFetching: parallelData.isFetching,
      isSuccess: parallelData.isSuccess,
      loadingStates: parallelData.loadingStates,
      errorStates: parallelData.errorStates,

      // Migration metadata (new)
      usingConsolidated: parallelData.usingConsolidated,
      usingLegacy: parallelData.usingLegacy,
      performanceMetrics: parallelData.performanceMetrics,
    };

    return returnValue;
  }, [
    // CRITICAL FIX: Minimal dependencies to prevent render cascades
    parallelData.calculation?.id,
    parallelData.isLoading,
    parallelData.isError,
    parallelData.usingConsolidated,
  ]);

  return result;
};

/**
 * MIGRATED: Hook for selective parallel data fetching
 * Note: With consolidated endpoint, all data is fetched together for optimal performance
 * This hook is maintained for backward compatibility but doesn't provide selective fetching
 */
export const useSelectiveParallelData = (
  calculationId: string,
  options: {
    fetchCalculation?: boolean;
    fetchPackages?: boolean;
    fetchLineItems?: boolean;
    fetchCategories?: boolean;
  } = {},
  config: ParallelDataConfig = {}
) => {
  const {
    fetchCalculation = true,
    fetchPackages = true,
    fetchLineItems = true,
    fetchCategories = true,
  } = options;

  // Note: Consolidated endpoint fetches all data together
  // Selective fetching is not supported but maintained for compatibility
  console.warn(
    "[MIGRATION] useSelectiveParallelData: Selective fetching not supported with consolidated endpoint. All data will be fetched."
  );

  return useParallelCalculationData(calculationId, {
    ...config,
    enabled:
      config.enabled !== false &&
      (fetchCalculation || fetchPackages || fetchLineItems || fetchCategories),
  });
};
