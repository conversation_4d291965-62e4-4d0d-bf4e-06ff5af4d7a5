import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsN<PERSON>ber,
  Min,
  MaxLength,
  IsUUID,
} from 'class-validator';

export class UpdateLineItemDto {
  @ApiProperty({
    description: 'Name of the item (for custom items)',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  itemName?: string;

  @ApiProperty({
    description: 'Description or notes for the item',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Quantity of the item',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0.01)
  quantity?: number;

  @ApiProperty({
    description: 'Unit price (for custom items)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  unitPrice?: number;

  @ApiProperty({
    description: 'Unit cost (for custom items)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  unitCost?: number;

  @ApiProperty({
    description: 'Number of days/units this item applies for',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  itemQuantityBasis?: number;

  @ApiProperty({
    description: 'Quantity basis for calculation',
    required: false,
    enum: ['PER_EVENT', 'PER_DAY', 'PER_ATTENDEE', 'PER_ITEM', 'PER_ITEM_PER_DAY', 'PER_ATTENDEE_PER_DAY'],
  })
  @IsOptional()
  @IsString()
  quantityBasis?: string;

  @ApiProperty({
    description: 'Category ID (for custom items)',
    required: false,
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiProperty({
    description: 'Duration in days (for package items)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  duration_days?: number;

  @ApiProperty({
    description: 'Notes (for package items)',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
