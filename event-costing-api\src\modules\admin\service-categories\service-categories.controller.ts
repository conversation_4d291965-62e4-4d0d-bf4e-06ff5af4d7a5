import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ServiceCategoriesService } from './service-categories.service';
import { CreateServiceCategoryDto } from './dto/create-service-category.dto';
import { UpdateServiceCategoryDto } from './dto/update-service-category.dto';
import { ServiceCategoryDto } from './dto/service-category.dto';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from 'src/modules/auth/guards/admin-role.guard';

@ApiTags('Admin: Service Categories')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AdminRoleGuard)
@Controller('admin/service-categories')
export class ServiceCategoriesController {
  private readonly logger = new Logger(ServiceCategoriesController.name);

  constructor(
    private readonly serviceCategoriesService: ServiceCategoriesService,
  ) {}

  // --- Create Service Category --- //
  @Post()
  @ApiOperation({ summary: 'Create a new service category' })
  @ApiResponse({
    status: 201,
    description: 'Service category created successfully.',
    type: ServiceCategoryDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({
    status: 409,
    description: 'Conflict (e.g., unique constraint)',
  })
  create(
    @Body() createDto: CreateServiceCategoryDto,
  ): Promise<ServiceCategoryDto> {
    this.logger.log(
      `Received request to create service category: ${JSON.stringify(createDto)}`,
    );
    return this.serviceCategoriesService.create(createDto);
  }

  // --- Find All Service Categories --- //
  @Get()
  @ApiOperation({ summary: 'Get all service categories' })
  @ApiResponse({
    status: 200,
    description: 'List of service categories.',
    type: [ServiceCategoryDto],
  })
  findAll(): Promise<ServiceCategoryDto[]> {
    this.logger.log('Received request to find all service categories');
    return this.serviceCategoriesService.findAll();
  }

  // --- Find One Service Category --- //
  @Get(':id')
  @ApiOperation({ summary: 'Get a specific service category by ID' })
  @ApiParam({ name: 'id', description: 'UUID of the service category' })
  @ApiResponse({
    status: 200,
    description: 'Service category details.',
    type: ServiceCategoryDto,
  })
  @ApiResponse({ status: 404, description: 'Service category not found' })
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ServiceCategoryDto> {
    this.logger.log(`Received request to find service category with ID: ${id}`);
    return this.serviceCategoriesService.findOne(id);
  }

  // --- Update Service Category --- //
  @Patch(':id')
  @ApiOperation({ summary: 'Update a specific service category' })
  @ApiParam({
    name: 'id',
    description: 'UUID of the service category to update',
  })
  @ApiResponse({
    status: 200,
    description: 'Service category updated successfully.',
    type: ServiceCategoryDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Service category not found' })
  @ApiResponse({ status: 409, description: 'Conflict' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateServiceCategoryDto,
  ): Promise<ServiceCategoryDto> {
    this.logger.log(
      `Received request to update service category ${id}: ${JSON.stringify(updateDto)}`,
    );
    return this.serviceCategoriesService.update(id, updateDto);
  }

  // --- Delete Service Category --- //
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Soft-delete a specific service category' })
  @ApiParam({
    name: 'id',
    description: 'UUID of the service category to delete',
  })
  @ApiResponse({
    status: 204,
    description: 'Service category soft-deleted successfully.',
  })
  @ApiResponse({ status: 404, description: 'Service category not found' })
  remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(
      `Received request to delete service category with ID: ${id}`,
    );
    return this.serviceCategoriesService.remove(id);
  }
}
