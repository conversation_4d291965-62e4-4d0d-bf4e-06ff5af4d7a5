import { useMemo } from 'react';
import { LineItem } from '@/types/calculation';
import {
  CategoryWithOrder,
  CategoryEntry,
  sortLineItemsByCategory,
  transformCategoryMapToSortedEntries
} from '../../utils/sortingUtils';
import { debug } from '@/lib/debugUtils';

/**
 * Custom hook for sorting line items and categories
 *
 * @param lineItems - Array of line items to sort
 * @param categories - Array of categories with id, name, and display_order
 * @param lineItemsByCategory - Record mapping category IDs to arrays of line items
 * @returns Object with sorted line items and category entries
 */
export function useCategorySorting(
  lineItems: LineItem[],
  categories: CategoryWithOrder[],
  lineItemsByCategory: Record<string, LineItem[]>
) {
  // Memoize the sorted line items to avoid re-sorting on every render
  const sortedLineItems = useMemo(() => {
    debug('Sorting line items by category order');
    return sortLineItemsByCategory(lineItems, categories);
  }, [lineItems, categories]);

  // Memoize the sorted category entries to avoid re-sorting on every render
  const sortedCategoryEntries = useMemo(() => {
    debug('Transforming category map to sorted entries');
    return transformCategoryMapToSortedEntries(lineItemsByCategory, categories);
  }, [lineItemsByCategory, categories]);

  return {
    sortedLineItems,
    sortedCategoryEntries
  };
}
