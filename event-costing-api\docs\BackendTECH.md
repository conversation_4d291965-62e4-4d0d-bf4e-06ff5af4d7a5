# Revised Backend Technical Plan

This document outlines key dependencies, project structure, and guidelines for developing the backend API using NestJS and Supabase (via `@supabase/supabase-js` client), with redundant content from behavior-rules.txt removed.

## 1. Key Third-Party Dependencies (npm packages)

- **Core Framework & Runtime:**

  - `@nestjs/common`, `@nestjs/core`, `@nestjs/platform-express` (or `@nestjs/platform-fastify`)
  - `reflect-metadata`
  - `rxjs`

- **Database Client:**

  - `@supabase/supabase-js` (For Auth interactions, Database operations via PostgREST, Storage uploads, RPC calls)

- **Configuration:**

  - `@nestjs/config` (For managing environment variables like Supabase URL, Keys)

- **Validation:**

  - `class-validator` (For decorating DTOs)
  - `class-transformer` (Used with `class-validator` and NestJS pipes)

- **File Generation (Exports):**

  - For Excel/: `exceljs` or `x`
  - For PDF: `pdf-lib` (programmatic creation) or `puppeteer` (HTML to PDF via headless browser - heavier dependency)

- **Logging:**

  - `winston`
  - `nest-winston` (Or similar NestJS integration package for Winston)

- **Async/Queues (Deferred):**

  - _(Optional - Not needed for initial implementation)_
  - `@nestjs/bullmq`
  - `bullmq`
  - `ioredis` (or another Redis client)

- **Development & Testing:**
  - `@nestjs/cli` (Global or dev dependency)
  - `@nestjs/testing`
  - `typescript`, `@types/node`, `ts-node`, `tsconfig-paths` (Development dependencies)
  - `jest`, `@types/jest`, `ts-jest` (Testing framework)
  - `supertest`, `@types/supertest` (For E2E API testing)

## 2. Key Technical Implementation Points

- **Schema Management Workflow:** Strictly follow the **`Supabase Migrations (SQL)`** workflow. Your SQL files in `/supabase/migrations` (or applied via Supabase Studio) are the source of truth for the database structure. **No** ORM-based migrations are used.

- **Database Interaction:** Use the `@supabase/supabase-js` client for all database operations (CRUD, RPC calls). Consider creating a dedicated, injectable `SupabaseService` to manage the client instance and encapsulate common interactions.

  - **Service Account Key:** Use the `SUPABASE_SERVICE_ROLE_KEY` (from Supabase Project Settings > API) for backend operations that need to bypass RLS policies. Store this securely in environment variables.
  - **PostgREST:** Leverage the Supabase client's wrappers around PostgREST for type-safe (if using generated types) or flexible database querying (`select`, `insert`, `update`, `delete`, `rpc`).
  - **Database Functions (RPC):** For complex queries, business logic requiring atomicity, or operations needing elevated privileges securely, define PostgreSQL functions in your Supabase database and call them using `supabase.rpc('your_function_name', { args })`.

- **Database Transactions:** The standard `@supabase/supabase-js` client does not directly expose transaction control (BEGIN/COMMIT/ROLLBACK) for multiple operations. For atomicity across several writes:

  - **Option 1 (Recommended):** Encapsulate the related operations within a single **Database Function (RPC)**. PostgreSQL functions run within an implicit transaction.
  - **Option 2:** Structure logic carefully to minimize the chance of partial failure. Implement compensating actions if necessary.

- **Price/Cost Snapshotting:** The logic in `POST /calculations/{calc_id}/line-items/package` (and potentially template loading) MUST correctly fetch the _current_ prices/costs from `package_prices`/`package_options` (using Supabase client) and store them as snapshots in the new `calculation_line_items`/`calculation_line_item_options` rows.

- **Calculation Logic:** The logic for calculating `calculated_line_total` and `calculated_line_cost` MUST use the **snapshot values** stored in the line item tables and apply multipliers based on `packages.quantity_basis` and the line item's `item_quantity`/`duration_days`. The overall `calculation_history` totals (`subtotal`, `total_cost`, `estimated_profit`, `total`) must be recalculated accurately whenever line items change or `taxes`/`discount` are updated.

- **Quantity/Duration Determination:** Implement the logic carefully (likely when adding/updating line items) to determine the correct `item_quantity` and `duration_days` based on `packages.quantity_basis` and context (attendees, event dates, user input).

- **Dependency Checks:** Implement the logic to query `package_dependencies` (using Supabase client) and provide feedback/enforcement during package browsing and addition.

- **Admin UI Complexity:** Remember that the Admin APIs for package catalogue management need to support a potentially complex UI that simplifies the multi-table relationships for the admin user.

## 3. Project Structure & Conventions (NestJS Focus)

### Architecture Design

- **Modular Monolith:** NestJS excels at organizing the application into distinct **feature modules** (e.g., `CalculationsModule`, `PackagesModule`, `AuthModule`). This provides good separation of concerns within a single backend codebase.

- **Standard Layers:** Adhere to clear layering within modules and the application:

  - `Controllers`: Entry points for HTTP requests. Responsible for parsing requests, validating input via DTOs/Pipes, calling the appropriate service method, and formatting the HTTP response.
  - `Services`: Contain the core business logic, orchestrate operations, interact with the database (via the injected `SupabaseService` or directly using the Supabase client), perform calculations, call other services if needed.
  - `Modules`: Group related Controllers, Services, and Providers. Manage dependencies between modules using imports/exports.
  - `Entities/Models/Types`: Define TypeScript types/interfaces representing your database tables and API structures. Consider using tools like `supabase gen types typescript` to generate types from your schema for better type safety with the Supabase client.
  - `DTOs (Data Transfer Objects)`: Classes defining the expected structure and validation rules for API request payloads and response bodies. Use `class-validator` decorators.

- **Dependency Injection (DI):** Utilize NestJS's built-in DI container. Inject Services into Controllers, inject `SupabaseService` (if created) or `ConfigService` into Services, etc. Avoid manual instantiation of the Supabase client within services.

### Folder Structure (Example)

```
src/
├── main.ts             # App entry point (bootstrap)
├── app.module.ts       # Root application module
├── app.controller.ts   # Optional: Root health check/info controller
├── app.service.ts      # Optional: Root service
│
├── modules/            # Directory for all feature modules
│   ├── auth/           # Authentication module
│   │   ├── auth.module.ts
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   ├── guards/
│   │   │   └── jwt-auth.guard.ts # JWT validation using Supabase client
│   │   └── strategies/ # Optional: for Passport.js if not solely relying on Supabase client
│   │       └── jwt.strategy.ts
│   ├── calculations/   # Calculation History & Line Items Module
│   │   ├── calculations.module.ts
│   │   ├── calculations.controller.ts
│   │   ├── calculations.service.ts
│   │   ├── line-items.controller.ts # Controller for line item actions
│   │   ├── line-items.service.ts    # Service for line item logic
│   │   └── dtos/
│   │       ├── create-calculation.dto.ts
│   │       ├── update-calculation.dto.ts
│   │       ├── add-package-line-item.dto.ts
│   │       └── add-custom-line-item.dto.ts
│   ├── packages/         # Package catalogue browsing/options module
│   │   └── ...
│   ├── templates/        # Template module
│   │   └── ...
│   ├── clients/          # Client module
│   │   └── ...
│   ├── events/           # Event module
│   │   └── ...
│   ├── config-data/      # Module for Cities, Currencies, Categories etc.
│   │   └── ...
│   └── admin/            # Parent module for all admin-specific features
│       └── ...
│
├── core/               # Core functionalities (providers, middleware, filters) used globally
│   ├── supabase/         # Supabase client setup
│   │   └── supabase.module.ts
│   │   └── supabase.service.ts # Optional: Service providing configured client
│   ├── middleware/
│   │   └── logger.middleware.ts # Example: Request logging
│   ├── filters/
│   │   └── http-exception.filter.ts # Global error formatting
│   └── pipes/
│       └── validation.pipe.ts # Global validation pipe setup
│
├── shared/             # Reusable utilities, interfaces, constants, enums not tied to core or a specific module
│   └── utils/
│   └── constants/
│   └── types/          # e.g., Shared interfaces/types, Generated Supabase types
│
└── config/             # Application configuration setup (using @nestjs/config)
    └── config.module.ts
    └── app-config.service.ts # Typed access to env variables
```

### Folder Naming and Responsibility

#### Folder Naming

- Use `lowercase` and `kebab-case` for folder names (e.g., `calculation-history`, `line-items`).
- Feature module folders should generally represent the primary entity or concept they manage (e.g., `calculations`, `packages`, `users`).

#### Folder Responsibility

- `src/`: Root source code directory.
- `src/modules`: Contains the distinct business features/domains of your application. Each module is self-contained but can import/export providers/services with other modules via the `*.module.ts` file.
- `src/core`: Holds essential, application-wide infrastructure setup like Supabase client configuration (`SupabaseModule`/`SupabaseService`), global error filters, global pipes, core middleware. These are typically imported once in `app.module.ts`.
- `src/shared`: Place for code reusable across _multiple_ feature modules but not fundamental enough for `core`. Examples: common utility functions, shared TypeScript interfaces/types (including generated Supabase types), application-wide constants. Avoid putting core business logic here.
- `src/config`: Dedicated to loading and providing access to configuration (environment variables, etc.), often using `@nestjs/config`.

**Within a Feature Module (src/modules/feature/):**

- `*.module.ts`: Defines the NestJS module scope, imports other modules, declares controllers, and provides services.
- `*.controller.ts`: Defines API routes for the feature, handles HTTP request/response cycle, performs input validation using DTOs/Pipes, delegates business logic to services.
- `*.service.ts`: Implements the core business logic for the feature, interacts with the database (via injected `SupabaseService` or Supabase client directly), calls other services if necessary.
- `dtos/`: Contains Data Transfer Object classes used for API request validation and response structuring.
- `guards/`, `pipes/`, `filters/`: Contains module-specific implementations if needed (though often these are global in `core/`).

### Middleware Responsibility (NestJS)

- Middleware functions execute _before_ route handlers in the request pipeline.
- **Use Cases:** Request logging (like the `LoggerMiddleware` example), setting CORS headers, applying security headers (e.g., using `helmet`), manipulating raw request/response objects (less common), very basic auth checks (though Guards are preferred for authorization).
- **What it's NOT for:** Business logic, request payload validation (use Pipes/DTOs), authorization logic (use Guards).

### File Naming (NestJS Conventions)

- Follow the `feature-name.suffix.ts` pattern:
  - Module: `calculations.module.ts`
  - Controller: `calculations.controller.ts`
  - Service: `calculations.service.ts`
  - DTOs: `create-calculation.dto.ts`, `update-calculation.dto.ts` (in `dtos/` subfolder)
  - Guards: `jwt-auth.guard.ts`
  - Pipes: `validation.pipe.ts`
  - Filters: `http-exception.filter.ts`
  - Middleware: `logger.middleware.ts`
- Test files use `.spec.ts`: `calculations.service.spec.ts`

### Database Naming vs. Code

Database tables/columns use `snake_case` (e.g., `calculation_history`, `event_start_date`). When using the `@supabase/supabase-js` client:

- Raw SQL or direct queries will use `snake_case`.
- The client's helper methods (`.select()`, `.insert()`, etc.) typically work with `snake_case` column names by default when interacting with the API layer (PostgREST).
- You can define your TypeScript interfaces/types using `camelCase` and perform mapping in your service layer if desired for consistency within the NestJS application code, but the interaction with Supabase client methods will likely involve `snake_case`.
- Using `supabase gen types typescript` can help bridge this by generating types based on your `snake_case` schema.
