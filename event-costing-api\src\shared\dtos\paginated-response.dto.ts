import { ApiProperty } from '@nestjs/swagger';

export class PaginatedResponseDto<T> {
  @ApiProperty({
    isArray: true,
    description: 'Array of data items for the current page',
  })
  data: T[];

  @ApiProperty({
    type: Number,
    description: 'Total count of matching records across all pages',
  })
  count: number;

  @ApiProperty({
    type: Number,
    description: 'Number of items per page',
    default: 20,
  })
  limit: number;

  @ApiProperty({
    type: Number,
    description: 'Number of items to skip',
    default: 0,
  })
  offset: number;
}
