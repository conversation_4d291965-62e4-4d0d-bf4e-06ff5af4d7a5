import { ApiProperty } from '@nestjs/swagger';

export class ClientDto {
  @ApiProperty({ type: String, format: 'uuid' })
  id: string;

  @ApiProperty()
  client_name: string;

  @ApiProperty({ nullable: true })
  contact_person: string | null;

  @ApiProperty({ nullable: true, format: 'email' })
  email: string | null;

  @ApiProperty({ nullable: true })
  phone_number: string | null;

  // Add other relevant fields from the 'clients' table if needed
  // e.g., address, notes, etc.
  @ApiProperty({ type: String, format: 'date-time' })
  created_at: string;
}
