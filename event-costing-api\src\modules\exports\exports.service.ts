import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { CalculationsService } from '../calculations/calculations.service';
import { User } from '@supabase/supabase-js';
import { CreateExportDto } from './dto/create-export.dto';
import { ExportResponseDto } from './dto/export-response.dto';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ExportStatusResponseDto } from './dto/export-status-response.dto';
import { ExportStatus } from './enums/export-status.enum';
import { ExportFormat } from './enums/export-format.enum';
import { ExportHistory } from './interfaces/export-history.interface';
import { ExportUpdateDetails } from './interfaces/export-update-details.interface';
import { ExportStorageService } from './services/export-storage.service';
import { CsvExportJobData } from './processors/csv-export.processor';
import { PdfExportJobData } from './processors/pdf-export.processor';
import { XlsxExportJobData } from './processors/xlsx-export.processor';

@Injectable()
export class ExportsService {
  private readonly logger = new Logger(ExportsService.name);
  private readonly EXPORT_HISTORY_TABLE = 'export_history';

  /**
   * Maps database status (uppercase) to frontend-compatible status (lowercase)
   * while maintaining type safety with the ExportStatus enum
   */
  private mapStatusToFrontend(dbStatus: string): ExportStatus {
    switch (dbStatus.toUpperCase()) {
      case 'PENDING':
        return 'pending' as ExportStatus;
      case 'PROCESSING':
        return 'processing' as ExportStatus;
      case 'COMPLETED':
        return 'completed' as ExportStatus;
      case 'FAILED':
        return 'failed' as ExportStatus;
      default:
        this.logger.warn(
          `Unknown export status: ${dbStatus}, defaulting to pending`,
        );
        return 'pending' as ExportStatus;
    }
  }

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly calculationsService: CalculationsService,
    private readonly storageService: ExportStorageService, // Inject storage service
    @InjectQueue('csv-exports')
    private readonly csvExportsQueue: Queue<CsvExportJobData>,
    @InjectQueue('pdf-exports')
    private readonly pdfExportsQueue: Queue<PdfExportJobData>,
    @InjectQueue('xlsx-exports')
    private readonly xlsxExportsQueue: Queue<XlsxExportJobData>,
  ) {}

  async initiateExport(
    createDto: CreateExportDto,
    user: User,
  ): Promise<ExportResponseDto> {
    const { calculationId, format, recipient } = createDto;

    if (!calculationId || !format) {
      throw new BadRequestException(
        'Invalid export request: Missing calculationId or format.',
      );
    }

    this.logger.log(
      `User ${user.id} initiating export for calculation ${calculationId} as ${format}`,
    );

    // 1. Check Ownership
    await this.calculationsService.checkCalculationOwnership(
      calculationId,
      user.id,
    );

    const supabase = this.supabaseService.getClient();

    // 2. Create history record
    let historyRecord: ExportHistory | null = null;
    try {
      const { data, error: insertError } = await supabase
        .from(this.EXPORT_HISTORY_TABLE)
        .insert({
          calculation_id: calculationId,
          created_by: user.id,
          export_type: format,
          recipient: recipient,
          status: ExportStatus.PENDING,
        })
        .select('id, created_at')
        .single<{ id: string; created_at: string }>();

      if (insertError) throw insertError;
      if (!data) throw new Error('History record ID not returned.');

      historyRecord = {
        // Build partial record for immediate use
        id: data.id,
        created_at: data.created_at,
        calculation_id: calculationId,
        created_by: user.id,
        export_type: format,
        status: ExportStatus.PENDING,
        recipient: recipient,
      };
      this.logger.log(`Created export history record ${historyRecord.id}`);
    } catch (error: unknown) {
      this.logger.error(
        `Failed to create export history record: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException(
        'Failed to initiate export process (database error).',
      );
    }

    // 3. Add job to the appropriate queue based on format
    const baseJobData = {
      calculationId,
      userId: user.id,
      exportHistoryId: historyRecord.id,
    };

    try {
      let queueName: string;
      let queue: Queue;

      switch (format) {
        case ExportFormat.CSV:
          queue = this.csvExportsQueue;
          queueName = 'csv-exports';
          break;
        case ExportFormat.PDF:
          queue = this.pdfExportsQueue;
          queueName = 'pdf-exports';
          break;
        case ExportFormat.XLSX:
          queue = this.xlsxExportsQueue;
          queueName = 'xlsx-exports';
          break;
        default:
          throw new BadRequestException(`Unsupported export format: ${format}`);
      }

      await queue.add('process-export', baseJobData, {
        attempts: 3,
        backoff: { type: 'exponential', delay: 5000 },
        removeOnComplete: 1000,
        removeOnFail: 500,
      });

      this.logger.log(
        `Added ${format} export job to ${queueName} queue for history ID: ${historyRecord.id}`,
      );
    } catch (queueError: unknown) {
      this.logger.error(
        `Failed to add job to exports queue for history ${historyRecord.id}: ${queueError instanceof Error ? queueError.message : String(queueError)}`,
        queueError instanceof Error ? queueError.stack : undefined,
      );
      // Attempt to mark history as FAILED
      try {
        await this.updateExportHistoryStatus(
          historyRecord.id,
          ExportStatus.FAILED,
          {
            error: 'Failed to queue the export job.',
          },
        );
      } catch (updateError: unknown) {
        this.logger.error(
          `Failed to update history ${historyRecord.id} to FAILED after queue error: ${updateError instanceof Error ? updateError.message : String(updateError)}`,
        );
      }
      throw new InternalServerErrorException('Failed to queue export job.');
    }

    // 4. Return response
    return {
      exportId: historyRecord.id,
      status: ExportStatus.PENDING,
      message: `Export process initiated for calculation ${calculationId} as ${format}. Check status later.`,
      createdAt: new Date(historyRecord.created_at),
    };
  }

  // Method to update export history status (called by processor or service)
  async updateExportHistoryStatus(
    historyId: string,
    status: ExportStatus,
    details?: ExportUpdateDetails,
  ): Promise<void> {
    this.logger.log(`Updating export history ${historyId} to status ${status}`);
    const supabase = this.supabaseService.getClient();
    const updateData: Partial<ExportHistory> = { status };

    if (status === ExportStatus.COMPLETED || status === ExportStatus.FAILED) {
      updateData.completed_at = new Date().toISOString();
    }

    if (details?.storagePath) updateData.storage_path = details.storagePath;
    if (details?.fileName) updateData.file_name = details.fileName;
    // if (details?.fileSize) updateData.file_size_bytes = details.fileSize; // Uncomment if column exists
    // if (details?.mimeType) updateData.mime_type = details.mimeType; // Uncomment if column exists
    if (details?.error) updateData.error_message = details.error;

    const { error } = await supabase
      .from(this.EXPORT_HISTORY_TABLE)
      .update(updateData)
      .eq('id', historyId);

    if (error) {
      this.logger.error(
        `Failed to update export history ${historyId} status: ${error.message}`,
      );
      // Consider if this should throw an error
    }
  }

  async getExportStatus(
    exportId: string,
    userId: string,
  ): Promise<ExportStatusResponseDto> {
    this.logger.log(`User ${userId} checking status for export ${exportId}`);

    // 1. Check Ownership
    await this.checkExportOwnership(exportId, userId);

    // 2. Fetch Full History Record
    const supabase = this.supabaseService.getClient();
    const { data: historyRecord, error: fetchError } = await supabase
      .from(this.EXPORT_HISTORY_TABLE)
      .select('*')
      .eq('id', exportId)
      .single<ExportHistory>();

    if (fetchError) {
      this.logger.error(
        `Error fetching export status for ${exportId}: ${fetchError.message}`,
      );
      throw new InternalServerErrorException(
        'Could not retrieve export status.',
      );
    }

    if (!historyRecord) {
      throw new NotFoundException(
        `Export record with ID ${exportId} not found.`,
      );
    }

    // 3. Get Signed URL using ExportStorageService
    const downloadUrl = await this.storageService.getSignedUrl(
      historyRecord.storage_path,
    );

    // 4. Construct Response DTO
    const response: ExportStatusResponseDto = {
      exportId: historyRecord.id,
      status: this.mapStatusToFrontend(historyRecord.status),
      format: historyRecord.export_type.toLowerCase() as ExportFormat, // ExportFormat enum already uses lowercase
      downloadUrl: downloadUrl ?? undefined,
      fileName: historyRecord.file_name ?? undefined,
      errorMessage: historyRecord.error_message ?? undefined,
      createdAt: new Date(historyRecord.created_at),
      completedAt: historyRecord.completed_at
        ? new Date(historyRecord.completed_at)
        : undefined,
    };

    return response;
  }

  /**
   * Get all exports for a specific calculation
   */
  async getExportsByCalculation(
    calculationId: string,
    userId: string,
  ): Promise<ExportStatusResponseDto[]> {
    this.logger.log(
      `User ${userId} fetching exports for calculation ${calculationId}`,
    );

    // 1. Check Calculation Ownership
    await this.calculationsService.checkCalculationOwnership(
      calculationId,
      userId,
    );

    // 2. Fetch Export History Records
    const supabase = this.supabaseService.getClient();
    const { data: historyRecords, error: fetchError } = await supabase
      .from(this.EXPORT_HISTORY_TABLE)
      .select('*')
      .eq('calculation_id', calculationId)
      .eq('created_by', userId)
      .order('created_at', { ascending: false });

    if (fetchError) {
      this.logger.error(
        `Error fetching exports for calculation ${calculationId}: ${fetchError.message}`,
      );
      throw new InternalServerErrorException(
        'Could not retrieve export history.',
      );
    }

    // 3. Transform to Response DTOs
    const responses: ExportStatusResponseDto[] = [];

    for (const record of historyRecords || []) {
      // Get signed URL if file exists
      const downloadUrl = record.storage_path
        ? await this.storageService.getSignedUrl(record.storage_path)
        : undefined;

      responses.push({
        exportId: record.id,
        status: this.mapStatusToFrontend(record.status),
        format: record.export_type.toLowerCase() as ExportFormat, // ExportFormat enum already uses lowercase
        downloadUrl: downloadUrl ?? undefined,
        fileName: record.file_name ?? undefined,
        errorMessage: record.error_message ?? undefined,
        createdAt: new Date(record.created_at),
        completedAt: record.completed_at
          ? new Date(record.completed_at)
          : undefined,
      });
    }

    return responses;
  }

  private async checkExportOwnership(
    exportId: string,
    userId: string,
  ): Promise<void> {
    type ExportOwnerCheck = Pick<ExportHistory, 'id' | 'created_by'>;

    const { data: historyRecord, error } = await this.supabaseService
      .getClient()
      .from(this.EXPORT_HISTORY_TABLE)
      .select('id, created_by')
      .eq('id', exportId)
      .single<ExportOwnerCheck>();

    if (error) {
      this.logger.error(
        `Error fetching export history ${exportId} for ownership check: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Error checking export history ownership.',
      );
    }

    if (!historyRecord) {
      throw new NotFoundException(
        `Export record with ID ${exportId} not found.`,
      );
    }

    if (historyRecord.created_by !== userId) {
      this.logger.warn(
        `User ${userId} attempted to access export ${exportId} owned by ${historyRecord.created_by}`,
      );
      throw new ForbiddenException(
        'You do not have permission to access this export record.',
      );
    }
  }
}
