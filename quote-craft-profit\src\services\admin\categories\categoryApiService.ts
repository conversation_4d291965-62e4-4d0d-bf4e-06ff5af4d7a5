/**
 * Category API Service
 *
 * This service provides methods for interacting with the categories API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import { Category, CategoryOrderItem } from '@/pages/admin/categories/types';
import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';

/**
 * Get all categories from the backend API
 * @returns Promise resolving to an array of categories
 */
export const getAllCategoriesFromApi = async (): Promise<Category[]> => {
  try {
    console.log('Fetching categories from backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.CATEGORIES.GET_ALL);

    console.log('Categories fetched successfully from backend API');

    // Transform the data to match the expected format
    return response.data.map((category: any) => ({
      id: category.id,
      code: category.code || category.id, // Use code if available, fallback to id
      name: category.name,
      description: category.description || '',
      icon: category.icon,
      display_order: category.display_order,
      created_at: category.created_at,
      updated_at: category.updated_at,
    }));
  } catch (error) {
    console.error('Error fetching categories from backend API:', error);
    throw error;
  }
};

/**
 * Get a category by ID from the backend API
 * @param id - The category ID
 * @returns Promise resolving to a category
 */
export const getCategoryByIdFromApi = async (id: string): Promise<Category> => {
  try {
    console.log(`Fetching category with ID ${id} from backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.CATEGORIES.GET_BY_ID(id));

    console.log('Category fetched successfully from backend API');

    // Transform the data to match the expected format
    return {
      id: response.data.id,
      code: response.data.code || response.data.id, // Use code if available, fallback to id
      name: response.data.name,
      description: response.data.description || '',
      icon: response.data.icon,
      display_order: response.data.display_order,
      created_at: response.data.created_at,
      updated_at: response.data.updated_at,
    };
  } catch (error) {
    console.error(`Error fetching category with ID ${id} from backend API:`, error);
    throw error;
  }
};

/**
 * Create a new category using the backend API
 * @param categoryData - The category data to create
 * @returns Promise resolving to the created category
 */
export const createCategoryFromApi = async (categoryData: {
  name: string;
  description?: string;
  display_order?: number;
  is_active?: boolean;
}): Promise<Category> => {
  try {
    console.log('Creating category with backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.post(API_ENDPOINTS.CATEGORIES.CREATE, categoryData);

    console.log('Category created successfully with backend API');

    // Transform the data to match the expected format
    return {
      id: response.data.id,
      code: response.data.code || response.data.id,
      name: response.data.name,
      description: response.data.description || '',
      icon: response.data.icon,
      display_order: response.data.display_order,
      created_at: response.data.created_at,
      updated_at: response.data.updated_at,
    };
  } catch (error) {
    console.error('Error creating category with backend API:', error);
    throw error;
  }
};

/**
 * Update a category using the backend API
 * @param id - The category ID
 * @param categoryData - The category data to update
 * @returns Promise resolving to the updated category
 */
export const updateCategoryFromApi = async (
  id: string,
  categoryData: {
    name?: string;
    description?: string;
    display_order?: number;
    is_active?: boolean;
  },
): Promise<Category> => {
  try {
    console.log(`Updating category with ID ${id} using backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.patch(API_ENDPOINTS.CATEGORIES.UPDATE(id), categoryData);

    console.log('Category updated successfully with backend API');

    // Transform the data to match the expected format
    return {
      id: response.data.id,
      code: response.data.code || response.data.id,
      name: response.data.name,
      description: response.data.description || '',
      icon: response.data.icon,
      display_order: response.data.display_order,
      created_at: response.data.created_at,
      updated_at: response.data.updated_at,
    };
  } catch (error) {
    console.error(`Error updating category with ID ${id} using backend API:`, error);
    throw error;
  }
};

/**
 * Delete a category using the backend API
 * @param id - The category ID
 * @returns Promise resolving when the category is deleted
 */
export const deleteCategoryFromApi = async (id: string): Promise<void> => {
  try {
    console.log(`Deleting category with ID ${id} using backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.delete(API_ENDPOINTS.CATEGORIES.DELETE(id));

    console.log('Category deleted successfully with backend API');
  } catch (error) {
    console.error(`Error deleting category with ID ${id} using backend API:`, error);
    throw error;
  }
};

/**
 * Update the display order of multiple categories using the backend API
 * @param categories - Array of category order items with id and display_order
 * @returns Promise resolving to the updated categories
 */
export const updateCategoryOrderFromApi = async (
  categories: CategoryOrderItem[],
): Promise<Category[]> => {
  try {
    console.log('Updating category order with backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.patch(API_ENDPOINTS.CATEGORIES.UPDATE_ORDER, {
      categories,
    });

    console.log('Category order updated successfully with backend API');

    // Return the updated categories
    return response.data.categories;
  } catch (error) {
    console.error('Error updating category order with backend API:', error);
    throw error;
  }
};
