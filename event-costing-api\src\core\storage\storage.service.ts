import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';

/**
 * Interface for file metadata
 */
export interface FileMetadata {
  name: string;
  size: number;
  mimetype: string;
  created_at: string;
  updated_at: string;
  last_accessed_at: string;
  publicUrl: string;
  isFolder?: boolean;
}

/**
 * Centralized storage service for handling file operations
 * This service provides methods for uploading, downloading, and managing files in Supabase Storage
 */
@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);

  constructor(private readonly supabaseService: SupabaseService) {
    // Initialize required buckets on service startup
    this.initializeBuckets().catch(error => {
      this.logger.error('Failed to initialize storage buckets', error);
    });
  }

  /**
   * Initialize required storage buckets
   * This method checks if required buckets exist and creates them if they don't
   */
  private async initializeBuckets(): Promise<void> {
    this.logger.log('Initializing storage buckets');
    const requiredBuckets = ['profiles', 'calculation-exports'];

    for (const bucketName of requiredBuckets) {
      await this.ensureBucketExists(bucketName);
    }
  }

  /**
   * Ensure a bucket exists, creating it if it doesn't
   * @param bucketName - The name of the bucket to check/create
   */
  async ensureBucketExists(bucketName: string): Promise<void> {
    this.logger.log(`Ensuring bucket exists: ${bucketName}`);
    const supabase = this.supabaseService.getClient();

    try {
      // Check if bucket exists
      const { data: buckets, error: listError } =
        await supabase.storage.listBuckets();

      if (listError) {
        this.logger.error(
          `Failed to list buckets: ${listError.message}`,
          listError.stack,
        );
        throw new InternalServerErrorException(
          'Failed to check if bucket exists',
        );
      }

      const bucketExists = buckets.some(bucket => bucket.name === bucketName);

      if (!bucketExists) {
        this.logger.log(`Bucket ${bucketName} does not exist, creating it`);

        // Create the bucket with public access
        const { error: createError } = await supabase.storage.createBucket(
          bucketName,
          {
            public: true, // Make files publicly accessible
          },
        );

        if (createError) {
          this.logger.error(
            `Failed to create bucket ${bucketName}: ${createError.message}`,
            createError.stack,
          );
          throw new InternalServerErrorException(
            `Failed to create bucket ${bucketName}`,
          );
        }

        this.logger.log(`Bucket ${bucketName} created successfully`);
      } else {
        this.logger.log(`Bucket ${bucketName} already exists`);
      }
    } catch (error) {
      this.logger.error(
        `Unexpected error checking/creating bucket ${bucketName}: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException(
        `Failed to ensure bucket ${bucketName} exists`,
      );
    }
  }

  /**
   * Upload a file to a specific bucket
   * @param bucketName - The name of the storage bucket
   * @param filePath - The path where the file will be stored
   * @param fileBuffer - The file data as a Buffer
   * @param contentType - The MIME type of the file
   * @param options - Additional upload options
   * @returns The storage path of the uploaded file
   */
  async uploadFile(
    bucketName: string,
    filePath: string,
    fileBuffer: Buffer,
    contentType: string,
    options?: { upsert?: boolean },
  ): Promise<string> {
    this.logger.log(`Uploading file to ${bucketName}/${filePath}`);

    // Ensure the bucket exists before uploading
    await this.ensureBucketExists(bucketName);

    const supabase = this.supabaseService.getClient();

    try {
      const { error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, fileBuffer, {
          upsert: options?.upsert ?? true,
          contentType,
        });

      if (uploadError) {
        this.logger.error(
          `Failed to upload file to ${bucketName}/${filePath}: ${uploadError.message}`,
          uploadError.stack,
        );
        throw new InternalServerErrorException(
          `Failed to upload file: ${uploadError.message}`,
        );
      }

      this.logger.log(
        `File uploaded successfully to ${bucketName}/${filePath}`,
      );
      return filePath;
    } catch (error) {
      if (error instanceof InternalServerErrorException) {
        throw error; // Re-throw if it's already our custom exception
      }

      this.logger.error(
        `Unexpected error uploading file to ${bucketName}/${filePath}: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException(
        'Failed to upload file due to an unexpected error.',
      );
    }
  }

  /**
   * Get a signed URL for a file
   * @param bucketName - The name of the storage bucket
   * @param filePath - The path of the file
   * @param expiresIn - The number of seconds until the URL expires (default: 3600 = 1 hour)
   * @returns The signed URL or null if the file doesn't exist
   */
  async getSignedUrl(
    bucketName: string,
    filePath: string | null | undefined,
    expiresIn: number = 3600,
  ): Promise<string | null> {
    if (!filePath) {
      return null;
    }

    this.logger.debug(`Generating signed URL for ${bucketName}/${filePath}`);
    const supabase = this.supabaseService.getClient();

    try {
      const { data: urlData, error: urlError } = await supabase.storage
        .from(bucketName)
        .createSignedUrl(filePath, expiresIn);

      if (urlError) {
        this.logger.error(
          `Failed to create signed URL for ${bucketName}/${filePath}: ${urlError.message}`,
        );
        return null;
      }

      return urlData?.signedUrl ?? null;
    } catch (error) {
      this.logger.error(
        `Unexpected error generating signed URL for ${bucketName}/${filePath}: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined,
      );
      return null;
    }
  }

  /**
   * Get a public URL for a file
   * @param bucketName - The name of the storage bucket
   * @param filePath - The path of the file
   * @returns The public URL
   */
  getPublicUrl(bucketName: string, filePath: string): string {
    this.logger.debug(`Generating public URL for ${bucketName}/${filePath}`);
    const supabase = this.supabaseService.getClient();

    const { data } = supabase.storage.from(bucketName).getPublicUrl(filePath);
    return data.publicUrl;
  }

  /**
   * Delete a file
   * @param bucketName - The name of the storage bucket
   * @param filePath - The path of the file to delete
   */
  async deleteFile(bucketName: string, filePath: string): Promise<void> {
    this.logger.log(`Deleting file ${bucketName}/${filePath}`);
    const supabase = this.supabaseService.getClient();

    const { error } = await supabase.storage
      .from(bucketName)
      .remove([filePath]);

    if (error) {
      this.logger.error(
        `Failed to delete file ${bucketName}/${filePath}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed to delete file.');
    }

    this.logger.log(`File ${bucketName}/${filePath} deleted successfully`);
  }

  /**
   * Get metadata for a file
   * @param bucketName - The name of the storage bucket
   * @param filePath - The path of the file
   * @returns The file metadata
   */
  async getFileMetadata(
    bucketName: string,
    filePath: string,
  ): Promise<FileMetadata | null> {
    this.logger.log(`Getting metadata for file ${bucketName}/${filePath}`);
    const supabase = this.supabaseService.getClient();

    try {
      // First, check if the file exists by listing files with the same path
      const { data: files, error: listError } = await supabase.storage
        .from(bucketName)
        .list(filePath.split('/').slice(0, -1).join('/'), {
          limit: 100,
          offset: 0,
          sortBy: { column: 'name', order: 'asc' },
        });

      if (listError) {
        this.logger.error(
          `Failed to list files in ${bucketName}/${filePath.split('/').slice(0, -1).join('/')}: ${listError.message}`,
          listError.stack,
        );
        throw new InternalServerErrorException('Failed to get file metadata.');
      }

      // Find the file in the list
      const fileName = filePath.split('/').pop();
      const file = files?.find(f => f.name === fileName);

      if (!file) {
        this.logger.warn(`File ${bucketName}/${filePath} not found`);
        return null;
      }

      // Get the public URL
      const publicUrl = this.getPublicUrl(bucketName, filePath);

      // Return the metadata
      const metadata: FileMetadata = {
        name: file.name,
        size: file.metadata?.size || 0,
        mimetype: file.metadata?.mimetype || 'application/octet-stream',
        created_at: file.created_at,
        updated_at: file.updated_at,
        last_accessed_at: file.last_accessed_at,
        publicUrl,
      };
      return metadata;
    } catch (error) {
      this.logger.error(
        `Unexpected error getting metadata for ${bucketName}/${filePath}: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException('Failed to get file metadata.');
    }
  }

  /**
   * List files in a bucket or folder
   * @param bucketName - The name of the storage bucket
   * @param folderPath - The path of the folder (optional)
   * @returns Array of file metadata
   */
  async listFiles(
    bucketName: string,
    folderPath: string = '',
  ): Promise<FileMetadata[]> {
    this.logger.log(`Listing files in ${bucketName}/${folderPath}`);
    const supabase = this.supabaseService.getClient();

    try {
      const { data: files, error } = await supabase.storage
        .from(bucketName)
        .list(folderPath, {
          limit: 100,
          offset: 0,
          sortBy: { column: 'name', order: 'asc' },
        });

      if (error) {
        this.logger.error(
          `Failed to list files in ${bucketName}/${folderPath}: ${error.message}`,
          error.stack,
        );
        throw new InternalServerErrorException('Failed to list files.');
      }

      // Add public URLs to the files
      return files.map(file => {
        const filePath = folderPath ? `${folderPath}/${file.name}` : file.name;
        const publicUrl = this.getPublicUrl(bucketName, filePath);
        const isFolder = file.id.endsWith('/');

        const fileMetadata: FileMetadata = {
          name: file.name,
          size: file.metadata?.size || 0,
          mimetype:
            file.metadata?.mimetype ||
            (isFolder ? 'folder' : 'application/octet-stream'),
          created_at: file.created_at,
          updated_at: file.updated_at,
          last_accessed_at: file.last_accessed_at,
          publicUrl: isFolder ? '' : publicUrl,
          isFolder,
        };
        return fileMetadata;
      });
    } catch (error) {
      this.logger.error(
        `Unexpected error listing files in ${bucketName}/${folderPath}: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException('Failed to list files.');
    }
  }
}
