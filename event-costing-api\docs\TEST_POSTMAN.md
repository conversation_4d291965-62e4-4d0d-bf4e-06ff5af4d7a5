# Postman API Documentation Index

This document serves as an index for the detailed Postman documentation, which has been split into module-specific files.

**Base URL:** `http://localhost:5000` (or your configured port)

---

## Modules

- **[Authentication](./postman/auth.md)** (`/auth`): Login, Logout
- **[Users](./postman/users.md)** (`/users`): Get Profile
- **[Calculations](./postman/calculations.md)** (`/calculations`): CRUD, Create from Template, Get Totals
- **[Calculation Items](./postman/calculation-items.md)** (`/calculations/{calcId}/items`): Add/Delete Line Items (Package/Custom)
- **[Templates](./postman/templates.md)** (`/templates`): List, Create from Calculation (Admin)
- **[Reference Data](./postman/reference-data.md)** (`/cities`, `/currencies`, `/categories`, `/clients`, `/events`): Get Lists
- **[Package Catalogue (User)](./postman/package-catalogue.md)** (`/packages`): Find Variations, Get Options
- **[Admin Catalogue Management](./postman/admin-catalogue.md)** (`/admin/catalogue`): CRUD for Packages, Prices, Options, Dependencies, Cities
- **[Exports](./postman/exports.md)** (`/exports`): Initiate Export (CSV)

# Backend Testing Progress (API Endpoints)

This document tracks the testing status of API endpoints using Postman collections found in `/docs/postman/`.

**Status Legend:** `[x]` = Tested OK, `[ ]` = Not Tested, `[~]` = Partially Tested/Needs Update

---

## End User Endpoints

- `Auth`: `[x]`
- `Users (/me)`: `[x]`
- `Calculations (CRUD, Create from Template)`: `[x]`
- `Calculation Items (Add/Delete Package/Custom)`: `[x]`
- `Calculation Totals`: `[x]`
- `Templates (Public)`: `[ ]`
  - `[ ] GET /templates`
  - `[ ] GET /templates?search=...&cityId=...` // Example filters
  - `[ ] GET /templates/:id`
- `Clients (CRUD)`: `[ ]`
  - `[ ] POST /clients`
  - `[ ] GET /clients`
  - `[ ] GET /clients?search=...`
  - `[ ] GET /clients/:id`
  - `[ ] PATCH /clients/:id`
  - `[ ] DELETE /clients/:id`
- `Events (CRUD)`: `[ ]`
  - `[ ] POST /events`
  - `[ ] GET /events`
  - `[ ] GET /events?search=...&status=...&clientId=...`
  - `[ ] GET /events/:id`
  - `[ ] PATCH /events/:id`
  - `[ ] DELETE /events/:id` (Soft Delete)
- `Supporting Data (Cities, Currencies, Categories - GET)`: `[x]`
- `Package Catalogue (GET Variations/Options)`: `[x]`
- `Exports`: `[~]`
  - `[ ] POST /exports` (Initiate PDF/xlsx/CSV Export)
  - `[ ] GET /exports/:id/status` (Check Export Status - TBD) // Added April 21, 2025

## Admin Endpoints

- `Admin Settings (GET/PUT key)`: `[x]`
- `Admin Cities (CRUD)`: `[x]`
- `Admin Currencies (CRUD)`: `[~]` (Needs update for `
- `Admin Catalogue (...)`: `[x]`
- `Admin Templates (CRUD)`: `[ ]`
  - `[ ] POST /admin/templates/from-calculation`
  - `[ ] GET /admin/templates`
  - `[ ] GET /admin/templates?name=...&isPublic=...&isDeleted=...` // Example filters
  - `[ ] GET /admin/templates/:id`
  - `[ ] PUT /admin/templates/:id`
  - `[ ] DELETE /admin/templates/:id`
- `Admin Users (CRUD)`: `[ ]`
