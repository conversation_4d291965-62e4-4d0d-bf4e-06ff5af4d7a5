import { useState, useEffect } from "react";
import { showSuccess, showError, showInfo } from "@/lib/notifications";
import { toast } from "sonner";

// Hook for smart notifications
export const useSmartNotifications = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  const showSuccessNotification = (message: string, description?: string) => {
    showSuccess(message, { description });
  };

  const showErrorNotification = (message: string, description?: string) => {
    showError(message, { description });
  };

  const showInfoNotification = (message: string, description?: string) => {
    showInfo(message, { description });
  };

  const showLoading = (message: string, description?: string) => {
    return toast.loading(message, {
      description,
    });
  };

  return {
    isOnline,
    showSuccess: showSuccessNotification,
    showError: showErrorNotification,
    showInfo: showInfoNotification,
    showLoading,
  };
};
