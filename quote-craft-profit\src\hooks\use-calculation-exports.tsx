import { useState, useEffect, useRef, useCallback } from "react";
import { showSuccess, showError } from "@/lib/notifications";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { ExportLogger } from "@/utils/export-logger";
import { useExportPerformance } from "./use-export-performance";

// Match backend enum exactly - check backend export-format.enum.ts
export type ExportFormat = "pdf" | "xlsx" | "csv";

export interface Export {
  id: string;
  format: ExportFormat;
  created_at: string;
  status: "pending" | "processing" | "completed" | "failed";
  file_url?: string;
  download_url?: string;
  error_message?: string;
}

export interface ExportResponse {
  exportId: string;
  status: string;
  message: string;
  createdAt: string;
}

/**
 * Hook for managing calculation exports
 * Connects to the real backend export API
 * @param calculationId - The calculation ID to fetch exports for
 * @param isDialogOpen - Whether the export dialog is currently open (controls polling and data fetching)
 */
export function useCalculationExports(
  calculationId: string,
  isDialogOpen: boolean = false
) {
  const queryClient = useQueryClient();
  const [internalDialogOpen, setInternalDialogOpen] = useState(false);
  const previousExportsRef = useRef<Export[]>([]);
  const isFirstLoadRef = useRef(true);
  const { trackApiCall, trackPollingCycle } = useExportPerformance();

  // Reset refs when calculationId changes or component mounts
  useEffect(() => {
    ExportLogger.logDialogState(isDialogOpen, calculationId);
    previousExportsRef.current = [];
    isFirstLoadRef.current = true;
  }, [calculationId, isDialogOpen]);

  // Smart polling strategy based on export age and status
  const getPollingInterval = useCallback(
    (data: Export[]): number | false => {
      if (!isDialogOpen) {
        ExportLogger.logPollingActivity(0, 0, false);
        return false;
      }

      if (!Array.isArray(data) || data.length === 0) {
        return false;
      }

      const activeExports = data.filter(
        (exportItem) =>
          exportItem.status === "pending" || exportItem.status === "processing"
      );

      if (activeExports.length === 0) {
        ExportLogger.logPollingActivity(0, data.length, false);
        return false;
      }

      // Adaptive polling based on export age
      const now = new Date().getTime();
      const hasRecentExports = activeExports.some((exportItem) => {
        const createdAt = new Date(exportItem.created_at).getTime();
        const ageInMinutes = (now - createdAt) / (1000 * 60);
        return ageInMinutes < 2; // Less than 2 minutes old
      });

      // More frequent polling for recent exports
      const interval = hasRecentExports ? 2000 : 5000;
      ExportLogger.logPollingActivity(
        activeExports.length,
        data.length,
        interval
      );
      return interval;
    },
    [isDialogOpen]
  );

  // Fetch export history for the calculation
  const {
    data: exports = [],
    isLoading,
    refetch,
    error,
    isError,
  } = useQuery({
    queryKey: ["exports", calculationId],
    queryFn: async (): Promise<Export[]> => {
      const startTime = Date.now();
      try {
        console.log(
          `🚀 Fetching exports for calculation ${calculationId} (dialog open: ${isDialogOpen})`
        );
        ExportLogger.logCacheOperation("fetch", calculationId);
        const apiClient = await getAuthenticatedApiClient();
        const response = await apiClient.get(
          `/exports/calculation/${calculationId}`
        );

        trackApiCall("fetch exports", Date.now() - startTime);

        // Transform backend response to match frontend interface
        console.log("📥 Raw API response:", response.data);

        const transformedData = response.data.map((item: any) => {
          console.log("🔍 Processing item keys:", Object.keys(item));
          console.log("🔍 Processing item values:", item);

          const transformed = {
            id: item.exportId, // Backend uses 'exportId'
            format: item.format, // Backend uses 'format'
            created_at: item.createdAt, // Backend uses 'createdAt'
            status: item.status?.toLowerCase(), // Backend uses 'status', convert to lowercase
            file_url: item.downloadUrl, // Backend uses 'downloadUrl'
            download_url: item.downloadUrl, // Backend uses 'downloadUrl'
            error_message: item.errorMessage, // Backend uses 'errorMessage'
            file_name: item.fileName, // Backend uses 'fileName'
            completed_at: item.completedAt, // Backend uses 'completedAt'
          };

          console.log("🔄 Transformed export item:", {
            original: item,
            transformed: transformed,
          });

          return transformed;
        });

        console.log("📤 Final transformed data:", transformedData);
        return transformedData;
      } catch (error: any) {
        console.error("Error fetching exports:", error);

        // Handle specific error cases
        if (error.response?.status === 401) {
          console.error("Authentication failed for exports");
          showError("Authentication Error", {
            description: "Please log in again to view exports.",
          });
        } else if (error.response?.status === 404) {
          console.log("No exports found for calculation:", calculationId);
          // Check if it's a calculation not found vs no exports found
          const errorMessage = error.response?.data?.message || "";
          if (errorMessage.includes("not found or not accessible")) {
            console.warn(
              `Calculation ${calculationId} may be stale or deleted - clearing cache and refreshing`
            );

            // Immediately invalidate calculations cache to remove stale data
            queryClient.invalidateQueries({ queryKey: ["calculations"] });
            queryClient.removeQueries({
              queryKey: ["calculation", calculationId],
            });
            queryClient.removeQueries({ queryKey: ["exports", calculationId] });

            showError("Calculation No Longer Available", {
              description:
                "This calculation has been removed. The page will refresh with updated data.",
            });

            // Force refresh the page to get fresh data
            setTimeout(() => {
              window.location.reload();
            }, 1500);

            // Re-throw the error so React Query knows this is a real error
            throw error;
          }
          // For normal "no exports" 404s, don't show error toast and don't re-throw
          console.log("Normal 404 - no exports found, returning empty array");
        } else {
          console.error(
            "Unexpected error fetching exports:",
            error.response?.data || error.message
          );
          showError("Error Loading Exports", {
            description: "Failed to load export history. Please try again.",
          });
        }

        // Return empty array if no exports found or error occurs
        return [];
      }
    },
    enabled: !!calculationId && isDialogOpen, // Only fetch when dialog is open - prevents unnecessary API calls on page load
    staleTime: 30 * 1000, // Consider data fresh for 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: false, // Don't refetch on mount if data exists
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error?.response?.status === 401) {
        return false;
      }
      // Don't retry on calculation not found errors (404 with specific message)
      if (error?.response?.status === 404) {
        const errorMessage = error.response?.data?.message || "";
        if (errorMessage.includes("not found or not accessible")) {
          return false;
        }
      }
      // Don't retry on client errors (400-499) except for 429 (rate limit)
      if (
        error?.response?.status >= 400 &&
        error?.response?.status < 500 &&
        error?.response?.status !== 429
      ) {
        return false;
      }
      // Retry up to 3 times for server errors and rate limits
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Enhanced polling with smart intervals
    refetchInterval: (query) => {
      // Stop polling on persistent errors
      if (
        query?.state?.error &&
        query?.state?.errorUpdateCount &&
        query.state.errorUpdateCount > 3
      ) {
        ExportLogger.logError("polling", "Too many errors, stopping polling");
        return false;
      }

      const pollingStart = Date.now();
      const data = query?.state?.data;
      const safeData = Array.isArray(data) ? data : [];
      const interval = getPollingInterval(safeData);

      if (interval !== false) {
        // Track polling performance
        setTimeout(() => {
          const activeExports = safeData.filter(
            (e) => e.status === "pending" || e.status === "processing"
          );
          trackPollingCycle(
            activeExports.length,
            safeData.length,
            Date.now() - pollingStart
          );
        }, 0);
      }

      return interval;
    },
    refetchIntervalInBackground: false, // Stop background polling to prevent memory leaks
  });

  // Handle errors separately using useEffect
  useEffect(() => {
    if (isError && error) {
      ExportLogger.logError("fetch exports", error);
      // Only show error toast for non-404 errors
      if ((error as any)?.response?.status !== 404) {
        showError("Failed to load exports", {
          description: "Please check your connection and try again.",
        });
      }
    }
  }, [isError, error]);

  // Enhanced status change detection with better logic
  const handleStatusChange = useCallback(
    (previous: Export, current: Export) => {
      ExportLogger.logStatusChange(current.id, previous.status, current.status);

      if (
        (previous.status === "pending" || previous.status === "processing") &&
        current.status === "completed"
      ) {
        showSuccess("Export Ready! 🎉", {
          description: `Your ${current.format.toUpperCase()} export is ready for download.`,
          category: "export",
          replace: true,
        });
      } else if (
        (previous.status === "pending" || previous.status === "processing") &&
        current.status === "failed"
      ) {
        showError("Export Failed", {
          description: `Your ${current.format.toUpperCase()} export failed. ${
            current.error_message || "Please try again."
          }`,
          category: "export",
          replace: true,
        });
      }
    },
    []
  );

  // Detect status changes and show notifications
  useEffect(() => {
    if (!Array.isArray(exports) || exports.length === 0) {
      return;
    }

    // Skip notifications on first load
    if (isFirstLoadRef.current) {
      isFirstLoadRef.current = false;
      previousExportsRef.current = [...exports]; // Create new array to avoid mutations
      return;
    }

    // Only process if we have previous data to compare
    if (
      Array.isArray(previousExportsRef.current) &&
      previousExportsRef.current.length > 0
    ) {
      exports.forEach((currentExport) => {
        const previousExport = previousExportsRef.current.find(
          (prev) => prev.id === currentExport.id
        );

        // Enhanced status change detection
        if (previousExport && previousExport.status !== currentExport.status) {
          handleStatusChange(previousExport, currentExport);
        }
      });
    }

    // Update reference with new array to avoid mutations
    previousExportsRef.current = [...exports];
  }, [exports, handleStatusChange]);

  // Enhanced mutation for generating new exports
  const generateExportMutation = useMutation({
    mutationFn: async (format: ExportFormat): Promise<ExportResponse> => {
      const startTime = Date.now();
      ExportLogger.logExportInitiation(calculationId, format);

      // Validate calculation ID format
      if (
        !calculationId ||
        typeof calculationId !== "string" ||
        calculationId.trim() === ""
      ) {
        throw new Error("Invalid calculation ID provided");
      }
      const apiClient = await getAuthenticatedApiClient();
      const response = await apiClient.post("/exports", {
        calculationId,
        format,
      });

      trackApiCall("generate export", Date.now() - startTime);
      return response.data;
    },
    onSuccess: (data, format) => {
      showSuccess("Export initiated", {
        description: `Your ${format.toUpperCase()} export is being generated. You'll be notified when it's ready.`,
        category: "export",
        replace: true,
      });

      ExportLogger.logCacheOperation("optimistic update", calculationId, {
        exportId: data.exportId,
        format,
      });

      // Optimistic update instead of full invalidation
      queryClient.setQueryData(
        ["exports", calculationId],
        (oldData: Export[] | undefined) => {
          if (!oldData) return oldData;

          // Add new export with pending status
          const newExport: Export = {
            id: data.exportId,
            format: format,
            created_at: data.createdAt,
            status: "pending",
            file_url: undefined,
            download_url: undefined,
          };

          return [newExport, ...oldData];
        }
      );

      // Still invalidate to ensure fresh data, but with less aggressive timing
      setTimeout(() => {
        ExportLogger.logCacheOperation("invalidate", calculationId);
        queryClient.invalidateQueries({ queryKey: ["exports", calculationId] });
      }, 500);
    },
    onError: (error: any) => {
      ExportLogger.logError("generate export", error);

      // Enhanced error handling with specific messages
      const getErrorMessage = (error: any): string => {
        if (error.response?.status === 401) {
          return "Authentication failed. Please log in again.";
        } else if (error.response?.status === 400) {
          return error.response?.data?.message || "Invalid export request.";
        } else if (error.response?.status === 404) {
          return "Calculation not found or you don't have permission to export it.";
        } else if (error.response?.status === 429) {
          return "Too many export requests. Please wait a moment and try again.";
        } else if (error.response?.status >= 500) {
          return "Server error occurred. Please try again later.";
        } else {
          return (
            error.response?.data?.message ||
            error.message ||
            "Failed to initiate export. Please try again."
          );
        }
      };

      // If calculation not found, invalidate calculations cache to refresh data
      if (
        error.response?.status === 404 ||
        error.message?.includes("not found")
      ) {
        console.log("🔄 Invalidating calculations cache due to export error");
        queryClient.invalidateQueries({ queryKey: ["calculations"] });
      }

      showError("Export failed", {
        description: getErrorMessage(error),
      });
    },
  });

  const generateExport = (format: ExportFormat) => {
    generateExportMutation.mutate(format);
  };

  return {
    exports,
    isLoading,
    generateExport,
    isGenerating: generateExportMutation.isPending,
    refetch,
    error,
    isError,
    isDialogOpen: internalDialogOpen,
    setIsDialogOpen: setInternalDialogOpen,
  };
}
