import { Module } from '@nestjs/common';
import { CurrenciesService } from './currencies.service';
import { CurrenciesController } from './currencies.controller';
import { AdminCurrenciesController } from './admin-currencies.controller';
import { SupabaseModule } from '../../core/supabase/supabase.module';
// Import AuthModule directly to provide AuthService for guards
import { AuthModule } from '../auth/auth.module';

@Module({
  // Import AuthModule, which exports AuthService needed by JwtAuthGuard/AdminRoleGuard
  imports: [SupabaseModule, AuthModule],
  controllers: [CurrenciesController, AdminCurrenciesController],
  providers: [CurrenciesService],
  exports: [CurrenciesService],
})
export class CurrenciesModule {}
