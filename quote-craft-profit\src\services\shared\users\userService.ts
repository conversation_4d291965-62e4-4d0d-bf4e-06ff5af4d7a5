import { User } from '@/types/types';
import {
  UserWithProfile,
  getUsersFromA<PERSON>,
  updateUserRoleFromApi,
  getRolesFromApi,
  getUserProfileFromApi,
} from './userApiService';

/**
 * Get all users
 * This function now uses the backend API instead of direct Supabase calls
 * @returns Promise resolving to an array of users with profile information
 */
export async function getUsers(): Promise<UserWithProfile[]> {
  try {
    console.log('Getting users using backend API');

    // Use the API service to fetch users
    return await getUsersFrom<PERSON>pi();
  } catch (error) {
    console.error('Error in getUsers:', error);
    throw error;
  }
}

/**
 * Update a user's role
 * This function now uses the backend API instead of direct Supabase calls
 * @param userId - The user ID
 * @param roleId - The role ID to assign
 * @returns Promise resolving when the role is updated
 */
export async function updateUserRole(userId: string, roleId: number): Promise<void> {
  try {
    console.log(
      `Updating role for user ${userId} to role ID ${roleId} using backend API`,
    );

    // Use the API service to update the user role
    await updateUser<PERSON>ole<PERSON>rom<PERSON>pi(userId, roleId);
  } catch (error) {
    console.error(`Error updating role for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Get current user profile
 * This function uses the backend API instead of direct Supabase calls
 * @returns Promise resolving to the current user's profile data
 */
export async function getUserProfile(): Promise<any> {
  try {
    console.log('Getting current user profile using backend API');

    // Use the API service to fetch the current user's profile
    return await getUserProfileFromApi();
  } catch (error) {
    console.error('Error in getUserProfile:', error);
    throw error;
  }
}

/**
 * Get all roles
 * This function now uses the backend API instead of direct Supabase calls
 * @returns Promise resolving to an array of roles
 */
export async function getRoles() {
  try {
    console.log('Getting roles using backend API');

    // Use the API service to fetch roles
    return await getRolesFromApi();
  } catch (error) {
    console.error('Error in getRoles:', error);
    throw error;
  }
}

export type { UserWithProfile };
