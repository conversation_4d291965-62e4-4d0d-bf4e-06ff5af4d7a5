import { z } from 'zod';
import { formFieldPatterns } from '@/schemas/common';

/**
 * Division management validation schemas
 * Centralized schemas for all division forms and components
 */

// Division form schema (extracted from DivisionFormDialog.tsx)
export const divisionFormSchema = z.object({
  name: formFieldPatterns.name('Division name'),
  code: formFieldPatterns.code('Division code'),
});

// Type exports for TypeScript inference
export type DivisionFormValues = z.infer<typeof divisionFormSchema>;
