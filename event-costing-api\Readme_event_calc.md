# Event Costing API


## Project Brief: Dynamic Event Costing Platform

**(Based on Schema & Features Finalized as of April 18, 2025)**

**1. Vision**

To develop a web platform for event planning businesses to streamline event cost calculations, generate accurate quotes rapidly, manage a detailed service catalogue, and track calculation profitability.

**2. Target Audience**

- **Primary Users:** Event Planning Companies / Agencies, including:
  - **Administrators:** Manage catalogue, settings, users, templates.
  - **Event Planners / Sales Staff:** Create calculations/quotes for clients/events.
- **Secondary Users (Implied):** Event clients receiving the generated outputs.

**3. Core Capabilities**

- **Manage Service Catalogue:** Provide functionality (primarily via Admin interfaces) to define packages including variations, configurable options, multi-currency pricing & costs, city-based availability, categories/divisions, and package dependencies (requires/conflicts).
- **Generate Calculations:** Allow users to initiate calculations from scratch or pre-filled templates. Capture essential event details (dates, attendees, city, currency) and optionally link the calculation to `Client` and `Event` records. Support guest/unlinked calculations.
- **Build Estimates:** Enable users to browse the package catalogue (filtered by category/city, showing conflicts based on dependencies) and add items to the calculation. Support adding both standard catalogue items and one-off custom line items. Automatically calculate the effective quantity for standard items based on package rules (`quantity_basis`: per-day, per-attendee, etc.) and event inputs. Dynamically display and allow configuration of applicable package options.
- **Realtime Totals:** Display running subtotals that update as items are added/modified. Calculate and display final totals including manually entered/configured tax and discount parameters (applying service charges before tax if configured).
- **Ensure Accuracy (Price Locking):** Implement logic to automatically capture price and cost snapshots for each line item (`calculation_line_items`, `calculation_line_item_options`) when the calculation is saved, ensuring historical estimates remain consistent. Recalculations must use these snapshots.
- **Track Profitability:** Implement backend logic to automatically calculate the estimated `total_cost` and `estimated_profit` for each `calculation_history` record based on the snapshotted costs of its line items.
- **Manage Access & Workflow:** Enforce role-based permissions (Admin vs. Planner). Support saving calculations as 'draft' and marking them as 'completed'.
- **Export Results:** Provide functionality to generate calculation summary documents (e.g., PDF, xlsx) based on the saved calculation data and log these export events (`export_history`).

**4. Technical Approach**

- Built on a robust PostgreSQL backend (Supabase compatible) using a normalized structure for core calculation data (`calculation_line_items`, etc.).
- An API layer handles complex business logic (quantity calculations, price snapshotting, dependency checks, profitability calculation, final total calculation).

## Overview

This API supports:

- Service catalog management (packages, options, dependencies)
- Dynamic event cost calculations (attendees, duration, location)
- Multi-currency support and price/cost snapshotting
- Profitability tracking
- Calculation templates and line-item operations
- Role-based user management (Admin vs. Planner)

## Folder Structure & Module Breakdown

```
event-costing-api/
├── docs/               # Planning & reference docs (ProjectBrief, BACKEND_PLAN, BackendTECH, Schema, TEST_POSTMAN)
├── src/
│   ├── main.ts         # Application entry point
│   ├── app.module.ts   # Root module
│   ├── core/
│   │   ├── filters/    # Global exception filters
│   │   ├── supabase/   # SupabaseModule & service wiring
│   │   └── database/   # Database config & migrations
│   ├── modules/        # Feature modules
│   │   ├── auth/       # /auth login & logout
│   │   ├── users/      # /users profiles & management
│   │   ├── calculations/   # /calculations endpoints
│   │   ├── calculation-items/ # Calculation line-item logic
│   │   ├── templates/  # /templates blueprint operations
│   │   ├── clients/    # /clients data management
│   │   ├── packages/   # /packages catalog
│   │   ├── categories/ # /categories package categories
│   │   ├── cities/     # /cities location data
│   │   ├── currencies/ # /currencies currency support
│   │   └── events/     # /events event types metadata
│   └── shared/         # Shared DTOs, interfaces, pipes
├── test/               # E2E tests
├── .env                # Environment variables (DO NOT COMMIT)
├── package.json        # Dependencies & scripts
├── tsconfig.json       # TypeScript config
└── README.md           # Project overview & instructions
```

## Core Features

- **Service Catalog Management**: Define packages with variations, configurable options, and pricing rules
- **Dynamic Calculations**: Create cost calculations based on event parameters (attendees, duration, location)
- **Dependency Resolution**: Handle package dependencies (requires/conflicts)
- **Multi-Currency Support**: Handle different currencies and location-specific pricing
- **Price Locking**: Snapshot prices and costs when calculations are saved
- **Profitability Tracking**: Calculate estimated profit based on costs and prices
- **User Management**: Role-based access control (Admin vs. Planner)
- **Export Functionality**: Generate calculation summaries in various formats

## Core Technologies

- **Framework:** [NestJS](https://nestjs.com/) v11 (TypeScript)
- **Database & Backend Services:** [Supabase](https://supabase.com/)
  - PostgreSQL Database
  - Authentication (with JWT)
  - Storage (for exports/documents)
- **Validation:** [class-validator](https://github.com/typestack/class-validator) & [class-transformer](https://github.com/typestack/class-transformer)
- **Configuration:** [@nestjs/config](https://docs.nestjs.com/techniques/configuration)
- **Logging:** [Winston](https://github.com/winstonjs/winston) via nest-winston
- **Authentication:** [@nestjs/passport](https://docs.nestjs.com/security/authentication) with JWT
- **Testing:** Jest, Supertest

# API Endpoint Testing Checklist

Here is a list of all endpoints defined in the OpenAPI specification. Mark each one as you complete testing.

**Notes & Status:**
- Package Prices uniqueness (e.g., per currency) seems enforced (409 Conflict). (Is this the correct workflow?)
- Package Option Code uniqueness is not explicitly mentioned as enforced by the spec (409 Conflict). (Is this the correct workflow?)
- `PUT /admin/packages/{packageId}/cities/{cityId}` endpoint is still missing.

**App**
- [x] `GET /`

**Auth**
- [x] `POST /auth/login`
- [x] `POST /auth/logout`

**Users**
- [x] `GET /users/me`

**Calculations**
- [x] `POST /calculations`
- [x] `GET /calculations`
- [o] `POST /calculations/from-template/{templateId}`
- [x] `GET /calculations/{id}`
- [x] `PUT /calculations/{id}`
- [x] `DELETE /calculations/{id}`
- [x] `GET /calculations/{id}/totals`
- [x] `PATCH /calculations/{id}/status`

**Calculation Items**
- [x] `POST /calculations/{calcId}/items/package`
- [x] `POST /calculations/{calcId}/items/custom`
- [x] `DELETE /calculations/{calcId}/items/package/{itemId}`
- [x] `DELETE /calculations/{calcId}/items/custom/{itemId}`

**Templates**
- [x] `GET /templates` (List public templates)
- [x] `GET /templates/{id}` (Get public template details)

**Admin Templates**
- [x] `POST /admin/templates/from-calculation`
- [x] `GET /admin/templates` (List all templates - admin)
- [x] `GET /admin/templates/{id}` (Get template details - admin)
- [x] `PUT /admin/templates/{id}`
- [x] `DELETE /admin/templates/{id}`

**Admin - Packages - Cities**
- [x] `POST /admin/packages/{packageId}/cities`
- [x] `GET /admin/packages/{packageId}/cities`
- [x] `DELETE /admin/packages/{packageId}/cities/{cityId}`

**Cities**
- [x] `GET /cities`

**Admin - Cities**
- [x] `POST /admin/cities`
- [x] `PUT /admin/cities/{id}`
- [x] `DELETE /admin/cities/{id}`

**Currencies**
- [x] `GET /currencies`

**Admin - Currencies**
- [x] `POST /admin/currencies`
- [x] `GET /admin/currencies/{id}`
- [x] `PUT /admin/currencies/{id}`
- [x] `DELETE /admin/currencies/{id}`

**Categories**
- [x] `GET /categories`

**Admin - Categories**
- [x] `POST /admin/categories`
- [x] `GET /admin/categories/{id}`
- [x] `PUT /admin/categories/{id}`
- [x] `DELETE /admin/categories/{id}`

**Admin | Packages**
- [x] `POST /admin/packages`
- [x] `GET /admin/packages`
- [x] `GET /admin/packages/{id}`
- [x] `PATCH /admin/packages/{id}`
- [x] `DELETE /admin/packages/{id}`

**Packages Catalogue**
- [o] `GET /packages/variations`
- [o] `GET /packages/variations/{packageId}/options`

**Admin - Package Prices**
- [x] `POST /admin/packages/{packageId}/prices`
- [x] `GET /admin/packages/{packageId}/prices`
- [x] `GET /admin/packages/{packageId}/prices/{packagePriceId}`
- [x] `PATCH /admin/packages/{packageId}/prices/{packagePriceId}`
- [x] `DELETE /admin/packages/{packageId}/prices/{packagePriceId}`

**Admin/Package Dependencies**
- [x] `POST /admin/packages/{packageId}/dependencies`
- [x] `GET /admin/packages/{packageId}/dependencies`
- [x] `DELETE /admin/packages/{packageId}/dependencies/{dependencyId}`

**Admin | Packages / Options**
- [x] `POST /admin/packages/{packageId}/options`
- [x] `GET /admin/packages/{packageId}/options`
- [x] `GET /admin/packages/{packageId}/options/{id}`
- [x] `PATCH /admin/packages/{packageId}/options/{id}`
- [x] `DELETE /admin/packages/{packageId}/options/{id}`

**Admin - Cost Items**
- [~] `POST /admin/cost-items`
- [~] `GET /admin/cost-items`
- [~] `GET /admin/cost-items/{id}`
- [~] `PATCH /admin/cost-items/{id}`
- [~] `DELETE /admin/cost-items/{id}`

**Admin: Service Categories**
- [~] `POST /admin/service-categories`
- [~] `GET /admin/service-categories`
- [~] `GET /admin/service-categories/{id}`
- [~] `PATCH /admin/service-categories/{id}`
- [~] `DELETE /admin/service-categories/{id}`

**Clients**
- [x] `POST /clients`
- [x] `GET /clients` (List all clients with optional search)
- [x] `GET /clients/{id}`
- [x] `PATCH /clients/{id}`
- [x] `DELETE /clients/{id}`

**Events**
- [x] `POST /events`
- [x] `GET /events` (List all events with optional filtering/search)
- [x] `GET /events/{id}`
- [x] `PATCH /events/{id}`
- [x] `DELETE /events/{id}`

**Admin - Divisions**
- [x] `POST /admin/divisions`
- [x] `GET /admin/divisions/{id}`
- [x] `PUT /admin/divisions/{id}`
- [x] `DELETE /admin/divisions/{id}`

**Exports**
- [ ] `POST /exports` (Initiate Export)
- [ ] `GET /exports/{id}/status` (Get Export Status)

**Admin - Settings**
- [ ] `GET /admin/settings/{key}`
- [ ] `PUT /admin/settings/{key}`

