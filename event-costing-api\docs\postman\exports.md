## Exports (`/exports`)

### 1. Get Calculation as CSV (Direct Download)

- **Method:** `GET`
- **URL:** `/exports/calculations/{id}/csv` (Replace `{id}` with the actual calculation UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Description:** DEPRECATED (potentially). Retrieves the specified calculation directly as a CSV file download.
- **Success Response (200 OK):**
  - **Headers:**
    - `Content-Type`: `text/csv`
    - `Content-Disposition`: `attachment; filename="calculation-{id}-{date}.csv"`
  - **Body:** Raw CSV content.
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (404 Not Found):** If calculation ID does not exist or user lacks access.

### 2. Initiate Calculation Export

- **Method:** `POST`
- **URL:** `/exports/calculations/{id}/initiate` (Replace `{id}` with the actual calculation UUID)
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Body:** `raw (JSON)`
  ```json
  {
    "format": "csv"
  }
  ```
- **Description:** Initiates the export process for a calculation (currently only CSV). This creates an entry in the export history but does _not_ return the file directly in the response. Future implementations might save to storage or handle other formats.
- **Success Response (201 Created):**
  ```json
  {
    "message": "CSV export initiated and history recorded.",
    "exportHistoryId": "newly-created-export-history-uuid"
  }
  ```
- **Error Response (400 Bad Request):** If the format in the body is invalid or not supported by this endpoint.
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (404 Not Found):** If calculation ID does not exist or user lacks access.
- **Error Response (500 Internal Server Error):** If history recording fails.
