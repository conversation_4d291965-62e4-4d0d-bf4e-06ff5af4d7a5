/**
 * Centralized Cache Management Service for Calculations
 *
 * This service provides a unified approach to managing React Query cache
 * for calculation-related data, ensuring consistency across all components.
 */

import { QueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { LineItem } from "@/types/calculation";

export interface CacheInvalidationOptions {
  /** Whether to invalidate calculation data */
  calculation?: boolean;
  /** Whether to invalidate line items */
  lineItems?: boolean;
  /** Whether to invalidate packages by category */
  packagesByCategory?: boolean;
  /** Whether to invalidate custom items */
  customItems?: boolean;
  /** Whether to invalidate financial calculations */
  financialCalculations?: boolean;
  /** Whether to wait for all invalidations to complete */
  waitForInvalidation?: boolean;
}

export const calculationCacheService = {
  /**
   * Comprehensive cache invalidation for calculation data
   * @param calculationId - The calculation ID
   * @param queryClient - React Query client instance
   * @param options - Invalidation options
   */
  invalidateCalculationData: async (
    calculationId: string,
    queryClient: QueryClient,
    options: CacheInvalidationOptions = {}
  ) => {
    const {
      calculation = true,
      lineItems = true,
      packagesByCategory = true,
      customItems = true,
      financialCalculations = true,
      waitForInvalidation = true,
    } = options;

    const invalidationPromises: Promise<void>[] = [];

    if (calculation) {
      invalidationPromises.push(
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.detail(calculationId),
        })
      );
    }

    if (lineItems) {
      invalidationPromises.push(
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
        })
      );
    }

    if (packagesByCategory) {
      invalidationPromises.push(
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.packagesByCategory(calculationId),
        })
      );
    }

    if (customItems) {
      invalidationPromises.push(
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.customItems(calculationId),
        })
      );
    }

    if (financialCalculations) {
      // Invalidate financial calculation caches using new hierarchical structure
      invalidationPromises.push(
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.financials(calculationId),
        })
      );
    }

    if (waitForInvalidation) {
      await Promise.all(invalidationPromises);
    } else {
      // Fire and forget
      Promise.all(invalidationPromises).catch((error) => {
        console.error("Error in cache invalidation:", error);
      });
    }
  },

  /**
   * Optimistic line item removal with proper cache management
   * @param calculationId - The calculation ID
   * @param lineItemId - The line item ID to remove
   * @param queryClient - React Query client instance
   * @returns Previous line items data for rollback
   */
  optimisticLineItemRemoval: (
    calculationId: string,
    lineItemId: string,
    queryClient: QueryClient
  ): LineItem[] | undefined => {
    // Cancel outgoing queries to prevent race conditions
    queryClient.cancelQueries({
      queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
    });

    // Get previous data for rollback
    const previousData = queryClient.getQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId)
    );

    // Optimistically update line items
    queryClient.setQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId),
      (old) => (old ? old.filter((item) => item.id !== lineItemId) : [])
    );

    return previousData;
  },

  /**
   * Optimistic line item addition with proper cache management
   * @param calculationId - The calculation ID
   * @param optimisticItem - The optimistic line item to add
   * @param queryClient - React Query client instance
   * @returns Previous line items data for rollback
   */
  optimisticLineItemAddition: (
    calculationId: string,
    optimisticItem: LineItem,
    queryClient: QueryClient
  ): LineItem[] | undefined => {
    // Cancel outgoing queries
    queryClient.cancelQueries({
      queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
    });

    // Get previous data for rollback
    const previousData = queryClient.getQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId)
    );

    // Optimistically add the item
    queryClient.setQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId),
      (old) => (old ? [...old, optimisticItem] : [optimisticItem])
    );

    return previousData;
  },

  /**
   * Optimistic line item update with proper cache management
   * @param calculationId - The calculation ID
   * @param lineItemId - The line item ID to update
   * @param updates - Partial updates to apply
   * @param queryClient - React Query client instance
   * @returns Previous line items data for rollback
   */
  optimisticLineItemUpdate: (
    calculationId: string,
    lineItemId: string,
    updates: Partial<LineItem>,
    queryClient: QueryClient
  ): LineItem[] | undefined => {
    // Cancel outgoing queries
    queryClient.cancelQueries({
      queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
    });

    // Get previous data for rollback
    const previousData = queryClient.getQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId)
    );

    // Optimistically update the item
    queryClient.setQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId),
      (old) => {
        if (!old) return old;
        return old.map((item) =>
          item.id === lineItemId
            ? { ...item, ...updates, _isOptimistic: true }
            : item
        );
      }
    );

    return previousData;
  },

  /**
   * Rollback optimistic updates
   * @param calculationId - The calculation ID
   * @param previousData - Previous data to restore
   * @param queryClient - React Query client instance
   */
  rollbackOptimisticUpdate: (
    calculationId: string,
    previousData: LineItem[] | undefined,
    queryClient: QueryClient
  ) => {
    if (previousData) {
      queryClient.setQueryData(
        QUERY_KEYS.calculations.lineItems(calculationId),
        previousData
      );
    }
  },

  /**
   * Ensure data consistency after mutations
   * @param calculationId - The calculation ID
   * @param queryClient - React Query client instance
   */
  ensureDataConsistency: async (
    calculationId: string,
    queryClient: QueryClient
  ) => {
    // Refetch critical data to ensure consistency
    await Promise.all([
      queryClient.refetchQueries({
        queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
      }),
      queryClient.refetchQueries({
        queryKey: QUERY_KEYS.calculations.detail(calculationId),
      }),
    ]);
  },
};
