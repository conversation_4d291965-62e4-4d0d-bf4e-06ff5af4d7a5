import { ApiProperty } from '@nestjs/swagger';

// DTO for representing a single package option with its price/cost adjustments
export class PackageOptionDetailDto {
  @ApiProperty({ type: String, format: 'uuid' })
  id: string;

  @ApiProperty()
  option_name: string;

  @ApiProperty({ nullable: true })
  description: string | null;

  @ApiProperty({
    description: 'Price adjustment for this option in the specified currency',
  })
  price_adjustment: number;

  @ApiProperty({
    description: 'Cost adjustment for this option in the specified currency',
  })
  cost_adjustment: number;
}
