import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { PackageOptionFormValues, packageOptionSchema } from '../../types/packageOptions';
import { savePackageOption } from '../../../../../services/admin/packages/packageOptionService';
import { formatRupiah } from '@/lib/utils';
import { getPackageById } from '../../../../../services/admin/packages/packageService';
import { useQuery } from '@tanstack/react-query';

interface PackageOptionFormProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  packageId: string;
  optionId?: string | null;
  initialData?: Partial<PackageOptionFormValues>;
}

export const PackageOptionForm: React.FC<PackageOptionFormProps> = ({
  isOpen,
  onClose,
  packageId,
  optionId,
  initialData,
}) => {
  const isEditMode = !!optionId;
  const IDR_CURRENCY_ID = '685860b9-257f-41eb-b223-b3e1fad8f3b9'; // Static IDR currency ID
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Fetch package details to get the base price
  const { data: packageData, isLoading: isLoadingPackage } = useQuery({
    queryKey: ['package', packageId],
    queryFn: () => getPackageById(packageId),
    enabled: !!packageId && isOpen,
    meta: {
      onError: () => {
        toast.error('Failed to load package details');
      },
    },
  });

  const basePrice = packageData?.price || '0';
  const basePriceNumber = parseInt(basePrice, 10) || 0;

  const form = useForm<PackageOptionFormValues>({
    resolver: zodResolver(packageOptionSchema),
    defaultValues: {
      option_name: initialData?.option_name || '',
      option_code: initialData?.option_code || '',
      description: initialData?.description || '',
      price_adjustment: initialData?.price_adjustment || '0',
      cost_adjustment: initialData?.cost_adjustment || '0',
      is_default_for_package: initialData?.is_default_for_package || false,
      is_required: initialData?.is_required || false,
    },
  });

  const handleSubmit = async (values: PackageOptionFormValues) => {
    // Prevent double submission
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      await savePackageOption({
        id: optionId || undefined,
        option_name: values.option_name,
        option_code: values.option_code,
        description: values.description || null,
        price_adjustment: parseFloat(values.price_adjustment) || 0,
        cost_adjustment: parseFloat(values.cost_adjustment) || 0,
        currency_id: IDR_CURRENCY_ID,
        option_group: null, // We're removing option_group as requested
        is_default_for_package: values.is_default_for_package,
        is_required: values.is_required,
        applicable_package_id: packageId,
      });

      toast.success(`Package option ${isEditMode ? 'updated' : 'created'} successfully`);
      onClose(true); // Refresh the options list
    } catch (error) {
      toast.error(`Failed to ${isEditMode ? 'update' : 'create'} package option`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && !isSubmitting && onClose()}>
      <DialogContent className='sm:max-w-[700px] max-h-[90vh] overflow-y-auto'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='text-xl font-semibold'>
            {isEditMode ? 'Edit Package Option' : 'Add New Package Option'}
          </DialogTitle>
          <DialogDescription className='text-muted-foreground'>
            {isEditMode
              ? 'Update the details of this package option.'
              : 'Create a new option that can be added to this package.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6 pt-4'>
            {/* Basic Information Section */}
            <div className='space-y-4'>
              <div className='flex items-center gap-2 pb-2'>
                <div className='h-1 w-1 rounded-full bg-primary'></div>
                <h3 className='text-sm font-medium text-foreground'>Basic Information</h3>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='option_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-medium'>Option Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='e.g., Premium Sound System'
                          className='h-10'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='option_code'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-medium'>Option Code *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='e.g., SOUND_PREMIUM'
                          className='h-10 font-mono text-sm'
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className='text-xs text-muted-foreground'>
                        Unique identifier for internal reference
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='text-sm font-medium'>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Describe what this option includes and its benefits...'
                        className='min-h-[80px] resize-none'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Pricing Section */}
            <div className='space-y-4'>
              <div className='flex items-center gap-2 pb-2'>
                <div className='h-1 w-1 rounded-full bg-primary'></div>
                <h3 className='text-sm font-medium text-foreground'>Pricing & Cost</h3>
              </div>

              {/* Package Base Price Info */}
              <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium text-blue-900'>Package Base Price</span>
                  <span className='text-lg font-semibold text-blue-900'>
                    {isLoadingPackage ? (
                      <div className='h-4 w-20 bg-blue-200 rounded animate-pulse'></div>
                    ) : (
                      formatRupiah(basePriceNumber)
                    )}
                  </span>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='price_adjustment'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-medium'>Price Adjustment</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <span className='absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm'>
                            Rp
                          </span>
                          <Input
                            placeholder='0'
                            className='pl-8 h-10'
                            {...field}
                            onChange={(e) => {
                              // Allow negative numbers (minus sign)
                              const value = e.target.value.replace(/[^0-9-]/g, '');
                              // Ensure only one minus sign at the beginning
                              const formattedValue = value.startsWith('-')
                                ? '-' + value.substring(1).replace(/-/g, '')
                                : value.replace(/-/g, '');
                              field.onChange(formattedValue);
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormDescription className='text-xs text-muted-foreground'>
                        Add (+) or subtract (-) from base price
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='cost_adjustment'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-medium'>Cost Adjustment</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <span className='absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm'>
                            Rp
                          </span>
                          <Input
                            placeholder='0'
                            className='pl-8 h-10'
                            {...field}
                            onChange={(e) => {
                              // Allow negative numbers (minus sign)
                              const value = e.target.value.replace(/[^0-9-]/g, '');
                              // Ensure only one minus sign at the beginning
                              const formattedValue = value.startsWith('-')
                                ? '-' + value.substring(1).replace(/-/g, '')
                                : value.replace(/-/g, '');
                              field.onChange(formattedValue);
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormDescription className='text-xs text-muted-foreground'>
                        Add (+) or subtract (-) from base cost
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Calculation Summary */}
            <div className='space-y-4'>
              <div className='flex items-center gap-2 pb-2'>
                <div className='h-1 w-1 rounded-full bg-primary'></div>
                <h3 className='text-sm font-medium text-foreground'>Price Summary</h3>
              </div>

              <div className='bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 space-y-3'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium text-green-900'>Final Price</span>
                  <span className='text-xl font-bold text-green-900'>
                    {(() => {
                      const priceAdjustment = parseInt(form.watch('price_adjustment') || '0');
                      const totalPrice = basePriceNumber + priceAdjustment;
                      return formatRupiah(totalPrice);
                    })()}
                  </span>
                </div>

                <div className='flex items-center justify-between text-sm'>
                  <span className='text-green-700'>Profit Margin</span>
                  <span className='font-semibold text-green-700'>
                    {(() => {
                      const priceAdjustment = parseInt(form.watch('price_adjustment') || '0');
                      const costAdjustment = parseInt(form.watch('cost_adjustment') || '0');

                      const totalPrice = basePriceNumber + priceAdjustment;
                      const totalCost =
                        (packageData?.unitBaseCost ? parseInt(packageData.unitBaseCost) : 0) +
                        costAdjustment;

                      const profit = totalPrice - totalCost;
                      const margin = totalPrice > 0 ? (profit / totalPrice) * 100 : 0;

                      return `${formatRupiah(profit)} (${margin.toFixed(1)}%)`;
                    })()}
                  </span>
                </div>
              </div>
            </div>

            {/* Option Settings */}
            <div className='space-y-4'>
              <div className='flex items-center gap-2 pb-2'>
                <div className='h-1 w-1 rounded-full bg-primary'></div>
                <h3 className='text-sm font-medium text-foreground'>Option Settings</h3>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='is_default_for_package'
                  render={({ field }) => (
                    <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-lg border border-gray-200 p-4 hover:bg-gray-50 transition-colors'>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className='mt-0.5'
                        />
                      </FormControl>
                      <div className='space-y-1 leading-none'>
                        <FormLabel className='text-sm font-medium cursor-pointer'>
                          Default Option
                        </FormLabel>
                        <FormDescription className='text-xs text-muted-foreground'>
                          Automatically selected when package is added
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='is_required'
                  render={({ field }) => (
                    <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-lg border border-gray-200 p-4 hover:bg-gray-50 transition-colors'>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className='mt-0.5'
                        />
                      </FormControl>
                      <div className='space-y-1 leading-none'>
                        <FormLabel className='text-sm font-medium cursor-pointer'>
                          Required Option
                        </FormLabel>
                        <FormDescription className='text-xs text-muted-foreground'>
                          Cannot be deselected by users
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter className='mt-8 pt-6 border-t bg-gray-50/50 -mx-6 -mb-6 px-6 pb-6 rounded-b-lg'>
              <div className='flex items-center justify-end gap-3 w-full'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => onClose()}
                  disabled={isSubmitting}
                  className='min-w-[100px]'
                >
                  Cancel
                </Button>
                <Button
                  type='submit'
                  disabled={isSubmitting}
                  className='min-w-[140px] bg-primary hover:bg-primary/90'
                >
                  {isSubmitting ? (
                    <>
                      <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground border-t-transparent'></div>
                      {isEditMode ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>
                      {isEditMode ? (
                        <>
                          <svg className='mr-2 h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z' />
                          </svg>
                          Update Option
                        </>
                      ) : (
                        <>
                          <svg className='mr-2 h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M12 6v6m0 0v6m0-6h6m-6 0H6' />
                          </svg>
                          Create Option
                        </>
                      )}
                    </>
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default PackageOptionForm;
