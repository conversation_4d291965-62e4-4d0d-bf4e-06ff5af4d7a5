import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, X } from "lucide-react";

interface TaxFormProps {
  newTaxName: string;
  newTaxPercentage: string;
  onTaxNameChange: (value: string) => void;
  onTaxPercentageChange: (value: string) => void;
  onAdd: () => void;
  onCancel: () => void;
}

/**
 * Tax addition form with validation
 * Provides input fields for tax name and percentage with add/cancel actions
 */
export const TaxForm: React.FC<TaxFormProps> = ({
  newTaxName,
  newTaxPercentage,
  onTaxNameChange,
  onTaxPercentageChange,
  onAdd,
  onCancel,
}) => {
  return (
    <div className="flex flex-col space-y-2 pb-2 border-b">
      <div className="flex space-x-2">
        <Input
          placeholder="Tax name"
          value={newTaxName}
          onChange={(e) => onTaxNameChange(e.target.value)}
          className="flex-1"
        />
        <Input
          placeholder="Percentage"
          type="number"
          min="0"
          step="0.01"
          value={newTaxPercentage}
          onChange={(e) => onTaxPercentageChange(e.target.value)}
          className="w-24"
        />
      </div>
      <div className="flex justify-end space-x-2">
        <Button variant="outline" size="sm" onClick={onCancel}>
          <X size={16} className="mr-1" /> Cancel
        </Button>
        <Button variant="default" size="sm" onClick={onAdd}>
          <Plus size={16} className="mr-1" /> Add
        </Button>
      </div>
    </div>
  );
};
