// Barrel exports for template services
export * from './templateService';

// Re-export from the new unified template calculation service
export {
  calculateTemplateTotal as calculateTemplateFromAPI,
  getTemplateCalculationSummary,
  formatCurrency,
  formatCurrency as formatCurrencyAPI,
  validateTemplatePricing,
  getTemplatePricingIssues,
  calculateTemplateFromObject as calculateTemplateTotal,
  getTemplateCalculationSummaryFromResult as getCalculationSummary,
} from '@/services/calculations/templates';

// Export types from the new unified service
export type {
  TemplateCalculationResult,
  TemplateCalculationSummary,
  CalculationBreakdown,
} from '@/services/calculations/templates';
