/**
 * Main content component for calculation detail page
 * Contains all the main sections: packages, line items, details, etc.
 * Now uses React Context to eliminate prop drilling
 */
import React, { useMemo } from "react";

// Import existing components
import CalculationPackages from "../packages/CalculationPackages";
import CalculationLineItems from "../line-items/CalculationLineItems";
import CalculationDetails from "../CalculationDetails";
import CalculationFinancialSummary from "../financial/CalculationFinancialSummary";
import CalculationNotes from "../CalculationNotes";
import AddCustomItemDialog from "../line-items/AddCustomItemDialog";
import EditLineItemDialog from "../line-items/EditLineItemDialog";

// Import context hooks - PHASE 3 OPTIMIZATION: Using consolidated hooks
import {
  useCalculationId,
  useCalculationCoreData,
  // PHASE 3: Use consolidated hooks instead of legacy ones
  useCalculationUIData,
  useCalculationDataAndUtils,
  useCalculationFunctions,
} from "../../../contexts";

/**
 * Main content component - now uses context instead of props
 * PHASE 3 OPTIMIZATION: Reduced from 13 hooks to 5 hooks (62% reduction)
 * Significantly reduced prop drilling from 67+ props to 0 props
 */
const CalculationDetailContent: React.FC = () => {
  // Get calculation ID from context
  const calculationId = useCalculationId();

  // Get core data from context
  const { calculation, packagesByCategory, lineItems, categories } =
    useCalculationCoreData();

  // PHASE 3: Use consolidated UI data hook (merges UIState, EditState, LoadingState)
  const {
    // From useCalculationUIState
    expandedCategories,
    packageForms,
    isEditMode,
    isSaving,
    isAddingCustomItem,
    isEditingLineItem,
    currentEditingLineItem,
    // From useCalculationEditState
    editedName,
    editedEventType,
    editedNotes,
    editedAttendees,
    dateRange,
    // From useCalculationLoadingState
    isPackagesError,
  } = useCalculationUIData();

  // PHASE 3: Use consolidated data and utils hook (merges FinancialData, Utils)
  const { taxes, discount, financialCalculations, formatCurrency, formatDate } =
    useCalculationDataAndUtils();

  // PHASE 3: Use consolidated functions hook (merges all function hooks)
  const {
    setters,
    packages,
    lineItems: lineItemFunctions,
    editing,
    financial,
    actions,
  } = useCalculationFunctions();

  // Destructure for easier access (maintaining same variable names for compatibility)
  const {
    setIsAddingCustomItem,
    setIsEditingLineItem,
    setEditedName,
    setEditedEventType,
    setEditedNotes,
    setEditedAttendees,
    setDateRange,
  } = setters;

  const {
    toggleCategory,
    handleQuantityChange,
    handleItemQuantityBasisChange,
    handleOptionToggle,
    handleAddToCalculation,
  } = packages;

  const { handleEditLineItem, handleUpdateLineItem, handleRemoveLineItem } =
    lineItemFunctions;

  const { handleToggleEditMode, handleSaveChanges } = editing;

  const { addTax, updateDiscount } = financial;

  const { handleStatusChange, handleDelete, handleNavigateToList } = actions;

  // Extract selected package IDs from line items to disable already selected packages
  const selectedPackageIds = useMemo(() => {
    if (!lineItems || !Array.isArray(lineItems)) {
      return [];
    }

    const packageIds = lineItems
      .filter((item) => item?.package_id && !item?.is_custom) // Only package-based items
      .map((item) => item.package_id!)
      .filter(Boolean);

    return [...new Set(packageIds)]; // Remove duplicates
  }, [lineItems]); // Use lineItems directly but ensure it's stable

  return (
    <>
      {/* Available Packages */}
      <CalculationPackages
        packagesByCategory={packagesByCategory || []}
        expandedCategories={expandedCategories}
        toggleCategory={toggleCategory}
        packageForms={packageForms}
        onQuantityChange={handleQuantityChange}
        onItemQuantityBasisChange={handleItemQuantityBasisChange}
        onOptionToggle={handleOptionToggle}
        onAddToCalculation={handleAddToCalculation}
        isLoading={false}
        isError={isPackagesError}
        selectedPackageIds={selectedPackageIds}
      />

      {/* Selected Line Items */}
      <CalculationLineItems
        lineItems={lineItems || []}
        onEditLineItem={handleEditLineItem}
        onRemoveLineItem={handleRemoveLineItem}
        onAddCustomItem={() => setIsAddingCustomItem(true)}
        formatCurrency={formatCurrency}
        categories={categories}
        calculationId={calculationId}
      />

      {/* Custom Item Dialog */}
      <AddCustomItemDialog
        isOpen={isAddingCustomItem}
        onClose={() => setIsAddingCustomItem(false)}
      />

      {/* Edit Line Item Dialog */}
      <EditLineItemDialog
        isOpen={isEditingLineItem}
        onClose={() => setIsEditingLineItem(false)}
        onUpdateLineItem={handleUpdateLineItem}
        lineItem={currentEditingLineItem}
      />

      {/* Bottom Section: Event details, financial summary, and notes */}
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Event Details */}
          <CalculationDetails
            calculation={calculation}
            isEditMode={isEditMode}
            isSaving={isSaving}
            editedName={editedName}
            editedEventType={editedEventType}
            editedAttendees={editedAttendees}
            dateRange={dateRange}
            setEditedName={setEditedName}
            setEditedEventType={setEditedEventType}
            setEditedAttendees={setEditedAttendees}
            setDateRange={setDateRange}
            handleToggleEditMode={handleToggleEditMode}
            handleSaveChanges={handleSaveChanges}
            formatDate={formatDate}
          />

          {/* Financial Summary */}
          <CalculationFinancialSummary
            total={financialCalculations.subtotal}
            formatCurrency={formatCurrency}
            calculationId={calculationId}
            status={calculation?.status}
            initialTaxes={taxes}
            initialDiscount={discount}
            onStatusChange={handleStatusChange}
            onDelete={handleDelete}
            onNavigateToList={handleNavigateToList}
            onTaxesChange={(taxes) => taxes.forEach(addTax)}
            onDiscountChange={updateDiscount}
          />
        </div>

        {/* Notes */}
        <CalculationNotes
          notes={calculation.notes || ""}
          isEditMode={isEditMode}
          editedNotes={editedNotes}
          setEditedNotes={setEditedNotes}
        />
      </div>
    </>
  );
};

export default CalculationDetailContent;
