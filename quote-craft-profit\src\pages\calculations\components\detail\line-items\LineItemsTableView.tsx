import React, { useState, useCallback, memo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Edit,
  Trash2,
  X,
  Package,
  Sparkles,
  Calculator,
  DollarSign,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { LineItem, QuantityBasisEnum } from "@/types/calculation";
import { cn } from "@/lib/utils";

interface LineItemsTableViewProps {
  lineItems: LineItem[];
  onEdit: (lineItem: LineItem) => void;
  onDelete: (lineItemId: string) => void;
  onUpdateField: (
    lineItemId: string,
    field: string,
    value: string | number
  ) => Promise<void>;
  formatCurrency: (amount: number) => string;
  categoryNamesMap: Record<string, string>;
  isUpdating?: boolean;
}

interface EditingState {
  rowId: string | null;
  field: string | null;
  value: string;
  originalValue: string | number;
}

const LineItemsTableView: React.FC<LineItemsTableViewProps> = memo(
  ({
    lineItems,
    onEdit,
    onDelete,
    onUpdateField,
    formatCurrency,
    categoryNamesMap,
    isUpdating = false,
  }) => {
    const [editingState, setEditingState] = useState<EditingState>({
      rowId: null,
      field: null,
      value: "",
      originalValue: "",
    });
    const [savingField, setSavingField] = useState<string | null>(null);
    const [errors, setErrors] = useState<Record<string, string>>({});

    // Helper function to render unit price breakdown for package items
    const renderUnitPriceBreakdown = useCallback(
      (item: LineItem) => {
        // Only show breakdown for package items (not custom items)
        if (item.is_custom || !item.package_id) {
          return null;
        }

        // Extract pricing components from the line item
        // For package items, unit_price represents the total unit price (base + options)
        const totalUnitPrice = item.unit_price || 0;

        // Get the options total from the line item data
        // The lineItemService fetches options_total_adjustment from the database
        const optionsTotal = item.options_total_adjustment || 0;
        const basePrice = totalUnitPrice - optionsTotal;

        // Only show breakdown if there's meaningful data
        if (totalUnitPrice <= 0) {
          return null;
        }

        return (
          <div className="text-xs text-gray-500 mt-1 leading-tight">
            <span className="font-mono">
              (Base: {formatCurrency(basePrice)} + Options:{" "}
              {formatCurrency(optionsTotal)})
            </span>
          </div>
        );
      },
      [formatCurrency]
    );

    // Check if a row is currently being edited
    const isRowEditing = useCallback(
      (rowId: string) => editingState.rowId === rowId,
      [editingState.rowId]
    );
    const isFieldEditing = useCallback(
      (rowId: string, field: string) =>
        editingState.rowId === rowId && editingState.field === field,
      [editingState.rowId, editingState.field]
    );

    // Start editing a cell
    const startEditing = useCallback(
      (rowId: string, field: string, currentValue: string | number) => {
        // Don't allow editing if another row is being edited
        if (editingState.rowId && editingState.rowId !== rowId) {
          return;
        }

        setEditingState({
          rowId,
          field,
          value: currentValue.toString(),
          originalValue: currentValue,
        });
        setErrors({});
      },
      [editingState.rowId]
    );

    // Cancel editing
    const cancelEditing = useCallback(() => {
      setEditingState({
        rowId: null,
        field: null,
        value: "",
        originalValue: "",
      });
      setErrors({});
    }, []);

    // Save the current edit
    const saveEdit = useCallback(async () => {
      if (!editingState.rowId || !editingState.field) return;

      const fieldKey = `${editingState.rowId}-${editingState.field}`;
      setSavingField(fieldKey);
      setErrors({});

      // Store original value for potential rollback
      const originalValue = editingState.originalValue;

      try {
        // Validate the value
        let parsedValue: string | number = editingState.value;

        if (
          ["quantity", "item_quantity_basis", "unit_price"].includes(
            editingState.field
          )
        ) {
          parsedValue = Number(editingState.value);
          if (isNaN(parsedValue)) {
            throw new Error("Please enter a valid number");
          }
          if (editingState.field === "quantity" && parsedValue <= 0) {
            throw new Error("Quantity must be greater than 0");
          }
          if (
            ["item_quantity_basis", "unit_price"].includes(
              editingState.field
            ) &&
            parsedValue < 0
          ) {
            throw new Error("Value must be 0 or greater");
          }
        }

        // Call the update function
        await onUpdateField(
          editingState.rowId,
          editingState.field,
          parsedValue
        );

        // Clear editing state on success
        cancelEditing();
      } catch (error) {
        console.error("Error saving field:", error);

        // For auto-save failures, revert to original value
        setEditingState((prev) => ({
          ...prev,
          value: originalValue.toString(),
        }));

        setErrors({
          [fieldKey]:
            error instanceof Error
              ? error.message
              : "Auto-save failed. Please try again.",
        });
      } finally {
        setSavingField(null);
      }
    }, [editingState, onUpdateField, cancelEditing]);

    // Handle keyboard events
    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === "Enter") {
          e.preventDefault();
          saveEdit();
        } else if (e.key === "Escape") {
          e.preventDefault();
          cancelEditing();
        }
      },
      [saveEdit, cancelEditing]
    );

    // Handle blur (clicking outside) to auto-save
    const handleBlur = useCallback(
      (e: React.FocusEvent) => {
        // Don't auto-save if we're not currently editing
        if (!editingState.rowId || !editingState.field) {
          return;
        }

        // Don't auto-save if the value hasn't changed
        const currentValue = editingState.value.toString().trim();
        const originalValue = editingState.originalValue.toString().trim();

        if (currentValue === originalValue) {
          // Value unchanged, just cancel editing
          cancelEditing();
          return;
        }

        // Use a small timeout to allow for potential focus changes to related elements
        setTimeout(() => {
          // Double-check we're still in the same editing state and value has changed
          const currentState = editingState;
          if (
            currentState.rowId &&
            currentState.field &&
            currentState.value.toString().trim() !==
              currentState.originalValue.toString().trim()
          ) {
            saveEdit();
          }
        }, 100);
      },
      [editingState, saveEdit, cancelEditing]
    );

    // Render editable cell
    const renderEditableCell = useCallback(
      (
        item: LineItem,
        field: string,
        value: string | number,
        type: "text" | "number" | "textarea" = "text",
        disabled = false
      ) => {
        const isEditing = isFieldEditing(item.id, field);
        const fieldKey = `${item.id}-${field}`;
        const isSaving = savingField === fieldKey;
        const error = errors[fieldKey];

        if (disabled) {
          return (
            <div className="p-2 text-sm text-gray-500 bg-gray-50 rounded">
              {type === "number" && field === "unit_price"
                ? formatCurrency(Number(value))
                : value}
              <div className="text-xs text-gray-400 mt-1">Read-only</div>
              {/* Show price breakdown for package items */}
              {type === "number" &&
                field === "unit_price" &&
                renderUnitPriceBreakdown(item)}
            </div>
          );
        }

        if (isEditing) {
          const InputComponent = type === "textarea" ? Textarea : Input;

          return (
            <div className="relative">
              <InputComponent
                value={editingState.value}
                onChange={(e) =>
                  setEditingState((prev) => ({
                    ...prev,
                    value: e.target.value,
                  }))
                }
                onKeyDown={handleKeyDown}
                onBlur={handleBlur}
                type={type === "number" ? "number" : "text"}
                className={cn(
                  "h-8 text-sm border-2 border-blue-400 bg-blue-50 focus:border-blue-500 focus:ring-1 focus:ring-blue-500",
                  type === "textarea" && "min-h-[60px] resize-none",
                  error && "border-red-500 bg-red-50"
                )}
                disabled={isSaving}
                autoFocus
              />
              {isSaving && (
                <div className="absolute inset-0 bg-white/50 rounded flex items-center justify-center">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                </div>
              )}
              {error && (
                <div className="text-xs text-red-600 mt-1 flex items-center gap-1">
                  <X className="h-3 w-3" />
                  {error}
                </div>
              )}
            </div>
          );
        }

        return (
          <div
            className={cn(
              "p-2 rounded cursor-pointer transition-all duration-200",
              "border-2 border-dashed border-gray-200 hover:border-blue-300 hover:bg-blue-50",
              "group relative",
              isRowEditing(item.id) && "bg-blue-50 border-blue-300"
            )}
            onClick={() => startEditing(item.id, field, value)}
            title="Click to edit"
          >
            <div className="text-sm flex items-center justify-between">
              <div className="flex-1">
                <span className={cn(!value && "text-gray-400 italic")}>
                  {type === "number" && field === "unit_price"
                    ? formatCurrency(Number(value))
                    : value || "Click to edit..."}
                </span>
                {/* Show price breakdown for package items */}
                {type === "number" &&
                  field === "unit_price" &&
                  renderUnitPriceBreakdown(item)}
              </div>
              <Edit className="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
          </div>
        );
      },
      [
        editingState,
        savingField,
        errors,
        formatCurrency,
        startEditing,
        handleKeyDown,
        handleBlur,
        isFieldEditing,
        isRowEditing,
        renderUnitPriceBreakdown,
      ]
    );

    return (
      <div className="border dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 px-4 py-3 border-b dark:border-gray-700">
          <div className="flex items-center gap-2">
            <Calculator className="h-4 w-4 text-gray-600 dark:text-gray-300" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
              Click any cell to edit • Auto-saves when you click outside • Press
              Enter to save • Press Escape to cancel
            </span>
          </div>
        </div>
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 dark:bg-gray-800 border-b-2 dark:border-gray-700">
              <TableHead className="w-[300px] font-semibold dark:text-gray-200">
                Item Details
              </TableHead>
              <TableHead className="w-[120px] text-center font-semibold dark:text-gray-200">
                Quantity
              </TableHead>
              <TableHead className="w-[120px] text-center font-semibold dark:text-gray-200">
                Days/Units
              </TableHead>
              <TableHead className="w-[140px] text-center font-semibold dark:text-gray-200">
                Unit Price
              </TableHead>
              <TableHead className="w-[140px] text-center font-semibold dark:text-gray-200">
                Total Price
              </TableHead>
              <TableHead className="w-[100px] text-center font-semibold dark:text-gray-200">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lineItems.map((item) => {
              const isEditing = isRowEditing(item.id);
              const categoryName = item.category_id
                ? categoryNamesMap[item.category_id]
                : undefined;
              const totalPrice =
                item.total_price ||
                item.quantity *
                  (item.unit_price || 0) *
                  (item.item_quantity_basis || 1);

              return (
                <TableRow
                  key={item.id}
                  className={cn(
                    "transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800",
                    isEditing &&
                      "bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-blue-500 shadow-sm",
                    isUpdating && "opacity-50"
                  )}
                >
                  {/* Item Name & Info */}
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div
                        className={cn(
                          "p-1 rounded",
                          item.is_custom
                            ? "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300"
                            : "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-300"
                        )}
                      >
                        {item.is_custom ? (
                          <Sparkles className="h-3 w-3" />
                        ) : (
                          <Package className="h-3 w-3" />
                        )}
                      </div>
                      <div className="flex-1">
                        {item.is_custom ? (
                          renderEditableCell(item, "name", item.name, "text")
                        ) : (
                          <div>
                            <div className="font-medium text-sm dark:text-gray-200">
                              {item.name}
                            </div>
                            {categoryName && (
                              <Badge variant="outline" className="text-xs mt-1">
                                {categoryName}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>

                  {/* Quantity */}
                  <TableCell className="text-center">
                    {renderEditableCell(
                      item,
                      "quantity",
                      item.quantity,
                      "number"
                    )}
                  </TableCell>

                  {/* Days/Units */}
                  <TableCell className="text-center">
                    {renderEditableCell(
                      item,
                      "item_quantity_basis",
                      item.item_quantity_basis || 1,
                      "number"
                    )}
                  </TableCell>

                  {/* Unit Price */}
                  <TableCell className="text-center">
                    {renderEditableCell(
                      item,
                      "unit_price",
                      item.unit_price || 0,
                      "number",
                      !item.is_custom
                    )}
                  </TableCell>

                  {/* Total Price */}
                  <TableCell className="text-center">
                    <div className="font-medium text-sm dark:text-gray-200">
                      {formatCurrency(totalPrice)}
                    </div>
                  </TableCell>

                  {/* Actions */}
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(item)}
                        disabled={
                          isUpdating ||
                          (editingState.rowId !== null && !isEditing)
                        }
                        className="h-8 w-8 p-0 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-300"
                        title="Advanced Edit"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(item.id)}
                        disabled={
                          isUpdating ||
                          (editingState.rowId !== null && !isEditing)
                        }
                        className="h-8 w-8 p-0 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-300"
                        title="Delete Item"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    );
  }
);

LineItemsTableView.displayName = "LineItemsTableView";

export default LineItemsTableView;
