import { apiClient, API_ENDPOINTS, getAuthenticatedApiClient } from '@/integrations/api';
import { Event, EventRequest, EventResponse, transformApiEvent, transformEventToApiRequest } from '@/types/events';
import axios from 'axios';

/**
 * Fetch all events with optional filtering
 * @returns List of events
 */
export const getAllEvents = async (): Promise<Event[]> => {
  try {
    console.log('%cService: Fetching all events', 'color: purple; font-weight: bold');

    // Use the authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.EVENTS.LIST);

    // Transform API events to frontend Event type
    const events = response.data.map(transformApiEvent);

    console.log('%cService: Events fetched successfully', 'color: green; font-weight: bold', events);
    return events;
  } catch (error) {
    console.error('Error fetching events:', error);

    if (axios.isAxiosError(error)) {
      console.error('API error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    throw error;
  }
};

/**
 * Fetch an event by ID
 * @param id - The event ID
 * @returns The event data
 */
export const getEventById = async (id: string): Promise<Event> => {
  try {
    console.log(`Fetching event with ID: ${id}`);

    // Use the authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get<EventResponse>(API_ENDPOINTS.EVENTS.GET_BY_ID(id));

    // Transform API event to frontend Event type
    const event = transformApiEvent(response.data);

    console.log('Event data received:', event);
    return event;
  } catch (error) {
    console.error(`Error fetching event with ID ${id}:`, error);

    if (axios.isAxiosError(error)) {
      console.error('API error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    throw error;
  }
};

/**
 * Create a new event
 * @param eventData - The event data to create (can be EventRequest or Partial<Event>)
 * @returns The created event
 */
export const createEvent = async (eventData: EventRequest | Partial<Event>): Promise<Event> => {
  try {
    console.log('Creating new event:', eventData);

    // Check if eventData is already in API format (EventRequest)
    let apiEventData: EventRequest;
    if ('event_name' in eventData) {
      // Already in API format
      apiEventData = eventData as EventRequest;
    } else {
      // Transform frontend event to API request (legacy support)
      apiEventData = transformEventToApiRequest(eventData as Partial<Event>);
    }

    // Use the authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.post<EventResponse>(
      API_ENDPOINTS.EVENTS.CREATE,
      apiEventData
    );

    // Transform API event to frontend Event type
    const createdEvent = transformApiEvent(response.data);

    console.log('Event created successfully:', createdEvent);
    return createdEvent;
  } catch (error) {
    console.error('Error creating event:', error);

    if (axios.isAxiosError(error)) {
      console.error('API error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    throw error;
  }
};

/**
 * Update an existing event
 * @param id - The event ID
 * @param eventData - The event data to update (can be EventRequest or Partial<Event>)
 * @returns The updated event
 */
export const updateEvent = async (id: string, eventData: EventRequest | Partial<Event>): Promise<Event> => {
  try {
    console.log(`Updating event with ID ${id}:`, eventData);

    // Check if eventData is already in API format (EventRequest)
    let apiEventData: EventRequest;
    if ('event_name' in eventData) {
      // Already in API format
      apiEventData = eventData as EventRequest;
    } else {
      // Transform frontend event to API request (legacy support)
      apiEventData = transformEventToApiRequest(eventData as Partial<Event>);
    }

    // Use the authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.patch<EventResponse>(
      API_ENDPOINTS.EVENTS.UPDATE(id),
      apiEventData
    );

    // Transform API event to frontend Event type
    const updatedEvent = transformApiEvent(response.data);

    console.log('Event updated successfully:', updatedEvent);
    return updatedEvent;
  } catch (error) {
    console.error(`Error updating event with ID ${id}:`, error);

    if (axios.isAxiosError(error)) {
      console.error('API error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    throw error;
  }
};

/**
 * Delete an event
 * @param id - The event ID
 * @returns True if successful
 */
export const deleteEvent = async (id: string): Promise<boolean> => {
  try {
    console.log(`Deleting event with ID: ${id}`);

    // Use the authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.delete(API_ENDPOINTS.EVENTS.DELETE(id));

    console.log('Event deleted successfully');
    return true;
  } catch (error) {
    console.error(`Error deleting event with ID ${id}:`, error);

    if (axios.isAxiosError(error)) {
      console.error('API error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    throw error;
  }
};
