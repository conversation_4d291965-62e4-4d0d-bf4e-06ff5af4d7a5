import { LineItem } from '@/types/calculation';

/**
 * Category type with optional display_order
 */
export interface CategoryWithOrder {
  id: string;
  name: string;
  display_order?: number;
}

/**
 * Category entry with items for tab display
 */
export interface CategoryEntry<T> {
  categoryId: string;
  items: T[];
  displayOrder: number;
}

/**
 * Create a map of category IDs to their display order
 *
 * @param categories - Array of categories with id, name, and display_order
 * @returns Map of category IDs to their display order
 */
export const createCategoryOrderMap = (
  categories: CategoryWithOrder[],
): Map<string, number> => {
  const categoryOrderMap = new Map<string, number>();

  // Add all categories to the map with their display order
  categories.forEach((category, index) => {
    // Use display_order if available, otherwise use the index as a fallback
    categoryOrderMap.set(category.id, category.display_order ?? index + 1);
  });

  // Set a high number for uncategorized items to display them last
  const uncategorizedOrder = categories.length + 1;
  categoryOrderMap.set('uncategorized', uncategorizedOrder);

  return categoryOrderMap;
};

/**
 * Sort categories by their display_order
 *
 * @param categories - Array of categories with id, name, and display_order
 * @returns Sorted array of categories
 */
export const sortCategoriesByOrder = (
  categories: CategoryWithOrder[],
): CategoryWithOrder[] => {
  if (!Array.isArray(categories) || categories.length === 0) {
    return [];
  }

  return [...categories].sort(
    (a, b) => (a.display_order ?? 9999) - (b.display_order ?? 9999),
  );
};

/**
 * Sort category entries by display order
 *
 * @param categoryEntries - Array of category entries with items
 * @returns Sorted array of category entries
 */
export const sortCategoryEntries = <T>(
  categoryEntries: CategoryEntry<T>[],
): CategoryEntry<T>[] => {
  if (!Array.isArray(categoryEntries) || categoryEntries.length === 0) {
    return [];
  }

  return [...categoryEntries].sort((a, b) => a.displayOrder - b.displayOrder);
};

/**
 * Transform category map to sorted category entries
 *
 * @param categoryMap - Record mapping category IDs to arrays of items
 * @param categories - Array of categories with id, name, and display_order
 * @returns Array of category entries sorted by display order
 */
export const transformCategoryMapToSortedEntries = <T>(
  categoryMap: Record<string, T[]>,
  categories: CategoryWithOrder[],
): CategoryEntry<T>[] => {
  return Object.entries(categoryMap)
    .filter(([_, items]) => items.length > 0)
    .map(([categoryId, items]) => {
      // Find the category object to get its display_order
      const category = categories.find((cat) => cat.id === categoryId);
      return {
        categoryId,
        items,
        displayOrder: category?.display_order || 9999,
      };
    })
    .sort((a, b) => a.displayOrder - b.displayOrder);
};

/**
 * Sort line items by category display order
 *
 * @param lineItems - Array of line items to sort
 * @param categories - Array of categories with id, name, and display_order
 * @returns Sorted array of line items
 */
export const sortLineItemsByCategory = (
  lineItems: LineItem[],
  categories: CategoryWithOrder[],
): LineItem[] => {
  if (!Array.isArray(lineItems) || lineItems.length === 0) {
    return [];
  }

  if (!Array.isArray(categories) || categories.length === 0) {
    return [...lineItems];
  }

  // Create a map of category IDs to their display order
  const categoryOrderMap = createCategoryOrderMap(categories);

  // Get the uncategorized order value
  const uncategorizedOrder = categories.length + 1;

  // Sort the line items by category order
  return [...lineItems].sort((a, b) => {
    const categoryIdA = a.category_id || 'uncategorized';
    const categoryIdB = b.category_id || 'uncategorized';

    const orderA = categoryOrderMap.get(categoryIdA) ?? uncategorizedOrder;
    const orderB = categoryOrderMap.get(categoryIdB) ?? uncategorizedOrder;

    // Sort by category order first
    if (orderA !== orderB) {
      return orderA - orderB;
    }

    // If same category, maintain original order (by creation date or ID)
    return 0;
  });
};
