import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { PackageFormValues } from '../../../types/package';
import { FormSection } from '../../shared';

interface StatusSectionProps {
  form: UseFormReturn<PackageFormValues>;
}

export const StatusSection: React.FC<StatusSectionProps> = ({ form }) => {
  return (
    <FormSection title='Status' stepNumber={5}>
        <FormField
          control={form.control}
          name='isActive'
          render={({ field }) => (
            <FormItem className='flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm'>
              <div className='space-y-0.5'>
                <FormLabel>Active Status</FormLabel>
                <FormDescription>
                  Inactive packages won't appear in search results or be available for
                  selection
                </FormDescription>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )}
        />
    </FormSection>
  );
};
