/**
 * User Preferences Hook
 *
 * Manages user preferences including timezone settings
 */

import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { DEFAULT_TIMEZONE, isValidTimezone } from "@/lib/timezone-utils";

export interface UserPreferences {
  timezone: string;
  dateFormat?: string;
  currency?: string;
  language?: string;
  dashboardVersion?: "v1" | "v2";
  notifications?: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

const DEFAULT_PREFERENCES: UserPreferences = {
  timezone: DEFAULT_TIMEZONE,
  dateFormat: "dd/MM/yyyy",
  currency: "IDR",
  language: "en",
  dashboardVersion: "v1",
  notifications: {
    email: true,
    push: true,
    sms: false,
  },
};

/**
 * Hook to manage user preferences
 */
export const useUserPreferences = () => {
  const queryClient = useQueryClient();
  const [isInitialized, setIsInitialized] = useState(false);

  // Fetch user preferences
  const {
    data: preferences,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["user-preferences"],
    queryFn: async (): Promise<UserPreferences> => {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      const { data: profile, error } = await supabase
        .from("profiles")
        .select("preferences")
        .eq("id", user.id)
        .single();

      if (error) {
        console.error("Error fetching user preferences:", error);
        throw error;
      }

      // Merge with defaults and validate
      const userPrefs = { ...DEFAULT_PREFERENCES, ...profile?.preferences };

      // Validate timezone
      if (!isValidTimezone(userPrefs.timezone)) {
        userPrefs.timezone = DEFAULT_TIMEZONE;
      }

      return userPrefs;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
  });

  // Update user preferences
  const updatePreferencesMutation = useMutation({
    mutationFn: async (
      newPreferences: Partial<UserPreferences>
    ): Promise<UserPreferences> => {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      // Merge with existing preferences
      const updatedPreferences = { ...preferences, ...newPreferences };

      // Validate timezone if being updated
      if (
        newPreferences.timezone &&
        !isValidTimezone(newPreferences.timezone)
      ) {
        throw new Error("Invalid timezone provided");
      }

      const { data, error } = await supabase
        .from("profiles")
        .update({
          preferences: updatedPreferences,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id)
        .select("preferences")
        .single();

      if (error) {
        console.error("Error updating user preferences:", error);
        throw error;
      }

      return data.preferences;
    },
    onSuccess: (updatedPreferences) => {
      // Update the cache
      queryClient.setQueryData(["user-preferences"], updatedPreferences);
      toast.success("Preferences updated successfully");
    },
    onError: (error) => {
      console.error("Error updating preferences:", error);
      toast.error("Failed to update preferences");
    },
  });

  // Initialize user preferences if they don't exist
  const initializePreferencesMutation = useMutation({
    mutationFn: async (): Promise<UserPreferences> => {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      // Check if profile exists
      const { data: existingProfile } = await supabase
        .from("profiles")
        .select("id, preferences")
        .eq("id", user.id)
        .single();

      if (existingProfile && existingProfile.preferences) {
        // Profile exists with preferences
        return existingProfile.preferences;
      }

      // Create or update profile with default preferences
      const { data, error } = await supabase
        .from("profiles")
        .upsert({
          id: user.id,
          username: user.email?.split("@")[0] || "user",
          preferences: DEFAULT_PREFERENCES,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select("preferences")
        .single();

      if (error) {
        console.error("Error initializing user preferences:", error);
        throw error;
      }

      return data.preferences;
    },
    onSuccess: (initializedPreferences) => {
      queryClient.setQueryData(["user-preferences"], initializedPreferences);
      setIsInitialized(true);
    },
    onError: (error) => {
      console.error("Error initializing preferences:", error);
    },
  });

  // Initialize preferences on first load if needed
  useEffect(() => {
    if (!isLoading && !preferences && !isError && !isInitialized) {
      initializePreferencesMutation.mutate();
    }
  }, [isLoading, preferences, isError, isInitialized]);

  // Helper functions
  const updateTimezone = (timezone: string) => {
    if (!isValidTimezone(timezone)) {
      toast.error("Invalid timezone selected");
      return;
    }
    updatePreferencesMutation.mutate({ timezone });
  };

  const updateDateFormat = (dateFormat: string) => {
    updatePreferencesMutation.mutate({ dateFormat });
  };

  const updateCurrency = (currency: string) => {
    updatePreferencesMutation.mutate({ currency });
  };

  const updateNotifications = (
    notifications: Partial<UserPreferences["notifications"]>
  ) => {
    const updatedNotifications = {
      ...preferences?.notifications,
      ...notifications,
    };
    updatePreferencesMutation.mutate({ notifications: updatedNotifications });
  };

  const updateDashboardVersion = (dashboardVersion: "v1" | "v2") => {
    updatePreferencesMutation.mutate({ dashboardVersion });
  };

  const resetToDefaults = () => {
    updatePreferencesMutation.mutate(DEFAULT_PREFERENCES);
  };

  return {
    // Data
    preferences: preferences || DEFAULT_PREFERENCES,
    timezone: preferences?.timezone || DEFAULT_TIMEZONE,

    // Loading states
    isLoading: isLoading || initializePreferencesMutation.isPending,
    isUpdating: updatePreferencesMutation.isPending,
    isError,
    error,

    // Actions
    updatePreferences: updatePreferencesMutation.mutate,
    updateTimezone,
    updateDateFormat,
    updateCurrency,
    updateNotifications,
    updateDashboardVersion,
    resetToDefaults,

    // Helper
    isInitialized: isInitialized || !!preferences,
  };
};
