import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException) // Catch only HttpExceptions
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;

    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message: exception.message || 'Internal server error',
      // Include more details from the exception response if available
      ...(typeof exception.getResponse() === 'object'
        ? (exception.getResponse() as object)
        : { error: exception.getResponse() }),
    };

    // Log the error
    this.logger.error(
      `HTTP Status: ${status} Error Message: ${JSON.stringify(errorResponse.message)} Path: ${errorResponse.path}`,
      exception.stack,
    );

    response.status(status).json(errorResponse);
  }
}
