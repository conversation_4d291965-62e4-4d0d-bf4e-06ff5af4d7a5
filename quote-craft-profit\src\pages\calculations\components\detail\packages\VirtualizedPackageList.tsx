import React, { useRef, memo, useMemo, useCallback, useEffect } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { PackageWithOptions } from '@/types/calculation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { PlusCircle } from 'lucide-react';
import { generatePriceFormula } from '../../../utils/calculationUtils';
import { UI_CONSTANTS } from '../../../constants';
import { usePerformanceMonitor } from '../../../hooks/utils/usePerformanceMonitor';

interface VirtualizedPackageListProps {
  packages: PackageWithOptions[];
  onAddToCalculation: (packageId: string) => void;
  formatCurrency: (amount: number) => string;
  isLoading?: boolean;
  isError?: boolean;
  maxHeight?: number;
  estimatedItemSize?: number;
  overscan?: number;
}

const VirtualizedPackageList: React.FC<VirtualizedPackageListProps> = ({
  packages,
  onAddToCalculation,
  formatCurrency,
  isLoading = false,
  isError = false,
  maxHeight = 600,
  estimatedItemSize = 200,
  overscan = 5,
}) => {
  const parentRef = useRef<HTMLDivElement>(null);

  // Performance monitoring
  const { logSummary } = usePerformanceMonitor({
    componentName: 'VirtualizedPackageList',
    enabled: packages.length > UI_CONSTANTS.VIRTUALIZATION_THRESHOLD,
    slowRenderThreshold: 20, // Slightly higher threshold for virtualized lists
  });

  // Log performance summary on unmount
  useEffect(() => {
    return () => {
      logSummary();
    };
  }, [logSummary]);

  // Memoize the add to calculation handler to prevent unnecessary re-renders
  const handleAddToCalculation = useCallback(
    (packageId: string) => {
      onAddToCalculation(packageId);
    },
    [onAddToCalculation]
  );

  // Memoize the quantity basis label formatter
  const formatQuantityBasisLabel = useCallback((basis: string) => {
    switch (basis) {
      case 'PER_EVENT':
        return 'per event';
      case 'PER_DAY':
        return 'per day';
      case 'PER_ATTENDEE':
        return 'per attendee';
      case 'PER_ITEM':
        return 'per item';
      case 'PER_ITEM_PER_DAY':
        return 'per item per day';
      case 'PER_ATTENDEE_PER_DAY':
        return 'per attendee per day';
      default:
        return basis.replace(/_/g, ' ').toLowerCase();
    }
  }, []);

  // Set up virtualization with configurable options
  const virtualizer = useVirtualizer({
    count: isLoading ? 5 : packages.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimatedItemSize,
    overscan,
  });

  // Render loading skeletons
  if (isLoading) {
    return (
      <div className='space-y-4'>
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index} className='w-full'>
            <CardHeader className='pb-2'>
              <Skeleton className='h-6 w-3/4' />
              <Skeleton className='h-4 w-1/2 mt-2' />
            </CardHeader>
            <CardContent>
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-3/4 mt-2' />
            </CardContent>
            <CardFooter>
              <Skeleton className='h-10 w-32' />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // Show error state
  if (isError) {
    return (
      <div className='text-center py-8 border border-red-200 rounded-lg bg-red-50'>
        <p className='text-red-600 font-medium'>Failed to load packages</p>
        <p className='text-sm text-red-500 mt-2'>
          Please try again later or contact support if the problem persists
        </p>
      </div>
    );
  }

  // If no packages, show empty state
  if (packages.length === 0) {
    return (
      <div className='text-center py-8'>
        <p className='text-muted-foreground'>No packages available in this category</p>
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className='overflow-auto'
      style={{
        height: `${Math.min(maxHeight, packages.length * estimatedItemSize)}px`,
        width: '100%',
        maxHeight: `${maxHeight}px`,
      }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const pkg = packages[virtualItem.index];
          return (
            <div
              key={virtualItem.key}
              data-index={virtualItem.index}
              ref={virtualizer.measureElement}
              className='absolute top-0 left-0 w-full'
              style={{
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              <Card className='w-full mb-4 hover:shadow-md transition-shadow'>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-lg'>{pkg.name}</CardTitle>
                  <div className='flex flex-col'>
                    <div className='flex items-center'>
                      <Badge variant='outline' className='mr-2'>
                        {formatQuantityBasisLabel(pkg.quantity_basis || 'PER_ITEM')}
                      </Badge>
                      <span className='font-semibold'>
                        {formatCurrency(Number(pkg.price))}
                      </span>
                    </div>
                    <CardDescription className='text-xs text-muted-foreground mt-1'>
                      {generatePriceFormula(
                        Number(pkg.price),
                        1,
                        1,
                        pkg.quantity_basis,
                        pkg.currency_symbol || 'Rp',
                      )}
                    </CardDescription>
                  </div>
                </CardHeader>
                {pkg.description && (
                  <CardContent>
                    <p className='text-sm text-muted-foreground'>{pkg.description}</p>
                  </CardContent>
                )}
                {pkg.options && pkg.options.length > 0 && (
                  <CardContent className='pt-0'>
                    <p className='text-xs text-muted-foreground'>
                      {pkg.options.length} option{pkg.options.length !== 1 ? 's' : ''}{' '}
                      available
                    </p>
                  </CardContent>
                )}
                <CardFooter>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handleAddToCalculation(pkg.id)}
                    className='flex items-center gap-1'
                  >
                    <PlusCircle className='h-4 w-4' />
                    <span>Add to Calculation</span>
                  </Button>
                </CardFooter>
              </Card>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(VirtualizedPackageList);
