import { z } from 'zod';
import { commonValidations, formFieldPatterns } from '@/schemas/common';

/**
 * Client-related validation schemas
 * Centralized schemas for all client forms and components
 */

// Client form schema (extracted from ClientFormDialog.tsx)
export const clientFormSchema = z.object({
  client_name: formFieldPatterns.name('Client name'),
  contact_person: commonValidations.optionalString(),
  email: commonValidations.email(false), // Optional email
  phone: commonValidations.requiredString(1, 'Phone number'), // Now required
  address: formFieldPatterns.address(),
  company_name: commonValidations.requiredString(1, 'Company name'), // Now required
});

// Type exports for TypeScript inference
export type ClientFormValues = z.infer<typeof clientFormSchema>;
