import { Package } from '../../../pages/admin/packages/types/package';
import { PackageFilters } from '../../../pages/admin/packages/types/filters';
import { PaginatedResult } from '@/types/pagination';
import {
  deletePackageWithApi,
  getAllPackagesFromApi,
  getPackageByIdFromApi,
  savePackageWithApi,
  togglePackageStatusWithApi,
} from './packageApiService';

/**
 * Get all packages with optional filtering
 * This function now uses the backend API instead of direct Supabase calls
 * @param filters - Optional filters for packages
 * @returns Promise resolving to a paginated result of packages
 */
export const getAllPackages = async (
  filters: PackageFilters = {},
): Promise<PaginatedResult<Package>> => {
  try {
    // Use the API service to fetch packages
    return await getAllPackagesFromApi(filters);
  } catch (error) {
    console.error('Error in getAllPackages:', error);

    // Return empty result on error
    return {
      data: [],
      totalCount: 0,
      page: filters.page || 1,
      pageSize: filters.pageSize || 10,
      totalPages: 0,
    };
  }
};

/**
 * Get package by ID
 * This function now uses the backend API instead of direct Supabase calls
 * @param id - The package ID
 * @returns Promise resolving to a package or null
 */
export const getPackageById = async (id: string): Promise<Package | null> => {
  try {
    // Use the API service to fetch the package
    return await getPackageByIdFromApi(id);
  } catch (error) {
    console.error(`Error in getPackageById for ID ${id}:`, error);
    return null;
  }
};

/**
 * Toggle package active status
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageId - The package ID
 * @param isActive - Whether the package should be active
 * @returns Promise resolving to the updated package
 */
export const togglePackageStatus = async (
  packageId: string,
  isActive: boolean,
): Promise<Package> => {
  try {
    // Use the API service to toggle package status and return the updated package
    return await togglePackageStatusWithApi(packageId, isActive);
  } catch (error) {
    console.error(`Error in togglePackageStatus for ID ${packageId}:`, error);
    throw error;
  }
};

/**
 * Create or update a package
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageData - The package data to save
 * @returns Promise resolving to the saved package
 */
export const savePackage = async (packageData: any): Promise<Package> => {
  try {
    // Use the API service to save the package
    return await savePackageWithApi(packageData);
  } catch (error) {
    console.error('Error in savePackage:', error);
    throw error;
  }
};

/**
 * Delete a package
 * This function uses the backend API to delete a package
 * @param packageId - The ID of the package to delete
 * @returns Promise resolving to void
 */
export const deletePackage = async (packageId: string): Promise<void> => {
  try {
    // Use the API service to delete the package
    await deletePackageWithApi(packageId);
  } catch (error) {
    console.error(`Error in deletePackage for ID ${packageId}:`, error);
    throw error;
  }
};
