import { CacheService, CacheMetrics, CacheInvalidationEvent } from './cache.service';
export interface CacheAlert {
    id: string;
    type: 'performance' | 'error' | 'capacity' | 'health';
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    timestamp: Date;
    metrics?: any;
    resolved?: boolean;
}
export interface CachePerformanceReport {
    timestamp: Date;
    period: string;
    metrics: CacheMetrics;
    health: {
        status: 'healthy' | 'degraded' | 'unhealthy';
        hitRate: number;
        errorRate: number;
    };
    alerts: CacheAlert[];
    recommendations: string[];
}
export declare class CacheMonitoringService {
    private readonly cacheService;
    private readonly logger;
    private alerts;
    private performanceHistory;
    private readonly maxHistorySize;
    private readonly maxAlertsSize;
    constructor(cacheService: CacheService);
    monitorCachePerformance(): void;
    handleCacheInvalidation(event: CacheInvalidationEvent): void;
    private checkForAlerts;
    private generateRecommendations;
    private handleCriticalAlerts;
    getCacheStatus(): {
        health: any;
        metrics: CacheMetrics;
        activeAlerts: CacheAlert[];
        recentRecommendations: string[];
    };
    getPerformanceHistory(limit?: number): CachePerformanceReport[];
    getAlertsHistory(limit?: number): CacheAlert[];
    resolveAlert(alertId: string): boolean;
    getDashboardData(): {
        currentHealth: any;
        metrics: CacheMetrics;
        trends: {
            hitRateTrend: number[];
            errorRateTrend: number[];
            operationsTrend: number[];
        };
        alerts: {
            critical: number;
            high: number;
            medium: number;
            low: number;
        };
    };
    private trimAlerts;
    private trimHistory;
    resetMonitoringData(): void;
}
