import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import AdminLayout from '@/components/layout/AdminLayout';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from '@/components/ui/breadcrumb';
import { getAllDivisions } from '@/services/admin/divisions';
import { DivisionList, DivisionFormDialog } from './components';
import { Division } from '@/types/types';

const DivisionsPage: React.FC = () => {
  const [isDivisionFormOpen, setIsDivisionFormOpen] = useState(false);
  const [editingDivisionId, setEditingDivisionId] = useState<string | null>(null);

  // Fetch divisions using the service layer
  const {
    data: divisions,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['divisions'],
    queryFn: () => getAllDivisions(),
    meta: {
      onError: () => {
        toast.error('Failed to load divisions');
      },
    },
  });

  const handleOpenNewDivisionForm = () => {
    setEditingDivisionId(null);
    setIsDivisionFormOpen(true);
  };

  const handleEditDivision = (divisionId: string) => {
    setEditingDivisionId(divisionId);
    setIsDivisionFormOpen(true);
  };

  const handleDivisionFormClose = (shouldRefresh: boolean = false) => {
    setIsDivisionFormOpen(false);
    setEditingDivisionId(null);

    if (shouldRefresh) {
      refetch();
    }
  };

  return (
    <AdminLayout title='Manage Divisions'>
      {/* Breadcrumbs */}
      <Breadcrumb className='mb-4'>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href='/admin'>Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href='/admin/catalogue'>Catalogue</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Divisions</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className='mb-6 text-muted-foreground'>
        Create and manage business divisions to organize your service packages.
      </div>

      {/* Action Bar Block */}
      <div className='mb-6'>
        <Button onClick={handleOpenNewDivisionForm}>
          <Plus className='w-4 h-4 mr-2' /> Add New Division
        </Button>
      </div>

      {/* Division List Block */}
      <DivisionList
        divisions={divisions || []}
        isLoading={isLoading}
        isError={isError}
        onEdit={handleEditDivision}
        onRefresh={refetch}
      />

      {/* Add/Edit Division Dialog */}
      <DivisionFormDialog
        isOpen={isDivisionFormOpen}
        onClose={handleDivisionFormClose}
        divisionId={editingDivisionId}
      />
    </AdminLayout>
  );
};

export default DivisionsPage;
