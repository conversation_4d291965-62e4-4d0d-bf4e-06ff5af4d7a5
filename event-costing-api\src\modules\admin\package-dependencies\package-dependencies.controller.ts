import {
  <PERSON>,
  Post,
  Body,
  Param,
  Get,
  Delete,
  UseGuards,
  Logger,
  Parse<PERSON><PERSON><PERSON>ip<PERSON>,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiConflictResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from 'src/modules/auth/guards/admin-role.guard';
import { PackageDependenciesService } from './package-dependencies.service';
import { CreatePackageDependencyDto } from './dto/create-package-dependency.dto';
import { PackageDependencyDto } from './dto/package-dependency.dto';

@ApiTags('Admin/Package Dependencies')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AdminRoleGuard)
@Controller('admin/packages/:packageId/dependencies')
export class PackageDependenciesController {
  private readonly logger = new Logger(PackageDependenciesController.name);

  constructor(
    private readonly packageDependenciesService: PackageDependenciesService,
  ) {}

  // POST /admin/packages/:packageId/dependencies
  @Post()
  @ApiOperation({ summary: 'Add a dependency relationship for a package' })
  @ApiResponse({
    status: 201,
    description: 'Package dependency created successfully.',
    type: PackageDependencyDto,
  })
  @ApiConflictResponse({
    description: 'Conflict (e.g., dependency already exists)',
  })
  @ApiBadRequestResponse({
    description: 'Bad Request (e.g., validation error or self-dependency)',
  })
  @ApiResponse({
    status: 404,
    description: 'Package or dependent package not found',
  })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'The ID of the package to add a dependency to',
  })
  async create(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Body() createDto: CreatePackageDependencyDto,
  ): Promise<PackageDependencyDto> {
    this.logger.log(
      `Received request to create dependency for package ${packageId}`,
    );
    // Basic check: Prevent self-dependency directly in controller for immediate feedback
    if (packageId === createDto.dependent_package_id) {
      throw new BadRequestException('A package cannot depend on itself.');
    }
    return await this.packageDependenciesService.create(packageId, createDto);
  }

  // GET /admin/packages/:packageId/dependencies
  @Get()
  @ApiOperation({ summary: 'List all dependencies for a specific package' })
  @ApiResponse({
    status: 200,
    description: 'List of package dependencies.',
    type: [PackageDependencyDto],
  })
  @ApiResponse({ status: 404, description: 'Package not found' })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'The ID of the package whose dependencies to list',
  })
  async findAllByPackage(
    @Param('packageId', ParseUUIDPipe) packageId: string,
  ): Promise<PackageDependencyDto[]> {
    this.logger.log(
      `Received request to find all dependencies for package ${packageId}`,
    );
    return await this.packageDependenciesService.findAllByPackage(packageId);
  }

  // DELETE /admin/packages/:packageId/dependencies/:dependencyId
  @Delete(':dependencyId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remove a specific dependency relationship' })
  @ApiResponse({
    status: 204,
    description: 'Package dependency deleted successfully.',
  })
  @ApiResponse({ status: 404, description: 'Dependency record not found' })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'The ID of the package owning the dependency',
  })
  @ApiParam({
    name: 'dependencyId',
    type: 'string',
    format: 'uuid',
    description: 'The ID of the dependency record to delete',
  })
  async remove(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('dependencyId', ParseUUIDPipe) dependencyId: string,
  ): Promise<void> {
    this.logger.log(
      `Received request to delete dependency ${dependencyId} for package ${packageId}`,
    );
    // Service handles the actual deletion and checks if packageId matches the record
    await this.packageDependenciesService.remove(dependencyId);
  }
}
