/**
 * React Context definition for calculation detail page
 * Separated to satisfy React Fast Refresh requirements
 */
import { createContext } from "react";
import {
  CalculationDetailState,
  CalculationDetailActions,
} from "../types/calculationState";

/**
 * Context value interface combining state and actions
 */
export interface CalculationContextValue {
  calculationId: string;
  state: CalculationDetailState;
  actions: CalculationDetailActions;
}

/**
 * Context for calculation detail page
 */
export const CalculationContext = createContext<CalculationContextValue | null>(null);
