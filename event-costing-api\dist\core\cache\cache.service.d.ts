import { Cache } from 'cache-manager';
export interface CacheInvalidationEvent {
    domain: string;
    id: string;
    action: 'create' | 'update' | 'delete';
    relatedKeys?: string[];
}
export interface CacheMetrics {
    hits: number;
    misses: number;
    sets: number;
    deletes: number;
    errors: number;
    lastReset: Date;
}
export declare class CacheService {
    private cacheManager;
    private readonly logger;
    private metrics;
    constructor(cacheManager: Cache);
    get<T>(key: string): Promise<T | null>;
    set(key: string, value: any, ttl?: number): Promise<void>;
    delete(key: string): Promise<void>;
    clear(): Promise<void>;
    getOrSet<T>(key: string, factory: () => Promise<T>, ttl?: number): Promise<T>;
    invalidateWithRelationships(domain: string, id: string, action: 'create' | 'update' | 'delete', relatedKeys?: string[]): Promise<void>;
    deleteByPattern(pattern: string): Promise<void>;
    private getKeysByPattern;
    warmCache(warmingConfig: Array<{
        key: string;
        factory: () => Promise<any>;
        ttl?: number;
        priority?: number;
    }>): Promise<void>;
    getMetrics(): CacheMetrics;
    resetMetrics(): void;
    getHealthStatus(): {
        status: 'healthy' | 'degraded' | 'unhealthy';
        hitRate: number;
        errorRate: number;
        metrics: CacheMetrics;
    };
}
