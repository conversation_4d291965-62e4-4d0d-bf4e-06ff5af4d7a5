/**
 * Configuration for the external API client
 *
 * In a production environment, these values would be loaded from environment variables.
 * For now, we'll use hardcoded values that can be easily updated.
 */

// Base URL for the external API
export const API_CONFIG = {
  // Use environment variable if available, otherwise use the production URL
  BASE_URL: import.meta.env.VITE_EVENT_COSTING_API_URL || 'http://localhost:5000',

  // Default timeout for API requests in milliseconds
  TIMEOUT: 30000,

  // Default headers for API requests
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
  },
};

// Note: API_ENDPOINTS has been moved to endpoints.ts for better organization
