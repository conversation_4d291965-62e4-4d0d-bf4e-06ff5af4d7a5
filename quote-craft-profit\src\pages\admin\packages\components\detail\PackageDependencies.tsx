import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Pencil, Trash2, AlertCircle } from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { showSuccess, showError } from "@/lib/notifications";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
  getPackageDependencies,
  deletePackageDependency,
} from "@/services/admin/packages";
import {
  PackageDependencyDisplay,
  getDependencyTypeLabel,
  getDependencyTypeColorClass,
  DependencyType,
} from "../../types/packageDependencies";
import { PackageDependencyForm } from "../../components/form/PackageDependencyForm";

interface PackageDependenciesProps {
  packageId: string;
}

export const PackageDependencies: React.FC<PackageDependenciesProps> = ({
  packageId,
}) => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedDependencyId, setSelectedDependencyId] = useState<
    string | null
  >(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [dependencyToDelete, setDependencyToDelete] = useState<string | null>(
    null
  );

  const {
    data: dependencies = [],
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["packageDependencies", packageId],
    queryFn: () => getPackageDependencies(packageId),
    enabled: !!packageId,
    meta: {
      onError: () => {
        showError("Failed to load package dependencies");
      },
    },
  });

  const handleAddDependency = () => {
    setSelectedDependencyId(null);
    setIsFormOpen(true);
  };

  const handleEditDependency = (dependencyId: string) => {
    setSelectedDependencyId(dependencyId);
    setIsFormOpen(true);
  };

  const handleDeleteDependency = (dependencyId: string) => {
    setDependencyToDelete(dependencyId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!dependencyToDelete) return;

    try {
      await deletePackageDependency(packageId, dependencyToDelete);
      showSuccess("Package dependency deleted successfully");
      refetch();
    } catch (error) {
      showError("Failed to delete package dependency");
      console.error(error);
    } finally {
      setIsDeleteDialogOpen(false);
      setDependencyToDelete(null);
    }
  };

  const handleFormClose = (shouldRefresh?: boolean) => {
    setIsFormOpen(false);
    setSelectedDependencyId(null);
    if (shouldRefresh) {
      refetch();
    }
  };

  // Find the selected dependency for editing
  const selectedDependency = selectedDependencyId
    ? dependencies.find((dependency) => dependency.id === selectedDependencyId)
    : null;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Package Dependencies</CardTitle>
          <CardDescription>
            Define requirements or conflicts with other packages
          </CardDescription>
        </div>
        <Button
          onClick={handleAddDependency}
          className="flex items-center"
          disabled={!packageId}
        >
          <Plus className="w-4 h-4 mr-2" /> Add Dependency
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-4">Loading dependencies...</div>
        ) : isError ? (
          <div className="text-center py-4 text-red-500">
            <AlertCircle className="w-8 h-8 mx-auto mb-2" />
            <p>Failed to load package dependencies. Please try again.</p>
          </div>
        ) : dependencies.length === 0 ? (
          <div className="text-center py-8 border rounded-lg">
            No dependencies defined for this package yet. Click "Add Dependency"
            to create one.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted">
                  <th className="text-left p-2">Type</th>
                  <th className="text-left p-2">Package</th>
                  <th className="text-left p-2">Description</th>
                  <th className="text-right p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {dependencies.map((dependency) => (
                  <tr
                    key={dependency.id}
                    className="border-b hover:bg-muted/50"
                  >
                    <td className="p-2">
                      <Badge
                        variant="outline"
                        className={getDependencyTypeColorClass(
                          dependency.dependency_type as DependencyType
                        )}
                      >
                        {getDependencyTypeLabel(
                          dependency.dependency_type as DependencyType
                        )}
                      </Badge>
                    </td>
                    <td className="p-2 font-medium">
                      {dependency.dependent_package?.name || "Unknown Package"}
                    </td>
                    <td className="p-2">{dependency.description || "-"}</td>
                    <td className="p-2 text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditDependency(dependency.id)}
                        >
                          <Pencil className="h-4 w-4 mr-1" /> Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteDependency(dependency.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-1" /> Delete
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Package Dependency Form Dialog */}
        {isFormOpen && packageId && (
          <PackageDependencyForm
            isOpen={isFormOpen}
            onClose={handleFormClose}
            packageId={packageId}
            dependencyId={selectedDependencyId}
            initialData={
              selectedDependency
                ? {
                    dependent_package_id:
                      selectedDependency.dependent_package_id,
                    dependency_type:
                      selectedDependency.dependency_type as DependencyType,
                    description: selectedDependency.description || "",
                  }
                : undefined
            }
          />
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete this package dependency. This
                action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-destructive text-destructive-foreground"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
};
