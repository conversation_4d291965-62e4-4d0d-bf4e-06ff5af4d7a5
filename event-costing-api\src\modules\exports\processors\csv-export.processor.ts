import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, InternalServerErrorException } from '@nestjs/common';
import { Job } from 'bullmq';
import { ExportsService } from '../exports.service';
import { ExportStorageService } from '../services/export-storage.service';
import { ExportGenerationService } from '../services/export-generation.service';
import { CalculationsService } from '../../calculations/calculations.service';
import { ExportStatus } from '../enums/export-status.enum';
import { ExportFormat } from '../enums/export-format.enum';

export interface CsvExportJobData {
  exportHistoryId: string;
  calculationId: string;
  userId: string;
}

@Processor('csv-exports')
export class CsvExportProcessor extends WorkerHost {
  private readonly logger = new Logger(CsvExportProcessor.name);

  constructor(
    private readonly exportsService: ExportsService,
    private readonly storageService: ExportStorageService,
    private readonly generationService: ExportGenerationService,
    private readonly calculationsService: CalculationsService,
  ) {
    super();
  }

  async process(job: Job<CsvExportJobData>): Promise<void> {
    const { exportHistoryId, calculationId, userId } = job.data;

    this.logger.log(
      `[CSV] Processing job ${job.id} for export history ${exportHistoryId}`,
    );
    this.logger.log(`[CSV] Calculation: ${calculationId}, User: ${userId}`);

    let generatedFileName: string = '';
    let storagePath: string | undefined = undefined;

    try {
      // 1. Update status to PROCESSING
      this.logger.log(
        `[CSV] Updating status to PROCESSING for export ${exportHistoryId}`,
      );
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.PROCESSING,
      );

      // 2. Fetch calculation data
      this.logger.log(`[CSV] Fetching calculation data for ${calculationId}`);
      const calculationData =
        await this.calculationsService.findCalculationForExport(
          calculationId,
          userId,
        );

      if (!calculationData) {
        throw new InternalServerErrorException(
          `[CSV] Calculation data not found for ID: ${calculationId}`,
        );
      }
      this.logger.log(
        `[CSV] Calculation data fetched successfully for ${calculationId}`,
      );

      // 3. Transform data
      this.logger.log(
        `[CSV] Transforming calculation data for ${calculationId}`,
      );
      const transformedData =
        this.generationService.transformCalculationData(calculationData);
      this.logger.log(
        `[CSV] Data transformation completed for ${calculationId}`,
      );

      // 4. Generate CSV file
      this.logger.log(`[CSV] Generating CSV buffer for ${calculationId}`);
      const sanitizedCalcName = calculationData.name
        .replace(/[^a-z0-9]/gi, '_')
        .toLowerCase();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      generatedFileName = `${sanitizedCalcName}_${timestamp}.csv`;

      const fileBuffer =
        await this.generationService.generateCsvBuffer(transformedData);

      if (!fileBuffer) {
        throw new InternalServerErrorException(
          '[CSV] File buffer not generated.',
        );
      }
      this.logger.log(
        `[CSV] CSV buffer generated successfully, size: ${fileBuffer.length} bytes`,
      );

      // 5. Upload file to storage
      this.logger.log(`[CSV] Uploading file to storage: ${generatedFileName}`);
      storagePath = await this.storageService.uploadExportFile(
        userId,
        generatedFileName,
        fileBuffer,
        'text/csv',
      );
      this.logger.log(`[CSV] File uploaded successfully to: ${storagePath}`);

      // 6. Update status to COMPLETED
      this.logger.log(
        `[CSV] Updating status to COMPLETED for export ${exportHistoryId}`,
      );
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.COMPLETED,
        {
          storagePath: storagePath,
          fileName: generatedFileName,
          fileSize: fileBuffer.length,
          mimeType: 'text/csv',
        },
      );

      this.logger.log(
        `[CSV] Job ${job.id} completed successfully for export history ${exportHistoryId}`,
      );
    } catch (error: unknown) {
      this.logger.error(
        `[CSV] Job ${job.id} failed for export history ${exportHistoryId}: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );

      // Update status to FAILED
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.FAILED,
        {
          error: error instanceof Error ? error.message : String(error),
          fileName: generatedFileName || undefined,
          storagePath: storagePath || undefined,
        },
      );

      throw error; // Re-throw to let BullMQ handle retries/failure state
    }
  }
}
