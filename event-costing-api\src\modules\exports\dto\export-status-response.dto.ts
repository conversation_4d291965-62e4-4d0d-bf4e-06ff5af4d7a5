import { ApiProperty } from '@nestjs/swagger';
import {
  IsUUID,
  IsString,
  IsOptional,
  IsDate,
  IsEnum,
  IsUrl,
} from 'class-validator';
import { ExportStatus } from './../enums/export-status.enum';
import { ExportFormat } from '../enums/export-format.enum';

// Define possible export statuses
// export enum ExportStatus {
//   PENDING = 'PENDING',
//   PROCESSING = 'PROCESSING',
//   COMPLETED = 'COMPLETED',
//   FAILED = 'FAILED',
// }

export class ExportStatusResponseDto {
  @ApiProperty({
    description: 'The unique identifier of the export record.',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsUUID()
  exportId: string;

  @ApiProperty({
    description: 'The current status of the export job.',
    enum: ExportStatus,
    example: ExportStatus.COMPLETED,
  })
  @IsEnum(ExportStatus)
  status: ExportStatus;

  @ApiProperty({
    description: 'The export format type.',
    enum: ExportFormat,
    example: ExportFormat.PDF,
  })
  @IsEnum(ExportFormat)
  format: ExportFormat;

  @ApiProperty({
    description:
      'A message providing more details about the status (e.g., error message if failed).',
    example: 'Export completed successfully.',
    required: false,
  })
  @IsString()
  @IsOptional()
  message?: string; // General message or error details

  @ApiProperty({
    description: 'The timestamp when the export request was created.',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'The timestamp when the export job last changed status.',
    required: false,
  })
  @IsDate()
  @IsOptional()
  updatedAt?: Date;

  @ApiProperty({
    description:
      'The name of the generated file (available when status is COMPLETED).',
    example: 'export_calculation_name_2023-10-27T10:30:00Z.xlsx',
    required: false,
  })
  @IsString()
  @IsOptional()
  fileName?: string;

  @ApiProperty({
    description:
      'A temporary signed URL to download the generated file (available when status is COMPLETED).',
    example:
      'https://your-supabase-url/storage/v1/object/sign/exports/user-id/file.xlsx?token=...',
    required: false,
  })
  @IsUrl()
  @IsOptional()
  downloadUrl?: string; // To be populated if status is COMPLETED

  @ApiProperty({
    description: 'Error message if the export failed',
    example: 'Failed to connect to storage.',
    required: false,
  })
  @IsString()
  @IsOptional()
  errorMessage?: string;

  @ApiProperty({
    description: 'The timestamp when the export job was completed.',
    required: false,
  })
  @IsDate()
  @IsOptional()
  completedAt?: Date;
}

