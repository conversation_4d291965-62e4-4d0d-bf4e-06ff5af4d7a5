import { Package, PackageFormValues, PackageSaveData } from '../types/package';

/**
 * Convert package data to form values
 * @param packageData - Package data from API
 * @returns Form values for package form
 */
export const packageToFormValues = (packageData: Package | null): PackageFormValues => {
  if (!packageData) {
    return {
      name: '',
      description: '',
      categoryId: '',
      divisionId: '',
      cityIds: [],
      venueIds: [],
      enableVenues: false,
      quantityBasis: 'PER_EVENT',
      isActive: true,
      price: '',
      unitBaseCost: '',
      currencyId: '',
    };
  }

  return {
    name: packageData.name,
    description: packageData.description || '',
    categoryId: packageData.categoryId || '',
    divisionId: packageData.divisionId || '',
    cityIds: packageData.cityIds || [],
    venueIds: packageData.venueIds || [],
    enableVenues: (packageData.venueIds || []).length > 0,
    quantityBasis: packageData.quantityBasis,
    isActive: !packageData.isDeleted,
    price: packageData.price || '',
    unitBaseCost: packageData.unitBaseCost || '',
    currencyId: '', // This should be populated from the API
  };
};

/**
 * Convert form values to package save data
 * @param values - Form values from package form
 * @param packageId - Package ID (optional, for updates)
 * @returns Package save data for API
 */
export const formValuesToPackageSaveData = (
  values: PackageFormValues,
  packageId?: string
): PackageSaveData => {
  return {
    id: packageId,
    name: values.name,
    description: values.description,
    categoryId: values.categoryId || undefined,
    divisionId: values.divisionId || undefined,
    cityIds: values.cityIds || [],
    venueIds: values.enableVenues ? values.venueIds || [] : [],
    enableVenues: values.enableVenues,
    quantityBasis: values.quantityBasis,
    isDeleted: !values.isActive,
    price: values.price ? parseFloat(values.price) : undefined,
    unitBaseCost: values.unitBaseCost ? parseFloat(values.unitBaseCost) : undefined,
    currencyId: values.currencyId,
  };
};

/**
 * Format currency value
 * @param value - Numeric value
 * @param currencySymbol - Currency symbol
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number | string | undefined,
  currencySymbol = 'Rp'
): string => {
  if (value === undefined || value === null || value === '') {
    return '-';
  }

  const numericValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numericValue)) {
    return '-';
  }

  // Convert IDR to Rp for display
  const displaySymbol = currencySymbol === 'IDR' ? 'Rp' : currencySymbol;

  return `${displaySymbol} ${numericValue.toLocaleString('id-ID')}`;
};

/**
 * Get quantity basis label
 * @param basis - Quantity basis enum value
 * @returns Human-readable label
 */
export const getQuantityBasisLabel = (basis: string): string => {
  switch (basis) {
    case 'PER_EVENT':
      return 'Per Event';
    case 'PER_DAY':
      return 'Per Day';
    case 'PER_ATTENDEE':
      return 'Per Attendee';
    case 'PER_ITEM':
      return 'Per Item';
    case 'PER_ITEM_PER_DAY':
      return 'Per Item Per Day';
    case 'PER_ATTENDEE_PER_DAY':
      return 'Per Attendee Per Day';
    default:
      return basis;
  }
};
