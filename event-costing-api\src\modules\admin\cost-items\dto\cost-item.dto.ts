import { ApiProperty } from '@nestjs/swagger';

export class CostItemDto {
  @ApiProperty({
    description: 'Unique identifier for the cost item',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({ description: 'Unique code for the cost item' })
  item_code: string;

  @ApiProperty({ description: 'Name of the cost item' })
  name: string;

  @ApiProperty({ description: 'Detailed description', required: false })
  description?: string;

  @ApiProperty({ description: 'Unit of measure' })
  unit_of_measure: string;

  @ApiProperty({ description: 'Default price/cost per unit' })
  default_price: number;

  @ApiProperty({
    description: 'ID of the currency for the default price',
    format: 'uuid',
  })
  currency_id: string;

  @ApiProperty({
    description: 'ID of the category this item belongs to',
    format: 'uuid',
    required: false,
  })
  category_id?: string;

  @ApiProperty({
    description: 'ID of the default supplier',
    format: 'uuid',
    required: false,
  })
  supplier_id?: string;

  @ApiProperty({ description: 'Whether the cost item is active' })
  is_active: boolean;

  @ApiProperty({ description: 'Timestamp of creation' })
  created_at: Date;

  @ApiProperty({ description: 'Timestamp of last update' })
  updated_at: Date;

  @ApiProperty({
    description: 'Flag indicating soft deletion',
    required: false,
  })
  is_deleted?: boolean;

  @ApiProperty({
    description: 'Timestamp of soft deletion',
    required: false,
    nullable: true,
  })
  deleted_at?: Date | null;

  // TODO: Potentially add related entity DTOs if needed (e.g., currency, category)
}
