## Templates (`/templates`)

### 1. List User Accessible Templates

- **Method:** `GET`
- **URL:** `/templates`
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Query Parameters (Optional - from `ListTemplatesDto`):**
  - `search`: string (text search on name/description)
  - `eventType`: string
  - `cityId`: UUID
  - `categoryId`: UUID
  - `dateStart`: YYYY-MM-DD
  - `dateEnd`: YYYY-MM-DD
  - `sortBy`: (default: `created_at`)
  - `sortOrder`: `asc` | `desc` (default: `desc`)
  - `limit`: Number (default: 20)
  - `offset`: Number (default: 0)
- **Description:** Retrieves a paginated list of templates accessible to the user (own private + public templates).
- **Success Response (200 OK):**
  ```json
  {
    "data": [
      {
        "id": "template-uuid-1",
        "name": "Standard Wedding Template",
        "description": "Base template for weddings",
        "event_type": "Wedding",
        "city_id": null,
        "attendees": 100,
        "template_start_date": null,
        "template_end_date": null,
        "category_id": "category-uuid-weddings",
        "created_at": "timestamp",
        "updated_at": "timestamp",
        "created_by": "user-uuid-admin",
        "is_public": true
      }
      // ... other templates
    ],
    "count": 15 // Total accessible templates matching query
  }
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (400 Bad Request):** If query parameter validation fails.
- **Error Response (500 Internal Server Error):** If database query/RPC fails.

### 2. Create Template From Calculation (Admin)

- **Method:** `POST`
- **URL:** `/templates/from-calculation` (Note: May move to `/admin/templates/from-calculation` later)
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Body:** `raw (JSON)`
  ```json
  {
    "calculationId": "source-calculation-uuid",
    "name": "New Template from Calc XYZ",
    "description": "Template created based on the successful Calculation XYZ event."
    // Add other optional fields from CreateTemplateFromCalculationDto if needed
  }
  ```
- **Description:** Creates a new private template based on the package/option structure of an existing calculation. Requires admin privileges.
- **Success Response (201 Created):**
  ```json
  // Returns TemplateSummaryDto of the newly created template
  {
    "id": "new-template-uuid",
    "name": "New Template from Calc XYZ",
    "description": "Template created based on the successful Calculation XYZ event.",
    "event_type": null, // Copied from calculation or null
    "city_id": null, // Copied from calculation or null
    "attendees": null, // Copied from calculation or null
    "template_start_date": null,
    "template_end_date": null,
    "category_id": null, // Copied from calculation or null
    "created_at": "timestamp",
    "updated_at": "timestamp",
    "created_by": "admin-user-uuid",
    "is_public": false
  }
  ```
- **Error Response (401 Unauthorized):** If token is missing, invalid, or doesn't belong to an admin user (when admin check is implemented).
- **Error Response (403 Forbidden):** If the user is authenticated but not an admin (when admin check is implemented).
- **Error Response (400 Bad Request):** If validation fails (e.g., missing `calculationId` or `name`, invalid UUID).
- **Error Response (404 Not Found):** If the specified `calculationId` does not exist or has no standard line items.
- **Error Response (500 Internal Server Error):** If fetching data or creating the template fails.

---

## Admin - Template Management (`/admin/templates`)

**Note:** All endpoints in this section require an `Authorization: Bearer <ADMIN_USER_JWT>` header and admin role privileges.

### 1. Create Template From Calculation (Admin)

- **Method:** `POST`
- **URL:** `/admin/templates/from-calculation`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)`
  ```json
  {
    "calculationId": "source-calculation-uuid",
    "name": "New Template from Calc XYZ",
    "description": "Template created based on the successful Calculation XYZ event."
    // Add other optional fields from CreateTemplateFromCalculationDto if needed
  }
  ```
- **Description:** Creates a new private template based on the package/option structure of an existing calculation. Requires admin privileges.
- **Success Response (201 Created):** Returns `TemplateSummaryDto` of the newly created template.
- **Error Response (401 Unauthorized):** If token is missing, invalid.
- **Error Response (403 Forbidden):** If the user is authenticated but not an admin.
- **Error Response (400 Bad Request):** If validation fails (e.g., missing `calculationId` or `name`, invalid UUID).
- **Error Response (404 Not Found):** If the specified `calculationId` does not exist or has no standard line items.
- **Error Response (500 Internal Server Error):** If fetching data or creating the template fails.

### 2. List Templates (Admin)

- **Method:** `GET`
- **URL:** `/admin/templates`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Query Parameters (from `ListAdminTemplatesQueryDto`):**
  - `limit`, `offset`, `sortBy`, `sortOrder` (standard pagination/sorting)
  - `name`: String (filter by name, case-insensitive)
  - `categoryId`: UUID (String)
  - `cityId`: UUID (String)
  - `isPublic`: Boolean (String: "true" or "false")
  - `isDeleted`: Boolean (String: "true" or "false", default: false)
- **Example URL:** `/admin/templates?limit=10&sortBy=name&isPublic=true&isDeleted=false`
- **Description:** Retrieves a paginated list of templates, including deleted ones if requested. Allows filtering.
- **Success Response (200 OK):** Returns `PaginatedAdminTemplatesResponse` (includes `package_selections` in data items).
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (400 Bad Request):** Invalid query parameters.
- **Error Response (500 Internal Server Error):** DB error.

### 3. Get Template by ID (Admin)

- **Method:** `GET`
- **URL:** `/admin/templates/{id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Retrieves detailed information for a specific template, including the `package_selections` blueprint.
- **Success Response (200 OK):** Returns `AdminTemplateDetailDto`.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (400 Bad Request):** Invalid UUID format for `id`.
- **Error Response (404 Not Found):** Template not found.
- **Error Response (500 Internal Server Error):** DB error.

### 4. Update Template (Admin)

- **Method:** `PUT`
- **URL:** `/admin/templates/{id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Include fields to update from `UpdateTemplateDto`, all optional)
  ```json
  {
    "name": "Updated Template Name",
    "is_public": true,
    "package_selections": "[{\"package_id\": \"new-uuid\", \"option_ids\": []}]" // Example: JSON string
  }
  ```
- **Description:** Updates the metadata and/or the `package_selections` blueprint of a template.
- **Success Response (200 OK):** Returns the complete updated `AdminTemplateDetailDto`.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (400 Bad Request):** Validation failed (invalid UUID, invalid DTO fields, invalid JSON string for `package_selections`).
- **Error Response (404 Not Found):** Template not found.
- **Error Response (500 Internal Server Error):** DB error.

### 5. Delete Template (Admin - Soft Delete)

- **Method:** `DELETE`
- **URL:** `/admin/templates/{id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Soft deletes a template by setting `is_deleted = true`.
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (400 Bad Request):** Invalid UUID format for `id`.
- **Error Response (404 Not Found):** Template not found or already deleted.
- **Error Response (500 Internal Server Error):** DB error.
