import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for admin user response
 */
export class AdminUserDto {
  @ApiProperty({
    description: 'User ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Username',
    example: 'johndoe',
    required: false,
  })
  username: string | null;

  @ApiProperty({
    description: 'Full name',
    example: '<PERSON>',
    required: false,
  })
  full_name: string | null;

  @ApiProperty({
    description: 'Role name',
    example: 'admin',
    required: false,
  })
  role_name: string | null;

  @ApiProperty({
    description: 'User creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: string;

  @ApiProperty({
    description: 'Last sign in date',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  last_sign_in_at: string | null;

  @ApiProperty({
    description: 'Profile picture URL',
    example: 'https://example.com/avatar.png',
    required: false,
  })
  profile_picture_url: string | null;

  @ApiProperty({
    description: 'User status',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'INACTIVE'],
  })
  status: 'ACTIVE' | 'INACTIVE';
}

