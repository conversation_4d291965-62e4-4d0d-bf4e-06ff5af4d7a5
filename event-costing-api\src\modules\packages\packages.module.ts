import { Modu<PERSON> } from '@nestjs/common';
import { PackagesService } from './packages.service';
import { PackagesController } from './packages.controller';
import { PackageCatalogService } from './services/package-catalog.service';
import { PackageCatalogController } from './controllers/package-catalog.controller';
import { AuthModule } from '../auth/auth.module'; // For protecting controller
import { CategoriesModule } from '../categories/categories.module'; // For catalog service
import { CitiesModule } from '../cities/cities.module'; // For catalog service

@Module({
  imports: [
    AuthModule, // Needed for JwtAuthGuard
    CategoriesModule, // For catalog service dependencies
    CitiesModule, // For catalog service dependencies
  ],
  controllers: [
    PackagesController,
    PackageCatalogController, // New consolidated catalog controller
  ],
  providers: [
    PackagesService,
    PackageCatalogService, // New consolidated catalog service
  ],
  exports: [
    PackagesService,
    PackageCatalogService, // Export for use in other modules
  ],
})
export class PackagesModule {}
