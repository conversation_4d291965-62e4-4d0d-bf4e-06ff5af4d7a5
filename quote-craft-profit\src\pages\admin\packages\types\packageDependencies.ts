import { z } from 'zod';

// Dependency types
export type DependencyType = 'REQUIRES' | 'CONFLICTS' | 'RECOMMENDS' | 'OPTIONAL';

// Interface for package dependency data from the database
export interface PackageDependency {
  id: string;
  package_id: string;
  dependent_package_id: string;
  dependency_type: DependencyType;
  description: string | null;
  created_at: string;
  updated_at: string;
}

// Interface for frontend display with formatted data
export interface PackageDependencyDisplay extends PackageDependency {
  dependent_package?: {
    name: string;
  };
}

// Data for creating or updating a package dependency
export interface SavePackageDependencyData {
  id?: string;
  package_id: string;
  dependent_package_id: string;
  dependency_type: DependencyType;
  description?: string | null;
}

// Form values for package dependency form
export interface PackageDependencyFormValues {
  dependent_package_id: string;
  dependency_type: DependencyType;
  description: string;
}

// Zod schema for package dependency validation
export const packageDependencySchema = z.object({
  dependent_package_id: z.string().min(1, 'Dependent package is required'),
  dependency_type: z.enum(['REQUIRES', 'CONFLICTS', 'RECOMMENDS', 'OPTIONAL'], {
    required_error: 'Dependency type is required',
  }),
  description: z.string().optional(),
});

// Helper function to get human-readable dependency type label
export const getDependencyTypeLabel = (type: DependencyType): string => {
  switch (type) {
    case 'REQUIRES':
      return 'Requires';
    case 'CONFLICTS':
      return 'Conflicts with';
    case 'RECOMMENDS':
      return 'Recommends';
    case 'OPTIONAL':
      return 'Optional';
    default:
      return type;
  }
};

// Helper function to get dependency type color class
export const getDependencyTypeColorClass = (type: DependencyType): string => {
  switch (type) {
    case 'REQUIRES':
      return 'bg-red-50 text-red-700 border-red-200';
    case 'CONFLICTS':
      return 'bg-amber-50 text-amber-700 border-amber-200';
    case 'RECOMMENDS':
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case 'OPTIONAL':
      return 'bg-green-50 text-green-700 border-green-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};
