import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/useAuth';
import LoadingSpinner from '@/components/ui/loading-spinner';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * AdminProtectedRoute Component
 *
 * A simplified admin route that checks if the user is authenticated and has admin role.
 * If not authenticated, redirects to the login page.
 * If authenticated but not admin, redirects to the dashboard.
 * If authenticated and admin, renders the children.
 */
const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { user, isAdmin, loading, isRoleLoading, triggerRefreshSession } = useAuth();
  const [showRoleLoading, setShowRoleLoading] = useState(false);

  // Add debugging logs to track component rendering and auth state
  console.log(`[${new Date().toISOString()}] AdminProtectedRoute rendering with:`, {
    has_user: !!user,
    user_id: user?.id,
    user_email: user?.email,
    isAdmin,
    loading,
    isRoleLoading,
    showRoleLoading,
  });

  // Show role loading spinner after a delay to avoid flashing
  useEffect(() => {
    console.log(
      `[${new Date().toISOString()}] AdminProtectedRoute: isRoleLoading changed to ${isRoleLoading}`,
    );
    let timer: NodeJS.Timeout;
    if (isRoleLoading) {
      console.log(
        `[${new Date().toISOString()}] AdminProtectedRoute: Setting timer for role loading spinner`,
      );
      timer = setTimeout(() => {
        console.log(
          `[${new Date().toISOString()}] AdminProtectedRoute: Timer fired, showing role loading spinner`,
        );
        setShowRoleLoading(true);
      }, 500); // Show loading spinner after 500ms of role loading
    } else {
      console.log(
        `[${new Date().toISOString()}] AdminProtectedRoute: Hiding role loading spinner`,
      );
      setShowRoleLoading(false);
    }
    return () => {
      if (timer) {
        console.log(
          `[${new Date().toISOString()}] AdminProtectedRoute: Clearing role loading timer`,
        );
        clearTimeout(timer);
      }
    };
  }, [isRoleLoading]);

  // Show loading state while checking authentication
  if (loading) {
    console.log(
      `[${new Date().toISOString()}] AdminProtectedRoute: Showing auth loading spinner (loading=true)`,
    );
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <LoadingSpinner text='Verifying authentication...' size={48} />
      </div>
    );
  }

  // Redirect to auth page if user is not authenticated
  if (!user) {
    console.log(
      `[${new Date().toISOString()}] AdminProtectedRoute: Redirecting to /auth (no user)`,
    );
    return <Navigate to='/auth' replace />;
  }

  // Show loading state while checking role, but only if it's taking a while
  if (isRoleLoading && showRoleLoading) {
    console.log(
      `[${new Date().toISOString()}] AdminProtectedRoute: Showing role loading spinner (isRoleLoading=true, showRoleLoading=true)`,
    );
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <LoadingSpinner text='Verifying admin access...' size={48} />
      </div>
    );
  }

  // Redirect to dashboard if user is not an admin
  if (!isAdmin) {
    console.log(
      `[${new Date().toISOString()}] AdminProtectedRoute: Redirecting to / (user not admin)`,
    );
    return <Navigate to='/' replace />;
  }

  // Render children if user is authenticated and has admin role
  console.log(
    `[${new Date().toISOString()}] AdminProtectedRoute: Rendering children (user is admin)`,
  );
  return <>{children}</>;
};

export default AdminProtectedRoute;
