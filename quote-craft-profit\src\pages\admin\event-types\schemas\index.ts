import { z } from "zod";

export const eventTypeFormSchema = z.object({
  name: z
    .string()
    .min(1, "Event type name is required")
    .max(100, "Event type name must be less than 100 characters"),
  code: z
    .string()
    .min(1, "Event type code is required")
    .max(20, "Event type code must be less than 20 characters")
    .regex(/^[A-Z0-9_]+$/, "Code must contain only uppercase letters, numbers, and underscores"),
  description: z
    .string()
    .max(500, "Description must be less than 500 characters")
    .optional(),
  icon: z
    .string()
    .max(50, "Icon name must be less than 50 characters")
    .optional(),
  color: z
    .string()
    .min(1, "Color is required")
    .regex(/^[a-z]+$/, "Color must be a valid color name (lowercase letters only)"),
  display_order: z
    .number()
    .int("Display order must be a whole number")
    .min(0, "Display order must be 0 or greater")
    .optional(),
});

export type EventTypeFormValues = z.infer<typeof eventTypeFormSchema>;

// Default values for the form
export const defaultEventTypeFormValues: Partial<EventTypeFormValues> = {
  name: "",
  code: "",
  description: "",
  icon: "",
  color: "blue",
  display_order: 0,
};
