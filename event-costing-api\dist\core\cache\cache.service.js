"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const common_1 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
let CacheService = CacheService_1 = class CacheService {
    cacheManager;
    logger = new common_1.Logger(CacheService_1.name);
    metrics = {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        errors: 0,
        lastReset: new Date(),
    };
    constructor(cacheManager) {
        this.cacheManager = cacheManager;
    }
    async get(key) {
        try {
            const value = await this.cacheManager.get(key);
            if (value !== null && value !== undefined) {
                this.metrics.hits++;
                this.logger.debug(`Cache hit for key: ${key}`);
            }
            else {
                this.metrics.misses++;
                this.logger.debug(`Cache miss for key: ${key}`);
            }
            return value;
        }
        catch (error) {
            this.metrics.errors++;
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(`Error getting cache key ${key}: ${errorMessage}`, errorStack);
            return null;
        }
    }
    async set(key, value, ttl) {
        try {
            await this.cacheManager.set(key, value, ttl ? ttl : undefined);
            this.metrics.sets++;
            this.logger.verbose(`Cache set for key: ${key}${ttl ? ` with TTL: ${ttl}s` : ''}`);
        }
        catch (error) {
            this.metrics.errors++;
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(`Error setting cache key ${key}: ${errorMessage}`, errorStack);
        }
    }
    async delete(key) {
        try {
            await this.cacheManager.del(key);
            this.metrics.deletes++;
            this.logger.verbose(`Cache deleted for key: ${key}`);
        }
        catch (error) {
            this.metrics.errors++;
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(`Error deleting cache key ${key}: ${errorMessage}`, errorStack);
        }
    }
    async clear() {
        try {
            await this.cacheManager.clear();
            this.logger.log('Cache cleared');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(`Error clearing cache: ${errorMessage}`, errorStack);
        }
    }
    async getOrSet(key, factory, ttl) {
        const cachedValue = await this.get(key);
        if (cachedValue !== null && cachedValue !== undefined) {
            return cachedValue;
        }
        this.logger.debug(`Cache miss for key: ${key}, fetching data...`);
        const value = await factory();
        await this.set(key, value, ttl);
        return value;
    }
    async invalidateWithRelationships(domain, id, action, relatedKeys = []) {
        this.logger.log(`Invalidating cache for ${domain}:${id} (${action}) with ${relatedKeys.length} related keys`);
        const keysToDelete = [
            `${domain}:${id}`,
            `${domain}:all`,
            `${domain}:list:*`,
            ...relatedKeys,
        ];
        for (const keyPattern of keysToDelete) {
            if (keyPattern.includes('*')) {
                await this.deleteByPattern(keyPattern);
            }
            else {
                await this.delete(keyPattern);
            }
        }
        this.logger.debug(`Cache invalidation completed for ${domain}:${id} (${action}) with ${relatedKeys.length} related keys`);
    }
    async deleteByPattern(pattern) {
        try {
            const keys = this.getKeysByPattern(pattern);
            for (const key of keys) {
                await this.delete(key);
            }
            this.logger.verbose(`Deleted ${keys.length} keys matching pattern: ${pattern}`);
        }
        catch (error) {
            this.metrics.errors++;
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`Error deleting keys by pattern ${pattern}: ${errorMessage}`);
        }
    }
    getKeysByPattern(pattern) {
        try {
            this.logger.debug(`Pattern matching not implemented for pattern: ${pattern}`);
            return [];
        }
        catch (error) {
            this.logger.error(`Error getting keys by pattern ${pattern}:`, error);
            return [];
        }
    }
    async warmCache(warmingConfig) {
        this.logger.log(`Warming cache with ${warmingConfig.length} entries`);
        const sortedConfig = warmingConfig.sort((a, b) => (b.priority || 0) - (a.priority || 0));
        const concurrencyLimit = 5;
        const chunks = [];
        for (let i = 0; i < sortedConfig.length; i += concurrencyLimit) {
            chunks.push(sortedConfig.slice(i, i + concurrencyLimit));
        }
        for (const chunk of chunks) {
            await Promise.allSettled(chunk.map(async (config) => {
                try {
                    const existingValue = await this.get(config.key);
                    if (existingValue === null || existingValue === undefined) {
                        const value = await config.factory();
                        await this.set(config.key, value, config.ttl);
                        this.logger.debug(`Cache warmed for key: ${config.key}`);
                    }
                }
                catch (error) {
                    this.logger.error(`Error warming cache for key ${config.key}:`, error);
                }
            }));
        }
        this.logger.log('Cache warming completed');
    }
    getMetrics() {
        return { ...this.metrics };
    }
    resetMetrics() {
        this.metrics = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            errors: 0,
            lastReset: new Date(),
        };
        this.logger.log('Cache metrics reset');
    }
    getHealthStatus() {
        const totalOperations = this.metrics.hits + this.metrics.misses;
        const hitRate = totalOperations > 0 ? (this.metrics.hits / totalOperations) * 100 : 0;
        const errorRate = totalOperations > 0 ? (this.metrics.errors / totalOperations) * 100 : 0;
        let status = 'healthy';
        if (errorRate > 10 || hitRate < 30) {
            status = 'unhealthy';
        }
        else if (errorRate > 5 || hitRate < 60) {
            status = 'degraded';
        }
        return {
            status,
            hitRate: Math.round(hitRate * 100) / 100,
            errorRate: Math.round(errorRate * 100) / 100,
            metrics: this.getMetrics(),
        };
    }
};
exports.CacheService = CacheService;
exports.CacheService = CacheService = CacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [Object])
], CacheService);
//# sourceMappingURL=cache.service.js.map