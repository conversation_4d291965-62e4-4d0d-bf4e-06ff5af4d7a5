# Quote Craft Profit - Developer Guide

This guide provides comprehensive instructions for developers working on the Quote Craft Profit project, covering setup, coding standards, development workflow, and deployment processes.

## Important :

- **API Error Handling**: Check backend API error handling first, then frontend handling (excludes: Auth, Calculations, Packages)
- **Authentication & Calculations**: Handled by direct Supabase integration in frontend; backend uses Supabase for API operations
- **Database Operations**: Use Supabase MCP or Augment tools for table operations and schema changes
- **Feature-Based Architecture**: Organize components under their respective feature folders in pages/ rather than generic components/
- **Service Organization**: Keep services in global src/services/ with subfolders for each service type
- **Code Verification**: Always verify file existence before creating new files to avoid duplicates and understand current structure
- **No Mock Data**: Never use mock data to create files - always confirm with user and work with existing code only

## Project Structure

Frontend: React 18, TypeScript 5, Vite (build tool), Tailwind CSS & shadcn/ui (styling & UI components), React Query (data fetching & server state), Axios (HTTP client), React Router (navigation), React Hook Form & Zod (form handling & validation), Supabase Client, Recharts (data visualization), Date-fns (date utilities), Lucide React (icons), Son<PERSON> (toast notifications), React Image Crop (image editing), Embla Carousel (carousels), Next Themes (theme management).

Backend: NestJS (framework), TypeScript 5, Express (via NestJS), Supabase (PostgreSQL database, Authentication, Storage), class-validator & class-transformer (DTO validation), JWT (JSON Web Tokens for auth), Winston (logging), exceljs & pdf-lib (file generation).
Development & Deployment Tools: ESLint (code linting), Prettier (code formatting), Git (version control). Deployment via Vercel (frontend) and Railway (backend).

### Integration Points

#### Frontend to Backend Communication

The frontend communicates with the backend through two main channels:

1. **Direct Supabase Integration**:

   - Authentication using Supabase Auth
   - Some database operations using the Supabase client

2. **External API Integration**:
   - RESTful API communication using a custom Axios-based client
   - The API client is configured to:
     - Add authentication tokens to requests
     - Handle error responses
     - Transform data for UI consumption
     - Provide logging for debugging

#### Authentication Flow

1. User authentication is handled by Supabase Auth
2. JWT tokens are used for secure communication
3. The frontend stores tokens in localStorage and the Supabase client
4. The backend validates tokens for protected endpoints

#### Database Access

1. The frontend uses the Supabase client for direct database operations
2. The backend uses Supabase's PostgreSQL database for data storage
3. Row-level security policies in Supabase ensure data access control
4. Complex database operations are handled through the backend API

### Why These Technologies Were Chosen

#### Frontend Choices

- **React**: Provides a component-based architecture for building complex UIs
- **TypeScript**: Adds static typing to prevent common errors and improve developer experience
- **Vite**: Offers faster development experience and optimized builds compared to Create React App
- **Tailwind CSS**: Enables rapid UI development with utility classes
- **shadcn/ui**: Provides accessible, customizable components based on Radix UI
- **React Query**: Simplifies data fetching and caching with a declarative API
- **React Hook Form**: Provides performant form handling with minimal re-renders

#### Backend Choices

- **NestJS**: Offers a structured, modular architecture for building scalable APIs
- **Supabase**: Provides a comprehensive backend solution with authentication, database, and storage
- **PostgreSQL**: Offers a powerful relational database with advanced features
- **JWT**: Enables stateless authentication for scalable API access
- **class-validator/transformer**: Provides robust validation and transformation for API requests/responses

### Technology Evaluation

#### Strengths

- Modern, maintainable tech stack with strong typing
- Excellent developer experience with hot reloading and type checking
- Scalable architecture for future growth
- Strong focus on performance and user experience
- Comprehensive testing capabilities
- Separation of concerns between frontend and backend

#### Considerations

- Learning curve for developers new to TypeScript, NestJS, or Supabase
- Dependency on third-party services (Supabase)
- Need for careful management of client-side state
- Coordination required between frontend and backend for API changes

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Git
- Supabase account (for database and authentication)

### Setting Up the Frontend

1. **Navigate to the frontend directory**:

   ```bash
   cd quote-craft-profit
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Set up environment variables**:

   - Copy `.env.example` to `.env`
   - Update the variables with your Supabase credentials and API URL:
     ```
     VITE_SUPABASE_URL=your_supabase_url
     VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
     VITE_EVENT_COSTING_API_URL=http://localhost:5000
     ```

4. **Start the development server**:
   ```bash
   npm run dev
   ```

### Setting Up the Backend

1. **Navigate to the backend directory**:

   ```bash
   cd event-costing-api
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Set up environment variables**:

   - Copy `.env.example` to `.env`
   - Update the variables with your Supabase credentials:
     ```
     PORT=5000
     JWT_SECRET=your_jwt_secret
     SUPABASE_URL=your_supabase_url
     SUPABASE_KEY=your_supabase_key
     SUPABASE_ANON_KEY=your_supabase_anon_key
     SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
     ```

4. **Start the development server**:
   ```bash
   npm run start:dev
   ```

### Project Structure Details

#### Frontend Structure (quote-craft-profit)

```
quote-craft-profit/
├── public/              # Static assets
└── src/
    ├── components/      # Shared UI components
    │   ├── ui/          # Base UI components from shadcn
    │   ├── layout/      # Layout components (Navbar, MainLayout, etc.)
    │   └── notifications/ # Notification components
    ├── contexts/        # React Context providers
    ├── data/            # Mock data and constants
    ├── hooks/           # Shared custom React hooks
    ├── integrations/    # Third-party integrations
    │   ├── supabase/    # Supabase client and types
    │   └── api/         # External API client and configuration
    ├── lib/             # Utility functions and helpers
    ├── pages/           # Feature-based page organization
    │   ├── admin/       # Admin features
    │   ├── auth/        # Authentication features
    │   ├── calculations/# Calculation features
    │   ├── clients/     # Client management features
    │   ├── dashboard/   # Dashboard features
    │   ├── events/      # Event management features
    │   ├── profile/     # User profile features
    │   ├── reports/     # Reporting features
    │   └── templates/   # Template features
    ├── services/        # Global API service functions
    │   ├── admin/       # Admin-specific services
    │   ├── calculations/# Calculation services
    │   └── shared/      # Cross-feature services
    ├── styles/          # Global styles
    ├── types/           # Shared TypeScript type definitions
    ├── App.tsx          # Main application component
    ├── main.tsx         # Application entry point
    └── vite-env.d.ts    # Vite environment type definitions
```

#### Backend Structure (event-costing-api)

```
src/
├── main.ts             # Application entry point
├── app.module.ts       # Root module
├── core/               # Core functionality
│   ├── filters/        # Global exception filters
│   ├── supabase/       # Supabase integration
│   └── database/       # Database configuration
├── modules/            # Feature modules
│   ├── auth/           # Authentication module
│   ├── users/          # User management module
│   ├── calculations/   # Calculation module
│   ├── templates/      # Template management module
│   ├── clients/        # Client management module
│   ├── packages/       # Package catalog module
│   ├── categories/     # Category management module
│   ├── cities/         # City management module
│   └── events/         # Event management module
└── shared/             # Shared DTOs and interfaces
```

## Coding Standards

### General Guidelines

1. **TypeScript**: Use TypeScript for all new code. Define proper interfaces and types.
2. **Component Structure**: Use functional components with hooks.
3. **File Naming**: Use PascalCase for component files and camelCase for utility files.
4. **Code Formatting**: Use ESLint and Prettier for consistent code formatting.
5. **Comments**: Add comments for complex logic and component props.

### Frontend Guidelines

#### Component Organization

- Each component should have a single responsibility
- Break down complex components into smaller, reusable ones
- Use composition over inheritance

#### Props and State Management

```tsx
// Define prop types using TypeScript interfaces
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
}

// Use destructuring for props and provide default values
const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  onClick,
}) => {
  // Component implementation
};
```

- Use `useState` for component-level state
- Use React Context for global state
- Use React Query for server state and data fetching

#### Styling Guidelines

- Use Tailwind utility classes for styling
- Use the `className` prop for styling components
- Use shadcn/ui components for consistent UI
- Customize components using Tailwind classes
- Avoid direct modification of component source files

#### Data Fetching and State Management

```tsx
// Use React Query for API calls
const { data, isLoading, isError } = useQuery({
  queryKey: ['packages', filters],
  queryFn: () => getAllPackages(filters),
  meta: {
    onError: () => {
      toast.error('Failed to load packages');
    },
  },
});

// Use the supabase client for database operations
export const getAllPackages = async (
  filters: PackageFilters = {},
): Promise<Package[]> => {
  let query = supabase.from('packages').select(`
      id,
      name,
      description,
      category_id,
      division_id,
      quantity_basis,
      is_deleted,
      categories (name),
      divisions (name)
    `);

  // Apply filters
  if (filters.search) {
    query = query.ilike('name', `%${filters.search}%`);
  }

  // Execute the query
  const { data, error } = await query.order('name');

  if (error) {
    console.error('Error fetching packages:', error);
    throw error;
  }

  // Transform the data
  return data.map((pkg) => ({
    // Mapping logic
  }));
};
```

#### External API Integration

```tsx
// Use the custom API client for external API calls
export const getTemplateById = async (id: string): Promise<Template> => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.TEMPLATES.GET_BY_ID(id));
    return response.data;
  } catch (error) {
    console.error('Error fetching template:', error);
    throw error;
  }
};
```

### Backend Guidelines

#### Module Organization

- Create a module for each domain entity
- Use the NestJS CLI to generate modules, controllers, and services
- Follow the repository pattern for data access

#### DTO and Validation

- Create DTOs for all API requests and responses
- Use class-validator decorators for validation
- Use class-transformer for serialization

```typescript
import { IsNotEmpty, IsString, IsOptional, IsNumber } from 'class-validator';

export class CreatePackageDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  categoryId?: string;

  @IsNotEmpty()
  @IsString()
  quantityBasis: string;

  @IsOptional()
  @IsNumber()
  price?: number;
}
```

#### Supabase Integration

- Use the `@supabase/supabase-js` client for all database operations
- Create a dedicated, injectable `SupabaseService` to manage the client instance
- Use the `SUPABASE_SERVICE_ROLE_KEY` for backend operations that need to bypass RLS policies
- For complex queries, use PostgreSQL functions and call them using `supabase.rpc()`

#### Error Handling

- Use NestJS exception filters for consistent error responses
- Provide meaningful error messages
- Log errors with appropriate context

## Development Workflow

### Core Development Principles

1. **Backend First Approach**:

   - Always implement backend API changes before frontend implementation
   - Ensure API endpoints are fully tested before beginning frontend work
   - Document API contracts (request/response formats) before implementation
   - Check existing backend modules to avoid duplicating functionality

2. **Feature Implementation Flow**:

   - Start with database schema changes if needed
   - Implement backend API endpoints and services
   - Create or update DTOs and validation
   - Test backend implementation thoroughly
   - Implement frontend components and services
   - Test frontend integration with backend

3. **Cross-Component Communication**:
   - Maintain clear documentation of API contracts
   - Use TypeScript interfaces to enforce type safety across boundaries
   - Implement proper error handling on both sides
   - Consider backward compatibility when making changes

### Performance Optimization

#### Frontend Optimization

- Use React.memo for expensive components
- Implement proper React Query caching strategies
- Use code splitting for large components
- Optimize images and assets

#### Backend Optimization

- Implement database indexing for frequently queried fields
- Use pagination for large data sets
- Implement caching for expensive operations
- Use proper error handling and logging

## Troubleshooting

### Common Issues

1. **Authentication Issues**:

   - Check Supabase credentials in environment variables
   - Verify JWT token configuration
   - Check browser console for authentication errors

2. **API Connection Issues**:

   - Verify API URL in environment variables
   - Check CORS configuration in the backend
   - Verify network connectivity

3. **Database Issues**:
   - Check Supabase connection
   - Verify database schema
   - Check for missing indexes
