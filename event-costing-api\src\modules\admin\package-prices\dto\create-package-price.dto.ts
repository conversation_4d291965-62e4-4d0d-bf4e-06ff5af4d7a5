import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsNumber, Min, IsOptional } from 'class-validator';

export class CreatePackagePriceDto {
  @ApiProperty({
    description: 'The currency ID for this price.',
    format: 'uuid',
    example: '685860b9-257f-41eb-b223-b3e1fad8f3b9',
  })
  @IsNotEmpty()
  @IsUUID()
  currency_id: string;

  @ApiProperty({
    description: 'The base unit price for the package in this currency.',
    example: 1500000.0,
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  price: number;

  @ApiProperty({
    description: 'The base unit cost for the package in this currency.',
    example: 750000.0,
    type: Number,
    default: 0,
  })
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  unit_base_cost: number;

  @ApiProperty({
    description: 'Optional description for this price point.',
    example: 'Standard price for IDR market.',
    required: false,
  })
  @IsOptional()
  description?: string;
}
