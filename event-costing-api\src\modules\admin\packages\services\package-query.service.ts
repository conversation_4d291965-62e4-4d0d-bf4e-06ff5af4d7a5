import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PackageDto } from '../dto/package.dto';
import { PackageListQueryDto } from '../dto/package-list-query.dto';
import { PaginatedResponseDto } from 'src/shared/dtos/paginated-response.dto';

@Injectable()
export class PackageQueryService {
  private readonly logger = new Logger(PackageQueryService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Find all packages with filtering and pagination
   * @param queryDto - Query parameters
   * @returns Paginated list of packages
   */
  async findAllPackages(
    queryDto: PackageListQueryDto,
  ): Promise<PaginatedResponseDto<PackageDto>> {
    this.logger.log(
      `Finding all packages with query: ${JSON.stringify(queryDto)}`,
    );
    const supabase = this.supabaseService.getClient();
    const { name, categoryId, isDeleted, sortBy, sortOrder } = queryDto;

    // Base query
    const queryBuilder = supabase
      .from('packages')
      .select('*', { count: 'exact' });

    // Get pagination params from DTO, providing defaults if undefined
    const limit = queryDto.limit ?? 20;
    const offset = queryDto.offset ?? 0;

    // Apply filtering
    queryBuilder.eq('is_deleted', isDeleted === 'true');

    if (name) {
      queryBuilder.ilike('name', `%${name}%`);
    }

    if (categoryId) {
      queryBuilder.eq('category_id', categoryId);
    }

    // Apply sorting
    const sortColumn = sortBy || 'name';
    const sortDirection = sortOrder || 'asc';
    queryBuilder.order(sortColumn, { ascending: sortDirection === 'asc' });

    // Apply pagination
    queryBuilder.range(offset, offset + limit - 1);

    this.logger.log(
      `Executing query for findAll with limit: ${limit}, offset: ${offset}, sort: ${sortColumn} ${sortDirection}`,
    );

    const { data, error, count } = await queryBuilder;

    if (error) {
      this.logger.error(
        `Error finding packages: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed to retrieve packages.');
    }

    return {
      data: data || [],
      count: count || 0,
      limit: limit,
      offset: offset,
    };
  }

  /**
   * Find a single package by ID
   * @param id - Package ID
   * @returns Package data
   */
  async findPackageById(id: string): Promise<PackageDto> {
    this.logger.log(`Fetching package with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('packages')
      .select('*')
      .eq('id', id)
      .maybeSingle();

    if (error) {
      this.logger.error(
        `Error fetching package with ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to fetch package: ${error.message}`,
      );
    }

    if (!data) {
      this.logger.warn(`Package with ID ${id} not found.`);
      throw new NotFoundException(`Package with ID ${id} not found.`);
    }

    return data as PackageDto;
  }

  /**
   * Get package IDs from a list of packages
   * @param packages - List of packages
   * @returns Array of package IDs
   */
  getPackageIds(packages: any[]): string[] {
    return packages?.map(pkg => pkg.id) || [];
  }

  /**
   * Fetch categories for packages
   * @param categoryIds - Array of category IDs
   * @returns Map of category ID to category name
   */
  async fetchCategoriesMap(categoryIds: string[]): Promise<Map<string, string>> {
    if (categoryIds.length === 0) return new Map();

    const supabase = this.supabaseService.getClient();
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('categories')
      .select('id, name')
      .in('id', categoryIds);

    if (categoriesError) {
      this.logger.error(
        `Error fetching categories: ${categoriesError.message}`,
      );
      return new Map();
    }

    return new Map(
      categoriesData?.map(category => [category.id, category.name]) || [],
    );
  }

  /**
   * Fetch divisions for packages
   * @param divisionIds - Array of division IDs
   * @returns Map of division ID to division name
   */
  async fetchDivisionsMap(divisionIds: string[]): Promise<Map<string, string>> {
    if (divisionIds.length === 0) return new Map();

    const supabase = this.supabaseService.getClient();
    const { data: divisionsData, error: divisionsError } = await supabase
      .from('divisions')
      .select('id, name')
      .in('id', divisionIds);

    if (divisionsError) {
      this.logger.error(
        `Error fetching divisions: ${divisionsError.message}`,
      );
      return new Map();
    }

    return new Map(
      divisionsData?.map(division => [division.id, division.name]) || [],
    );
  }

  /**
   * Fetch package prices
   * @param packageIds - Array of package IDs
   * @returns Map of package ID to price info
   */
  async fetchPackagePricesMap(packageIds: string[]): Promise<Map<string, any>> {
    if (packageIds.length === 0) return new Map();

    const supabase = this.supabaseService.getClient();
    const { data: pricesData, error: pricesError } = await supabase
      .from('package_prices')
      .select('package_id, price, unit_base_cost, currency_id')
      .in('package_id', packageIds);

    if (pricesError) {
      this.logger.error(
        `Error fetching package prices: ${pricesError.message}`,
      );
      return new Map();
    }

    if (!pricesData || pricesData.length === 0) {
      return new Map();
    }

    // Get currency IDs and fetch currencies
    const currencyIds = [
      ...new Set(
        pricesData
          .filter(price => price.currency_id)
          .map(price => price.currency_id),
      ),
    ];

    const currencyMap = await this.fetchCurrenciesMap(currencyIds);

    // Create price map
    const priceMap = new Map();
    pricesData.forEach(price => {
      priceMap.set(price.package_id, {
        price: price.price?.toString() || null,
        unitBaseCost: price.unit_base_cost?.toString() || null,
        currencySymbol: price.currency_id
          ? currencyMap.get(price.currency_id) || 'Rp'
          : 'Rp',
        hasPricing: true,
      });
    });

    return priceMap;
  }

  /**
   * Fetch currencies for prices
   * @param currencyIds - Array of currency IDs
   * @returns Map of currency ID to currency code
   */
  private async fetchCurrenciesMap(currencyIds: string[]): Promise<Map<string, string>> {
    if (currencyIds.length === 0) return new Map();

    const supabase = this.supabaseService.getClient();
    const { data: currenciesData, error: currenciesError } = await supabase
      .from('currencies')
      .select('id, code')
      .in('id', currencyIds);

    if (currenciesError) {
      this.logger.error(
        `Error fetching currencies: ${currenciesError.message}`,
      );
      return new Map();
    }

    return new Map(
      currenciesData?.map(currency => [currency.id, currency.code]) || [],
    );
  }

  /**
   * Fetch package cities
   * @param packageIds - Array of package IDs
   * @returns Map of package ID to city names array
   */
  async fetchPackageCitiesMap(packageIds: string[]): Promise<Map<string, string[]>> {
    if (packageIds.length === 0) return new Map();

    const supabase = this.supabaseService.getClient();
    const { data: packageCitiesData, error: packageCitiesError } = await supabase
      .from('package_cities')
      .select('package_id, city_id')
      .in('package_id', packageIds);

    if (packageCitiesError) {
      this.logger.error(
        `Error fetching package cities: ${packageCitiesError.message}`,
      );
      return new Map();
    }

    if (!packageCitiesData || packageCitiesData.length === 0) {
      return new Map();
    }

    // Get city IDs and fetch cities
    const cityIds = [
      ...new Set(
        packageCitiesData.filter(pc => pc.city_id).map(pc => pc.city_id),
      ),
    ];

    const cityMap = await this.fetchCitiesMap(cityIds);

    // Create package cities map
    const packageCityMap = new Map<string, string[]>();
    packageCitiesData.forEach(pc => {
      if (!packageCityMap.has(pc.package_id)) {
        packageCityMap.set(pc.package_id, []);
      }
      const cityName = cityMap.get(pc.city_id);
      if (cityName) {
        packageCityMap.get(pc.package_id)!.push(cityName);
      }
    });

    return packageCityMap;
  }

  /**
   * Fetch cities
   * @param cityIds - Array of city IDs
   * @returns Map of city ID to city name
   */
  private async fetchCitiesMap(cityIds: string[]): Promise<Map<string, string>> {
    if (cityIds.length === 0) return new Map();

    const supabase = this.supabaseService.getClient();
    const { data: citiesData, error: citiesError } = await supabase
      .from('cities')
      .select('id, name')
      .in('id', cityIds);

    if (citiesError) {
      this.logger.error(`Error fetching cities: ${citiesError.message}`);
      return new Map();
    }

    return new Map(
      citiesData?.map(city => [city.id, city.name]) || [],
    );
  }
}
