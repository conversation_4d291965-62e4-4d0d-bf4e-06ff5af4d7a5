import { Test, TestingModule } from '@nestjs/testing';
import { CategoriesService } from '../categories.service';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { CategoryDto } from '../dto/category.dto';
import { CategoryOrderItemDto } from '../dto/update-category-order.dto';
import {
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Logger } from '@nestjs/common';

// Mock the Logger to avoid console output during tests
jest.mock('@nestjs/common', () => {
  const original = jest.requireActual('@nestjs/common');
  return {
    ...original,
    Logger: jest.fn().mockImplementation(() => ({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
    })),
  };
});

describe('CategoriesService', () => {
  let service: CategoriesService;
  let supabaseService: SupabaseService;

  // Mock categories data
  const mockCategories: CategoryDto[] = [
    {
      id: '1',
      code: 'VENUE',
      name: 'Venue',
      description: 'Venue services',
      icon: 'building',
      display_order: 1,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
    },
    {
      id: '2',
      code: 'CATERING',
      name: 'Catering',
      description: 'Food and beverage services',
      icon: 'utensils',
      display_order: 2,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
    },
    {
      id: '3',
      code: 'DECOR',
      name: 'Decoration',
      description: 'Decoration services',
      icon: 'palette',
      display_order: 3,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
    },
  ];

  // Mock Supabase client
  const mockSupabaseClient = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    rpc: jest.fn(),
  };

  beforeEach(async () => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CategoriesService,
        {
          provide: SupabaseService,
          useValue: {
            getClient: jest.fn().mockReturnValue(mockSupabaseClient),
          },
        },
      ],
    }).compile();

    service = module.get<CategoriesService>(CategoriesService);
    supabaseService = module.get<SupabaseService>(SupabaseService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all categories ordered by display_order', async () => {
      // Mock the Supabase response
      mockSupabaseClient.select.mockReturnThis();
      mockSupabaseClient.order.mockReturnThis();
      mockSupabaseClient.from.mockReturnThis();
      mockSupabaseClient.from().select().order.mockResolvedValue({
        data: mockCategories,
        error: null,
      });

      const result = await service.findAll();

      expect(result).toEqual(mockCategories);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('categories');
      expect(mockSupabaseClient.select).toHaveBeenCalled();
      expect(mockSupabaseClient.order).toHaveBeenCalledWith('display_order', {
        ascending: true,
      });
    });

    it('should throw an error if Supabase returns an error', async () => {
      // Mock the Supabase response with an error
      mockSupabaseClient.from.mockReturnThis();
      mockSupabaseClient.select.mockReturnThis();
      mockSupabaseClient.order.mockResolvedValue({
        data: null,
        error: { message: 'Database error', stack: 'Error stack' },
      });

      await expect(service.findAll()).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('updateCategoryOrder', () => {
    const categoryOrderItems: CategoryOrderItemDto[] = [
      { id: '1', display_order: 3 },
      { id: '2', display_order: 1 },
      { id: '3', display_order: 2 },
    ];

    const reorderedCategories: CategoryDto[] = [
      {
        ...mockCategories[1],
        display_order: 1,
      },
      {
        ...mockCategories[2],
        display_order: 2,
      },
      {
        ...mockCategories[0],
        display_order: 3,
      },
    ];

    it('should update category order and return success response', async () => {
      // Mock the transaction methods
      mockSupabaseClient.rpc.mockImplementation(method => {
        if (method === 'begin_transaction' || method === 'commit_transaction') {
          return Promise.resolve({ error: null });
        }
        return Promise.resolve({ error: null });
      });

      // Mock the update method
      mockSupabaseClient.from.mockReturnThis();
      mockSupabaseClient.update.mockReturnThis();
      mockSupabaseClient.eq.mockResolvedValue({ error: null });

      // Mock the select method for fetching updated categories
      mockSupabaseClient.from.mockReturnThis();
      mockSupabaseClient.select.mockReturnThis();
      mockSupabaseClient.order.mockResolvedValue({
        data: reorderedCategories,
        error: null,
      });

      const result = await service.updateCategoryOrder(categoryOrderItems);

      expect(result).toEqual({
        success: true,
        message: 'Category order updated successfully',
        categories: reorderedCategories,
      });

      // Verify transaction was started
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('begin_transaction');

      // Verify updates were made for each category
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('categories');
      expect(mockSupabaseClient.update).toHaveBeenCalledTimes(
        categoryOrderItems.length,
      );
      expect(mockSupabaseClient.eq).toHaveBeenCalledTimes(
        categoryOrderItems.length,
      );

      // Verify transaction was committed
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('commit_transaction');

      // Verify updated categories were fetched
      expect(mockSupabaseClient.select).toHaveBeenCalled();
      expect(mockSupabaseClient.order).toHaveBeenCalledWith('display_order', {
        ascending: true,
      });
    });

    it('should rollback transaction and throw error if update fails', async () => {
      // Mock the transaction methods
      mockSupabaseClient.rpc.mockImplementation(method => {
        if (method === 'begin_transaction') {
          return Promise.resolve({ error: null });
        }
        if (method === 'rollback_transaction') {
          return Promise.resolve({ error: null });
        }
        return Promise.resolve({ error: null });
      });

      // Mock the update method to fail on the second category
      mockSupabaseClient.from.mockReturnThis();
      mockSupabaseClient.update.mockReturnThis();
      mockSupabaseClient.eq.mockImplementation((field, value) => {
        if (value === '2') {
          return Promise.resolve({
            error: { message: 'Update failed', stack: 'Error stack' },
          });
        }
        return Promise.resolve({ error: null });
      });

      await expect(
        service.updateCategoryOrder(categoryOrderItems),
      ).rejects.toThrow(InternalServerErrorException);

      // Verify transaction was started
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('begin_transaction');

      // Verify rollback was called
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
        'rollback_transaction',
      );

      // Verify commit was not called
      expect(mockSupabaseClient.rpc).not.toHaveBeenCalledWith(
        'commit_transaction',
      );
    });

    it('should throw error if transaction start fails', async () => {
      // Mock the transaction start to fail
      mockSupabaseClient.rpc.mockImplementation(method => {
        if (method === 'begin_transaction') {
          return Promise.resolve({
            error: {
              message: 'Transaction start failed',
              stack: 'Error stack',
            },
          });
        }
        return Promise.resolve({ error: null });
      });

      await expect(
        service.updateCategoryOrder(categoryOrderItems),
      ).rejects.toThrow(InternalServerErrorException);

      // Verify transaction was attempted to start
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('begin_transaction');

      // Verify no updates were attempted
      expect(mockSupabaseClient.update).not.toHaveBeenCalled();
    });

    it('should throw error if transaction commit fails', async () => {
      // Mock the transaction methods
      mockSupabaseClient.rpc.mockImplementation(method => {
        if (method === 'begin_transaction') {
          return Promise.resolve({ error: null });
        }
        if (method === 'commit_transaction') {
          return Promise.resolve({
            error: { message: 'Commit failed', stack: 'Error stack' },
          });
        }
        return Promise.resolve({ error: null });
      });

      // Mock the update method
      mockSupabaseClient.from.mockReturnThis();
      mockSupabaseClient.update.mockReturnThis();
      mockSupabaseClient.eq.mockResolvedValue({ error: null });

      await expect(
        service.updateCategoryOrder(categoryOrderItems),
      ).rejects.toThrow(InternalServerErrorException);

      // Verify transaction was started
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('begin_transaction');

      // Verify updates were attempted
      expect(mockSupabaseClient.update).toHaveBeenCalled();

      // Verify commit was attempted
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('commit_transaction');
    });
  });
});
