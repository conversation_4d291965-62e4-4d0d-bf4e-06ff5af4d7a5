import {
  Controller,
  Get,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiT<PERSON>s,
  ApiBearerAuth,
  ApiOkResponse,
  ApiResponse,
  ApiOperation,
  ApiQuery,
} from '@nestjs/swagger';
import { PackageCatalogService } from '../services/package-catalog.service';
import { PackageCatalogDto } from '../dto/package-catalog.dto';
import { PackageFiltersDto } from '../dto/package-filters.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';

/**
 * Controller for package catalog operations
 * Implements consolidated package browsing endpoints
 */
@ApiTags('Package Catalog')
@ApiBearerAuth()
@Controller('packages/catalog')
@UseGuards(JwtAuthGuard)
export class PackageCatalogController {
  private readonly logger = new Logger(PackageCatalogController.name);

  constructor(
    private readonly packageCatalogService: PackageCatalogService,
  ) {}

  /**
   * Get complete package catalog data in a single API call
   * Replaces the need for multiple separate API calls for package browsing
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get complete package catalog data',
    description: 'Consolidated endpoint that returns packages, categories, cities, divisions, and currencies in a single response. Replaces the need for multiple separate API calls for package browsing.'
  })
  @ApiOkResponse({ 
    description: 'Complete package catalog data with metadata',
    type: PackageCatalogDto 
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to load package catalog data.',
  })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for package name' })
  @ApiQuery({ name: 'categoryId', required: false, description: 'Filter by category ID' })
  @ApiQuery({ name: 'divisionId', required: false, description: 'Filter by division ID' })
  @ApiQuery({ name: 'cityId', required: false, description: 'Filter by city ID' })
  @ApiQuery({ name: 'venueIds', required: false, description: 'Filter by venue IDs (comma-separated)' })
  @ApiQuery({ name: 'currencyId', required: false, description: 'Filter by currency ID' })
  @ApiQuery({ name: 'minPrice', required: false, description: 'Minimum price filter' })
  @ApiQuery({ name: 'maxPrice', required: false, description: 'Maximum price filter' })
  @ApiQuery({ name: 'quantityBasis', required: false, description: 'Filter by quantity basis' })
  @ApiQuery({ name: 'showDeleted', required: false, description: 'Include deleted packages' })
  @ApiQuery({ name: 'hasOptions', required: false, description: 'Include packages with options' })
  @ApiQuery({ name: 'venueExclusive', required: false, description: 'Include venue-exclusive packages' })
  @ApiQuery({ name: 'excludeId', required: false, description: 'Exclude package with specific ID' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number for pagination' })
  @ApiQuery({ name: 'pageSize', required: false, description: 'Number of items per page' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort by field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (asc/desc)' })
  @ApiQuery({ name: 'includeOptions', required: false, description: 'Include package options' })
  @ApiQuery({ name: 'includeDependencies', required: false, description: 'Include package dependencies' })
  @ApiQuery({ name: 'includeAvailability', required: false, description: 'Include availability information' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'isAvailable', required: false, description: 'Filter by availability status' })
  async getCatalogData(
    @Query() filters: PackageFiltersDto,
    @GetCurrentUser() user: User,
  ): Promise<PackageCatalogDto> {
    this.logger.log(`User ${user.email} requesting package catalog data`);

    // Handle comma-separated arrays in query parameters
    if (typeof filters.venueIds === 'string') {
      filters.venueIds = (filters.venueIds as string).split(',').filter(id => id.trim());
    }
    if (typeof filters.tags === 'string') {
      filters.tags = (filters.tags as string).split(',').filter(tag => tag.trim());
    }

    return this.packageCatalogService.getCatalogData(filters, user);
  }

  /**
   * Get advanced package catalog with enhanced data
   * Includes options, dependencies, and availability information
   */
  @Get('advanced')
  @ApiOperation({ 
    summary: 'Get advanced package catalog data',
    description: 'Enhanced catalog endpoint that includes package options, dependencies, and availability information. Optimized for detailed package browsing.'
  })
  @ApiOkResponse({ 
    description: 'Advanced package catalog data with enhanced information',
    type: PackageCatalogDto 
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to load advanced package catalog data.',
  })
  async getAdvancedCatalogData(
    @Query() filters: PackageFiltersDto & {
      includeOptions?: boolean;
      includeDependencies?: boolean;
      includeAvailability?: boolean;
    },
    @GetCurrentUser() user: User,
  ): Promise<PackageCatalogDto> {
    this.logger.log(`User ${user.email} requesting advanced package catalog data`);

    // Handle comma-separated arrays in query parameters
    if (typeof filters.venueIds === 'string') {
      filters.venueIds = (filters.venueIds as string).split(',').filter(id => id.trim());
    }
    if (typeof filters.tags === 'string') {
      filters.tags = (filters.tags as string).split(',').filter(tag => tag.trim());
    }

    return this.packageCatalogService.getAdvancedCatalog(filters, user);
  }

  /**
   * Get package catalog summary statistics
   * Provides overview metrics for the package catalog
   */
  @Get('summary')
  @ApiOperation({ 
    summary: 'Get package catalog summary statistics',
    description: 'Returns summary statistics about the package catalog including counts by category, division, and price ranges.'
  })
  @ApiOkResponse({ 
    description: 'Package catalog summary statistics',
    schema: {
      type: 'object',
      properties: {
        totalPackages: { type: 'number', example: 150 },
        totalCategories: { type: 'number', example: 12 },
        totalDivisions: { type: 'number', example: 5 },
        averagePrice: { type: 'number', example: 2500000 },
        priceRange: {
          type: 'object',
          properties: {
            min: { type: 'number', example: 50000 },
            max: { type: 'number', example: 10000000 },
          },
        },
        packagesByCategory: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              categoryId: { type: 'string' },
              categoryName: { type: 'string' },
              count: { type: 'number' },
            },
          },
        },
        packagesByDivision: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              divisionId: { type: 'string' },
              divisionName: { type: 'string' },
              count: { type: 'number' },
            },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            loadTime: { type: 'number' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  async getCatalogSummary(
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(`User ${user.email} requesting package catalog summary`);

    const startTime = Date.now();

    // Get basic catalog data without filters
    const catalogData = await this.packageCatalogService.getCatalogData({}, user);

    // Calculate summary statistics
    const packages = catalogData.packages.data;
    const totalPackages = catalogData.packages.totalCount;
    const totalCategories = catalogData.categories.length;
    const totalDivisions = catalogData.divisions.length;

    // Calculate price statistics
    const prices = packages.map(pkg => pkg.price || 0).filter(price => price > 0);
    const averagePrice = prices.length > 0 ? prices.reduce((sum, price) => sum + price, 0) / prices.length : 0;
    const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
    const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;

    // Group packages by category
    const packagesByCategory = catalogData.categories.map(category => ({
      categoryId: category.id,
      categoryName: category.name,
      count: packages.filter(pkg => pkg.category_id === category.id).length,
    }));

    // Group packages by division
    const packagesByDivision = catalogData.divisions.map(division => ({
      divisionId: division.id,
      divisionName: division.name,
      count: packages.filter(pkg => pkg.division_id === division.id).length,
    }));

    const loadTime = Date.now() - startTime;

    return {
      totalPackages,
      totalCategories,
      totalDivisions,
      averagePrice,
      priceRange: {
        min: minPrice,
        max: maxPrice,
      },
      packagesByCategory,
      packagesByDivision,
      metadata: {
        loadTime,
        timestamp: new Date().toISOString(),
      },
    };
  }
}
