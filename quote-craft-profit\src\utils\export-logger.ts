/**
 * Centralized logging utility for export operations
 * Provides structured logging for debugging and monitoring export workflow
 */

export class ExportLogger {
  private static readonly LOG_PREFIX = '🔄 [EXPORT]';

  /**
   * Log export initiation
   */
  static logExportInitiation(calculationId: string, format: string) {
    console.log(`🚀 ${this.LOG_PREFIX} Export initiated: ${format} for calculation ${calculationId}`);
  }

  /**
   * Log export status changes
   */
  static logStatusChange(exportId: string, oldStatus: string, newStatus: string) {
    console.log(`📊 ${this.LOG_PREFIX} Export ${exportId}: ${oldStatus} → ${newStatus}`);
  }

  /**
   * Log polling activity
   */
  static logPollingActivity(activeExports: number, totalExports: number, interval: number | false) {
    if (interval === false) {
      console.log(`⏸️ ${this.LOG_PREFIX} Polling stopped: No active exports`);
    } else {
      console.log(`🔄 ${this.LOG_PREFIX} Polling: ${activeExports}/${totalExports} active exports (${interval}ms interval)`);
    }
  }

  /**
   * Log errors with context
   */
  static logError(context: string, error: any) {
    console.error(`❌ ${this.LOG_PREFIX} Error [${context}]:`, error);
  }

  /**
   * Log cache operations
   */
  static logCacheOperation(operation: string, calculationId: string, details?: any) {
    console.log(`💾 ${this.LOG_PREFIX} Cache ${operation} for calculation ${calculationId}`, details || '');
  }

  /**
   * Log performance metrics
   */
  static logPerformance(metric: string, value: number, unit: string = 'ms') {
    console.log(`⏱️ ${this.LOG_PREFIX} Performance: ${metric} = ${value}${unit}`);
  }

  /**
   * Log dialog state changes
   */
  static logDialogState(isOpen: boolean, calculationId: string) {
    const state = isOpen ? 'opened' : 'closed';
    console.log(`🪟 ${this.LOG_PREFIX} Dialog ${state} for calculation ${calculationId}`);
  }
}
