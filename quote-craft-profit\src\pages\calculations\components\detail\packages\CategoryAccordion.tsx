import React from "react";
import { ChevronLeft } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { CategoryWithPackages } from "@/types/calculation";
import PackageCard from "./PackageCard";
import VirtualizedPackageList from "./VirtualizedPackageList";

interface CategoryAccordionProps {
  category: CategoryWithPackages;
  isExpanded: boolean;
  onToggle: (categoryId: string) => void;
  packageForms: Record<
    string,
    {
      quantity: number;
      item_quantity_basis: number;
      selectedOptions?: string[];
    }
  >;
  onQuantityChange: (packageId: string, value: number) => void;
  onDaysChange?: (packageId: string, value: number) => void; // Optional for backward compatibility
  onItemQuantityBasisChange: (packageId: string, value: number) => void; // New required prop
  onOptionToggle?: (
    packageId: string,
    optionId: string,
    isSelected: boolean
  ) => void;
  onAddToCalculation: (packageId: string) => void;
  isError?: boolean;
  selectedPackageIds?: string[]; // New prop to track selected packages
}

const CategoryAccordion: React.FC<CategoryAccordionProps> = ({
  category,
  isExpanded,
  onToggle,
  packageForms,
  onQuantityChange,
  onDaysChange,
  onItemQuantityBasisChange,
  onOptionToggle = () => {},
  onAddToCalculation,
  isError = false,
  selectedPackageIds = [],
}) => {
  // Use onItemQuantityBasisChange for all operations
  const handleItemQuantityBasisChange = onItemQuantityBasisChange;
  // Helper function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    })
      .format(amount)
      .replace("IDR", "Rp");
  };

  // Custom handler for adding package to calculation
  const handleAddToCalculation = (packageId: string) => {
    onAddToCalculation(packageId);
  };

  return (
    <div className="border dark:border-gray-700 rounded-md overflow-hidden">
      <div
        className="flex justify-between items-center px-4 py-3 bg-gray-50 dark:bg-gray-800 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
        onClick={() => onToggle(category.id)}
      >
        <div className="flex items-center">
          <h3 className="text-lg font-medium dark:text-white">
            {category.name}
          </h3>
          <Badge variant="secondary" className="ml-2">
            {category.packages.length}
          </Badge>
        </div>
        <ChevronLeft
          className={`transform transition-transform dark:text-gray-300 ${
            isExpanded ? "rotate-90" : "-rotate-90"
          }`}
          size={20}
        />
      </div>

      {isExpanded && (
        <div className="p-4">
          {/* Use VirtualizedPackageList for large lists */}
          {category.packages.length > 10 ? (
            <VirtualizedPackageList
              packages={category.packages}
              onAddToCalculation={handleAddToCalculation}
              formatCurrency={formatCurrency}
              isLoading={false}
              isError={isError}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {category.packages.map((pkg) => (
                <PackageCard
                  key={pkg.id}
                  pkg={pkg}
                  quantity={packageForms[pkg.id]?.quantity || 1}
                  itemQuantityBasis={
                    packageForms[pkg.id]?.item_quantity_basis || 1
                  }
                  selectedOptions={packageForms[pkg.id]?.selectedOptions || []}
                  onQuantityChange={onQuantityChange}
                  onItemQuantityBasisChange={handleItemQuantityBasisChange}
                  onOptionToggle={onOptionToggle}
                  onAddToCalculation={onAddToCalculation}
                  isAlreadySelected={selectedPackageIds.includes(pkg.id)}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CategoryAccordion;
