import { PartialType } from '@nestjs/swagger';
import { CreatePackageOptionDto } from './create-package-option.dto.js'; // Ensure .js extension for ESM

// We can extend CreatePackageOptionDto and make all fields optional
// However, some fields like option_code or currency_id might not be suitable for update
// depending on business rules (e.g., code should be immutable once created).
// For now, allowing update of all fields except potentially code/currency.

// Let's use PartialType to make all fields optional easily.
// We might want to exclude some fields explicitly later if needed.
export class UpdatePackageOptionDto extends PartialType(
  CreatePackageOptionDto,
) {}
