import { AdminSettings, defaultSettings } from '@/pages/admin/settings/types';

const SETTINGS_KEY = 'admin_settings';

/**
 * Load settings from localStorage
 */
export const loadSettings = (): AdminSettings => {
  try {
    const settingsJson = localStorage.getItem(SETTINGS_KEY);
    if (!settingsJson) {
      return defaultSettings;
    }
    
    const settings = JSON.parse(settingsJson) as AdminSettings;
    
    // Merge with default settings to ensure all properties exist
    // This handles the case where new settings are added in future updates
    return {
      ...defaultSettings,
      ...settings,
      // Deep merge for nested objects
      packageTable: {
        ...defaultSettings.packageTable,
        ...(settings.packageTable || {}),
      },
    };
  } catch (error) {
    console.error('Error loading settings:', error);
    return defaultSettings;
  }
};

/**
 * Save settings to localStorage
 */
export const saveSettings = (settings: AdminSettings): void => {
  try {
    localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving settings:', error);
  }
};

/**
 * Update specific settings category
 */
export const updateSettings = (
  category: keyof AdminSettings,
  values: Partial<AdminSettings[keyof AdminSettings]>
): AdminSettings => {
  const currentSettings = loadSettings();
  const updatedSettings = {
    ...currentSettings,
    [category]: {
      ...currentSettings[category],
      ...values,
    },
  };
  
  saveSettings(updatedSettings);
  return updatedSettings;
};
