/**
 * Unified Query Keys - Single Source of Truth
 *
 * This file contains all query keys used throughout the application
 * to ensure consistency and avoid cache misses. Replaces all existing
 * query key definitions including CALCULATION_QUERY_KEYS and admin QUERY_KEYS.
 *
 * CACHE OPTIMIZATION: Uses hierarchical structure for better invalidation control
 */

export const QUERY_KEYS = {
  // Calculation domain - hierarchical structure for better cache control
  calculations: {
    all: () => ["calculations"] as const,
    lists: () => [...QUERY_KEYS.calculations.all(), "list"] as const,
    list: (filters?: any) =>
      [...QUERY_KEYS.calculations.lists(), filters] as const,
    byStatus: (status: string) =>
      [...QUERY_KEYS.calculations.lists(), { status }] as const,
    details: () => [...QUERY_KEYS.calculations.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.calculations.details(), id] as const,

    // Calculation sub-resources - properly nested for cache invalidation
    lineItems: (id: string) =>
      [...QUERY_KEYS.calculations.detail(id), "line-items"] as const,
    lineItem: (calculationId: string, lineItemId: string) =>
      [
        ...QUERY_KEYS.calculations.lineItems(calculationId),
        lineItemId,
      ] as const,
    lineItemOptions: (calculationId: string, lineItemId: string) =>
      [
        ...QUERY_KEYS.calculations.lineItem(calculationId, lineItemId),
        "options",
      ] as const,

    customItems: (id: string) =>
      [...QUERY_KEYS.calculations.detail(id), "custom-items"] as const,
    customItem: (calculationId: string, itemId: string) =>
      [...QUERY_KEYS.calculations.customItems(calculationId), itemId] as const,

    packagesByCategory: (id: string) =>
      [...QUERY_KEYS.calculations.detail(id), "packages-by-category"] as const,
    financials: (id: string) =>
      [...QUERY_KEYS.calculations.detail(id), "financials"] as const,
  },

  // Package domain - unified structure for all package-related queries
  packages: {
    all: () => ["packages"] as const,
    lists: () => [...QUERY_KEYS.packages.all(), "list"] as const,
    list: (filters?: any) => [...QUERY_KEYS.packages.lists(), filters] as const,
    details: () => [...QUERY_KEYS.packages.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.packages.details(), id] as const,

    // Package sub-resources
    options: (id: string) =>
      [...QUERY_KEYS.packages.detail(id), "options"] as const,
    dependencies: (id: string) =>
      [...QUERY_KEYS.packages.detail(id), "dependencies"] as const,
    cities: (id: string) =>
      [...QUERY_KEYS.packages.detail(id), "cities"] as const,
    venues: (id: string) =>
      [...QUERY_KEYS.packages.detail(id), "venues"] as const,

    // Package catalog - consolidated queries
    catalog: {
      all: () => [...QUERY_KEYS.packages.all(), "catalog"] as const,
      list: (filters?: any) =>
        [...QUERY_KEYS.packages.catalog.all(), "list", filters] as const,
      advanced: (filters?: any) =>
        [...QUERY_KEYS.packages.catalog.all(), "advanced", filters] as const,
      summary: () => [...QUERY_KEYS.packages.catalog.all(), "summary"] as const,
      availability: () =>
        [...QUERY_KEYS.packages.catalog.all(), "availability"] as const,
    },
  },

  // Client domain
  clients: {
    all: () => ["clients"] as const,
    lists: () => [...QUERY_KEYS.clients.all(), "list"] as const,
    list: (filters?: any) => [...QUERY_KEYS.clients.lists(), filters] as const,
    details: () => [...QUERY_KEYS.clients.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.clients.details(), id] as const,
  },

  // Division domain
  divisions: {
    all: () => ["divisions"] as const,
    lists: () => [...QUERY_KEYS.divisions.all(), "list"] as const,
    list: (filters?: any) =>
      [...QUERY_KEYS.divisions.lists(), filters] as const,
    details: () => [...QUERY_KEYS.divisions.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.divisions.details(), id] as const,
  },

  // Event domain
  events: {
    all: () => ["events"] as const,
    lists: () => [...QUERY_KEYS.events.all(), "list"] as const,
    list: (filters?: any) => [...QUERY_KEYS.events.lists(), filters] as const,
    details: () => [...QUERY_KEYS.events.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.events.details(), id] as const,
  },

  // Venue domain
  venues: {
    all: () => ["venues"] as const,
    lists: () => [...QUERY_KEYS.venues.all(), "list"] as const,
    list: (filters?: any) => [...QUERY_KEYS.venues.lists(), filters] as const,
    byCity: (cityId: string) =>
      [...QUERY_KEYS.venues.lists(), { cityId }] as const,
    details: () => [...QUERY_KEYS.venues.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.venues.details(), id] as const,
  },

  // City domain
  cities: {
    all: () => ["cities"] as const,
    lists: () => [...QUERY_KEYS.cities.all(), "list"] as const,
    list: (filters?: any) => [...QUERY_KEYS.cities.lists(), filters] as const,
    details: () => [...QUERY_KEYS.cities.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.cities.details(), id] as const,
  },

  // Category domain
  categories: {
    all: () => ["categories"] as const,
    lists: () => [...QUERY_KEYS.categories.all(), "list"] as const,
    list: (filters?: any) =>
      [...QUERY_KEYS.categories.lists(), filters] as const,
    details: () => [...QUERY_KEYS.categories.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.categories.details(), id] as const,
  },

  // Currency domain
  currencies: {
    all: () => ["currencies"] as const,
    lists: () => [...QUERY_KEYS.currencies.all(), "list"] as const,
    list: (filters?: any) =>
      [...QUERY_KEYS.currencies.lists(), filters] as const,
  },

  // Template domain
  templates: {
    all: () => ["templates"] as const,
    lists: () => [...QUERY_KEYS.templates.all(), "list"] as const,
    list: (filters?: any) =>
      [...QUERY_KEYS.templates.lists(), filters] as const,
    details: () => [...QUERY_KEYS.templates.all(), "detail"] as const,
    detail: (id: string) => [...QUERY_KEYS.templates.details(), id] as const,
  },
} as const;

// Legacy compatibility - DEPRECATED: Use QUERY_KEYS.calculations instead
// These will be removed after migration is complete
export const LEGACY_QUERY_KEYS = {
  calculation: (id: string) => QUERY_KEYS.calculations.detail(id),
  calculations: () => QUERY_KEYS.calculations.all(),
  calculationsByStatus: (status: string) =>
    QUERY_KEYS.calculations.byStatus(status),
  lineItems: (calculationId: string) =>
    QUERY_KEYS.calculations.lineItems(calculationId),
  lineItem: (calculationId: string, lineItemId: string) =>
    QUERY_KEYS.calculations.lineItem(calculationId, lineItemId),
  lineItemOptions: (calculationId: string, lineItemId: string) =>
    QUERY_KEYS.calculations.lineItemOptions(calculationId, lineItemId),
  customItems: (calculationId: string) =>
    QUERY_KEYS.calculations.customItems(calculationId),
  customItem: (calculationId: string, itemId: string) =>
    QUERY_KEYS.calculations.customItem(calculationId, itemId),
  packagesByCategory: (calculationId: string) =>
    QUERY_KEYS.calculations.packagesByCategory(calculationId),
  categories: () => QUERY_KEYS.categories.all(),
  clients: () => QUERY_KEYS.clients.all(),
  client: (id: string) => QUERY_KEYS.clients.detail(id),
  venues: (cityId?: string) =>
    cityId ? QUERY_KEYS.venues.byCity(cityId) : QUERY_KEYS.venues.all(),
  venue: (id: string) => QUERY_KEYS.venues.detail(id),
  cities: () => QUERY_KEYS.cities.all(),
  city: (id: string) => QUERY_KEYS.cities.detail(id),
};
