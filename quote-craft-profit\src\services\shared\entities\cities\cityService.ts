import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';
import { City } from '@/types/types';

export interface CreateCityRequest {
  name: string;
  province?: string;
  country?: string;
  is_active?: boolean;
}

export interface UpdateCityRequest {
  name?: string;
  province?: string;
  country?: string;
  is_active?: boolean;
}

/**
 * Get all cities from the backend API
 * @returns Promise resolving to an array of cities
 */
export const getAllCities = async (): Promise<City[]> => {
  try {
    console.log('Fetching cities from backend API');

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.CITIES.GET_ALL);

    console.log('Cities fetched successfully from backend API');

    // Check if the response data is in the expected format
    const cities = Array.isArray(response.data)
      ? response.data
      : response.data.data || [];

    // Transform the data to match the expected format
    return cities.map((city: any) => ({
      id: city.id,
      name: city.name,
      created_at: city.created_at || new Date().toISOString(),
      updated_at: city.updated_at || new Date().toISOString(),
    }));
  } catch (error) {
    console.error('Error fetching cities from backend API:', error);
    throw error;
  }
};

/**
 * Get city by ID from the backend API
 * @param id - The city ID
 * @returns Promise resolving to a city
 */
export const getCityById = async (id: string): Promise<City> => {
  try {
    console.log(`Fetching city with ID ${id} from backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.CITIES.GET_BY_ID(id));

    console.log('City fetched successfully from backend API');

    // Get the city data from the response
    const cityData = response.data.data || response.data;

    // Transform the data to match the expected format
    return {
      id: cityData.id,
      name: cityData.name,
      created_at: cityData.created_at || new Date().toISOString(),
      updated_at: cityData.updated_at || new Date().toISOString(),
    };
  } catch (error) {
    console.error(`Error fetching city with ID ${id} from backend API:`, error);
    throw error;
  }
};

/**
 * Create a new city
 * @param data - The city data to create
 * @returns Promise resolving to the created city
 */
export const createCity = async (data: CreateCityRequest): Promise<City> => {
  try {
    console.log('Creating new city:', data);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.post(API_ENDPOINTS.CITIES.CREATE, data);

    console.log('City created successfully');
    return response.data;
  } catch (error) {
    console.error('Error creating city:', error);
    throw error;
  }
};

/**
 * Update a city
 * @param id - The city ID
 * @param data - The city data to update
 * @returns Promise resolving to the updated city
 */
export const updateCity = async (
  id: string,
  data: UpdateCityRequest
): Promise<City> => {
  try {
    console.log(`Updating city with ID ${id}:`, data);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.put(API_ENDPOINTS.CITIES.UPDATE(id), data);

    console.log('City updated successfully');
    return response.data;
  } catch (error) {
    console.error(`Error updating city with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a city
 * @param id - The city ID
 * @returns Promise resolving when the city is deleted
 */
export const deleteCity = async (id: string): Promise<void> => {
  try {
    console.log(`Deleting city with ID ${id}`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.delete(API_ENDPOINTS.CITIES.DELETE(id));

    console.log('City deleted successfully');
  } catch (error) {
    console.error(`Error deleting city with ID ${id}:`, error);
    throw error;
  }
};
