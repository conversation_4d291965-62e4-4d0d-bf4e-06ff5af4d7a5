import React, { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Users,
  MapPin,
  Calendar,
  Loader2,
  ExternalLink,
} from "lucide-react";
import { getPublicTemplates } from "@/services/templates/userTemplateService";
import { calculateTemplateTotal } from "@/services/calculations/templates";
import { getAllEventTypes } from "@/services/shared/entities/event-types";
import { WizardState } from "./WizardContainer";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

interface TemplateRecommendationsProps {
  wizardState: WizardState;
  updateWizardState: (updates: Partial<WizardState>) => void;
  onNext: () => void;
}

export const TemplateRecommendations: React.FC<
  TemplateRecommendationsProps
> = ({ wizardState }) => {
  const navigate = useNavigate();

  // Fetch event types for display purposes
  const { data: eventTypes } = useQuery({
    queryKey: ["event-types"],
    queryFn: getAllEventTypes,
  });

  // Build filters based on wizard state
  const templateFilters = useMemo(() => {
    const filters: any = {};

    if (wizardState.eventType) {
      filters.eventType = wizardState.eventType;
    }

    if (wizardState.cityId) {
      filters.cityId = wizardState.cityId;
    }

    // Add attendee range filtering
    if (wizardState.attendeeCount) {
      if (wizardState.attendeeCount <= 50) {
        filters.attendeesMin = 1;
        filters.attendeesMax = 50;
      } else if (wizardState.attendeeCount <= 100) {
        filters.attendeesMin = 51;
        filters.attendeesMax = 100;
      } else if (wizardState.attendeeCount <= 500) {
        filters.attendeesMin = 101;
        filters.attendeesMax = 500;
      } else {
        filters.attendeesMin = 500;
      }
    }

    return filters;
  }, [wizardState]);

  // Fetch templates based on wizard selections
  const {
    data: templatesResponse,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["wizard-templates", templateFilters],
    queryFn: () => getPublicTemplates(templateFilters),
    meta: {
      onError: (error: Error) => {
        toast.error(`Failed to load templates: ${error.message}`);
      },
    },
  });

  const handleTemplateSelect = async (templateId: string) => {
    try {
      // Navigate to create calculation from template
      navigate(`/calculations/from-template/${templateId}`);
    } catch (error) {
      console.error("Error selecting template:", error);
      toast.error("Failed to load template");
    }
  };

  const handleViewTemplate = (templateId: string) => {
    navigate(`/templates/${templateId}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Recommended Templates
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Finding the best templates for your event...
          </p>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-600 dark:text-gray-300">
              Loading templates...
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (isError || !templatesResponse) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Recommended Templates
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Based on your selections, here are the best templates for your event
          </p>
        </div>

        <div className="text-center py-12">
          <div className="text-red-600 dark:text-red-400 mb-4">
            <FileText className="h-12 w-12 mx-auto mb-2" />
            <p className="text-lg font-medium">Unable to load templates</p>
            <p className="text-sm">Please try refreshing the page</p>
          </div>
        </div>
      </div>
    );
  }

  const templates = templatesResponse.data || [];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          Recommended Templates
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Based on your selections, here are the best templates for your event
        </p>
      </div>

      {/* Summary of selections */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-2">
            <Badge
              variant="secondary"
              className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
            >
              {eventTypes?.find((et) => et.id === wizardState.eventType)
                ?.name || "Any event type"}
            </Badge>
            <Badge
              variant="secondary"
              className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
            >
              <Users className="h-3 w-3 mr-1" />
              {wizardState.attendeeCount} attendees
            </Badge>
            {wizardState.cityId && (
              <Badge
                variant="secondary"
                className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
              >
                <MapPin className="h-3 w-3 mr-1" />
                Selected city
              </Badge>
            )}
            {wizardState.venueId && (
              <Badge
                variant="secondary"
                className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
              >
                Specific venue
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {templates.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
            No templates found
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Try adjusting your selections or create a new calculation from
            scratch
          </p>
          <Button onClick={() => navigate("/calculations/new")}>
            Create New Calculation
          </Button>
        </div>
      ) : (
        <>
          <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            Found {templates.length} template{templates.length !== 1 ? "s" : ""}{" "}
            matching your criteria
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <Card
                key={template.id}
                className="hover:shadow-lg transition-all duration-200 hover:scale-105"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg line-clamp-2">
                      {template.name}
                    </CardTitle>
                    <Badge variant="outline" className="ml-2 flex-shrink-0">
                      {template.event_type || "General"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {template.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
                      {template.description}
                    </p>
                  )}

                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                    {template.attendees && (
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {template.attendees}
                      </div>
                    )}
                    {template.city_name && (
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {template.city_name}
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      onClick={() => handleTemplateSelect(template.id)}
                      className="flex-1"
                      size="sm"
                    >
                      Use Template
                    </Button>
                    <Button
                      onClick={() => handleViewTemplate(template.id)}
                      variant="outline"
                      size="sm"
                      className="flex items-center"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center pt-6">
            <Button
              onClick={() => navigate("/templates")}
              variant="outline"
              className="flex items-center gap-2"
            >
              Browse All Templates
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </>
      )}
    </div>
  );
};
