import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PostgrestError } from '@supabase/supabase-js';
import { CreatePackageOptionDto } from './dto/create-package-option.dto.js';
import { PackageOptionDto } from './dto/package-option.dto.js';
import { PartialType } from '@nestjs/swagger'; // Or @nestjs/mapped-types
import { IsOptional, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

// Query DTO for listing package options with pagination
export class PackageOptionListQueryDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10; // Default limit

  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  offset?: number = 0; // Default offset

  // Add other potential filters here later (e.g., option_code, status)
}

// Update DTO using PartialType
// Ensure CreatePackageOptionDto has decorators for properties if using PartialType extensively
export class UpdatePackageOptionDto extends PartialType(
  CreatePackageOptionDto,
) {}

@Injectable()
export class PackageOptionsService {
  private readonly logger = new Logger(PackageOptionsService.name);
  private TABLE_NAME = 'package_options' as const;

  constructor(private readonly supabaseService: SupabaseService) {}

  // --- Private Helper Methods ---

  /**
   * Checks if a package exists.
   * Throws NotFoundException if the package does not exist.
   */
  private async _checkPackageExists(packageId: string): Promise<void> {
    const supabase = this.supabaseService.getClient();
    const { error: pkgError, count } = await supabase
      .from('packages') // Assuming 'packages' table exists
      .select('id', { count: 'exact', head: true })
      .eq('id', packageId)
      .eq('is_deleted', false); // Ensure package is not deleted

    if (pkgError) {
      this.logger.error(
        `Error checking package existence for ID ${packageId}: ${pkgError.message}`,
        pkgError.stack,
      );
      throw new InternalServerErrorException(
        'Failed to verify package existence.',
      );
    }

    if (count === 0) {
      throw new NotFoundException(`Package with ID ${packageId} not found.`);
    }
  }

  /**
   * Checks if an option with the same code and currency already exists for the package.
   * Throws ConflictException if a duplicate is found.
   */
  private async _checkOptionUniqueness(
    packageId: string,
    optionCode: string,
    currencyId: string,
  ): Promise<void> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('id')
      .eq('applicable_package_id', packageId)
      .eq('option_code', optionCode)
      .eq('currency_id', currencyId)
      .single();

    if (data) {
      throw new ConflictException(
        `Option with code '${optionCode}' already exists for this package and currency combination.`,
      );
    }

    // If error is not "PGRST116" (no rows found), then it's a real error
    if (error && error.code !== 'PGRST116') {
      this.logger.error(
        `Error checking option uniqueness: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to check option uniqueness.',
      );
    }
  }

  // --- CRUD Methods ---

  async create(
    packageId: string,
    createDto: CreatePackageOptionDto,
  ): Promise<PackageOptionDto> {
    this.logger.log(
      `Attempting to create option '${createDto.option_code}' for package ${packageId}`,
    );
    await this._checkPackageExists(packageId);

    // Check if option with same code and currency already exists for this package
    await this._checkOptionUniqueness(packageId, createDto.option_code, createDto.currency_id);

    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .insert({
        applicable_package_id: packageId,
        ...createDto,
        // is_deleted is false by default in DB schema
      })
      .select('*')
      .single<PackageOptionDto>();

    if (error) {
      this.logger.error(
        `Error creating option for package ${packageId}: ${error.message}`,
        error.stack,
      );
      // Handle specific errors (e.g., unique constraints, foreign keys)
      if (error instanceof PostgrestError) {
        if (error.code === '23505') {
          // unique_violation - this should be caught by our pre-check, but handle just in case
          throw new ConflictException(
            `Option with code '${createDto.option_code}' already exists for this package and currency combination.`,
          );
        }
        if (error.code === '23503') {
          // foreign_key_violation
          if (error.details?.includes('currency_id')) {
            throw new NotFoundException(
              `Currency with ID ${createDto.currency_id} not found.`,
            );
          }
          // We already check package_id, but handle just in case
          if (error.details?.includes('applicable_package_id')) {
            throw new NotFoundException(
              `Package with ID ${packageId} not found.`,
            );
          }
        }
      }
      throw new InternalServerErrorException(
        `Failed to create package option: ${error.message}`,
      );
    }

    if (!data) {
      // Should not happen with .single() unless there's a policy issue or unexpected error
      this.logger.error(
        'Option creation did not return data unexpectedly for package ' +
          packageId,
      );
      throw new InternalServerErrorException(
        'Failed to create package option.',
      );
    }

    this.logger.log(
      `Option '${data.option_code}' (ID: ${data.id}) created successfully for package ${packageId}`,
    );
    return data;
  }

  async findAll(
    packageId: string,
    queryDto: PackageOptionListQueryDto,
  ): Promise<{
    data: PackageOptionDto[];
    count: number;
    limit: number;
    offset: number;
  }> {
    this.logger.log(
      `Fetching options for package ${packageId} with query: ${JSON.stringify(
        queryDto,
      )}`,
    );
    await this._checkPackageExists(packageId);

    const { limit = 10, offset = 0 } = queryDto;
    const supabase = this.supabaseService.getClient();

    const query = supabase
      .from(this.TABLE_NAME)
      .select('*', { count: 'exact' })
      .eq('applicable_package_id', packageId)
      .range(offset, offset + limit - 1)
      .returns<PackageOptionDto[]>(); // Add explicit return type

    // Add sorting if needed later, e.g., .order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      this.logger.error(
        `Error fetching package options for package ${packageId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to fetch package options: ${error.message}`,
      );
    }

    return {
      data: data || [],
      count: count || 0,
      limit,
      offset,
    };
  }

  async findOne(
    packageId: string,
    optionId: string,
  ): Promise<PackageOptionDto> {
    this.logger.log(
      `Attempting to find option ${optionId} for package ${packageId}`,
    );
    await this._checkPackageExists(packageId); // Ensure the parent package exists

    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .eq('id', optionId)
      .eq('applicable_package_id', packageId) // Ensure it belongs to the correct package
      .maybeSingle<PackageOptionDto>(); // Use maybeSingle to handle not found gracefully

    if (error) {
      this.logger.error(
        `Error fetching option ${optionId} for package ${packageId}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to fetch package option: ${error.message}`,
      );
    }

    if (!data) {
      this.logger.warn(`Option ${optionId} not found for package ${packageId}`);
      throw new NotFoundException(
        `Package option with ID ${optionId} not found for package ${packageId}.`,
      );
    }

    this.logger.log(
      `Option ${optionId} for package ${packageId} found successfully.`,
    );
    return data;
  }

  async update(
    packageId: string,
    optionId: string,
    updateDto: UpdatePackageOptionDto,
  ): Promise<PackageOptionDto> {
    this.logger.log(
      `Attempting to update option ${optionId} for package ${packageId} with data: ${JSON.stringify(
        updateDto,
      )}`,
    );

    // Selectively build the object with fields allowed for update
    const restUpdateDto: Partial<PackageOptionDto> = {};
    if (updateDto.option_name !== undefined)
      restUpdateDto.option_name = updateDto.option_name;
    if (updateDto.currency_id !== undefined)
      restUpdateDto.currency_id = updateDto.currency_id;
    if (updateDto.price_adjustment !== undefined)
      restUpdateDto.price_adjustment = updateDto.price_adjustment;
    if (updateDto.cost_adjustment !== undefined)
      restUpdateDto.cost_adjustment = updateDto.cost_adjustment;
    if (updateDto.description !== undefined)
      restUpdateDto.description = updateDto.description;
    if (updateDto.option_group !== undefined)
      restUpdateDto.option_group = updateDto.option_group;
    if (updateDto.is_default_for_package !== undefined)
      restUpdateDto.is_default_for_package = updateDto.is_default_for_package;
    if (updateDto.is_required !== undefined)
      restUpdateDto.is_required = updateDto.is_required;
    // Add other updatable fields here if necessary

    if (Object.keys(restUpdateDto).length === 0) {
      this.logger.warn(
        `Update called for option ${optionId} but no valid fields were provided.`,
      );
      // Optionally throw BadRequestException or return the existing record
      return this.findOne(packageId, optionId); // Return existing if no changes
    }

    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .update(restUpdateDto)
      .eq('id', optionId)
      .eq('applicable_package_id', packageId) // Ensure we only update if it matches the package route
      .select('*')
      .single<PackageOptionDto>();

    if (error) {
      this.logger.error(
        `Error updating option ${optionId} for package ${packageId}: ${error.message}\nStack: ${error.stack}`,
      );
      // Handle specific errors (e.g., foreign key violation if currency_id is changed)
      if (error instanceof PostgrestError && error.code === '23503') {
        throw new NotFoundException(
          `Update failed due to invalid reference (e.g., currency_id): ${error.details}`,
        );
      }
      throw new InternalServerErrorException(
        `Failed to update package option: ${error.message}`,
      );
    }

    // If data is null after the update attempt, it means the WHERE clause didn't match
    // (likely wrong optionId for the packageId, or already deleted)
    if (!data) {
      this.logger.warn(
        `Update failed for option ${optionId} - not found for package ${packageId} or already deleted.`,
      );
      throw new NotFoundException(
        `Package option with ID ${optionId} not found for package ${packageId}.`,
      );
    }

    this.logger.log(
      `Option ${optionId} updated successfully for package ${packageId}.`,
    );
    return data;
  }

  async remove(packageId: string, optionId: string): Promise<void> {
    this.logger.log(
      `Attempting delete option ${optionId} for package ${packageId}`,
    );

    const supabase = this.supabaseService.getClient();
    const { error, count } = await supabase
      .from(this.TABLE_NAME)
      .delete()
      .match({ id: optionId, applicable_package_id: packageId });

    if (error) {
      this.logger.error(
        `Error deleting option ${optionId} for package ${packageId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to delete package option: ${error.message}`,
      );
    }

    if (count === 0) {
      this.logger.warn(
        `Delete failed: Option ${optionId} not found for package ${packageId}.`,
      );
      throw new NotFoundException(
        `Package option with ID ${optionId} not found for package ${packageId}.`,
      );
    }

    this.logger.log(
      `Option ${optionId} deleted successfully for package ${packageId}.`,
    );
  }
}
