## Backend Implementation Plan - Detailed v2

**(Based on Finalized Schema, Functions & Brief as of April 18, 2025)**

This plan details the backend API development required to support the Dynamic Event Costing Platform, using the finalized normalized schema (`calculation_line_items`, cost tracking, etc.).

**Note:** For detailed technical implementation guidelines (dependencies, structure, conventions, key points) refer to `docs/BackendTECH.md`. For the complete database schema definition, refer to `docs/Schema.md`.

**Status Legend:** `[x]` = Done, `[~]` = In Progress / Partially Done, `[ ]` = Not Started

### Ongoing Practices / Cross-Cutting Concerns (Apply throughout development)

- **Testing:**
  - [ ] Write comprehensive Unit Tests (Services, complex logic) as features are developed.
    - _Focus Areas:_ `CalculationItemsService` quantity/duration logic, `PackagesService` conflict checking, Tax/Discount application logic (if any remains in service).
  - [ ] Write Integration Tests (Module interactions) for key flows.
    - _Focus Areas:_ Template creation flow (`POST /calculations/from-template`), Item addition + Recalculation (`POST /line-items/package` -> check totals).
  - [ ] Write E2E Tests (API endpoint testing using `supertest`) for core user stories and admin functions.
    - _Focus Areas:_ Full CRUD for `/calculations`, Add/Delete items & verify totals, Admin CRUD operations, Auth flow, Export generation.
- **Documentation:**
  - [~] Keep this plan (`BACKEND_PLAN.md`) updated with progress and changes.
  - [x] Update/Add Postman tests (`TEST_POSTMAN.md` -> now `docs/postman/` directory with index) updated with progress and changes.
  - [ ] Ensure code comments explain non-trivial logic.

### Phase 0: Project Setup & Initialization

- [x] **Initialize NestJS Project:**
  - [x] Use NestJS CLI: `nest new event-costing-api` (or chosen project name).
  - [x] Select package manager (e.g., `npm`).
  - [x] Initialize Git repository (`git init`, initial commit).
- [x] **Install Core Dependencies:**
  - [x] Supabase Client: `npm install @supabase/supabase-js`
  - [x] Configuration: `npm install @nestjs/config`
  - [x] Validation: `npm install class-validator class-transformer`
  - [x] Basic Logging (Optional but recommended): `npm install winston nest-winston`
  - [x] Other NestJS modules as needed (e.g., platform-express is default).
- [x] **Database Synchronization:**
  - [x] Ensure existing SQL schema from `docs/Schema.md` is applied to Supabase DB (via Supabase SQL Editor or migrations tool). This is the source of truth.
- [x] **Setup Supabase Integration:**
  - [x] Configure `.env` with `SUPABASE_URL` and `SUPABASE_ANON_KEY` (or `SUPABASE_SERVICE_ROLE_KEY` for backend operations).
  - [x] Consider creating a `SupabaseModule` and `SupabaseService` in `src/core/supabase` to provide an injectable Supabase client instance (configured via `ConfigService`).
- [x] **Establish Core Structure:**
  - [x] Create initial folders: `src/modules`, `src/core`, `src/config` (as per technical plan).
  - [x] Create `src/shared` directory for reusable code (DTOs, interfaces, utils) not fitting into `src/core` or specific feature modules.
  - [x] Create `src/shared/dtos` subdirectory.
  - [x] **TODO:** Move generic pagination DTOs (e.g., `PaginationQueryDto`, `PaginatedResponseDto`) from their current locations into `src/shared/dtos`. Update imports accordingly.
  - [x] Setup global `ValidationPipe` in `main.ts`.
  - [x] Setup basic global `HttpExceptionFilter` (as per technical plan).
- [x] **Configure Environment Variables:**
  - [x] Setup `ConfigModule` (`@nestjs/config`) globally in `app.module.ts`.
  - [x] Load `.env` file. Define necessary variables (Supabase URL/Key, JWT Secret if needed).

### Phase 1: End User Features (Backend Implementation)

**Module 1: Authentication & User Profile**

- **Goal:** Secure access and provide user context.
- **Endpoints:**
  - [x] `POST /auth/login`: Authenticate user via Supabase Auth. Return session/token.
  - [x] `POST /auth/logout`: Invalidate session/token (client-side/Supabase).
  - [x] `GET /users/me`: Fetch `profiles` record joined with `roles` for the authenticated user (using Supabase client).
- **Logic:**
  - [x] Implement `AuthModule`, `AuthService`, `AuthController`.
  - [x] Implement `AuthService.login` and `AuthService.logout` using Supabase client.
  - [x] Implement `AuthService.validateUserFromSupabaseToken` for guard integration.
  - [x] Create `JwtAuthGuard` in `src/modules/auth/guards/jwt-auth.guard.ts` (using `AuthService.validateUserFromSupabaseToken`).
  - [ ] (Optional) If using Passport.js for more complex strategies, implement `JwtStrategy` in `src/modules/auth/strategies/jwt.strategy.ts` (also using `AuthService.validateUserFromSupabaseToken`).
  - [x] Apply `JwtAuthGuard` (or other guards) to protected routes/controllers.
  - [x] Implement logic in `UserService` (or dedicated `ProfileService`) for `GET /users/me` using the Supabase client.
  - [x] Verify `handle_new_user` trigger functionality creates profiles correctly.
- **Tables:** `auth.users`, `profiles`, `roles`.

**Module 2: Core Calculation Management (calculation_history)**

- **Goal:** Manage the lifecycle of cost calculations.
- **Endpoints:**
  - [x] `POST /calculations`:
    - [x] Define `CreateCalculationDto`.
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client): Create `calculation_history` record, set defaults, validate inputs.
    - [x] Return `{ "id": "new_calculation_uuid" }`.
  - [x] `GET /calculations`:
    - [x] Define DTO for query parameters (`ListCalculationsDto`).
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client): List `calculation_history` for user, apply filters/sorting/pagination.
    - [x] Return paginated list of summaries (`PaginatedCalculationsResponse` DTO created in `dto/paginated-calculations.dto.ts`).
  - [x] `GET /calculations/{id}`:
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client): Fetch `calculation_history` with relations (`clients`, `events`, `currencies`, line items, custom items, options) via joins or separate queries. Check ownership.
    - [x] Define DTO for the comprehensive response.
    - [x] Return calculation object.
  - [x] `PUT /calculations/{id}`:
    - [x] Define `UpdateCalculationDto` (include updatable fields, tax/discount JSON).
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client): Check ownership, update fields, handle tax/discount JSON update, trigger recalculation.
    - [x] Return updated calculation object (using `CalculationDetailDto`).
  - [x] `DELETE /calculations/{id}`:
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client): Set `is_deleted = true`, `deleted_at = now()`. Check ownership.
    - [x] Return success status.
- **Logic:**
  - [x] Implement authorization checks (ownership) for all relevant endpoints.
  - [x] Ensure efficient queries for `GET /{id}` using Supabase client methods.
  - [x] Implement robust recalculation logic triggered by `PUT /{id}` and subsequent line item changes.
- **Tables:** `calculation_history`, `clients`, `events`, `currencies`, `calculation_line_items`, `calculation_line_item_options`, `calculation_custom_items`, `auth.users`, `packages`, `categories`.

#### Calculations Service (`calculations.service.ts`)

- **Responsibilities:** Handles core CRUD operations for calculations (fetching list/details, creating, updating), manages calculation history/versions, handles templates.
- **Key Logic:**
  - Fetching calculation list (`CalculationSummaryDto`) and detail (`CalculationDetailDto`) data with related entities (currency, city, client, event, line items, custom items).
  - Retrieving pre-calculated totals (`subtotal`, `taxes`, `discount`, `total`, `total_cost`, `estimated_profit`) directly from the `calculation_history` table for detailed views.
  - Mapping raw database results (`CalculationHistoryRaw`, `CalculationSummaryRaw`) to corresponding DTOs.
  - Ownership checks to ensure users access only their data.
  - Template creation and usage logic (populating items via `CalculationItemsService`).
- **Status:**
  - CRUD operations for fetching lists/details, creating, updating implemented.
  - Template creation/usage logic implemented.
  - **Recent Refactoring (April 2025):**
    - Updated service methods (`findUserCalculations`, `findCalculationById`) to fetch data primarily from `calculation_history`.
    - Service now **reads** pre-calculated totals from `calculation_history`; calculation logic moved to DB function (`recalculate_calculation_totals`). The RPC is triggered directly from `CalculationsService.updateCalculation` after an update, and also triggered by `CalculationItemsService` after item additions/deletions (via its own RPC calls like `add_package_item_and_recalculate`).
    - Defined and corrected internal interfaces (`CalculationHistoryRaw`, `CalculationSummaryRaw`, `CalculationTotalsRaw`) to match DB schema and query results.
    - Resolved all ESLint errors and improved type safety through explicit typing of Supabase query/RPC results.
    - Removed unused imports.
  - **TODO:** Further review calculation logic if needed. Verify snapshot management against requirements.

#### Calculation Items Service (`calculation-items.service.ts`)

- **Responsibilities:** Handles CRUD operations for calculation line items and custom items, manages snapshots, and performs calculations.
- **Key Logic:**
  - Creating, updating, and deleting line items and custom items.
  - Managing snapshots of prices, costs, and other relevant data for line items.
  - Calculating totals for line items and custom items.
  - Triggering overall calculation recalculation on item changes.
- **Status:**
  - Initial CRUD implemented for line items and custom items.
  - Snapshot management implemented for line items.
  - Calculation logic implemented for line items and custom items.
  - **Recent Refactoring (April 2025):**
    - Corrected internal interfaces (`CalculationLineItemRaw`) to accurately match DB schema.
    - Rewritten `_mapRawToDetailDto` to ensure correct and type-safe mapping from raw data to `CalculationLineItemDetailDto`.
    - Refined the `calculateTotals` method to correctly calculate totals for line items and custom items.
    - Corrected helper functions (`_getSummedTotal`, `_getTaxAmount`, `_getDiscountAmount`) and their usage.
    - Addressed numerous linting errors and type mismatches for improved code quality and robustness.
    - Resolved outstanding ESLint errors and significantly improved type safety through explicit typing of Supabase query/RPC results.
    - Removed unused imports.
  - **TODO:**
    - [ ] Further review calculation logic if needed.
    - [ ] Verify snapshot management against requirements (See Phase X review points).
    - [ ] Implement fixes/improvements identified in Phase X review (snapshotting verification, recalculation trigger calls, transaction handling).
      - **Refactoring Plan (Move to DB Function):**
        - [x] **Step 1: Create DB Function:** Define and create the `recalculate_calculation_totals` PostgreSQL function. Implemented logic for summing items/costs and basic tax/discount application based on JSON structure. (_Note: Tax/Discount JSON calculation logic may need further refinement based on exact business rules_).
        - [x] **Step 2: Refactor Service Method:** Update the `CalculationItemsService.addPackageLineItem` method to call the new `add_package_item_and_recalculate` RPC function instead of performing the logic directly. (**Done**).
        - [x] **Step 3: Update Template Population:** Modify the `CalculationItemsService.populateItemsFromTemplateBlueprint` method to call the same `add_package_item_and_recalculate` RPC function within its loop for each package selection, ensuring atomicity and reusing logic. (**Done**, requires user context passing fix in caller).

**Module 3: Template Handling (User Side)**

- **Goal:** Allow users to start calculations from templates.
- **Endpoints:**
  - [x] `GET /templates`: (Implemented in Templates Module)
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client/RPC).
    - [x] Return template list.
  - [x] `POST /calculations/from-template/{template_id}`: (Implemented in Calculations Module)
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client, calls CalculationItemsService).
    - [x] Return new `calculation_id`.
- **`Logic (POST /from-template):`**
  - [x] 1. Verify user access to `template_id` (Implemented basic check: public or owner).
  - [x] 2. Fetch template data (`templates` table), including `package_selections` blueprint.
  - [x] 3. Create `calculation_history` record (copy fields, set user, status='draft', currency=IDR, dates).
  - [x] 4. Parse blueprint JSON. (Logic in CalculationItemsService)
    - [x] Fetch current package/option details (`packages`, `package_prices`, `package_options`) including **price and cost** for the calculation's currency.
    - [x] Determine effective `item_quantity` and `duration_days` based on `packages.quantity_basis` and context (Implemented basic cases).
    - [x] **DB Operations:** Use Supabase client to insert `calculation_line_items` with snapshots, insert `calculation_line_item_options` with snapshots, calculate & store line totals/costs.
  - [x] 5. Trigger overall total recalculation for the new `calculation_history`. - Implemented via RPC call in `CalculationsService.createFromTemplate` after items are populated by `CalculationItemsService`.
- **Tables:** `templates`, `calculation_history`, `calculation_line_items`, `calculation_line_item_options`, `packages`, `auth.users`.

#### Packages Service (`packages.service.ts`)

- **Responsibilities:** Handles CRUD operations for packages and their dependencies.
- **Key Logic:**
  - Creating, updating, and deleting packages.
  - Managing package dependencies.
  - Fetching package variations with price/cost data.
- **Status:**
  - Initial CRUD implemented for packages.
  - Dependency management implemented.
  - **Recent Refactoring (April 2025):** Resolved outstanding ESLint errors and significantly improved type safety through explicit typing of Supabase query results and removal of unused directives.

**Module 4: Supporting Data Retrieval (Reference Data)**

- **Goal:** Populate UI dropdowns/selectors.
- **Endpoints:**
  - [x] `GET /cities` (Moved to Cities Module)
  - [x] `GET /currencies` (Moved to Currencies Module)
  - [x] `GET /categories` (Moved to Categories Module)
  - [x] `GET /clients` (Implemented in Clients Module)
  - [x] `GET /events` (Implemented in Events Module)
- **Logic:**
  - [x] Implement controllers and services for basic reads.
  - [ ] Apply filtering (active/non-deleted) - _Deferred/Optional_
  - [x] Apply access control where needed (clients, events - basic ownership implemented).
- **Tables:** `cities`, `currencies`, `categories`, `clients`, `events`.

**Module 5: Package Catalogue Browsing**

- **Goal:** Enable users to find suitable packages and options.
- **Endpoints:**
  - [x] `GET /packages/variations`:
    - [x] Define DTO for query parameters (`ListPackageVariationsDto`).
    - [x] Implement controller action.
    - [x] Implement service logic as detailed below (using Supabase client).
    - [x] Return list of variations (`PackageVariationDto`) with price/cost, basis, conflicts.
  - [x] `GET /packages/variations/{package_id}/options`:
    - [x] Define DTO for query parameters (`ListPackageOptionsDto`).
    - [x] Implement controller action.
    - [x] Implement service logic: Fetch applicable options (`PackageOptionDetailDto`) with price/cost adjustments for the currency (using Supabase client).
    - [x] Return option list.
- **`Logic (GET /variations):`**
  - [x] Query relevant tables (`packages`, `package_cities`, `package_prices`) using Supabase client.
  - [x] Apply filters (`category_id`, `city_id`).
  - [x] Query `package_dependencies` against `current_selection_ids` for conflicts.
  - [x] Fetch price & cost for the specified `currency_id` (filtering out packages without price).
- **Tables:** `packages`, `package_cities`, `package_prices`, `package_options`, `package_dependencies`.

**Module 6: Calculation Line Item Management (Completed)**

- **Goal:** Add/Remove items from a calculation, handling snapshots and calculations.
- **Endpoints:**
  - [x] `POST /calculations/{calc_id}/line-items/package`: fully implemented
    - [x] Define `AddPackageLineItemDto`.
    - [x] Implement controller action.
    - [x] Implement service logic (snapshot price/cost and calculation)
    - [x] Trigger overall calculation recalculation on add (via service calling DB function RPC)
    - [x] Return new line-item ID
  - [x] `POST /calculations/{calc_id}/line-items/custom`: fully implemented
    - [x] Define `AddCustomLineItemDto`.
    - [x] Implement controller action.
    - [x] Implement service logic (Simple insert into `calculation_custom_items`)
    - [x] Trigger overall calculation recalculation on add (via service calling DB function RPC)
    - [x] Return new item ID
  - [x] `DELETE /calculations/{calc_id}/line-items/{item_id}`: fully implemented
    - [x] Implement controller action.
    - [x] Implement service logic (Delete standard line item row(s))
    - [x] Trigger overall calculation recalculation on delete (via service calling DB function RPC)
    - [x] Return success status (204 No Content)
  - [x] `DELETE /calculations/{calc_id}/custom-items/{item_id}`: fully implemented
    - [x] Implement controller action.
    - [x] Implement service logic (Delete custom line item row)
    - [x] Trigger overall calculation recalculation on delete (via service calling DB function RPC)
    - [x] Return success status (204 No Content)
- **`Logic (POST /package):`**
  - [x] 1. Validate input & check dependencies (`package_dependencies`)
  - [x] 2. Fetch `packages.quantity_basis`
  - [x] 3. Determine `item_quantity`, `duration_days` based on basis & context
  - [x] 4. Fetch **current** price/cost data (`package_prices`, `package_options`)
  - [x] 5. **DB Operations (Supabase Client):**
    - [x] Insert `calculation_line_items` row storing all snapshots (names, prices, costs, quantity, duration, currency)
    - [x] Insert `calculation_line_item_options` rows storing price/cost adjustment snapshots
    - [x] Calculate `calculated_line_total` & `calculated_line_cost` using snapshots & multipliers. Update the line item row
    - [x] Consider Supabase Edge Function/RPC for atomicity if needed
  - [x] 6. **Commit/Execute Operations.** (Should include triggering recalc via RPC call to `recalculate_calculation_totals`)
- **Tables:** `calculation_line_items`, `calculation_line_item_options`, `calculation_custom_items`, `packages`, `package_prices`, `package_options`, `package_dependencies`, `calculation_history`.

**Module 7: Calculation Totals & Export** _(Partial — Totals Implemented)_

- **Goal:** Provide final calculated values and export functionality.
- **Endpoints:**
  - [x] `GET /calculations/{id}/totals`: implemented with breakdown DTO & service logic
    - [x] Implement controller action.
    - [x] Implement service logic as detailed below (using Supabase client).
    - [x] Return calculation breakdown DTO.
  - [x] `GET /exports/calculations/:id/csv`: Export a specific calculation as a CSV file. _(Status: Implemented, may be deprecated for POST flow)_
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client).
    - [x] Return CSV file.
  - [~] `POST /exports/calculations/:id/initiate`: Initiate export process and record history. _(Status: Implemented for CSV initiation, no storage/file return - 2025-04-21)_
    - [x] Define DTO (`InitiateExportDto`).
    - [x] Implement controller action.
    - [x] Implement service logic (`initiateCsvExport`).
    - [ ] TODO: Implement PDF generation.
    - [ ] TODO: Implement optional saving to Supabase Storage.
    - [x] Return history ID and status message.
    - [ ] **TODO:** Refactor `generateCalculationCsv` and `initiateCsvExport` to avoid duplicate `recordExportHistory` calls.
- **`Logic (GET /totals):`**
  - [x] Fetch and sum pre-calculated totals/costs from `calculation_line_items`. -- **NOTE:** Now reads pre-calculated totals directly from `calculation_history`. This endpoint may be redundant if `GET /calculations/{id}` provides sufficient total information. Verify necessity.
  - [x] Fetch and sum totals/costs from `calculation_custom_items`. -- **NOTE:** Now reads pre-calculated totals directly from `calculation_history`.
  - [x] Calculate `subtotal`, `total_cost`, `estimated_profit`. -- **NOTE:** These are now read from `calculation_history`.
- **`Logic (POST /initiate):`** // Renamed from POST /export
  - [x] Fetch calculation data (via `generateCalculationCsv` call in service).
  - [~] Generate export file (CSV done in `generateCalculationCsv`; PDF TBD).
  - [ ] Optionally save to Supabase Storage.
  - [x] Insert record into `export_history` (via `initiateCsvExport` calling `recordExportHistory`).
  - [x] Return status message and history ID.
- **Tables:** `calculation_history`, `calculation_line_items`, `calculation_custom_items`, `export_history`, `auth.users`, `clients`, `events`.

### Phase 2: Admin Features (Backend Implementation)

**Refactored Admin Modules (Previously Catalogue Management):**

- **Module: Admin - Cost Items**

  - **Goal:** Manage core cost items (materials, services, labor) used in packages.
  - **Endpoints:** CRUD for `cost_items`.
  - **Status:** `[x]` (Core CRUD implemented in `src/modules/admin/cost-items`)
  - **Tables:** `cost_items`, `currencies`, `service_categories`.

- **Module: Admin - Service Categories**

  - **Goal:** Manage categories for cost items.
  - **Endpoints:** CRUD for `service_categories`.
  - **Status:** `[x]` (Core CRUD implemented in `src/modules/admin/service-categories`)
  - **Tables:** `service_categories`.

- **Module: Admin - Packages**

  - **Goal:** Manage service packages offered.
  - **Endpoints:** CRUD for `packages`.
  - **Status:** `[x]` (Core CRUD implemented in `src/modules/admin/packages`)
  - **Tables:** `packages`, `categories`.

- **Module: Admin - Package Options**

  - **Goal:** Manage optional add-ons or variations for packages.
  - **Endpoints:** CRUD for `package_options`.
  - **Status:** `[x]` (Core CRUD implemented in `src/modules/admin/package-options`)
  - **Tables:** `package_options`, `packages`, `cost_items`.

- **Module: Admin - Package Prices**

  - **Goal:** Manage pricing for packages and options based on currency.
  - **Endpoints:** CRUD for `package_prices` (linking packages/options to prices/costs).
  - **Status:** `[x]` (Core CRUD implemented in `src/modules/admin/package-prices`)
  - **Tables:** `package_prices`, `packages`, `package_options`, `currencies`.

- **Module: Admin - Package Dependencies**

  - **Goal:** Define dependencies between packages (e.g., Package B requires Package A).
  - **Endpoints:** CRUD for `package_dependencies`.
  - **Status:** `[x]` (Core CRUD implemented in `src/modules/admin/package-dependencies`)
  - **Tables:** `package_dependencies`, `packages`.

- **Module: Admin - Package Cities**
  - **Goal:** Associate packages with specific cities where they are available.
  - **Endpoints:** Manage associations in `package_cities`.
  - **Status:** `[x]` (Core CRUD implemented in `src/modules/admin/package-cities`)
  - **Tables:** `package_cities`, `packages`, `cities`.

**Module 8: Admin - Template Management**
{{ ... }}

**Module 11: User Management (Admin)**

- **Goal:** Allow admins to manage user roles and potentially details.
- **Endpoints:**
  - [ ] `GET /admin/users`
  - [ ] `GET /admin/users/{id}`
  - [ ] `PUT /admin/users/{id}` (e.g., update role_id in `profiles`)
  - [ ] `GET /admin/roles`
- **Logic:**
  - [ ] Implement controllers/services.
  - [ ] Implement admin-only authorization guards (`AdminGuard`).
  - [ ] Interact with `profiles` table (for roles) and potentially `auth.users` via Supabase admin client if needed.
- **Tables:** `profiles`, `roles`, `auth.users`.

### Phase X: Core Module Refactoring (Calculations, Items, Exports) - April 2025

**Goal:** Ensure the `calculations`, `calculation-items`, and `exports` modules are robust, correctly implement business logic (recalculation, snapshotting), and align strictly with API contracts (`TEST_POSTMAN.md`), database schema (`Schema.md`), and this plan.

**Plan:**

1.  **Alignment Check:**

    - [x] **`calculations` Module:** Verify Controller endpoints, Service logic outputs, and DTOs against `TEST_POSTMAN.md` and `Schema.md`. (Service largely done).
    - [~] **`calculation-items` Module:**
      - [ ] Verify Controller endpoints (add/update/delete items/options) against `TEST_POSTMAN.md`.
      - [~] **Critically Review Service:**
        - [ ] **Snapshot Verification:**
          - [ ] Confirm `addPackageLineItem` correctly snapshots all required fields (`item_name_snapshot`, `option_summary_snapshot`, `price`, `options_total_adjustment`, `unit_base_cost_snapshot`, `options_total_cost_snapshot`) from current package/option data.
          - [ ] Confirm `calculation_line_item_options` correctly snapshots `price_adjustment_snapshot` (and `cost_adjustment_snapshot` if added to schema) when added via `addPackageLineItem`.
          - [ ] Define and verify snapshot behavior for `updatePackageLineItem` (if applicable - does it re-snapshot?).
        - [ ] **Recalculation Trigger Verification:**
          - [ ] Ensure `recalculate_calculation_totals` RPC is reliably called after successful completion of:
            - `addPackageLineItem` (including options insert)
            - `addCustomLineItem`
            - `deletePackageLineItem`
            - `deleteCustomLineItem`
            - `updatePackageLineItem` (if applicable)
            - `updateLineItemOptions` (if applicable)
          - [ ] Ensure transaction management is used for item/option CUD + recalculation RPC call.
      - [ ] Verify DTOs against docs and add validation.
    - [ ] **`exports` Module:**
      - [ ] Verify/Define Controller endpoints (`GET /exports/{id}`, `POST /calculations/{id}/export`).
      - [ ] Review Service logic: Ensure it fetches correct `CalculationDetailDto` from `calculations.service`; verify export generation logic (CSV/PDF).
      - [ ] Verify/Define DTOs (`ExportCalculationDto`, etc).

2.  **Refactoring Implementation:**

    - [~] **`calculation-items.service.ts`:** Implement fixes/improvements identified in the alignment check (snapshotting verification, recalculation trigger calls, transaction handling). -- **(In Progress)**
    - [ ] **Controllers & DTOs (All Modules):** Align routes, params, responses, validation based on reviews. -- _(Add notes in relevant modules if specific changes needed)_
    - [ ] **`exports.service.ts`:** Refactor data fetching and implement robust export generation (CSV, PDF). -- _(Moved to Module 7 TODO)_

3.  **Testing & Documentation:**
    - [ ] . -- _(Covered by Ongoing E2E Testing)_
    - [ ] Update this plan (`BACKEND_PLAN.md`) with progress. -- _(Covered by Ongoing Documentation)_
    - [ ] Update Swagger documentation. -- _(Moved to Phase 3)_

### Phase 3: Refinement & Deployment

- [ ] **API Documentation (Swagger):**
  - [ ] Ensure all endpoints and DTOs are fully documented using `@nestjs/swagger`.
  - [ ] Verify `/api` documentation is accurate and usable.
- [ ] **Security Review:**
  - [ ] Check Input Validation coverage (ensure `class-validator` used thoroughly).
  - [ ] Verify Authorization logic (Guards) on all protected endpoints (`JwtAuthGuard`, `AdminGuard`).
  - [ ] Check for potential SQL injection vectors (though Supabase client helps mitigate).
  - [ ] Review Supabase RLS policies (if used alongside service role key) for data access control.
  - [ ] Run `npm audit` and address vulnerabilities.
- [ ] **Performance Optimization:**
  - [ ] Review database queries for efficiency (use `EXPLAIN` in Supabase SQL Editor for key queries like `findCalculationById`, `findUserCalculations`, `findPackageVariations`).
  - [ ] Identify potential bottlenecks under load (e.g., complex calculations, large lists).
  - [ ] Consider adding database indexes based on query analysis.
- [ ] **Deployment Preparation:**
  - [ ] Create build scripts (`npm run build`).
  - [ ] Prepare production `.env` file (secure handling of keys).
  - [ ] Document deployment process for chosen provider(s).
  - [ ] Configure logging for production (e.g., `nest-winston` with appropriate transports).
- [ ] **Deployment:**
  - [ ] Deploy to chosen hosting provider (e.g., Fly.io, Render, AWS EC2/ECS/Lambda, Vercel Serverless Functions).
  - [ ] Set up monitoring and alerting.
- [ ] **Ongoing Maintenance:**
  - [ ] Address bug fixes as they arise.
  - [ ] Perform regular dependency updates.

---

## Module Implementation Details

{{ ... }}

**Module 3: Template Handling (User Side)**

- **Goal:** Allow users to start calculations from templates.
- **Endpoints:**
  - [x] `GET /templates`: (Implemented in Templates Module)
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client/RPC).
    - [x] Return template list.
  - [x] `POST /calculations/from-template/{template_id}`: (Implemented in Calculations Module)
    - [x] Implement controller action.
    - [x] Implement service logic (using Supabase client, calls CalculationItemsService).
    - [x] Return new `calculation_id`.
- **`Logic (POST /from-template):`**
  - [x] 1. Verify user access to `template_id` (Implemented basic check: public or owner).
  - [x] 2. Fetch template data (`templates` table), including `package_selections` blueprint.
  - [x] 3. Create `calculation_history` record (copy fields, set user, status='draft', currency=IDR, dates).
  - [x] 4. Parse blueprint JSON. (Logic in CalculationItemsService)
    - [x] Fetch current package/option details (`packages`, `package_prices`, `package_options`) including **price and cost** for the calculation's currency.
    - [x] Determine effective `item_quantity` and `duration_days` based on `packages.quantity_basis` and context (Implemented basic cases).
    - [x] **DB Operations:** Use Supabase client to insert `calculation_line_items` with snapshots, insert `calculation_line_item_options` with snapshots, calculate & store line totals/costs.
  - [x] 5. Trigger overall total recalculation for the new `calculation_history`. - Implemented via RPC call in `CalculationsService.createFromTemplate` after items are populated by `CalculationItemsService`.
- **Tables:** `templates`, `calculation_history`, `calculation_line_items`, `calculation_line_item_options`, `packages`, `auth.users`.

- **Status:** Implemented (Requires Testing - Note: Resolved TS/ESLint typing issues with Supabase RPC call using workarounds like `@ts-expect-error`)

```

```
