import {
  useOptimizedCalculationDetail,
  OptimizedCalculationDetailConfig,
} from "./useOptimizedCalculationDetail";

/**
 * Main hook for calculation detail page
 * PHASE 4 OPTIMIZATION: Simplified hook chain, removed legacy branching
 * Uses optimized parallel loading for improved performance
 *
 * @param id - The ID of the calculation
 * @param config - Configuration options for optimization
 * @returns Combined state and functions for the calculation detail page
 */
export const useCalculationDetail = (
  id: string,
  config: OptimizedCalculationDetailConfig = {}
) => {
  return useOptimizedCalculationDetail(id, config);
};
