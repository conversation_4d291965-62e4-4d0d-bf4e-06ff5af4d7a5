import * as React from 'react';
import { cn } from '@/lib/utils';
import { Check, X, ChevronsUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';

export type Option = {
  value: string;
  label: string;
};

interface MultiSelectProps {
  options: Option[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const MultiSelect = React.forwardRef<HTMLDivElement, MultiSelectProps>(
  (
    {
      options,
      selected,
      onChange,
      placeholder = 'Select options',
      className,
      disabled = false,
    },
    ref,
  ) => {
    const [open, setOpen] = React.useState(false);

    // Ensure selected is always an array
    const selectedValues = Array.isArray(selected) ? selected : [];

    const handleUnselect = (value: string) => {
      onChange(selectedValues.filter((item) => item !== value));
    };

    const handleSelect = (value: string) => {
      if (selectedValues.includes(value)) {
        onChange(selectedValues.filter((item) => item !== value));
      } else {
        onChange([...selectedValues, value]);
      }
    };

    return (
      <div ref={ref}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              role='combobox'
              aria-expanded={open}
              className={cn(
                'w-full justify-between',
                selectedValues.length > 0 ? 'h-auto' : 'h-10',
                className,
              )}
              onClick={() => setOpen(!open)}
              disabled={disabled}
            >
              <div className='flex flex-wrap gap-1'>
                {selectedValues.length > 0 ? (
                  selectedValues.map((value) => (
                    <Badge
                      variant='secondary'
                      key={value}
                      className='mr-1 mb-1'
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUnselect(value);
                      }}
                    >
                      {options.find((option) => option.value === value)?.label || value}
                      <Button
                        className='ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleUnselect(value);
                          }
                        }}
                        onMouseDown={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleUnselect(value);
                        }}
                      >
                        <X className='h-3 w-3 text-muted-foreground hover:text-foreground' />
                      </Button>
                    </Badge>
                  ))
                ) : (
                  <span className='text-muted-foreground'>{placeholder}</span>
                )}
              </div>
              <ChevronsUpDown className='h-4 w-4 shrink-0 opacity-50' />
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-full p-0'>
            <Command>
              <CommandInput placeholder='Search options...' />
              <CommandEmpty>No options found.</CommandEmpty>
              <CommandGroup className='max-h-64 overflow-auto'>
                {options.map((option) => (
                  <CommandItem
                    key={option.value}
                    onSelect={() => handleSelect(option.value)}
                    className='flex items-center gap-2'
                  >
                    <div
                      className={cn(
                        'flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        selectedValues.includes(option.value)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}
                    >
                      <Check className='h-3 w-3' />
                    </div>
                    <span>{option.label}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    );
  },
);

MultiSelect.displayName = 'MultiSelect';
