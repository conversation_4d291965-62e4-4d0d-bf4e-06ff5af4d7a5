import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import NavigationCard from '@/pages/admin/dashboard/components/NavigationCard';
import { Package, Grid3X3, Building2, MapPin, Landmark } from 'lucide-react';

const CataloguePage: React.FC = () => {
  return (
    <AdminLayout title='Catalogue Management'>
      <div className='text-muted-foreground mb-6'>
        Manage your service packages, categories, divisions, and locations from this
        central hub.
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        <NavigationCard
          title='Manage Packages'
          description='Create, edit, and organize service packages available to your clients'
          icon={<Package className='w-6 h-6' />}
          to='/admin/packages'
        />

        <NavigationCard
          title='Manage Categories'
          description='Organize packages into logical categories for easier navigation'
          icon={<Grid3X3 className='w-6 h-6' />}
          to='/admin/categories'
        />

        <NavigationCard
          title='Manage Divisions'
          description='Setup business divisions that offer different types of services'
          icon={<Building2 className='w-6 h-6' />}
          to='/admin/divisions'
        />

        <NavigationCard
          title='Manage Cities'
          description='Configure cities and locations where your services are available'
          icon={<MapPin className='w-6 h-6' />}
          to='/admin/cities'
        />

        <NavigationCard
          title='Manage Venues'
          description='Configure venues where your services are available'
          icon={<Landmark className='w-6 h-6' />}
          to='/admin/venues'
        />
      </div>
    </AdminLayout>
  );
};

export default CataloguePage;
