import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';

// Renamed to avoid conflicts with admin module
@ApiExtraModels()
export class CategoryPackageOptionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  option_name: string;

  @ApiProperty({ required: false })
  description: string;

  @ApiProperty()
  price_adjustment: number;

  @ApiProperty()
  cost_adjustment: number;

  @ApiProperty()
  is_default_for_package: boolean;

  @ApiProperty()
  is_required: boolean;
}

// Renamed to avoid conflicts with admin module
@ApiExtraModels()
export class CategoryPackageDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  description: string;

  @ApiProperty()
  quantity_basis: string;

  @ApiProperty()
  price: number;

  @ApiProperty()
  unit_base_cost: number;

  @ApiProperty({ required: false, type: [CategoryPackageOptionDto] })
  options?: CategoryPackageOptionDto[];
}

export class CategoryWithPackagesDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  display_order: number;

  @ApiProperty({ type: [CategoryPackageDto] })
  packages: CategoryPackageDto[];
}

export class PackagesByCategoryResponseDto {
  @ApiProperty({ type: [CategoryWithPackagesDto] })
  categories: CategoryWithPackagesDto[];
}

