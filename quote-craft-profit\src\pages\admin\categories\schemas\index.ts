import { z } from 'zod';
import { formFieldPatterns } from '@/schemas/common';

/**
 * Category management validation schemas
 * Centralized schemas for all category forms and components
 */

// Category form schema (extracted from CategoryFormDialog.tsx)
export const categoryFormSchema = z.object({
  name: formFieldPatterns.name('Category name'),
  code: formFieldPatterns.code('Category code'),
  description: formFieldPatterns.description(),
  icon: z.string().optional(),
});

// Type exports for TypeScript inference
export type CategoryFormValues = z.infer<typeof categoryFormSchema>;
