/**
 * Profile Feature
 * 
 * This module provides user profile management functionality including
 * personal information editing, password changes, and profile picture management.
 */

// Export main page component
export { default as ProfilePage } from './ProfilePage';

// Export components
export * from './components';

// Export hooks (when created)
// export * from './hooks';

// Export utils (when created)
// export * from './utils';

// Export constants (when created)
// export * from './constants';
