import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { CustomItem, CustomItemInput } from '@/types/customItems';
import { CustomItemsService } from '@/services/calculations/customItemsService';
import { QUERY_KEYS } from '@/lib/queryKeys';

/**
 * Hook for managing custom items in calculations
 */
export function useCustomItems(calculationId: string) {
  const queryClient = useQueryClient();

  // Query for fetching custom items
  const {
    data: customItems = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: QUERY_KEYS.customItems(calculationId),
    queryFn: () => CustomItemsService.getCustomItems(calculationId),
    enabled: !!calculationId,
  });

  // Mutation for adding custom items
  const addCustomItemMutation = useMutation({
    mutationFn: (customItem: CustomItemInput) =>
      CustomItemsService.addCustomItem(calculationId, customItem),
    onSuccess: async (data, variables) => {
      console.log('Custom item added successfully:', data);
      // Don't show notification here - let the calling component handle it to avoid duplicates

      // Invalidate and refetch all related queries immediately
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.customItems(calculationId),
        }),
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.lineItems(calculationId),
        }),
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculation(calculationId),
        }),
      ]);

      // Force refetch line items to ensure immediate UI update
      await queryClient.refetchQueries({
        queryKey: QUERY_KEYS.lineItems(calculationId),
      });
    },
    onError: (error) => {
      console.error('Error adding custom item:', error);
      toast.error('Failed to add custom item. Please try again.');
    },
  });

  // Mutation for updating custom items
  const updateCustomItemMutation = useMutation({
    mutationFn: ({ itemId, updates }: { itemId: string; updates: Partial<CustomItemInput> }) =>
      CustomItemsService.updateCustomItem(calculationId, itemId, updates),
    onSuccess: async (data, variables) => {
      console.log('Custom item updated successfully:', data);
      toast.success('Custom item updated successfully');

      // Invalidate and refetch all related queries immediately
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.customItems(calculationId),
        }),
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.lineItems(calculationId),
        }),
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculation(calculationId),
        }),
      ]);

      // Force refetch line items to ensure immediate UI update
      await queryClient.refetchQueries({
        queryKey: QUERY_KEYS.lineItems(calculationId),
      });
    },
    onError: (error) => {
      console.error('Error updating custom item:', error);
      toast.error('Failed to update custom item. Please try again.');
    },
  });

  // Mutation for deleting custom items
  const deleteCustomItemMutation = useMutation({
    mutationFn: (itemId: string) =>
      CustomItemsService.deleteCustomItem(calculationId, itemId),
    onSuccess: async (data, variables) => {
      console.log('Custom item deleted successfully:', data);
      toast.success('Custom item deleted successfully');

      // Invalidate and refetch all related queries immediately
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.customItems(calculationId),
        }),
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.lineItems(calculationId),
        }),
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculation(calculationId),
        }),
      ]);

      // Force refetch line items to ensure immediate UI update
      await queryClient.refetchQueries({
        queryKey: QUERY_KEYS.lineItems(calculationId),
      });
    },
    onError: (error) => {
      console.error('Error deleting custom item:', error);
      toast.error('Failed to delete custom item. Please try again.');
    },
  });

  return {
    // Data
    customItems,
    isLoading,
    isError,
    error,

    // Mutations
    addCustomItem: addCustomItemMutation.mutate,
    updateCustomItem: updateCustomItemMutation.mutate,
    deleteCustomItem: deleteCustomItemMutation.mutate,

    // Mutation states
    isAddingCustomItem: addCustomItemMutation.isPending,
    isUpdatingCustomItem: updateCustomItemMutation.isPending,
    isDeletingCustomItem: deleteCustomItemMutation.isPending,
  };
}

/**
 * Hook for fetching a single custom item
 */
export function useCustomItem(calculationId: string, itemId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.customItem(calculationId, itemId),
    queryFn: () => CustomItemsService.getCustomItemById(calculationId, itemId),
    enabled: !!calculationId && !!itemId,
  });
}
