import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ExportFormat } from '../enums/export-format.enum';

export class CreateExportDto {
  @ApiProperty({
    description: 'The ID of the calculation history record to export.',
    format: 'uuid',
  })
  @IsUUID()
  @IsNotEmpty()
  calculationId: string;

  @ApiProperty({
    description: 'The desired export format.',
    enum: ExportFormat,
    example: ExportFormat.PDF,
  })
  @IsEnum(ExportFormat)
  @IsNotEmpty()
  format: ExportFormat;

  @ApiPropertyOptional({
    description: 'Optional email address to send the export to.',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  // Add @IsEmail() if strict validation is needed
  recipient?: string;
}
