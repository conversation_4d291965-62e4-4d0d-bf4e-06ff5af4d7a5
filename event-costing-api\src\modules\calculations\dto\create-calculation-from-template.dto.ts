import {
  IsString,
  IsOptional,
  IsDateString,
  <PERSON>Int,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCalculationFromTemplateDto {
  @ApiProperty({
    description: 'Name for the new calculation',
    example: 'Corporate Event 2025',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: 'Event start date (YYYY-MM-DD)',
    format: 'date',
    example: '2025-06-15',
  })
  @IsOptional()
  @IsDateString()
  eventStartDate?: string;

  @ApiPropertyOptional({
    description: 'Event end date (YYYY-MM-DD)',
    format: 'date',
    example: '2025-06-17',
  })
  @IsOptional()
  @IsDateString()
  eventEndDate?: string;

  @ApiPropertyOptional({
    description: 'Number of attendees',
    type: Number,
    minimum: 1,
    example: 150,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  attendees?: number;

  @ApiPropertyOptional({
    description: 'Client ID to associate with the calculation',
    format: 'uuid',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsOptional()
  @IsUUID('4')
  clientId?: string;

  @ApiPropertyOptional({
    description: 'Event ID to associate with the calculation',
    format: 'uuid',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsOptional()
  @IsUUID('4')
  eventId?: string;

  @ApiPropertyOptional({
    description: 'City ID for venue selection',
    format: 'uuid',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsOptional()
  @IsUUID('4')
  cityId?: string;

  @ApiPropertyOptional({
    description: 'Array of venue IDs to associate with the calculation',
    type: [String],
    format: 'uuid',
    example: ['a1b2c3d4-e5f6-7890-1234-567890abcdef'],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  venueIds?: string[];

  @ApiPropertyOptional({
    description: 'Additional notes for the calculation',
    maxLength: 1000,
    example: 'Based on template with custom modifications',
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
}
