/**
 * Package Option API Service
 *
 * This service provides methods for interacting with the package options API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import {
  PackageOption,
  PackageOptionDisplay,
  SavePackageOptionData,
} from '../../../pages/admin/packages/types/packageOptions';
import { apiClient, getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';

/**
 * Get all options for a package from the backend API
 * @param packageId - The package ID
 * @param currencyId - Optional currency ID
 * @param venueId - Optional venue ID
 * @returns Promise resolving to an array of package options
 */
export const getPackageOptionsFromApi = async (
  packageId: string,
  currencyId?: string,
  venueId?: string,
): Promise<PackageOptionDisplay[]> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Build query parameters for pagination and filters
    const queryParams = new URLSearchParams();
    queryParams.append('limit', '100'); // Get all options (adjust if needed)
    queryParams.append('offset', '0');

    if (currencyId) {
      queryParams.append('currencyId', currencyId);
    }
    if (venueId) {
      queryParams.append('venueId', venueId);
    }

    // Make API request with query parameters
    const url = API_ENDPOINTS.PACKAGES.OPTIONS.GET_ALL(packageId);
    const fullUrl = `${url}?${queryParams.toString()}`;

    const response = await authClient.get(fullUrl);

    // The backend returns a paginated response: { data: PackageOptionDto[], count, limit, offset }
    // Extract the data array and transform it to match frontend expectations
    const backendData = response.data?.data || [];

    return backendData.map((option: any) => ({
      ...option,
      currency_code: 'IDR', // Default currency code since we only have IDR
      profit_margin: option.price_adjustment - option.cost_adjustment,
      profit_percentage:
        option.price_adjustment > 0
          ? ((option.price_adjustment - option.cost_adjustment) /
              option.price_adjustment) *
            100
          : 0,
    }));
  } catch (error) {
    console.error(
      `Error fetching options for package ID ${packageId} from backend API:`,
      error,
    );
    // Return empty array instead of throwing to prevent UI errors
    return [];
  }
};

/**
 * Get a single package option by ID from the backend API
 * @param packageId - The package ID
 * @param optionId - The option ID
 * @returns Promise resolving to a package option or null
 */
export const getPackageOptionByIdFromApi = async (
  packageId: string,
  optionId: string,
): Promise<PackageOptionDisplay | null> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(
      API_ENDPOINTS.PACKAGES.OPTIONS.GET_BY_ID(packageId, optionId),
    );

    const option = response.data;
    if (!option) {
      return null;
    }

    // Transform the backend response to match frontend expectations
    return {
      ...option,
      currency_code: 'IDR', // Default currency code since we only have IDR
      profit_margin: option.price_adjustment - option.cost_adjustment,
      profit_percentage:
        option.price_adjustment > 0
          ? ((option.price_adjustment - option.cost_adjustment) /
              option.price_adjustment) *
            100
          : 0,
    };
  } catch (error) {
    console.error(`Error fetching option with ID ${optionId} from backend API:`, error);
    return null;
  }
};

/**
 * Create or update a package option using the backend API
 * @param optionData - The option data to save
 * @returns Promise resolving to the saved option
 */
export const savePackageOptionWithApi = async (
  optionData: SavePackageOptionData,
): Promise<PackageOption> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    if (optionData.id) {
      // Update existing option
      const response = await authClient.patch(
        API_ENDPOINTS.PACKAGES.OPTIONS.UPDATE(
          optionData.applicable_package_id,
          optionData.id,
        ),
        optionData,
      );

      return response.data;
    } else {
      // Create new option
      const response = await authClient.post(
        API_ENDPOINTS.PACKAGES.OPTIONS.CREATE(optionData.applicable_package_id),
        optionData,
      );

      return response.data;
    }
  } catch (error) {
    console.error('Error saving package option with backend API:', error);
    throw error;
  }
};

/**
 * Delete a package option using the backend API
 * @param packageId - The package ID
 * @param optionId - The option ID to delete
 * @returns Promise resolving to void
 */
export const deletePackageOptionWithApi = async (
  packageId: string,
  optionId: string,
): Promise<void> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.delete(API_ENDPOINTS.PACKAGES.OPTIONS.DELETE(packageId, optionId));
  } catch (error) {
    console.error(`Error deleting option with ID ${optionId} using backend API:`, error);
    throw error;
  }
};
