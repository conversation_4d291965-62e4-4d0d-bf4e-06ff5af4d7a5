/**
 * Comprehensive Timezone Management Utilities
 *
 * This module provides timezone-aware date handling to fix the "picked 19th but submitting 18th" issue
 * by ensuring all date operations respect the user's selected timezone.
 */

import { format, parseISO, formatISO } from "date-fns";
import { toZonedTime, fromZonedTime } from "date-fns-tz";

// Default timezone for Indonesian users
export const DEFAULT_TIMEZONE = "Asia/Jakarta"; // UTC+7

// Common timezone options for the application
export const TIMEZONE_OPTIONS = [
  { value: "Asia/Jakarta", label: "Jakarta (UTC+7)", offset: "+07:00" },
  { value: "Asia/Singapore", label: "Singapore (UTC+8)", offset: "+08:00" },
  {
    value: "Asia/Kuala_Lumpur",
    label: "Kuala Lumpur (UTC+8)",
    offset: "+08:00",
  },
  { value: "Asia/Bangkok", label: "Bangkok (UTC+7)", offset: "+07:00" },
  { value: "Asia/Manila", label: "Manila (UTC+8)", offset: "+08:00" },
  { value: "Asia/Tokyo", label: "Tokyo (UTC+9)", offset: "+09:00" },
  { value: "Asia/Seoul", label: "Seoul (UTC+9)", offset: "+09:00" },
  { value: "Asia/Hong_Kong", label: "Hong Kong (UTC+8)", offset: "+08:00" },
  { value: "Asia/Shanghai", label: "Shanghai (UTC+8)", offset: "+08:00" },
  { value: "Asia/Dubai", label: "Dubai (UTC+4)", offset: "+04:00" },
  { value: "Europe/London", label: "London (UTC+0/+1)", offset: "+00:00" },
  { value: "Europe/Paris", label: "Paris (UTC+1/+2)", offset: "+01:00" },
  { value: "America/New_York", label: "New York (UTC-5/-4)", offset: "-05:00" },
  {
    value: "America/Los_Angeles",
    label: "Los Angeles (UTC-8/-7)",
    offset: "-08:00",
  },
  { value: "Australia/Sydney", label: "Sydney (UTC+10/+11)", offset: "+10:00" },
  { value: "UTC", label: "UTC (Coordinated Universal Time)", offset: "+00:00" },
];

/**
 * Get the user's timezone from their preferences
 * Falls back to default timezone if not set
 */
export const getUserTimezone = (userPreferences?: any): string => {
  return userPreferences?.timezone || DEFAULT_TIMEZONE;
};

/**
 * Convert a Date object to YYYY-MM-DD format in the user's timezone
 * This prevents the "picked 19th but submitting 18th" issue
 */
export const formatDateForSubmission = (
  date: Date,
  timezone: string = DEFAULT_TIMEZONE
): string => {
  try {
    // Convert the date to the user's timezone first, then format as YYYY-MM-DD
    const zonedDate = toZonedTime(date, timezone);
    return format(zonedDate, "yyyy-MM-dd");
  } catch (error) {
    console.error("Error formatting date for submission:", error);
    // Fallback to local date string
    return date.toLocaleDateString("en-CA"); // en-CA gives YYYY-MM-DD format
  }
};

/**
 * Convert a Date object to ISO string in the user's timezone
 * Used for datetime fields that need full timestamp
 */
export const formatDateTimeForSubmission = (
  date: Date,
  timezone: string = DEFAULT_TIMEZONE
): string => {
  try {
    // Convert to user's timezone, then to UTC for storage
    const zonedDate = fromZonedTime(date, timezone);
    return formatISO(zonedDate);
  } catch (error) {
    console.error("Error formatting datetime for submission:", error);
    return date.toISOString();
  }
};

/**
 * Parse a date string and convert it to a Date object in the user's timezone
 * Used for displaying dates from the database
 */
export const parseDateFromDatabase = (
  dateString: string,
  timezone: string = DEFAULT_TIMEZONE
): Date => {
  try {
    if (!dateString) return new Date();

    // If it's just a date (YYYY-MM-DD), treat it as a date in the user's timezone
    if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      // Create a date at midnight in the user's timezone
      const localDate = new Date(`${dateString}T00:00:00`);
      return toZonedTime(localDate, timezone);
    }

    // If it's a full ISO string, parse and convert to user's timezone
    const utcDate = parseISO(dateString);
    return toZonedTime(utcDate, timezone);
  } catch (error) {
    console.error("Error parsing date from database:", error);
    return new Date();
  }
};

/**
 * Format a date for display in the user's timezone
 */
export const formatDateForDisplay = (
  dateString: string,
  timezone: string = DEFAULT_TIMEZONE,
  formatPattern: string = "dd MMM yyyy"
): string => {
  try {
    const date = parseDateFromDatabase(dateString, timezone);
    return format(date, formatPattern);
  } catch (error) {
    console.error("Error formatting date for display:", error);
    return "Invalid date";
  }
};

/**
 * Get the current date in the user's timezone as YYYY-MM-DD
 */
export const getCurrentDateInTimezone = (
  timezone: string = DEFAULT_TIMEZONE
): string => {
  const now = new Date();
  return formatDateForSubmission(now, timezone);
};

/**
 * Check if a date string is in the past relative to the user's timezone
 */
export const isDateInPast = (
  dateString: string,
  timezone: string = DEFAULT_TIMEZONE
): boolean => {
  try {
    const date = parseDateFromDatabase(dateString, timezone);
    const today = new Date();
    const todayInTimezone = toZonedTime(today, timezone);

    // Set time to start of day for comparison
    date.setHours(0, 0, 0, 0);
    todayInTimezone.setHours(0, 0, 0, 0);

    return date < todayInTimezone;
  } catch (error) {
    console.error("Error checking if date is in past:", error);
    return false;
  }
};

/**
 * Convert DateRange to separate date strings in user's timezone
 * Used for form submissions
 */
export const convertDateRangeForSubmission = (
  dateRange: { from?: Date; to?: Date },
  timezone: string = DEFAULT_TIMEZONE
): { startDate: string; endDate: string } => {
  return {
    startDate: dateRange.from
      ? formatDateForSubmission(dateRange.from, timezone)
      : "",
    endDate: dateRange.to
      ? formatDateForSubmission(dateRange.to, timezone)
      : "",
  };
};

/**
 * Convert separate date strings from database to DateRange in user's timezone
 * Used for form initialization
 */
export const convertDatabaseDatesToRange = (
  startDate: string,
  endDate: string,
  timezone: string = DEFAULT_TIMEZONE
): { from?: Date; to?: Date } => {
  return {
    from: startDate ? parseDateFromDatabase(startDate, timezone) : undefined,
    to: endDate ? parseDateFromDatabase(endDate, timezone) : undefined,
  };
};

/**
 * Validate timezone string
 */
export const isValidTimezone = (timezone: string): boolean => {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Get timezone offset string (e.g., "+07:00")
 */
export const getTimezoneOffset = (timezone: string): string => {
  try {
    const now = new Date();
    const utcDate = new Date(now.toLocaleString("en-US", { timeZone: "UTC" }));
    const tzDate = new Date(
      now.toLocaleString("en-US", { timeZone: timezone })
    );
    const offset = (tzDate.getTime() - utcDate.getTime()) / (1000 * 60 * 60);

    const sign = offset >= 0 ? "+" : "-";
    const hours = Math.floor(Math.abs(offset));
    const minutes = Math.round((Math.abs(offset) - hours) * 60);

    return `${sign}${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;
  } catch (error) {
    console.error("Error getting timezone offset:", error);
    return "+00:00";
  }
};

/**
 * Transform separate date strings to DateRange (timezone-aware)
 * Used when loading data from API to populate date range picker
 */
export const transformSeparateDatesToDateRange = (
  startDate?: string | null,
  endDate?: string | null,
  timezone: string = DEFAULT_TIMEZONE
): { from?: Date; to?: Date } => {
  if (!startDate && !endDate) return {};

  return {
    from: startDate ? parseDateFromDatabase(startDate, timezone) : undefined,
    to: endDate ? parseDateFromDatabase(endDate, timezone) : undefined,
  };
};

/**
 * Transform separate datetime strings to DateRange (timezone-aware)
 * Used when loading data from API to populate date range picker
 */
export const transformSeparateDatetimesToDateRange = (
  startDatetime?: string | null,
  endDatetime?: string | null,
  timezone: string = DEFAULT_TIMEZONE
): { from?: Date; to?: Date } => {
  if (!startDatetime && !endDatetime) return {};

  return {
    from: startDatetime
      ? parseDateFromDatabase(startDatetime, timezone)
      : undefined,
    to: endDatetime ? parseDateFromDatabase(endDatetime, timezone) : undefined,
  };
};

/**
 * Validate that a date range is valid (start date is not after end date)
 */
export const validateDateRange = (dateRange?: {
  from?: Date;
  to?: Date;
}): boolean => {
  if (!dateRange?.from || !dateRange?.to) return true; // Allow partial ranges
  return dateRange.from <= dateRange.to;
};

/**
 * Get a human-readable error message for invalid date ranges
 */
export const getDateRangeErrorMessage = (dateRange?: {
  from?: Date;
  to?: Date;
}): string | null => {
  if (!dateRange?.from || !dateRange?.to) return null;
  if (dateRange.from > dateRange.to) {
    return "Start date cannot be after end date";
  }
  return null;
};

/**
 * Check if a date range is complete (has both start and end dates)
 */
export const isDateRangeComplete = (dateRange?: {
  from?: Date;
  to?: Date;
}): boolean => {
  return !!(dateRange?.from && dateRange?.to);
};

/**
 * Format a date range for display with timezone awareness
 */
export const formatDateRangeForDisplay = (
  dateRange?: { from?: Date; to?: Date },
  timezone: string = DEFAULT_TIMEZONE,
  formatPattern: string = "MMM d, yyyy"
): string => {
  if (!dateRange?.from && !dateRange?.to) return "No dates selected";
  if (dateRange?.from && !dateRange?.to) {
    // Convert to user's timezone for display
    const zonedDate = toZonedTime(dateRange.from, timezone);
    const fromFormatted = format(zonedDate, formatPattern);
    return `${fromFormatted} - (select end date)`;
  }
  if (!dateRange?.from && dateRange?.to) {
    // Convert to user's timezone for display
    const zonedDate = toZonedTime(dateRange.to, timezone);
    const toFormatted = format(zonedDate, formatPattern);
    return `(select start date) - ${toFormatted}`;
  }
  if (dateRange?.from && dateRange?.to) {
    // Convert both dates to user's timezone for display
    const zonedFromDate = toZonedTime(dateRange.from, timezone);
    const zonedToDate = toZonedTime(dateRange.to, timezone);
    const fromFormatted = format(zonedFromDate, formatPattern);
    const toFormatted = format(zonedToDate, formatPattern);
    return `${fromFormatted} - ${toFormatted}`;
  }
  return "Invalid date range";
};
