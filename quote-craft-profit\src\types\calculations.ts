// API calculation response types
export interface ApiCalculation {
  id: string;
  name: string;
  status: "draft" | "completed" | "canceled";
  attendees: number;
  event_start_date: string;
  event_end_date: string;
  total: number;
  total_cost: number;
  estimated_profit: number;
  currency_id: string;
  created_at: string;
  updated_at: string;
  currency?: {
    id: string;
    code: string;
    symbol: string;
  };
  city?: {
    id: string;
    name: string;
  };
  client?: {
    id: string;
    name: string;
  } | null;
  event?: {
    id: string;
    name: string;
  } | null;
  venues?: {
    id: string;
    name: string;
  }[];
}

export interface ApiCalculationResponse {
  data: ApiCalculation[];
  count: number;
}

// Form data for creating/updating calculations
export interface CalculationFormData {
  name: string;
  currency_id: string;
  city_id: string;
  venue_ids: string[]; // Changed to array for multiple venues
  event_start_date: string; // ISO 8601 datetime format
  event_end_date: string; // ISO 8601 datetime format
  attendees: number;
  event_type_id?: string; // Updated to use event_type_id
  notes?: string;
  status: "draft" | "completed" | "canceled";
  client_id?: string;
  event_id?: string;
}

// Data for updating an existing calculation
export interface CalculationUpdateData {
  name?: string;
  event_start_date?: string;
  event_end_date?: string;
  event_type_id?: string; // Updated to use event_type_id
  notes?: string;
  status?: "draft" | "completed" | "canceled";
  attendees?: number;
  venue_ids?: string[]; // Added venue_ids for updating venues
  taxes?: any[]; // Array of tax objects
  discount?: any; // Discount object
}

// Detailed calculation data structure
export interface CalculationDetails {
  id: string;
  name: string;
  currency: {
    id: string;
    code: string;
  };
  city: {
    id: string;
    name: string;
  };
  venues: {
    id: string;
    name: string;
  }[];
  event_start_date: string;
  event_end_date: string;
  attendees: number;
  event_type_id: string | null; // Updated to use event_type_id
  notes?: string;
  status: "draft" | "completed" | "canceled";
  client?: {
    id: string;
    client_name: string;
  };
  event?: {
    id: string;
    event_name: string;
  };
  taxes?: any[]; // Array of tax objects
  discount?: any; // Discount object
  total?: number; // Total calculation value
  line_items?: Array<{
    id: string;
    name: string;
    isCustom?: boolean;
  }>; // Package-based line items
  custom_items?: Array<{
    id: string;
    item_name: string;
    description?: string;
  }>; // Custom line items
}
