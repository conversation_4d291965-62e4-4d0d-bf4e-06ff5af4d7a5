import React from 'react';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';

interface DeleteActionProps {
  isConfirmingDelete: boolean;
  isProcessing: boolean;
  onStartDelete: () => void;
  onCancelDelete: () => void;
  onConfirmDelete: () => void;
}

/**
 * Delete action component with confirmation flow
 * Handles the delete confirmation state and provides appropriate buttons
 */
export const DeleteAction: React.FC<DeleteActionProps> = ({
  isConfirmingDelete,
  isProcessing,
  onStartDelete,
  onCancelDelete,
  onConfirmDelete,
}) => {
  if (isConfirmingDelete) {
    return (
      <div className='flex space-x-2'>
        <span className='text-sm text-gray-600 mr-2 self-center'>
          Confirm delete?
        </span>
        <Button
          variant='outline'
          size='sm'
          onClick={onCancelDelete}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          variant='destructive'
          size='sm'
          onClick={onConfirmDelete}
          disabled={isProcessing}
        >
          {isProcessing ? 'Deleting...' : 'Delete'}
        </Button>
      </div>
    );
  }

  return (
    <Button
      variant='outline'
      size='sm'
      onClick={onStartDelete}
      className='text-red-500 hover:text-red-700 hover:bg-red-50'
      disabled={isProcessing}
    >
      <Trash2 size={16} className='mr-1' /> Delete
    </Button>
  );
};
