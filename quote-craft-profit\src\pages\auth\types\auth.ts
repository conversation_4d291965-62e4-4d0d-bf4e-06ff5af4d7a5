/**
 * Types for auth feature
 */

import { User, Session } from '@supabase/supabase-js';

/**
 * Authentication context interface
 */
export interface AuthContextType {
  session: Session | null;
  user: User | null;
  isAdmin: boolean;
  loading: boolean;
  isRoleLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signUp: (
    email: string,
    password: string,
    fullName: string,
  ) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  triggerRefreshSession: () => Promise<void>;
}

/**
 * Auth form mode
 */
export type AuthFormMode = 'login' | 'signup';

/**
 * Auth form props
 */
export interface AuthFormProps {
  onSuccess?: () => void;
  onToggleMode?: () => void;
}

/**
 * Protected route props
 */
export interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * Admin protected route props
 */
export interface AdminProtectedRouteProps {
  children: React.ReactNode;
}
