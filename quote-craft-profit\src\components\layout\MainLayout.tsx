import React from "react";
import Navbar from "./Navbar";
import { Toaster } from "@/components/ui/sonner";
import CachePerformanceDashboard from "@/components/cache/CachePerformanceDashboard";
import {
  useKeyboardShortcuts,
  KeyboardShortcut,
} from "@/hooks/useKeyboardShortcuts";

import Breadcrumbs, { BreadcrumbItem } from "@/components/ui/breadcrumbs";
import { useLocation } from "react-router-dom";

interface MainLayoutProps {
  children: React.ReactNode;
  pageShortcuts?: KeyboardShortcut[];
  title?: string;
  breadcrumbs?: BreadcrumbItem[];
  showBreadcrumbs?: boolean;
}

const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  pageShortcuts = [],
  title,
  breadcrumbs = [],
  showBreadcrumbs = true,
}) => {
  // Register keyboard shortcuts
  const { shortcuts } = useKeyboardShortcuts(pageShortcuts);
  const location = useLocation();

  // Generate default breadcrumbs based on the current path if none are provided
  const defaultBreadcrumbs: BreadcrumbItem[] = [];

  // Add breadcrumbs based on the current path
  const pathSegments = location.pathname.split("/").filter(Boolean);
  if (pathSegments.length > 0) {
    let currentPath = "";

    for (let i = 0; i < pathSegments.length; i++) {
      const segment = pathSegments[i];
      currentPath += `/${segment}`;

      // Format the segment for display (capitalize first letter, replace hyphens with spaces)
      const formattedSegment = segment
        .replace(/-/g, " ")
        .replace(/\b\w/g, (char) => char.toUpperCase());

      defaultBreadcrumbs.push({
        label: formattedSegment,
        href: currentPath,
      });
    }
  }

  // Use provided breadcrumbs or default ones
  const finalBreadcrumbs =
    breadcrumbs.length > 0 ? breadcrumbs : defaultBreadcrumbs;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navbar />
      <main className="container mx-auto py-8 px-4">
        {/* Show breadcrumbs if enabled and we have breadcrumbs to show */}
        {showBreadcrumbs && finalBreadcrumbs.length > 0 && (
          <div className="mb-6">
            <Breadcrumbs items={finalBreadcrumbs} className="mb-2" />
            {title && (
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
                {title}
              </h1>
            )}
          </div>
        )}
        {children}
      </main>
      <Toaster position="top-right" />

      {/* PHASE 2: Cache Performance Dashboard */}
      <CachePerformanceDashboard />

      {/* Keyboard shortcuts help button removed - no shortcuts available */}
    </div>
  );
};

export default MainLayout;
