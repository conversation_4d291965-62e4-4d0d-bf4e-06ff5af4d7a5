-- =====================================================
-- Database Rollback: Standardize DateTime Fields
-- Migration ID: 001_standardize_datetime_fields
-- Description: Rollback datetime standardization migration
-- Author: System Migration
-- Date: 2025-01-XX
-- =====================================================

-- IMPORTANT: This rollback script restores the original date fields
-- Use only if the migration needs to be reverted

BEGIN;

-- =====================================================
-- STEP 1: Verify backup exists
-- =====================================================

DO $$
DECLARE
    backup_exists BOOLEAN;
    backup_count INTEGER;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'templates_backup_001'
    ) INTO backup_exists;
    
    IF NOT backup_exists THEN
        RAISE EXCEPTION 'Backup table templates_backup_001 does not exist. Cannot proceed with rollback.';
    END IF;
    
    SELECT COUNT(*) INTO backup_count FROM templates_backup_001;
    RAISE NOTICE 'Backup table found with % records', backup_count;
END $$;

-- =====================================================
-- STEP 2: Restore original templates table
-- =====================================================

-- Drop current templates table
DROP TABLE IF EXISTS templates CASCADE;

-- Restore from backup
ALTER TABLE templates_backup_001 RENAME TO templates;

-- Verify restoration
DO $$
DECLARE
    template_start_type TEXT;
    template_end_type TEXT;
    record_count INTEGER;
BEGIN
    SELECT data_type INTO template_start_type 
    FROM information_schema.columns 
    WHERE table_name = 'templates' AND column_name = 'template_start_date';
    
    SELECT data_type INTO template_end_type 
    FROM information_schema.columns 
    WHERE table_name = 'templates' AND column_name = 'template_end_date';
    
    SELECT COUNT(*) INTO record_count FROM templates;
    
    IF template_start_type != 'date' OR template_end_type != 'date' THEN
        RAISE EXCEPTION 'Rollback verification failed. Start type: %, End type: %', template_start_type, template_end_type;
    END IF;
    
    RAISE NOTICE 'Rollback completed successfully. Restored % records with date fields.', record_count;
END $$;

-- =====================================================
-- STEP 3: Update migration log
-- =====================================================

UPDATE migration_log 
SET status = 'ROLLED_BACK', executed_at = NOW()
WHERE migration_id = '001_standardize_datetime_fields';

-- Add rollback log entry
INSERT INTO migration_log (migration_id, table_name, column_name, old_data_type, new_data_type, records_affected, status)
VALUES ('001_standardize_datetime_fields', 'templates', 'ROLLBACK_COMPLETED', 'timestamp with time zone', 'date', 
        (SELECT COUNT(*) FROM templates), 'ROLLED_BACK');

COMMIT;

-- =====================================================
-- Rollback Complete
-- =====================================================
