import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/auth.module';
import { AdminModule } from '../../auth/admin.module';
import { PackagesController } from './packages.controller.js'; // Added .js
import { PackagesService } from './packages.service.js'; // Added .js

// Import specialized services
import { PackageCrudService } from './services/package-crud.service.js';
import { PackageQueryService } from './services/package-query.service.js';
import { PackageRelationsService } from './services/package-relations.service.js';
import { PackageBatchService } from './services/package-batch.service.js';

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [PackagesController], // Declare the controller for this module
  providers: [
    PackagesService, // Main orchestrator service
    PackageCrudService, // CRUD operations
    PackageQueryService, // Query operations
    PackageRelationsService, // Relations management
    PackageBatchService, // Batch operations
  ],
})
export class AdminPackagesModule {}
