import React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { EventData } from "@/types/types";
import { Calendar, Clock, MapPin } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

interface EventsListProps {
  events: EventData[];
  isLoading: boolean;
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case "draft":
      return "bg-gray-400";
    case "planning":
      return "bg-blue-500";
    case "confirmed":
      return "bg-green-500";
    case "completed":
      return "bg-purple-500";
    case "cancelled":
      return "bg-red-500";
    default:
      return "bg-gray-400";
  }
};

const EventsList: React.FC<EventsListProps> = ({ events, isLoading }) => {
  const { formatForDisplay } = useTimezoneAwareDates();
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-48">
        <div className="text-gray-500 dark:text-gray-400">
          Loading events...
        </div>
      </div>
    );
  }

  if (events.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center h-48 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8">
        <h3 className="font-medium text-lg mb-2 text-gray-900 dark:text-white">
          No events found
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          Try adjusting your filters or create a new event.
        </p>
        <Button asChild>
          <Link to="/events/new">Add New Event</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
        {events.map((event) => (
          <Link
            to={`/events/${event.id}`}
            key={event.id}
            className="flex flex-col bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-md transition-all overflow-hidden"
          >
            <div className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium text-lg line-clamp-1 text-gray-900 dark:text-white">
                  {event.name}
                </h3>
                <div
                  className={`rounded-full h-3 w-3 ${getStatusColor(
                    event.status
                  )}`}
                />
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {event.clientName}
              </p>

              <div className="space-y-2 text-sm">
                <div className="flex items-center text-gray-600 dark:text-gray-400">
                  <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>
                    {formatForDisplay(event.startDate, "MMM d, yyyy")}
                    {event.startDate !== event.endDate &&
                      ` - ${formatForDisplay(event.endDate, "MMM d, yyyy")}`}
                  </span>
                </div>
                {event.location && (
                  <div className="flex items-center text-gray-600 dark:text-gray-400">
                    <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="line-clamp-1">{event.location}</span>
                  </div>
                )}
                {/* Removed attendees display - this information is tracked in calculation history */}
              </div>
            </div>

            <div className="mt-auto flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
              <Badge variant="outline" className="capitalize">
                {event.status}
              </Badge>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                <Clock className="h-3 w-3 inline mr-1" />
                <span>{event.primaryContact}</span>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default EventsList;
