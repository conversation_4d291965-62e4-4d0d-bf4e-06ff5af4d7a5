/**
 * Cache Coordinator Service
 *
 * Coordinates cache invalidation across related data to prevent race conditions
 * and ensure data consistency. Replaces manual delays and uncoordinated invalidations.
 *
 * CACHE OPTIMIZATION: Implements batched invalidation and relationship-aware caching
 */

import { QueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/lib/queryKeys";

export interface CacheInvalidationOptions {
  includeLineItems?: boolean;
  includeFinancials?: boolean;
  includePackages?: boolean;
  includeCustomItems?: boolean;
  includeRelatedCalculations?: boolean;
}

export class CacheCoordinator {
  private queryClient: QueryClient;
  private invalidationQueue = new Map<string, Set<string>>();
  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly BATCH_DELAY = 50; // 50ms batch delay to prevent race conditions

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }

  /**
   * Batch invalidate calculation-related caches to prevent race conditions
   * REPLACES: Manual 500ms delays and uncoordinated invalidations
   */
  async invalidateCalculationData(
    calculationId: string,
    options: CacheInvalidationOptions = {}
  ): Promise<void> {
    const {
      includeLineItems = true,
      includeFinancials = true,
      includePackages = true,
      includeCustomItems = true,
      includeRelatedCalculations = false,
    } = options;

    const invalidations: Promise<void>[] = [];

    // Always invalidate the calculation itself
    invalidations.push(
      this.queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.detail(calculationId),
      })
    );

    if (includeLineItems) {
      invalidations.push(
        this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
        })
      );
    }

    if (includeFinancials) {
      invalidations.push(
        this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.financials(calculationId),
        })
      );
    }

    if (includePackages) {
      invalidations.push(
        this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.packagesByCategory(calculationId),
        })
      );
    }

    if (includeCustomItems) {
      invalidations.push(
        this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.customItems(calculationId),
        })
      );
    }

    if (includeRelatedCalculations) {
      // Invalidate calculation lists that might include this calculation
      invalidations.push(
        this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.lists(),
        })
      );
    }

    // Execute all invalidations in parallel for better performance
    await Promise.all(invalidations);
  }

  /**
   * Smart invalidation that understands data relationships
   * REPLACES: Manual cache invalidation without relationship awareness
   */
  async invalidateWithRelationships(
    domain: string,
    id: string,
    action: "create" | "update" | "delete"
  ): Promise<void> {
    switch (domain) {
      case "calculation":
        await this.invalidateCalculationData(id, {
          includeRelatedCalculations:
            action === "create" || action === "delete",
        });
        break;

      case "lineItem": {
        // For line items, we need the calculation ID
        const calculationId = id.split(":")[0]; // Assuming format "calculationId:lineItemId"
        await this.invalidateCalculationData(calculationId, {
          includeLineItems: true,
          includeFinancials: true,
          includePackages: false, // Line item changes don't affect package catalog
          includeCustomItems: false,
        });
        break;
      }

      case "package":
        // Invalidate package and all calculations that might use it
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.packages.detail(id),
        });
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.packages.lists(),
        });

        // Invalidate all calculation packages (they might reference this package)
        await this.queryClient.invalidateQueries({
          predicate: (query) => {
            const key = query.queryKey;
            return (
              Array.isArray(key) &&
              key.includes("calculations") &&
              key.includes("packages-by-category")
            );
          },
        });
        break;

      case "client":
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.clients.detail(id),
        });
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.clients.lists(),
        });
        break;

      case "venue":
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.venues.detail(id),
        });
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.venues.lists(),
        });
        break;

      case "category":
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.categories.detail(id),
        });
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.categories.lists(),
        });
        // Also invalidate package lists as they depend on categories
        await this.queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.packages.lists(),
        });
        break;
    }
  }

  /**
   * Batch multiple invalidations to prevent excessive API calls
   * OPTIMIZATION: Reduces invalidation frequency by batching related operations
   */
  private batchInvalidation(key: string, queryKey: string): void {
    if (!this.invalidationQueue.has(key)) {
      this.invalidationQueue.set(key, new Set());
    }

    this.invalidationQueue.get(key)!.add(queryKey);

    // Clear existing timeout and set new one
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    this.batchTimeout = setTimeout(() => {
      this.processBatchedInvalidations();
    }, this.BATCH_DELAY);
  }

  /**
   * Process all batched invalidations
   */
  private async processBatchedInvalidations(): Promise<void> {
    const invalidations: Promise<void>[] = [];

    for (const [, queryKeys] of this.invalidationQueue.entries()) {
      for (const queryKey of queryKeys) {
        invalidations.push(
          this.queryClient.invalidateQueries({
            queryKey: JSON.parse(queryKey),
          })
        );
      }
    }

    // Clear the queue
    this.invalidationQueue.clear();
    this.batchTimeout = null;

    // Execute all invalidations
    await Promise.all(invalidations);
  }

  /**
   * Emergency cache clear for debugging purposes
   * USE SPARINGLY: Only for debugging cache issues
   */
  clearAllCaches(): void {
    this.queryClient.clear();
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): {
    totalQueries: number;
    staleQueries: number;
    hitRate: string;
  } {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    const totalQueries = queries.length;
    const staleQueries = queries.filter((q) => q.isStale()).length;
    const hitRate =
      totalQueries > 0
        ? (((totalQueries - staleQueries) / totalQueries) * 100).toFixed(2) +
          "%"
        : "0%";

    return {
      totalQueries,
      staleQueries,
      hitRate,
    };
  }
}

// Singleton instance for global use
let cacheCoordinatorInstance: CacheCoordinator | null = null;

export const createCacheCoordinator = (
  queryClient: QueryClient
): CacheCoordinator => {
  if (!cacheCoordinatorInstance) {
    cacheCoordinatorInstance = new CacheCoordinator(queryClient);
  }
  return cacheCoordinatorInstance;
};

export const getCacheCoordinator = (): CacheCoordinator => {
  if (!cacheCoordinatorInstance) {
    throw new Error(
      "CacheCoordinator not initialized. Call createCacheCoordinator first."
    );
  }
  return cacheCoordinatorInstance;
};
