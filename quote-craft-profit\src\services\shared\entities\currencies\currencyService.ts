/**
 * Currency Service
 *
 * This service provides methods for interacting with currencies.
 * It uses the currencyApiService to fetch data from the backend API.
 */

import { Currency, getAllCurrenciesFromApi, getCurrencyByIdFromApi } from './currencyApiService';

/**
 * Get all currencies
 * This function uses the backend API to fetch currencies
 * @returns Promise resolving to an array of currencies
 */
export const getAllCurrencies = async (): Promise<Currency[]> => {
  try {
    // Use the API service to fetch currencies
    return await getAllCurrenciesFromApi();
  } catch (error) {
    console.error('Error in getAllCurrencies:', error);
    throw error;
  }
};

/**
 * Get a currency by ID
 * This function uses the backend API to fetch a currency by ID
 * @param id - The currency ID
 * @returns Promise resolving to a currency or null
 */
export const getCurrencyById = async (id: string): Promise<Currency> => {
  try {
    // Use the API service to fetch the currency
    return await getCurrencyByIdFromApi(id);
  } catch (error) {
    console.error(`Error in getCurrencyById for ID ${id}:`, error);
    throw error;
  }
};
