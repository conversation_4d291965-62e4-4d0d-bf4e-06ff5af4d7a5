/**
 * Venue API Service
 *
 * This service provides methods for interacting with the venues API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import {
  SaveVenueData,
  Venue,
  VenueDisplay,
  VenueClassification,
} from "@/types/venues";
import {
  apiClient,
  getAuthenticatedApiClient,
} from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";
import { PaginatedResult } from "@/types/pagination";

// Interface for venue filters
interface VenueFilters {
  search?: string;
  cityId?: string;
  showDeleted?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  classification?: VenueClassification;
  minCapacity?: number;
  maxCapacity?: number;
}

/**
 * Get all venues from the backend API with optional filtering
 * @param filters - Optional filters for venues
 * @returns Promise resolving to a paginated result of venues
 */
export const getAllVenuesFromApi = async (
  filters: VenueFilters = {}
): Promise<PaginatedResult<VenueDisplay>> => {
  try {
    console.log("Fetching venues from backend API with filters:", filters);

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (filters.search) {
      queryParams.append("search", filters.search);
    }
    if (filters.cityId && filters.cityId !== "all") {
      queryParams.append("cityId", filters.cityId);
    }
    if (filters.showDeleted !== undefined) {
      queryParams.append("showDeleted", filters.showDeleted.toString());
    }
    if (filters.page) {
      queryParams.append("page", filters.page.toString());
    }
    if (filters.pageSize) {
      queryParams.append("pageSize", filters.pageSize.toString());
    }
    if (filters.sortBy) {
      queryParams.append("sortBy", filters.sortBy);
    }
    if (filters.sortOrder) {
      queryParams.append("sortOrder", filters.sortOrder);
    }
    if (filters.classification) {
      queryParams.append("classification", filters.classification);
    }
    if (filters.minCapacity !== undefined) {
      queryParams.append("minCapacity", filters.minCapacity.toString());
    }
    if (filters.maxCapacity !== undefined) {
      queryParams.append("maxCapacity", filters.maxCapacity.toString());
    }

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request - use admin endpoint for admin operations
    const response = await authClient.get(
      `${API_ENDPOINTS.ADMIN_VENUES.GET_ALL}${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`
    );

    console.log("Venues fetched successfully from backend API");

    return response.data;
  } catch (error) {
    console.error("Error fetching venues from backend API:", error);

    // Return empty result on error
    return {
      data: [],
      totalCount: 0,
      page: filters.page || 1,
      pageSize: filters.pageSize || 10,
      totalPages: 0,
    };
  }
};

/**
 * Get venues by city ID from the backend API with optional enhanced filtering
 * @param cityId - The city ID
 * @param filters - Optional enhanced filters
 * @returns Promise resolving to an array of venues
 */
export const getVenuesByCityFromApi = async (
  cityId: string,
  filters?: {
    classification?: VenueClassification;
    minCapacity?: number;
    maxCapacity?: number;
    active?: boolean;
  }
): Promise<Venue[]> => {
  try {
    console.log(
      `Fetching venues for city ID ${cityId} from backend API with filters:`,
      filters
    );

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append("cityId", cityId);
    queryParams.append("active", (filters?.active !== false).toString());

    if (filters?.classification) {
      queryParams.append("classification", filters.classification);
    }
    if (filters?.minCapacity !== undefined) {
      queryParams.append("minCapacity", filters.minCapacity.toString());
    }
    if (filters?.maxCapacity !== undefined) {
      queryParams.append("maxCapacity", filters.maxCapacity.toString());
    }

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request with enhanced filters
    const response = await authClient.get(
      `${API_ENDPOINTS.VENUES.GET_ALL}?${queryParams.toString()}`
    );

    console.log("Venues by city fetched successfully from backend API");

    // The response should now be an array of venues directly
    return response.data || [];
  } catch (error) {
    console.error(
      `Error fetching venues for city ID ${cityId} from backend API:`,
      error
    );
    return [];
  }
};

/**
 * Get a venue by ID from the backend API
 * @param id - The venue ID
 * @returns Promise resolving to a venue or null
 */
export const getVenueByIdFromApi = async (
  id: string
): Promise<Venue | null> => {
  try {
    console.log(`Fetching venue with ID ${id} from backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request - use admin endpoint for admin operations
    const response = await authClient.get(
      API_ENDPOINTS.ADMIN_VENUES.GET_BY_ID(id)
    );

    console.log("Venue fetched successfully from backend API");

    return response.data;
  } catch (error) {
    console.error(
      `Error fetching venue with ID ${id} from backend API:`,
      error
    );
    return null;
  }
};

/**
 * Create or update a venue using the backend API
 * @param venueData - The venue data to save
 * @returns Promise resolving to the saved venue
 */
export const saveVenueWithApi = async (
  venueData: SaveVenueData
): Promise<Venue> => {
  try {
    console.log("Saving venue with backend API:", venueData);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    if (venueData.id) {
      // Update existing venue - use admin endpoint
      const response = await authClient.put(
        API_ENDPOINTS.ADMIN_VENUES.UPDATE(venueData.id),
        venueData
      );

      console.log("Venue updated successfully with backend API");
      return response.data;
    } else {
      // Create new venue - use admin endpoint
      const response = await authClient.post(
        API_ENDPOINTS.ADMIN_VENUES.CREATE,
        venueData
      );

      console.log("Venue created successfully with backend API");
      return response.data;
    }
  } catch (error) {
    console.error("Error saving venue with backend API:", error);
    throw error;
  }
};

/**
 * Delete a venue (soft delete) using the backend API
 * @param id - The venue ID to delete
 * @returns Promise resolving to void
 */
export const deleteVenueWithApi = async (id: string): Promise<void> => {
  try {
    console.log(`Deleting venue with ID ${id} using backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request - use admin endpoint
    await authClient.delete(API_ENDPOINTS.ADMIN_VENUES.DELETE(id));

    console.log("Venue deleted successfully with backend API");
  } catch (error) {
    console.error(
      `Error deleting venue with ID ${id} using backend API:`,
      error
    );
    throw error;
  }
};

/**
 * Restore a deleted venue using the backend API
 * @param id - The venue ID to restore
 * @returns Promise resolving to void
 */
export const restoreVenueWithApi = async (id: string): Promise<void> => {
  try {
    console.log(`Restoring venue with ID ${id} using backend API`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request - use admin endpoint
    await authClient.post(API_ENDPOINTS.ADMIN_VENUES.RESTORE(id));

    console.log("Venue restored successfully with backend API");
  } catch (error) {
    console.error(
      `Error restoring venue with ID ${id} using backend API:`,
      error
    );
    throw error;
  }
};
