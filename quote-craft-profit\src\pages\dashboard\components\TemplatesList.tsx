import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";
import { Link } from "react-router-dom";
import { Skeleton } from "@/components/ui/skeleton";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

interface Template {
  id: string;
  name: string;
  eventType: string;
  createdAt: string;
  description: string;
}

/**
 * Fetch trending templates from the real API
 */
const fetchTrendingTemplates = async (): Promise<Template[]> => {
  try {
    const apiClient = await getAuthenticatedApiClient();

    // Fetch public templates (trending/popular ones)
    const response = await apiClient.get(API_ENDPOINTS.TEMPLATES.GET_ALL, {
      params: {
        page: 1,
        pageSize: 4, // Get top 4 trending templates
        // TODO: Add sorting by usage/popularity when backend supports it
      },
    });

    // Transform the response to match our interface
    const templates: Template[] =
      response.data.data?.map((template: any) => ({
        id: template.id,
        name: template.name,
        eventType: template.event_type || "General",
        createdAt: template.created_at || "",
        description: template.description || "No description available",
      })) || [];

    return templates;
  } catch (error) {
    console.error("Error fetching trending templates:", error);
    // Return empty array on error
    return [];
  }
};

const TemplatesList: React.FC = () => {
  const { formatForDisplay } = useTimezoneAwareDates();

  const {
    data: templates,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["dashboardTemplates"],
    queryFn: fetchTrendingTemplates,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="border rounded-md p-4">
            <div className="flex justify-between items-start mb-2">
              <div className="space-y-2">
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-3 w-[160px]" />
              </div>
              <Skeleton className="h-9 w-[100px]" />
            </div>
            <Skeleton className="h-3 w-full mt-4" />
            <Skeleton className="h-3 w-3/4 mt-2" />
            <div className="mt-3 pt-3 border-t flex justify-between items-center">
              <Skeleton className="h-3 w-[100px]" />
              <Skeleton className="h-3 w-[80px]" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <p className="text-muted-foreground py-4">Failed to load templates.</p>
    );
  }

  if (!templates?.length) {
    return (
      <div className="text-center py-8 animate-fadeIn">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-50 mb-4 animate-float">
          <FileText className="h-8 w-8 text-blue-500" />
        </div>
        <h3 className="text-lg font-medium mb-2">No templates yet</h3>
        <p className="text-gray-500 mb-4">
          Templates help you create calculations faster
        </p>
        <Link to="/admin/templates/new">
          <Button className="animate-pulse-subtle">
            Create your first template
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {templates.map((template) => (
        <div
          key={template.id}
          className="border rounded-md p-4 hover:border-blue-300 hover:shadow-sm transition-all hover-scale group"
        >
          <div className="flex justify-between items-start mb-2">
            <div>
              <h4 className="font-medium text-gray-800">{template.name}</h4>
              <div className="flex items-center mt-1">
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  {template.eventType}
                </span>
                <span className="text-xs text-gray-500 ml-2">
                  Added{" "}
                  {template.createdAt
                    ? formatForDisplay(template.createdAt, "MMM d, yyyy")
                    : "Unknown date"}
                </span>
              </div>
            </div>
            <Link to={`/calculations/new?templateId=${template.id}`}>
              <Button
                variant="outline"
                size="sm"
                className="opacity-70 group-hover:opacity-100 transition-all"
              >
                <FileText className="h-4 w-4 mr-1" />
                Use Template
              </Button>
            </Link>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            {template.description ||
              `A template for ${template.eventType.toLowerCase()} events.`}
          </p>
          <div className="mt-3 pt-3 border-t flex justify-between items-center">
            <span className="text-xs text-gray-500">Provided by Admin</span>
            <Link
              to={`/templates/${template.id}`}
              className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
            >
              View details →
            </Link>
          </div>
        </div>
      ))}
      <div className="col-span-full mt-4 text-center">
        <Link to="/templates">
          <Button variant="outline" size="sm" className="px-6">
            View all templates
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default TemplatesList;
