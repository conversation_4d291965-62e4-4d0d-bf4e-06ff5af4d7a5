/**
 * Migration Status Indicator Component
 * 
 * Shows users which API approach is being used and performance metrics
 * Useful for monitoring the migration progress and performance improvements
 */

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Zap, 
  Database, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  TrendingUp,
  Info,
} from 'lucide-react';

/**
 * Props for the migration status indicator
 */
interface MigrationStatusIndicatorProps {
  /**
   * Whether the consolidated endpoint is being used
   */
  usingConsolidated?: boolean;
  
  /**
   * Whether legacy endpoints are being used
   */
  usingLegacy?: boolean;
  
  /**
   * Performance metrics from the consolidated endpoint
   */
  performanceMetrics?: {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
  };
  
  /**
   * Whether to show detailed metrics
   * @default false
   */
  showDetails?: boolean;
  
  /**
   * Whether to show the indicator at all
   * @default true in development, false in production
   */
  visible?: boolean;
}

/**
 * Migration status indicator component
 */
export const MigrationStatusIndicator: React.FC<MigrationStatusIndicatorProps> = ({
  usingConsolidated = false,
  usingLegacy = false,
  performanceMetrics,
  showDetails = false,
  visible = process.env.NODE_ENV === 'development',
}) => {
  // Don't render in production unless explicitly visible
  if (!visible) {
    return null;
  }

  // Determine status
  const status = usingConsolidated ? 'consolidated' : usingLegacy ? 'legacy' : 'unknown';
  
  // Calculate performance improvement estimate
  const estimatedImprovement = usingConsolidated ? '75%' : '0%';
  
  // Render compact indicator
  if (!showDetails) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge 
              variant={status === 'consolidated' ? 'default' : 'secondary'}
              className={`
                fixed bottom-4 right-4 z-50 cursor-help
                ${status === 'consolidated' ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-600 hover:bg-gray-700'}
              `}
            >
              {status === 'consolidated' ? (
                <Zap className="h-3 w-3 mr-1" />
              ) : (
                <Database className="h-3 w-3 mr-1" />
              )}
              {status === 'consolidated' ? 'Optimized' : 'Legacy'}
            </Badge>
          </TooltipTrigger>
          <TooltipContent side="left" className="max-w-xs">
            <div className="space-y-2">
              <p className="font-semibold">
                {status === 'consolidated' ? 'Using Consolidated API' : 'Using Legacy APIs'}
              </p>
              <p className="text-sm">
                {status === 'consolidated' 
                  ? `Single API call instead of 4 separate calls. Load time: ${performanceMetrics?.loadTime || 'N/A'}ms`
                  : 'Using 4 separate API calls. Consider upgrading to consolidated endpoint.'
                }
              </p>
              {performanceMetrics?.errors && performanceMetrics.errors.length > 0 && (
                <p className="text-sm text-red-400">
                  {performanceMetrics.errors.length} error(s) occurred
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Render detailed card
  return (
    <Card className="fixed bottom-4 right-4 z-50 w-80 shadow-lg">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          {status === 'consolidated' ? (
            <Zap className="h-4 w-4 text-green-600" />
          ) : (
            <Database className="h-4 w-4 text-gray-600" />
          )}
          API Migration Status
          <Badge variant={status === 'consolidated' ? 'default' : 'secondary'}>
            {status === 'consolidated' ? 'Optimized' : 'Legacy'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 space-y-3">
        {/* Status Information */}
        <div className="flex items-center gap-2 text-sm">
          {status === 'consolidated' ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 text-yellow-600" />
          )}
          <span>
            {status === 'consolidated' 
              ? 'Using consolidated endpoint (1 API call)'
              : 'Using legacy endpoints (4 API calls)'
            }
          </span>
        </div>

        {/* Performance Metrics */}
        {performanceMetrics && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Clock className="h-4 w-4 text-blue-600" />
              <span>Load time: {performanceMetrics.loadTime}ms</span>
            </div>
            
            <div className="flex items-center gap-2 text-sm">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span>Estimated improvement: {estimatedImprovement}</span>
            </div>

            {performanceMetrics.errors && performanceMetrics.errors.length > 0 && (
              <div className="flex items-center gap-2 text-sm text-red-600">
                <AlertCircle className="h-4 w-4" />
                <span>{performanceMetrics.errors.length} error(s)</span>
              </div>
            )}
          </div>
        )}

        {/* Additional Information */}
        <div className="pt-2 border-t text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Info className="h-3 w-3" />
            <span>
              {status === 'consolidated' 
                ? 'Consolidated endpoint reduces network requests and improves performance'
                : 'Legacy endpoints will be deprecated. Migration to consolidated endpoint recommended.'
              }
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Hook to get migration status from calculation data
 */
export const useMigrationStatus = (calculationData: any) => {
  return {
    usingConsolidated: calculationData?.usingConsolidated || false,
    usingLegacy: calculationData?.usingLegacy || false,
    performanceMetrics: calculationData?.performanceMetrics,
  };
};

/**
 * Higher-order component to add migration status to any component
 */
export const withMigrationStatus = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P & { migrationData?: any; showMigrationStatus?: boolean }) => {
    const { migrationData, showMigrationStatus = false, ...componentProps } = props;
    const migrationStatus = useMigrationStatus(migrationData);

    return (
      <>
        <Component {...(componentProps as P)} />
        {showMigrationStatus && (
          <MigrationStatusIndicator
            {...migrationStatus}
            visible={showMigrationStatus}
          />
        )}
      </>
    );
  };
};
