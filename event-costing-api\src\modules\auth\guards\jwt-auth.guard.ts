import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { UserWithProfile } from '../services/jwt-validation.service';
import { Request } from 'express';
import { Observable } from 'rxjs';

/**
 * Interface representing an Express Request object augmented with our UserWithProfile.
 * Consider moving this to a shared types file.
 */
interface RequestWithUserProfile extends Request {
  user: UserWithProfile; // Use the enriched type
}

/**
 * JWT Authentication Guard
 * This guard uses Passport's JWT strategy to protect routes
 */
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor() {
    super();
  }

  /**
   * Determines if the current request is allowed to proceed
   * @param context The execution context
   * @returns A boolean or a Promise/Observable of a boolean indicating if the request can proceed
   */
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    this.logger.debug('JWT Auth Guard: Checking authentication');
    // Use Passport's built-in authentication mechanism
    return super.canActivate(context);
  }

  /**
   * Handles the result of the authentication check
   * @param err Any error that occurred during authentication
   * @param user The authenticated user
   * @param info Additional info from the strategy
   * @returns The user object if authentication was successful
   */
  handleRequest(err: any, user: any, info: any): any {
    if (err || !user) {
      const errorMessage =
        err?.message || info?.message || 'Unauthorized access';
      this.logger.error(
        `JWT Auth Guard: Authentication failed - ${errorMessage}`,
      );
      throw err || new UnauthorizedException(errorMessage);
    }

    this.logger.debug(
      `JWT Auth Guard: Authentication successful for user: ${user.email}`,
    );
    return user;
  }
}
