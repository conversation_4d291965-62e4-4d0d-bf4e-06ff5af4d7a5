// External API client for event-costing-api
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import { useAuth } from '@/contexts/useAuth';
import { API_CONFIG } from './config';
import { API_ENDPOINTS } from './endpoints';
import { supabase } from '@/integrations/supabase/client';

/**
 * Creates an axios instance for the external API
 * @param token - The authentication token
 * @returns An axios instance configured for the external API
 */
export const createApiClient = (token?: string): AxiosInstance => {
  const config: AxiosRequestConfig = {
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      ...API_CONFIG.DEFAULT_HEADERS,
    },
    withCredentials: false, // Set to false for cross-origin requests without credentials
  };

  // Add authorization header if token is provided
  if (token) {
    config.headers = {
      ...config.headers,
      Authorization: `Bearer ${token}`,
    };
  }

  const client = axios.create(config);

  // Add request interceptor for debugging
  client.interceptors.request.use(
    (config) => {
      console.log(
        `%cAPI Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`,
        'color: blue; font-weight: bold',
        {
          baseURL: config.baseURL,
          url: config.url,
          headers: config.headers,
          data: config.data,
        },
      );
      return config;
    },
    (error) => {
      console.error('API request configuration error:', error);
      return Promise.reject(error);
    },
  );

  // Add response interceptor for error handling, token refresh, and debugging
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      console.log(
        `%cAPI Response: ${response.status} ${response.config.method?.toUpperCase()} ${
          response.config.url
        }`,
        'color: green; font-weight: bold',
        {
          status: response.status,
          statusText: response.statusText,
          url: response.config.url,
          method: response.config.method,
          data: response.data,
          headers: response.headers,
        },
      );
      return response;
    },
    async (error: AxiosError) => {
      // Log the error for debugging
      console.error(
        `%cAPI Request Failed: ${error.config?.method?.toUpperCase()} ${
          error.config?.url
        }`,
        'color: red; font-weight: bold',
        {
          url: error.config?.url,
          baseURL: error.config?.baseURL,
          method: error.config?.method,
          status: error.response?.status,
          statusText: error.response?.statusText,
          headers: error.config?.headers,
          data: error.response?.data || error.message,
          error: error.message,
          stack: error.stack,
        },
      );

      // Check if the error is due to an expired token (401 Unauthorized)
      const originalRequest = error.config;
      if (
        error.response?.status === 401 &&
        originalRequest &&
        !(originalRequest as any)._retry &&
        // Don't retry for auth endpoints to avoid infinite loops
        !originalRequest.url?.includes('/auth/login')
      ) {
        try {
          (originalRequest as any)._retry = true;

          // Get the session from Supabase
          const { data } = await supabase.auth.getSession();
          if (!data.session?.refresh_token) {
            console.error('No refresh token found in session');
            // Redirect to login page
            window.location.href = '/auth';
            return Promise.reject(error);
          }

          // Use Supabase's built-in token refresh instead of calling backend
          console.log('Token expired, attempting to refresh using Supabase...');
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession({
            refresh_token: data.session.refresh_token,
          });

          if (refreshError || !refreshData.session) {
            console.error('Failed to refresh session:', refreshError);
            throw new Error('Session refresh failed');
          }

          console.log('Session refreshed successfully');
          const newSession = refreshData.session;

          // Retry the original request with the new token
          originalRequest.headers.Authorization = `Bearer ${newSession.access_token}`;
          return axios(originalRequest);
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);

          // If refresh fails, redirect to login
          window.location.href = '/auth';
          return Promise.reject(refreshError);
        }
      }

      // Rethrow the error to be handled by the calling code
      return Promise.reject(error);
    },
  );

  return client;
};

/**
 * Hook to get the API client with the current user's token
 * @returns An axios instance configured with the current user's token
 */
export const useApiClient = (): AxiosInstance => {
  const { session } = useAuth();
  return createApiClient(session?.access_token);
};

/**
 * Create a default API client without authentication
 * This is useful for API calls that don't require authentication
 */
export const apiClient = createApiClient();

/**
 * Get the current user's token from Supabase
 * @returns The current user's token or undefined
 */
export const getTokenFromStorage = async (): Promise<string | undefined> => {
  try {
    // Get token from Supabase client
    console.log('Attempting to get token from Supabase client');
    const { data } = await supabase.auth.getSession();
    if (data.session?.access_token) {
      console.log('Found token in Supabase client session');
      return data.session.access_token;
    }

    console.warn('No token found in Supabase session');
    return undefined;
  } catch (error) {
    console.error('Error getting token from storage:', error);
    return undefined;
  }
};

/**
 * Create an authenticated API client using the token from localStorage
 * This is useful for API calls that require authentication but are called outside of React components
 */
export const getAuthenticatedApiClient = async (): Promise<AxiosInstance> => {
  const token = await getTokenFromStorage();

  if (token) {
    console.log('%cAuthentication: Token found', 'color: green; font-weight: bold', {
      tokenLength: token.length,
      tokenStart: token.substring(0, 10) + '...',
      tokenEnd: '...' + token.substring(token.length - 10),
    });
  } else {
    console.warn(
      '%cAuthentication: No token found',
      'color: red; font-weight: bold',
      'API calls requiring authentication will likely fail',
    );
  }

  return createApiClient(token);
};
