/**
 * Hook for fetching calculation data
 */
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { getCalculationByIdFromSupabase } from "../../../../services/calculations";
import { useEffect } from "react";

/**
 * Hook for fetching calculation data by ID
 *
 * @param calculationId - The ID of the calculation to fetch
 * @returns Query result with calculation data
 */
export function useCalculation(calculationId: string) {
  const queryClient = useQueryClient();

  // Force a refetch when the component mounts
  useEffect(() => {
    if (calculationId) {
      // Invalidate the calculation query to ensure fresh data
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculations.detail(calculationId),
      });
    }
  }, [calculationId, queryClient]);

  return useQuery({
    queryKey: QUERY_KEYS.calculations.detail(calculationId),
    queryFn: () => getCalculationByIdFromSupabase(calculationId),
    enabled: !!calculationId,
    // CACHE OPTIMIZATION: Reduced stale time for active editing scenarios
    staleTime: 30 * 1000, // 30 seconds for active editing
    // Cache in background for 5 minutes (reduced from 10)
    gcTime: 5 * 60 * 1000,
    // Only refetch on mount if data is stale
    refetchOnMount: false,
    // Refetch on window focus only if data is stale
    refetchOnWindowFocus: false,
    meta: {
      onError: (error: Error) => {
        console.error(`Error fetching calculation ${calculationId}:`, error);
        toast.error("Failed to load calculation details");
      },
    },
  });
}
