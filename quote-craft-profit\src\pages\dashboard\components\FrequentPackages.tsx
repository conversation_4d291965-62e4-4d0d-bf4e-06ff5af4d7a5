import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { showSuccess } from "@/lib/notifications";
import { Skeleton } from "@/components/ui/skeleton";
import { getAllPackages } from "@/services/admin/packages";

interface FrequentPackage {
  id: string;
  name: string;
  category: string;
  usageCount: number;
}

/**
 * Fetch frequently used packages from the real API
 * This gets the most commonly used packages based on calculation line items
 */
const fetchFrequentPackages = async (): Promise<FrequentPackage[]> => {
  try {
    // Get all packages and simulate usage count calculation
    // In a real implementation, this would be a dedicated API endpoint
    // that calculates usage statistics from calculation_line_items table
    const packagesResponse = await getAllPackages({
      page: 1,
      pageSize: 5,
      // Could add filters for most used packages
    });

    // Transform packages to include mock usage count for now
    // TODO: Replace with real usage statistics from backend
    const frequentPackages: FrequentPackage[] = packagesResponse.data.map(
      (pkg, index) => ({
        id: pkg.id,
        name: pkg.name,
        category: pkg.categoryName || "Uncategorized",
        usageCount: Math.max(25 - index * 3, 5), // Simulate decreasing usage
      })
    );

    return frequentPackages;
  } catch (error) {
    console.error("Error fetching frequent packages:", error);
    // Return empty array on error
    return [];
  }
};

const FrequentPackages: React.FC = () => {
  const {
    data: packages,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["frequentPackages"],
    queryFn: fetchFrequentPackages,
  });

  const handleAddPackage = (packageId: string, packageName: string) => {
    // In a real implementation, this would add the package to a new or existing calculation
    showSuccess("Package added", {
      description: `${packageName} has been added to your draft calculation`,
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="flex justify-between items-center">
            <div className="space-y-2">
              <Skeleton className="h-4 w-[200px]" />
              <Skeleton className="h-3 w-[160px]" />
            </div>
            <Skeleton className="h-9 w-[70px]" />
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <p className="text-muted-foreground py-4">
        Failed to load frequent packages.
      </p>
    );
  }

  if (!packages?.length) {
    return (
      <div className="text-center py-6">
        <p className="text-muted-foreground">
          No frequently used packages found.
        </p>
        <p className="text-sm text-muted-foreground mt-1">
          Packages will appear here as you use them in calculations.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {packages.map((pkg) => (
        <div
          key={pkg.id}
          className="flex justify-between items-center group p-2 rounded-md hover:bg-muted/40"
        >
          <div>
            <h4 className="font-medium">{pkg.name}</h4>
            <p className="text-sm text-muted-foreground flex items-center gap-1">
              {pkg.category}
              <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full ml-2">
                Used {pkg.usageCount} times
              </span>
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleAddPackage(pkg.id, pkg.name)}
            className="opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <PlusCircle className="h-4 w-4 mr-1" />
            Add
          </Button>
        </div>
      ))}
    </div>
  );
};

export default FrequentPackages;
