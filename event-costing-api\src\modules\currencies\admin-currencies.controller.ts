import {
  Controller,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
  ParseUUI<PERSON>ipe,
  Get,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';
import { CurrenciesService } from './currencies.service';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';
import { CurrencyDto } from './dto/currency.dto';

@ApiTags('Admin - Currencies')
@ApiBearerAuth()
@Controller('admin/currencies')
@UseGuards(JwtAuthGuard, AdminRoleGuard)
export class AdminCurrenciesController {
  private readonly logger = new Logger(AdminCurrenciesController.name);

  constructor(private readonly currenciesService: CurrenciesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new currency' })
  @ApiBody({ type: CreateCurrencyDto })
  @ApiResponse({
    status: 201,
    description: 'Currency created',
    type: CurrencyDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  @ApiResponse({ status: 409, description: 'Conflict (Code exists)' })
  async createCurrency(
    @Body() createDto: CreateCurrencyDto,
  ): Promise<CurrencyDto> {
    this.logger.log(`Admin request to create currency: ${createDto.code}`);
    return await this.currenciesService.createCurrency(createDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific currency by ID (Admin)' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({
    status: 200,
    description: 'Currency found',
    type: CurrencyDto,
  })
  @ApiResponse({ status: 404, description: 'Currency not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<CurrencyDto> {
    this.logger.log(`Admin request to get currency ID: ${id}`);
    return await this.currenciesService.findOneById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a currency' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiBody({ type: UpdateCurrencyDto })
  @ApiResponse({
    status: 200,
    description: 'Currency updated',
    type: CurrencyDto,
  })
  @ApiResponse({ status: 404, description: 'Currency not found' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  async updateCurrency(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateCurrencyDto,
  ): Promise<CurrencyDto> {
    this.logger.log(`Admin request to update currency ID: ${id}`);
    return await this.currenciesService.updateCurrency(id, updateDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a currency' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({ status: 204, description: 'Currency deleted' })
  @ApiResponse({ status: 404, description: 'Currency not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Admin Role)' })
  @ApiResponse({ status: 409, description: 'Conflict (Currency in use)' })
  async deleteCurrency(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Admin request to delete currency ID: ${id}`);
    await this.currenciesService.deleteCurrency(id);
  }
}
