import {
  Controller,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { CitiesService } from './cities.service';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CityDto } from './dto/city.dto'; // Assuming this exists for responses
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';

@ApiTags('Admin - Cities')
@ApiBearerAuth()
@Controller('admin/cities')
@UseGuards(JwtAuthGuard, AdminRoleGuard) // Apply admin guard to all city admin actions
export class AdminCitiesController {
  private readonly logger = new Logger(AdminCitiesController.name);

  constructor(private readonly citiesService: CitiesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new city' })
  @ApiBody({ type: CreateCityDto })
  @ApiResponse({
    status: 201,
    description: 'City created successfully',
    type: CityDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Requires Admin Role)' })
  async createCity(@Body() createCityDto: CreateCityDto): Promise<CityDto> {
    this.logger.log(`Admin request to create city: ${createCityDto.name}`);
    return await this.citiesService.createCity(createCityDto);
  }

  // GET /admin/cities - Consider adding if needed (duplicates public GET /cities?)
  // We already have GET /cities for public listing

  // GET /admin/cities/:id - Consider adding if needed
  // Public GET /cities doesn't have a :id route, so maybe useful?
  // @Get(':id')
  // @ApiOperation({ summary: 'Get a specific city by ID (Admin)' })
  // async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<CityDto> { ... }

  @Put(':id')
  @ApiOperation({ summary: 'Update an existing city' })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: 'City ID',
  })
  @ApiBody({ type: UpdateCityDto })
  @ApiResponse({
    status: 200,
    description: 'City updated successfully',
    type: CityDto,
  })
  @ApiResponse({ status: 404, description: 'City not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Requires Admin Role)' })
  async updateCity(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCityDto: UpdateCityDto,
  ): Promise<CityDto> {
    this.logger.log(`Admin request to update city ID: ${id}`);
    return await this.citiesService.updateCity(id, updateCityDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a city' })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: 'City ID',
  })
  @ApiResponse({ status: 204, description: 'City deleted successfully' })
  @ApiResponse({ status: 404, description: 'City not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden (Requires Admin Role)' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCity(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Admin request to delete city ID: ${id}`);
    // Assuming service handles not found error
    await this.citiesService.deleteCity(id);
  }
}
