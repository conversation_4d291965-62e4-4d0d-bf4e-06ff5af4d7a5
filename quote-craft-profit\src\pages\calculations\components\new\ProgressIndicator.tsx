import React from 'react';

interface ProgressIndicatorProps {
  currentStep: 'basic-info' | 'additional-details';
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ currentStep }) => {
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center bg-primary text-white`}
          >
            1
          </div>
          <div className="ml-2 font-medium">Basic Information</div>
        </div>
        <div className="flex-1 mx-4 h-1 bg-gray-200">
          <div
            className={`h-full bg-primary ${
              currentStep === 'additional-details' ? 'w-full' : 'w-0'
            } transition-all duration-300`}
          ></div>
        </div>
        <div className="flex items-center">
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center ${
              currentStep === 'additional-details'
                ? 'bg-primary text-white'
                : 'bg-gray-200 text-gray-500'
            }`}
          >
            2
          </div>
          <div className="ml-2 font-medium">Additional Details</div>
        </div>
      </div>
    </div>
  );
};

export default ProgressIndicator;
