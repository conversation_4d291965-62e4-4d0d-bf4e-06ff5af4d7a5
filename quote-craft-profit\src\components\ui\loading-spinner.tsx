import React from 'react';

interface LoadingSpinnerProps {
  /**
   * The text to display below the spinner
   * @default "Loading..."
   */
  text?: string;
  
  /**
   * The size of the spinner in pixels
   * @default 48
   */
  size?: number;
  
  /**
   * Whether to show the text
   * @default true
   */
  showText?: boolean;
  
  /**
   * Additional CSS classes for the container
   */
  className?: string;
  
  /**
   * The color of the spinner
   * @default "eventcost-primary"
   */
  color?: string;
}

/**
 * A loading spinner component with optional text
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  text = "Loading...",
  size = 48,
  showText = true,
  className = "",
  color = "eventcost-primary"
}) => {
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div 
        className={`rounded-full border-2 border-gray-200 border-t-${color} animate-spin`}
        style={{ height: `${size}px`, width: `${size}px` }}
        role="status"
        aria-label="Loading"
      />
      {showText && (
        <p className="mt-4 text-gray-600 font-medium">{text}</p>
      )}
    </div>
  );
};

export default LoadingSpinner;
