import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import AdminLayout from "@/components/layout/AdminLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog } from "@/components/ui/dialog";
import { UserWithProfile } from "@/services/shared/users";
import { getAdminUsers, getAdminRoles } from "@/services/shared/users";
import { UserList, UserForm } from "./components";
import { Search, UserPlus } from "lucide-react";
import { toast } from "sonner";

const UsersPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isAddUserOpen, setIsAddUserOpen] = useState<boolean>(false);
  const [isEditUserOpen, setIsEditUserOpen] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<UserWithProfile | null>(
    null
  );

  const {
    data: users = [],
    isLoading: usersLoading,
    error: usersError,
    refetch: refetchUsers,
  } = useQuery({
    queryKey: ["admin-users"],
    queryFn: getAdminUsers,
  });

  const { data: roles = [], isLoading: rolesLoading } = useQuery({
    queryKey: ["admin-roles"],
    queryFn: getAdminRoles,
  });

  const filteredUsers = users.filter((user) => {
    const query = searchQuery.toLowerCase();
    return (
      user.email?.toLowerCase().includes(query) ||
      user.full_name?.toLowerCase().includes(query) ||
      user.username?.toLowerCase().includes(query) ||
      user.role_name?.toLowerCase().includes(query)
    );
  });

  const handleAddUser = () => {
    setIsAddUserOpen(true);
  };

  const handleEditUser = (user: UserWithProfile) => {
    setSelectedUser(user);
    setIsEditUserOpen(true);
  };

  const handleUserSaved = () => {
    refetchUsers();
    toast.success("User updated successfully");
    setIsAddUserOpen(false);
    setIsEditUserOpen(false);
    setSelectedUser(null);
  };

  const handleAddUserDialogChange = (open: boolean) => {
    setIsAddUserOpen(open);
    if (!open) {
      // Clear any form state when closing the add dialog
      setSelectedUser(null);
    }
  };

  const handleEditUserDialogChange = (open: boolean) => {
    setIsEditUserOpen(open);
    if (!open) {
      // Clear selected user when closing the edit dialog
      setSelectedUser(null);
    }
  };

  if (usersError) {
    toast.error("Failed to load users. Please try again later.");
  }

  return (
    <AdminLayout title="User Management">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Users</CardTitle>
          <div className="flex space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8 w-[250px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button onClick={handleAddUser}>
              <UserPlus className="mr-1 h-4 w-4" />
              Add New User
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <UserList
            users={filteredUsers}
            isLoading={usersLoading}
            onEditUser={handleEditUser}
            roles={roles}
            refetchUsers={refetchUsers}
          />
        </CardContent>
      </Card>

      {/* Add User Dialog */}
      <Dialog open={isAddUserOpen} onOpenChange={handleAddUserDialogChange}>
        <UserForm
          mode="add"
          onSave={handleUserSaved}
          onCancel={() => handleAddUserDialogChange(false)}
          roles={roles}
          isLoading={rolesLoading}
        />
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserOpen} onOpenChange={handleEditUserDialogChange}>
        {selectedUser && (
          <UserForm
            mode="edit"
            user={selectedUser}
            onSave={handleUserSaved}
            onCancel={() => handleEditUserDialogChange(false)}
            roles={roles}
            isLoading={rolesLoading}
          />
        )}
      </Dialog>
    </AdminLayout>
  );
};

export default UsersPage;
