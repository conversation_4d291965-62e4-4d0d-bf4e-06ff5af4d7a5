/**
 * Core business logic hooks for calculations feature
 * Now includes optimized parallel loading hooks for improved performance
 */

// PHASE 4 OPTIMIZATION: Simplified hooks, removed legacy branching
export { useCalculationDetail } from "./useCalculationDetail";
export { useCalculationDetailComplete } from "./useCalculationDetailComplete";
export { useCalculationDetailCore } from "./useCalculationDetailCore";
export { useCalculation } from "./useCalculation";
export { useCalculationForm } from "./useCalculationForm";
export { useCalculationData } from "./useCalculationData";

// Optimized parallel loading hooks
export {
  useParallelCalculationData,
  useOptimizedCalculationDetailCore,
  useSelectiveParallelData,
} from "./useParallelCalculationData";

export {
  useOptimizedCalculationDetail,
  useCalculationDetailWithOptimization,
  useCalculationDetailABTest,
  useCalculationDataPreloader,
} from "./useOptimizedCalculationDetail";
