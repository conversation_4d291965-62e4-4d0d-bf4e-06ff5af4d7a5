import {
  Injectable,
  Logger,
  UnauthorizedException,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { AuthEventLoggerService } from './auth-event-logger.service';

// Define an interface for the combined user and profile data
export interface UserWithProfile extends User {
  profile?: {
    id: string;
    role_id: number | null;
    roles: { role_name: string } | null;
    // Add other profile fields as needed
  } | null;
}

// Define a more specific type for the data structure returned by the Supabase query
interface ProfileQueryResult {
  id: string;
  role_id: number | null;
  // Assuming the join `roles ( role_name )` correctly returns a single object or null
  roles: { role_name: string } | null;
}

/**
 * JWT Validation Service
 * This service provides methods for validating JWT tokens and retrieving user information
 * It's a simplified version of the AuthService that only includes the methods needed by the JWT strategy
 */
@Injectable()
export class JwtValidationService {
  private readonly logger = new Logger(JwtValidationService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly configService: ConfigService,
    private readonly authEventLogger: AuthEventLoggerService,
  ) {}

  /**
   * Validate a user from a JWT payload
   * This method will be used by the JWT strategy
   * @param payload The JWT payload
   * @returns The user with profile information
   */
  async validateUserFromJwtPayload(payload: any): Promise<UserWithProfile> {
    try {
      this.logger.verbose(`Validating user from JWT payload: ${payload.email}`);

      if (!payload.sub) {
        this.logger.warn('JWT payload missing subject (user ID)');
        throw new UnauthorizedException('Invalid token: missing user ID');
      }

      const adminClient = this.supabaseService.getClient();

      // Instead of querying the users table directly, use the Supabase Auth API
      // to get the user information using the admin API
      const { data: userData, error: userError } =
        await adminClient.auth.admin.getUserById(payload.sub);

      if (userError || !userData || !userData.user) {
        this.logger.warn(`User not found for ID: ${payload.sub}`);
        throw new UnauthorizedException('User not found');
      }

      // Get the user's profile
      const { data: profile, error: profileError } = await adminClient
        .from('profiles')
        .select(
          `
          id,
          role_id,
          roles ( role_name )
        `,
        )
        .eq('id', payload.sub)
        .maybeSingle<ProfileQueryResult>();

      if (profileError) {
        this.logger.error(`Error fetching profile: ${profileError.message}`);
      }

      // Create a user object that matches the UserWithProfile interface
      const user: UserWithProfile = {
        id: payload.sub,
        email: payload.email,
        app_metadata: userData.user.app_metadata || {},
        user_metadata: userData.user.user_metadata || {},
        aud: payload.aud,
        created_at:
          userData.user.created_at ||
          (payload.iat
            ? new Date(payload.iat * 1000).toISOString()
            : new Date().toISOString()),
        profile: profile || null,
      };

      this.logger.verbose(`JWT validation successful for user: ${user.email}`);
      return user;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `JWT validation error: ${message}`,
        error instanceof Error ? error.stack : undefined,
      );

      if (error instanceof UnauthorizedException) {
        throw error;
      }

      throw new UnauthorizedException(
        'Invalid token or authentication failure',
      );
    }
  }
}
