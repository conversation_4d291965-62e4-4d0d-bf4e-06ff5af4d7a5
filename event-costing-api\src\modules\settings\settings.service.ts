import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { SettingDto } from './dto/setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';

@Injectable()
export class SettingsService {
  private readonly logger = new Logger(SettingsService.name);
  private readonly tableName = 'settings'; // Define table name

  constructor(private readonly supabaseService: SupabaseService) {}

  async getSetting(key: string): Promise<SettingDto> {
    this.logger.debug(`Fetching setting with key: ${key}`);
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .select('key, value, description, created_at, updated_at')
      .eq('key', key)
      .single<SettingDto>();

    if (error) {
      if (error.code === 'PGRST116') {
        // Not found
        this.logger.warn(`Setting not found for key: ${key}`);
        throw new NotFoundException(`Setting with key "${key}" not found.`);
      }
      this.logger.error(
        `Error fetching setting ${key}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not retrieve setting.');
    }

    if (!data) {
      // Should be caught by single(), but double-check
      throw new NotFoundException(`Setting with key "${key}" not found.`);
    }

    return data;
  }

  async updateSetting(
    key: string,
    updateDto: UpdateSettingDto,
  ): Promise<SettingDto> {
    this.logger.debug(`Upserting setting with key: ${key}`);
    const supabase = this.supabaseService.getClient();

    let parsedValue: unknown;
    try {
      parsedValue = JSON.parse(updateDto.value) as unknown;
    } catch (e) {
      this.logger.error(`Failed to parse JSON value for setting ${key}`, e);
      throw new BadRequestException('Invalid JSON format for value.');
    }

    const upsertData = {
      key: key,
      value: parsedValue, // Assign parsed unknown value directly
      description: updateDto.description,
    };

    const { data, error } = await supabase
      .from(this.tableName)
      .upsert(upsertData)
      .select('key, value, description, created_at, updated_at')
      .single<SettingDto>();

    if (error) {
      this.logger.error(
        `Error upserting setting ${key}: ${error.message}`,
        error.stack,
      );
      // Consider checking for specific DB errors if needed
      throw new InternalServerErrorException('Could not update setting.');
    }

    if (!data) {
      // Should not happen with upsert+single unless RLS interferes unexpectedly
      this.logger.error(
        `Upsert for setting ${key} succeeded but returned no data.`,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve updated setting.',
      );
    }

    this.logger.log(`Successfully upserted setting: ${key}`);
    return data;
  }
}
