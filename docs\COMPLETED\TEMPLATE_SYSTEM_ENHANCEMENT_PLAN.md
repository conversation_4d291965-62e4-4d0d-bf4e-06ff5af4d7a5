# Template System Enhancement Implementation Plan

## 🚀 **BACKEND STATUS: NO CHANGES NEEDED** ✅

**The backend at `event-costing-api/src/modules/templates/` is already fully implemented and requires NO modifications.**

### What's Already Available:
- ✅ **Public template endpoints** (`GET /templates`, `GET /templates/:id`)
- ✅ **User template access** with proper authentication and filtering
- ✅ **Template-to-calculation flow** (`POST /calculations/from-template/:templateId`)
- ✅ **Admin template management** with full CRUD operations
- ✅ **Attendees field** included in all DTOs and database schema
- ✅ **Proper authentication guards** (JwtAuthGuard, AdminRoleGuard)
- ✅ **Complete filtering and pagination** support

### Implementation Focus:
**This plan focuses entirely on FRONTEND changes** to integrate with the existing backend APIs.

## Overview
This document outlines the comprehensive implementation plan for enhancing the template system with user-facing template pages, template-to-calculation creation flow, and admin template dialog improvements.

## Current State Analysis

### Database Schema ✅
- **Templates table**: Contains `attendees` field (integer, nullable) - **CONFIRMED**
- **Backend API**: Has user template endpoints (`findUserTemplates`, `findPublicTemplates`) - **AVAILABLE**
- **Template-to-calculation endpoint**: `POST /calculations/from-template/:templateId` - **IMPLEMENTED**

### Backend Status ✅ **NO CHANGES NEEDED**

#### Already Implemented:
1. **Public Template Endpoints** (No Auth Required):
   - `GET /templates` - List public templates with filtering/pagination
   - `GET /templates/:id` - Get public template details
   - **DTOs include attendees field** ✅

2. **User Template Access** (Auth Required):
   - `findUserTemplates(user, queryDto)` - Templates accessible to specific user (public + owned)
   - Uses RPC `get_user_accessible_templates` for proper access control
   - **Full filtering and pagination support** ✅

3. **Template-to-Calculation Flow** (Auth Required):
   - `POST /calculations/from-template/:templateId` - Already implemented in calculations module
   - **Proper user access validation** ✅
   - **Complete implementation** ✅

4. **Admin Template Management** (Admin Auth Required):
   - Full CRUD operations via `/admin/templates/*`
   - **All endpoints protected with JwtAuthGuard + AdminRoleGuard** ✅

#### Backend Architecture:
- **TemplatesController**: Public endpoints (no auth)
- **AdminTemplatesController**: Admin endpoints (auth + admin role)
- **TemplatesService**: Business logic with user context
- **Template DTOs**: Include all fields including attendees ✅

### Current Issues Identified (Frontend Only)
1. **Frontend user templates page**: Uses mock data instead of real API calls
2. **Admin template dialog**: Missing attendees field display and editing
3. **Template total value calculation**: Not implemented in admin dialog
4. **User template-to-calculation flow**: Needs proper dialog/form implementation
5. **API endpoints configuration**: Missing user template endpoints in frontend config

## Implementation Plan

### Phase 1: Fix Admin Template Dialog - Attendees Field (Priority: Critical)

#### 1.1 Backend Verification ✅
- **Status**: Attendees field exists in database schema
- **Action**: Verify backend API responses include attendees field

#### 1.2 Frontend Admin Template Dialog Updates
**Files to modify:**
- `src/pages/admin/templates/components/detail/TemplateDetailsDialog.tsx`
- `src/pages/admin/templates/components/form/TemplateEditDialog.tsx`
- `src/pages/admin/templates/components/form/TemplateFormDialog.tsx`
- `src/pages/admin/templates/types/templates.ts`

**Changes needed:**
1. **TemplateDetailsDialog.tsx**:
   - Add attendees field display in template details view
   - Show attendees count with proper formatting (e.g., "150 attendees")

2. **TemplateEditDialog.tsx**:
   - Add attendees input field to edit form
   - Add validation (minimum 1, integer values)
   - Update form schema to include attendees

3. **TemplateFormDialog.tsx**:
   - Add attendees field to creation form
   - Include attendees in form validation schema

4. **Update TypeScript types**:
   - Ensure `Template` interface includes attendees field
   - Update form validation schemas

### Phase 2: Admin Template Dialog - Total Value Calculation (Priority: High)

#### 2.1 Implementation Requirements
**Files to modify:**
- `src/pages/admin/templates/components/detail/TemplateDetailsDialog.tsx`
- `src/pages/admin/templates/components/form/TemplateEditDialog.tsx`

**Features to implement:**
1. **Calculate total value based on**:
   - Template's package selections
   - Current package prices for template's currency
   - Attendees count and item quantities
   - Custom items (if any)

2. **Display breakdown**:
   - Packages total
   - Custom items total (if applicable)
   - Grand total
   - Show as read-only information

3. **Handle edge cases**:
   - Missing package prices for template's currency
   - Invalid package selections
   - Zero attendees

#### 2.2 Service Layer Updates
**Files to create/modify:**
- `src/services/admin/templates/templateCalculations.ts` (new)
- `src/services/admin/templates/index.ts`

**Functions to implement:**
```typescript
export const calculateTemplateTotal = async (template: Template): Promise<{
  packagesTotal: number;
  customItemsTotal: number;
  grandTotal: number;
  breakdown: CalculationBreakdown[];
}>;
```

### Phase 3: User Templates Pages Enhancement (Priority: High)

#### 3.1 Replace Mock Data with Real API Integration
**Files to modify:**
- `src/pages/templates/TemplatesPage.tsx`
- `src/pages/templates/TemplateDetailPage.tsx`

**Changes needed:**
1. **TemplatesPage.tsx**:
   - Remove mock data (`fetchAllTemplates`)
   - Implement real API service calls
   - Add proper error handling and loading states
   - Implement search and filtering with backend API

2. **TemplateDetailPage.tsx**:
   - Remove mock data (`fetchTemplateDetail`)
   - Use real template detail API
   - Calculate actual total from real package data

#### 3.2 Create User Template Services
**Files to create:**
- `src/services/templates/userTemplateService.ts`
- `src/services/templates/index.ts`

**API endpoints to implement:**
```typescript
// Get public templates (no auth required)
export const getPublicTemplates = async (filters: TemplateFilters): Promise<PaginatedTemplatesResponse>;

// Get user-accessible templates (auth required)
export const getUserTemplates = async (filters: TemplateFilters): Promise<PaginatedTemplatesResponse>;

// Get template details
export const getTemplateById = async (id: string): Promise<TemplateDetailDto>;
```

#### 3.3 Update API Endpoints Configuration
**Files to modify:**
- `src/integrations/api/endpoints.ts`

**Add user template endpoints:**
```typescript
TEMPLATES: {
  // User/Public endpoints (no auth required)
  GET_PUBLIC: '/templates',
  GET_PUBLIC_DETAIL: (id: string) => `/templates/${id}`,

  // Existing admin endpoints...
  GET_ADMIN_DETAILS: (id: string) => `/admin/templates/${id}`,
  CREATE_FROM_CALCULATION: '/admin/templates/from-calculation',
  UPDATE: (id: string) => `/admin/templates/${id}`,
  UPDATE_STATUS: (id: string) => `/admin/templates/${id}/status`,
  DELETE: (id: string) => `/admin/templates/${id}`,
  LIST: '/admin/templates',
}
```

**Note**: The backend already supports user-specific template access through the public endpoints with proper filtering. No additional user-specific endpoints are needed.

### Phase 4: Template-to-Calculation Creation Flow (Priority: Core)

#### 4.1 Create Template Selection Dialog
**Files to create:**
- `src/pages/templates/components/CreateCalculationFromTemplateDialog.tsx`
- `src/pages/templates/components/index.ts`

**Features to implement:**
1. **Template customization form**:
   - Calculation name input
   - Event dates selection
   - Attendees count modification
   - Venue selection (optional)
   - Notes/description

2. **Validation**:
   - Required fields validation
   - Date range validation
   - Attendees minimum validation

3. **API integration**:
   - Use existing `POST /calculations/from-template/:templateId` endpoint
   - Handle success/error responses
   - Redirect to new calculation detail page

#### 4.2 Update User Templates Pages
**Files to modify:**
- `src/pages/templates/TemplatesPage.tsx`
- `src/pages/templates/TemplateDetailPage.tsx`

**Changes needed:**
1. **Replace direct navigation** to `/calculations/new?templateId=${id}`
2. **Add "Create Calculation" button** that opens the new dialog
3. **Implement proper flow**: Template selection → Customization dialog → API call → Redirect

#### 4.3 Create Calculation Service
**Files to create/modify:**
- `src/services/calculations/templateCalculationService.ts`
- `src/services/calculations/index.ts`

**Function to implement:**
```typescript
export const createCalculationFromTemplate = async (
  templateId: string,
  customization: CalculationCustomization
): Promise<{ id: string }>;
```

### Phase 5: Enhanced User Experience Features

#### 5.1 Advanced Filtering and Search
**Features to implement:**
1. **Search by**:
   - Template name
   - Description
   - Event type

2. **Filter by**:
   - Event type
   - Attendees range (e.g., 1-50, 51-100, 100+)
   - City/location
   - Date range
   - Public vs owned templates

3. **Sorting options**:
   - Name (A-Z, Z-A)
   - Creation date (newest, oldest)
   - Attendees count
   - Event type

#### 5.2 Template Cards Enhancement
**Features to add:**
1. **Display additional information**:
   - Attendees count with icon
   - Creation date
   - Template owner (for public templates)
   - Estimated total value (if calculable)

2. **Improved actions**:
   - Quick preview button
   - Favorite/bookmark functionality (future)
   - Share template link (for public templates)

## Technical Implementation Details

### TypeScript Interfaces

#### Template Filters
```typescript
interface TemplateFilters {
  search?: string;
  eventType?: string;
  cityId?: string;
  attendeesMin?: number;
  attendeesMax?: number;
  dateStart?: string;
  dateEnd?: string;
  sortBy?: 'name' | 'created_at' | 'attendees' | 'event_type';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}
```

#### Calculation Customization
```typescript
interface CalculationCustomization {
  name: string;
  eventStartDate?: string;
  eventEndDate?: string;
  attendees?: number;
  venueIds?: string[];
  notes?: string;
}
```

### Error Handling Strategy

1. **API Errors**:
   - Network failures: Show retry option
   - Authentication errors: Redirect to login
   - Validation errors: Show field-specific messages
   - Server errors: Show generic error with support contact

2. **User Experience**:
   - Loading states for all async operations
   - Optimistic updates where appropriate
   - Toast notifications for success/error feedback
   - Graceful degradation for missing data

### Testing Strategy

1. **Unit Tests**:
   - Service functions
   - Utility functions
   - Form validation logic

2. **Integration Tests**:
   - API service calls
   - Component interactions
   - Form submissions

3. **E2E Tests**:
   - Complete template-to-calculation flow
   - Search and filtering functionality
   - Admin template management

## Implementation Timeline

### Week 1: Foundation
- [ ] Fix attendees field in admin dialogs
- [ ] Implement template total value calculation
- [ ] Create user template services

### Week 2: Core Features
- [ ] Replace mock data with real API calls
- [ ] Implement template-to-calculation dialog
- [ ] Add enhanced filtering and search

### Week 3: Polish & Testing
- [ ] Improve UI/UX based on feedback
- [ ] Add comprehensive error handling
- [ ] Write tests and documentation

## Success Criteria

1. **Admin templates** display attendees field correctly
2. **Admin templates** show calculated total value
3. **User templates page** uses real API data with search/filtering
4. **Template-to-calculation flow** works end-to-end
5. **Error handling** provides clear user feedback
6. **Performance** is acceptable for large template lists
7. **Type safety** is maintained throughout

## Risk Mitigation

1. **Backend API changes**: Coordinate with backend team for any required modifications
2. **Performance issues**: Implement pagination and lazy loading
3. **Data inconsistency**: Add validation and error boundaries
4. **User experience**: Conduct user testing and gather feedback

## Dependencies

1. **Backend API**: Ensure all required endpoints are available and documented
2. **Database**: Verify template and calculation table relationships
3. **Authentication**: Ensure proper user context for template access
4. **UI Components**: Leverage existing shadcn/ui components for consistency

## Detailed Implementation Steps

### Step 1: Admin Template Dialog - Attendees Field Fix

#### 1.1 Update Template Types
```typescript
// src/pages/admin/templates/types/templates.ts
export const updateTemplateSchema = z.object({
  name: z.string().min(2, 'Template name must be at least 2 characters'),
  description: z.string().optional(),
  event_type: z.string().optional(),
  attendees: z.number().int().min(1, 'Attendees must be at least 1').optional(),
  template_start_date: z.string().optional(),
  template_end_date: z.string().optional(),
  is_public: z.boolean().default(false),
});
```

#### 1.2 Update TemplateDetailsDialog
```typescript
// Add to template details display
<div className="grid grid-cols-2 gap-4">
  <div>
    <Label className="text-sm font-medium text-muted-foreground">Attendees</Label>
    <p className="text-sm">{template.attendees || 'Not specified'}</p>
  </div>
  {/* Other fields... */}
</div>
```

#### 1.3 Update TemplateEditDialog
```typescript
// Add attendees field to form
<FormField
  control={form.control}
  name="attendees"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Attendees</FormLabel>
      <FormControl>
        <Input
          type="number"
          min="1"
          placeholder="Enter number of attendees"
          {...field}
          onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

### Step 2: Template Total Value Calculation

#### 2.1 Create Calculation Service
```typescript
// src/services/admin/templates/templateCalculations.ts
export interface TemplateCalculationResult {
  packagesTotal: number;
  customItemsTotal: number;
  grandTotal: number;
  breakdown: {
    packageId: string;
    packageName: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }[];
  currency: string;
}

export const calculateTemplateTotal = async (
  template: Template
): Promise<TemplateCalculationResult> => {
  // Implementation details...
};
```

#### 2.2 Add to Template Details Dialog
```typescript
// Add total calculation display
const { data: calculationResult } = useQuery({
  queryKey: ['templateCalculation', template?.id],
  queryFn: () => calculateTemplateTotal(template!),
  enabled: !!template,
});

// Display in dialog
{calculationResult && (
  <Card>
    <CardHeader>
      <CardTitle>Estimated Total Value</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-2">
        <div className="flex justify-between">
          <span>Packages Total:</span>
          <span>{formatCurrency(calculationResult.packagesTotal)}</span>
        </div>
        <div className="flex justify-between font-semibold">
          <span>Grand Total:</span>
          <span>{formatCurrency(calculationResult.grandTotal)}</span>
        </div>
      </div>
    </CardContent>
  </Card>
)}
```

### Step 3: User Template Service Implementation

#### 3.1 Create User Template Service
```typescript
// src/services/templates/userTemplateService.ts
import { apiClient, API_ENDPOINTS } from '@/integrations/api';

export interface UserTemplateFilters {
  search?: string;
  eventType?: string;
  cityId?: string;
  attendeesMin?: number;
  attendeesMax?: number;
  limit?: number;
  offset?: number;
}

export const getPublicTemplates = async (
  filters: UserTemplateFilters = {}
): Promise<PaginatedTemplatesResponse> => {
  try {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get(
      `${API_ENDPOINTS.TEMPLATES.GET_PUBLIC}?${params.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching public templates:', error);
    throw error;
  }
};

export const getTemplateById = async (id: string): Promise<TemplateDetailDto> => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.TEMPLATES.GET_PUBLIC_DETAIL(id));
    return response.data;
  } catch (error) {
    console.error('Error fetching template details:', error);
    throw error;
  }
};
```

#### 3.2 Update API Endpoints
```typescript
// src/integrations/api/endpoints.ts
TEMPLATES: {
  // Existing admin endpoints...
  GET_PUBLIC: '/templates',
  GET_PUBLIC_DETAIL: (id: string) => `/templates/${id}`,
  // Admin endpoints...
}
```

### Step 4: Update User Templates Page

#### 4.1 Replace Mock Data in TemplatesPage
```typescript
// src/pages/templates/TemplatesPage.tsx
import { getPublicTemplates } from '@/services/templates/userTemplateService';

const TemplatesPage: React.FC = () => {
  const [filters, setFilters] = useState<UserTemplateFilters>({});

  const { data: templatesResponse, isLoading, isError } = useQuery({
    queryKey: ['publicTemplates', filters],
    queryFn: () => getPublicTemplates(filters),
    meta: {
      onError: () => {
        toast.error('Failed to load templates');
      },
    },
  });

  const templates = templatesResponse?.data || [];

  // Rest of component...
};
```

#### 4.2 Update Template Detail Page
```typescript
// src/pages/templates/TemplateDetailPage.tsx
import { getTemplateById } from '@/services/templates/userTemplateService';

const TemplateDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const { data: template, isLoading, isError } = useQuery({
    queryKey: ['templateDetail', id],
    queryFn: () => getTemplateById(id!),
    enabled: !!id,
    meta: {
      onError: () => {
        toast.error('Failed to load template details');
      },
    },
  });

  // Rest of component...
};
```

### Step 5: Template-to-Calculation Flow

#### 5.1 Create Calculation Service
```typescript
// src/services/calculations/templateCalculationService.ts
export interface CreateCalculationFromTemplateRequest {
  name: string;
  eventStartDate?: string;
  eventEndDate?: string;
  attendees?: number;
  venueIds?: string[];
  notes?: string;
}

export const createCalculationFromTemplate = async (
  templateId: string,
  customization: CreateCalculationFromTemplateRequest
): Promise<{ id: string }> => {
  try {
    const response = await apiClient.post(
      API_ENDPOINTS.CALCULATIONS.FROM_TEMPLATE(templateId),
      customization
    );
    return response.data;
  } catch (error) {
    console.error('Error creating calculation from template:', error);
    throw error;
  }
};
```

#### 5.2 Create Template-to-Calculation Dialog
```typescript
// src/pages/templates/components/CreateCalculationFromTemplateDialog.tsx
interface CreateCalculationFromTemplateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  template: TemplateDetailDto | null;
}

const CreateCalculationFromTemplateDialog: React.FC<CreateCalculationFromTemplateDialogProps> = ({
  isOpen,
  onClose,
  template,
}) => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CreateCalculationFromTemplateRequest>({
    resolver: zodResolver(createCalculationSchema),
    defaultValues: {
      name: template ? `${template.name} - Calculation` : '',
      attendees: template?.attendees || undefined,
    },
  });

  const onSubmit = async (values: CreateCalculationFromTemplateRequest) => {
    if (!template) return;

    setIsSubmitting(true);
    try {
      const result = await createCalculationFromTemplate(template.id, values);
      toast.success('Calculation created successfully');
      navigate(`/calculations/${result.id}`);
      onClose();
    } catch (error) {
      toast.error('Failed to create calculation');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Dialog implementation...
};
```

## Quality Assurance Checklist

### Functionality Testing
- [ ] Admin template dialog shows attendees field
- [ ] Admin template dialog displays total value calculation
- [ ] User templates page loads real data
- [ ] Template search and filtering works
- [ ] Template-to-calculation flow completes successfully
- [ ] Error handling works for all failure scenarios

### UI/UX Testing
- [ ] Loading states are shown appropriately
- [ ] Error messages are clear and actionable
- [ ] Forms validate input correctly
- [ ] Navigation flows are intuitive
- [ ] Responsive design works on mobile

### Performance Testing
- [ ] Template list loads quickly with pagination
- [ ] Search/filtering is responsive
- [ ] No memory leaks in dialogs
- [ ] API calls are optimized

### Security Testing
- [ ] User can only access authorized templates
- [ ] Input validation prevents XSS
- [ ] API endpoints require proper authentication
- [ ] Sensitive data is not exposed in logs

## Deployment Considerations

1. **Database Migration**: Ensure attendees field is properly indexed if needed
2. **API Versioning**: Coordinate with backend team for any breaking changes
3. **Feature Flags**: Consider gradual rollout for new features
4. **Monitoring**: Add logging for template usage analytics
5. **Documentation**: Update user guides and API documentation

## Post-Implementation Tasks

1. **User Training**: Create documentation for new template features
2. **Analytics**: Track template usage and conversion rates
3. **Feedback Collection**: Gather user feedback for improvements
4. **Performance Monitoring**: Monitor API response times and error rates
5. **Maintenance**: Regular cleanup of unused templates and calculations
