import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { User } from '@supabase/supabase-js';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { ListTemplatesDto } from '../dto/list-templates.dto';
import {
  PaginatedTemplatesResponse,
  TemplateSummaryDto,
  PaginatedAdminTemplatesResponse,
  TemplateDetailDto,
} from '../dto/template-summary.dto';
import { ListAdminTemplatesQueryDto } from '../dto/list-admin-templates.dto';
import { TemplateVenueService } from './template-venue.service';
import { TemplateConstants } from '../constants/template.constants';

// Define the raw type returned by the RPC function
interface AccessibleTemplateRaw {
  id: string;
  name: string;
  description?: string | null;
  event_type_id?: string | null; // Updated to use event_type_id
  city_id?: string | null;
  attendees?: number | null;
  template_start_date?: string | null;
  template_end_date?: string | null;
  category_id?: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  is_deleted?: boolean;
  is_public: boolean;
  total_count: number;
}

// Define the structure of the arguments expected by the RPC function
interface GetUserTemplatesRpcArgs {
  p_user_id: string;
  p_search?: string | null;
  p_event_type_id?: string | null; // Updated to use event_type_id
  p_city_id?: string | null;
  p_category_id?: string | null;
  p_date_start?: string | null;
  p_date_end?: string | null;
  p_sort_by?: string | null;
  p_sort_order?: string | null;
  p_limit?: number | null;
  p_offset?: number | null;
}

@Injectable()
export class TemplateQueryService {
  private readonly logger = new Logger(TemplateQueryService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly templateVenueService: TemplateVenueService,
  ) {}

  /**
   * Find templates accessible to a specific user
   */
  async findUserTemplates(
    user: User,
    queryDto: ListTemplatesDto,
  ): Promise<PaginatedTemplatesResponse> {
    this.logger.log(
      `Finding templates for user ${user.id} with query: ${JSON.stringify(queryDto)}`,
    );
    const supabase = this.supabaseService.getClient();
    const rpcParams: GetUserTemplatesRpcArgs = {
      p_user_id: user.id,
      p_search: queryDto.search,
      p_event_type_id: queryDto.eventType, // Updated to use event_type_id
      p_city_id: queryDto.cityId,
      p_category_id: queryDto.categoryId,
      p_date_start: queryDto.dateStart,
      p_date_end: queryDto.dateEnd,
      p_sort_by: queryDto.sortBy,
      p_sort_order: queryDto.sortOrder,
      p_limit: queryDto.limit,
      p_offset: queryDto.offset,
    };

    // @ts-expect-error - Suppress TS2558 due to potential type definition mismatch
    const result = await supabase.rpc<AccessibleTemplateRaw[]>(
      'get_user_accessible_templates',
      rpcParams,
    );
    const data = result.data as AccessibleTemplateRaw[] | null;
    const error = result.error;

    // Check for error first
    if (error) {
      this.logger.error(
        `Error calling get_user_accessible_templates for user ${user.id}: ${error.message}`,
        error.stack,
      );
      // Optionally throw, or return empty
      return { data: [], count: 0 };
    }

    // Check if data is null/undefined (rpc function might return nothing)
    if (!data) {
      this.logger.warn(
        `get_user_accessible_templates for user ${user.id} returned null or undefined data.`,
      );
      return { data: [], count: 0 };
    }

    // Now data should be AccessibleTemplateRaw[]
    const totalCount = data.length > 0 ? data[0].total_count || 0 : 0;

    const templates: TemplateSummaryDto[] = data.map(
      (raw: AccessibleTemplateRaw): TemplateSummaryDto => {
        return {
          id: raw.id,
          name: raw.name,
          description: raw.description ?? undefined,
          event_type_id: raw.event_type_id ?? undefined,
          city_id: raw.city_id ?? undefined,
          attendees: raw.attendees ?? undefined,
          template_start_date: raw.template_start_date
            ? new Date(raw.template_start_date)
            : undefined,
          template_end_date: raw.template_end_date
            ? new Date(raw.template_end_date)
            : undefined,
          category_id: raw.category_id ?? undefined,
          created_at: new Date(raw.created_at),
          updated_at: new Date(raw.updated_at),
          created_by: raw.created_by,
          is_public: raw.is_public,
          is_deleted: raw.is_deleted || false, // Ensure is_deleted is always included
        };
      },
    );

    // Add venue IDs to templates
    await this.templateVenueService.addVenueIdsToTemplates(templates);

    // Return structure { data, count }
    return { data: templates, count: totalCount };
  }

  /**
   * Find public templates with filtering/pagination
   */
  async findPublicTemplates(
    queryDto: ListTemplatesDto,
  ): Promise<PaginatedTemplatesResponse> {
    this.logger.log(
      `Finding public templates with query: ${JSON.stringify(queryDto)}`,
    );
    const supabase = this.supabaseService.getClient();

    // Direct query approach (simpler than RPC for public-only)
    let query = supabase
      .from(TemplateConstants.TABLE_NAME)
      .select(TemplateConstants.SUMMARY_SELECT_FIELDS, { count: 'exact' }) // Fetch count simultaneously
      .eq('is_public', true)
      .eq('is_deleted', false);

    // Apply Filters
    if (queryDto.search) {
      query = query.ilike('name', `%${queryDto.search}%`);
    }
    if (queryDto.eventType) {
      query = query.eq('event_type_id', queryDto.eventType);
    }
    if (queryDto.cityId) {
      query = query.eq('city_id', queryDto.cityId);
    }
    if (queryDto.categoryId) {
      query = query.eq('category_id', queryDto.categoryId);
    }
    if (queryDto.dateStart) {
      query = query.gte('template_start_date', queryDto.dateStart);
    }
    if (queryDto.dateEnd) {
      // Assuming template_start_date is the primary date for filtering range
      query = query.lte('template_start_date', queryDto.dateEnd);
    }

    // Apply Sorting
    const sortBy = queryDto.sortBy || 'created_at';
    const sortOrder = queryDto.sortOrder || 'desc';
    // Basic validation/mapping for sort column
    const validSortColumns = [
      'name',
      'created_at',
      'updated_at',
      'template_start_date',
    ];
    const dbSortColumn = validSortColumns.includes(sortBy)
      ? sortBy
      : 'created_at';
    query = query.order(dbSortColumn, {
      ascending: sortOrder === 'asc',
      nullsFirst: false,
    });

    // Apply Pagination
    const limit = queryDto.limit || 20;
    const offset = queryDto.offset || 0;
    query = query.range(offset, offset + limit - 1);

    // Execute Query
    const { data, error, count } = await query; // Expect array of summary DTOs

    if (error) {
      this.logger.error(
        `Error fetching public templates: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Could not retrieve public templates.',
      );
    }

    // Map raw data if necessary
    const templates: TemplateSummaryDto[] = (data || []).map((raw: any) => ({
      ...raw,
      template_start_date: raw.template_start_date
        ? new Date(raw.template_start_date as unknown as string)
        : undefined,
      template_end_date: raw.template_end_date
        ? new Date(raw.template_end_date as unknown as string)
        : undefined,
      created_at: new Date(raw.created_at as unknown as string),
      updated_at: new Date(raw.updated_at as unknown as string),
      is_deleted: raw.is_deleted || false, // Ensure is_deleted is always included
    }));

    // Add venue IDs to templates
    await this.templateVenueService.addVenueIdsToTemplates(templates);

    return { data: templates, count: count || 0 };
  }
}
