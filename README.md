# Quote Craft Profit

<p align="center">
  <img src="docs/assets/logo.png" alt="Quote Craft Profit Logo" width="200" height="200" />
</p>

<p align="center">
  A comprehensive event planning and cost calculation platform for event professionals
</p>

<p align="center">
  <a href="#project-overview">Overview</a> •
  <a href="#key-features">Features</a> •
  <a href="#architecture">Architecture</a> •
  <a href="#getting-started">Getting Started</a> •
  <a href="#deployment">Deployment</a> •
  <a href="#roadmap">Roadmap</a>
</p>

## Project Overview

Quote Craft Profit is a comprehensive web application designed for event planning professionals to streamline the process of creating detailed cost calculations and generating accurate quotes for events. The application addresses several key challenges faced by event planning businesses:

1. **Complex Pricing Models**: Event services often have different pricing models (per event, per day, per attendee, etc.)
2. **Dynamic Calculations**: Event costs vary based on multiple factors (duration, attendee count, location)
3. **Profitability Tracking**: Businesses need to track estimated profit margins across different events
4. **Quote Generation**: Creating professional quotes quickly and accurately is essential for winning business
5. **Service Catalog Management**: Maintaining a comprehensive catalog of services with pricing variations

## Project Components

The project consists of two main components:

1. **Frontend Application (quote-craft-profit)**

   - React-based web application with TypeScript
   - User interface for all application features
   - Direct integration with Supabase for authentication
   - Communication with the backend API for advanced features

2. **Backend API (event-costing-api)**
   - NestJS-based RESTful API
   - Handles complex business logic
   - Manages template operations
   - Provides advanced calculation features
   - Integrates with Supabase for data storage

## Key Features

### Service Catalog Management

- Define packages with variations and configurable options
- Set up different pricing models (per event, per day, per attendee)
- Configure multi-currency pricing and costs
- Manage city-based availability and venue-specific offerings
- Organize packages by categories and divisions
- Define package dependencies (requires/conflicts)

### Dynamic Calculations

- Create calculations from scratch or templates
- Capture essential event details (dates, attendees, city, currency)
- Link calculations to clients and events
- Browse and filter the package catalog
- Add standard catalog items and custom line items
- Automatically calculate quantities based on package rules and event inputs

### Real-time Totals and Profitability

- Display running subtotals that update as items are added/modified
- Calculate final totals including taxes and discounts
- Implement price locking to ensure historical estimates remain consistent
- Track estimated profit based on costs and prices

### User and Access Management

- Role-based permissions (Admin vs. Planner)
- User authentication and authorization
- Save calculations as drafts or mark them as completed
- Export calculation summaries in various formats

### Client and Event Management

- Maintain a database of clients
- Track events with detailed information
- Associate calculations with specific clients and events
- View calculation history by client or event

### Template Management

- Save common configurations as templates
- Create new calculations from templates
- Manage public and private templates
- Version control for templates

## Architecture

### Technology Stack

#### Frontend (quote-craft-profit)

- **Framework**: React 18 with TypeScript 5
- **Build Tool**: Vite
- **UI Components**: shadcn/ui with Tailwind CSS
- **State Management**: React Query for server state, React Context for global state
- **Form Handling**: React Hook Form with Zod validation
- **Routing**: React Router
- **API Client**: Axios
- **Data Visualization**: Recharts
- **Date Utilities**: Date-fns
- **Icons**: Lucide React
- **Notifications**: Sonner (toast notifications)
- **Image Editing**: React Image Crop
- **Carousels**: Embla Carousel
- **Theme Management**: Next Themes

#### Backend (event-costing-api)

- **Framework**: NestJS 11 with TypeScript 5
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with JWT
- **Validation**: class-validator & class-transformer
- **Documentation**: Swagger/OpenAPI
- **Logging**: Winston

### Code Organization

#### Feature-Based Structure

The frontend codebase is organized using a feature-based approach, where all code related to a specific feature is grouped together:

```
src/pages/calculations/           # Calculation feature
├── components/                   # Calculation-specific components
│   ├── detail/                   # Components for detail page
│   ├── new/                      # Components for new calculation page
│   ├── list/                     # Components for list page
│   └── shared/                   # Shared calculation components
├── hooks/                        # Calculation-specific hooks
├── utils/                        # Calculation-specific utility functions
├── services/                     # Calculation-specific services
├── types/                        # Calculation-specific types
├── constants/                    # Calculation-specific constants
├── CalculationDetailPage.tsx     # Main detail page
├── NewCalculationPage.tsx        # New calculation page
└── CalculationsPage.tsx          # List page
```

This organization provides several benefits:

- All related code is in one place, making it easier to find and maintain
- Better encapsulation of feature-specific code
- Easier onboarding for new developers
- Simplified imports with shorter paths
- Better scalability for adding new features

### Integration Points

Both the frontend and backend integrate with Supabase:

- **Authentication**: User login, registration, and session management
- **Database**: PostgreSQL database for data storage
- **Storage**: File storage for documents and exports

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Git
- Supabase account

### Setting Up the Frontend

1. **Navigate to the frontend directory**:

   ```bash
   cd quote-craft-profit
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Set up environment variables**:

   - Copy `.env.example` to `.env`
   - Update the variables with your Supabase credentials and API URL:
     ```
     VITE_SUPABASE_URL=your_supabase_url
     VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
     VITE_EVENT_COSTING_API_URL=http://localhost:5000
     ```

4. **Start the development server**:
   ```bash
   npm run dev
   ```

### Setting Up the Backend

1. **Navigate to the backend directory**:

   ```bash
   cd event-costing-api
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Set up environment variables**:

   - Copy `.env.example` to `.env`
   - Update the variables with your Supabase credentials:
     ```
     PORT=5000
     JWT_SECRET=your_jwt_secret
     SUPABASE_URL=your_supabase_url
     SUPABASE_KEY=your_supabase_key
     SUPABASE_ANON_KEY=your_supabase_anon_key
     SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
     ```

4. **Start the development server**:
   ```bash
   npm run start:dev
   ```

## Deployment

### Frontend Deployment (Vercel)

1. **Connect your repository to Vercel**:

   - Create a Vercel account at [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Select the "Quote Craft Profit" project

2. **Configure environment variables**:

   - Add the required environment variables in the Vercel dashboard

3. **Deploy**:
   - Vercel will automatically deploy your application
   - Each push to the main branch will trigger a new deployment

### Backend Deployment (Railway)

1. **Connect your repository to Railway**:

   - Create a Railway account at [railway.app](https://railway.app)
   - Import your GitHub repository
   - Select the "Event Costing API" project

2. **Configure environment variables**:

   - Add the required environment variables in the Railway dashboard

3. **Deploy**:
   - Railway will automatically deploy your application
   - Each push to the main branch will trigger a new deployment

## Roadmap

The project has several planned enhancements:

1. **Advanced Reporting**: Comprehensive reporting and analytics
2. **Mobile Application**: Native mobile experience for on-the-go access
3. **Integration with CRM Systems**: Connect with popular CRM platforms
4. **AI-Powered Recommendations**: Suggest packages based on event requirements
5. **Multi-Language Support**: Localization for international users

## Documentation

For more detailed documentation, please refer to:

- [Frontend Documentation](quote-craft-profit/README.md)
- [Backend Documentation](event-costing-api/README.md)
- [Project Overview](docs/project-overview.md)
- [Authentication Documentation](quote-craft-profit/docs/AUTHENTICATION.md)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
