import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { ArrowDownAZ, ArrowUpAZ, Calendar, Check, ChevronsUpDown } from 'lucide-react';

export type SortField = 'name' | 'category_id' | 'updated_at';

interface SortOption {
  value: SortField;
  label: string;
  icon: React.ReactNode;
}

interface SortMenuProps {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (field: string, order: 'asc' | 'desc') => void;
}

export const SortMenu: React.FC<SortMenuProps> = ({
  sortBy,
  sortOrder,
  onSortChange,
}) => {
  const sortOptions: SortOption[] = [
    {
      value: 'name',
      label: 'Name',
      icon: <ArrowDownAZ className='h-4 w-4 mr-2' />,
    },
    {
      value: 'category_id',
      label: 'Category',
      icon: <ArrowDownAZ className='h-4 w-4 mr-2' />,
    },
    {
      value: 'updated_at',
      label: 'Last Modified Date',
      icon: <Calendar className='h-4 w-4 mr-2' />,
    },
  ];

  const toggleSortOrder = () => {
    onSortChange(sortBy, sortOrder === 'asc' ? 'desc' : 'asc');
  };

  const handleSortFieldChange = (field: SortField) => {
    // If selecting the same field, toggle the order
    if (field === sortBy) {
      toggleSortOrder();
    } else {
      // For a new field, use ascending for name/category and descending for dates
      const newOrder = field === 'updated_at' ? 'desc' : 'asc';
      onSortChange(field, newOrder);
    }
  };

  const currentSortOption = sortOptions.find((option) => option.value === sortBy);
  const sortOrderIcon =
    sortOrder === 'asc' ? (
      <ArrowUpAZ className='h-4 w-4 ml-2' />
    ) : (
      <ArrowDownAZ className='h-4 w-4 ml-2' />
    );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='outline'
          size='sm'
          className='flex items-center gap-1 font-medium'
          aria-label={`Sort by ${currentSortOption?.label || 'Name'} (${
            sortOrder === 'asc' ? 'ascending' : 'descending'
          })`}
        >
          <ChevronsUpDown className='h-4 w-4 mr-1' />
          <span>
            Sort:{' '}
            <span className='text-primary'>{currentSortOption?.label || 'Name'}</span>
            <span className='ml-1 text-muted-foreground'>
              ({sortOrder === 'asc' ? 'A-Z' : 'Z-A'})
            </span>
          </span>
          {sortOrderIcon}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-56'>
        <DropdownMenuLabel>Sort by</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {sortOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleSortFieldChange(option.value)}
            className='flex items-center justify-between'
          >
            <span className='flex items-center'>
              {option.icon}
              {option.label}
            </span>
            {sortBy === option.value && <Check className='h-4 w-4' />}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuLabel className='text-xs text-muted-foreground pt-2'>
          Sort Order
        </DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => onSortChange(sortBy, 'asc')}
          className='flex items-center justify-between'
        >
          <span className='flex items-center'>
            <ArrowUpAZ className='h-4 w-4 mr-2' />
            Ascending (A-Z, oldest first)
          </span>
          {sortOrder === 'asc' && <Check className='h-4 w-4' />}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onSortChange(sortBy, 'desc')}
          className='flex items-center justify-between'
        >
          <span className='flex items-center'>
            <ArrowDownAZ className='h-4 w-4 mr-2' />
            Descending (Z-A, newest first)
          </span>
          {sortOrder === 'desc' && <Check className='h-4 w-4' />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
