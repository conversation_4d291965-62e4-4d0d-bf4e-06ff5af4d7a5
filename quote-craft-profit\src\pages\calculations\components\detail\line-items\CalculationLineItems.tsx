import React, { useMemo, memo } from "react";
import { Plus, Table, Zap } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { LineItem } from "@/types/calculation";
import LineItemsTableView from "./LineItemsTableView";
import { Badge } from "@/components/ui/badge";

import { calculateLineItemTotal } from "../../../utils/calculationUtils";
import { CategoryWithOrder } from "../../../utils/sortingUtils";
import { useCategorySorting } from "../../../hooks/utils/useCategorySorting";
import { useInlineLineItemUpdates } from "../../../hooks/data/useInlineLineItemUpdates";

interface CalculationLineItemsProps {
  lineItems: LineItem[];
  onEditLineItem: (lineItem: LineItem) => void;
  onRemoveLineItem: (lineItemId: string) => void;
  onAddCustomItem: () => void;
  formatCurrency: (amount: number) => string;
  categories?: CategoryWithOrder[];
  calculationId: string;
}

const CalculationLineItems: React.FC<CalculationLineItemsProps> = ({
  lineItems,
  onEditLineItem,
  onRemoveLineItem,
  onAddCustomItem,
  formatCurrency,
  categories,
  calculationId,
}) => {
  // Table row editing functionality
  const { updateField: updateFieldOriginal, isUpdating } =
    useInlineLineItemUpdates(calculationId);

  // Wrap updateField to match expected signature (Promise<void> instead of Promise<LineItem>)
  const updateField = async (
    lineItemId: string,
    field: string,
    value: string | number
  ): Promise<void> => {
    await updateFieldOriginal(lineItemId, field, value);
  };

  // Group line items by category
  const lineItemsByCategory = useMemo(() => {
    const grouped: Record<string, LineItem[]> = {};

    // Initialize with all categories (with null check)
    if (Array.isArray(categories)) {
      categories.forEach((category) => {
        grouped[category.id] = [];
      });
    }

    // Add an "Uncategorized" category
    grouped["uncategorized"] = [];

    // Group line items
    if (Array.isArray(lineItems)) {
      lineItems.forEach((item) => {
        const categoryId = item.category_id || "uncategorized";
        if (grouped[categoryId]) {
          grouped[categoryId].push(item);
        } else {
          grouped["uncategorized"].push(item);
        }
      });
    }

    return grouped;
  }, [lineItems, categories]);

  // Use our custom hook for sorting line items and categories
  // Note: Currently not using sorted results, but keeping hook for future use
  useCategorySorting(lineItems || [], categories || [], lineItemsByCategory);

  // Calculate totals using the utility function
  const totals = useMemo(() => {
    if (!Array.isArray(lineItems) || lineItems.length === 0) {
      return { totalItems: 0, totalAmount: 0 };
    }

    return lineItems.reduce(
      (acc, item) => {
        // Use the utility function to calculate the item price
        const itemPrice = calculateLineItemTotal(item);

        return {
          totalItems: acc.totalItems + 1,
          totalAmount: acc.totalAmount + itemPrice,
        };
      },
      { totalItems: 0, totalAmount: 0 }
    );
  }, [lineItems]);

  // Get category names map
  const categoryNamesMap = useMemo(() => {
    const map: Record<string, string> = {};
    if (Array.isArray(categories)) {
      categories.forEach((category) => {
        map[category.id] = category.name;
      });
    }
    map["uncategorized"] = "Uncategorized";
    return map;
  }, [categories]);

  return (
    <div className="mb-8">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border dark:border-gray-700">
        <div className="flex justify-between items-center mb-6">
          <div>
            <div className="flex items-center gap-3">
              <Table className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              <h2 className="text-xl font-bold dark:text-white">
                Selected Items
              </h2>
              <Badge
                variant="outline"
                className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700"
              >
                <Zap className="h-3 w-3 mr-1" />
                Row Editing Mode
              </Badge>
            </div>
            {lineItems.length > 0 && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {totals.totalItems} items | Total:{" "}
                {formatCurrency(totals.totalAmount)}
              </p>
            )}
          </div>
          <div className="flex items-center gap-4">
            <Button
              onClick={onAddCustomItem}
              className="flex items-center gap-2"
            >
              <Plus size={16} />
              Add Custom Item
            </Button>
          </div>
        </div>

        {!Array.isArray(lineItems) || lineItems.length === 0 ? (
          <div className="p-8 text-center border dark:border-gray-700 rounded-lg">
            <Table className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400 mb-2 font-medium">
              No items added to this calculation yet
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500">
              Select packages from above or add custom items to get started
            </p>
          </div>
        ) : (
          // Table View with Row Editing
          <LineItemsTableView
            lineItems={lineItems}
            onEdit={onEditLineItem}
            onDelete={onRemoveLineItem}
            onUpdateField={updateField}
            formatCurrency={formatCurrency}
            categoryNamesMap={categoryNamesMap}
            isUpdating={isUpdating}
          />
        )}
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(CalculationLineItems);
