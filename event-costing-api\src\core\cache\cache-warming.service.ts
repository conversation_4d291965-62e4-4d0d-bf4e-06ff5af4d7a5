/**
 * Cache Warming Service
 *
 * PHASE 2: Implements intelligent cache warming for frequently accessed data
 * Reduces cold start times and improves user experience
 */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { CacheService } from './cache.service';
import { SupabaseService } from '../supabase/supabase.service';

export interface CacheWarmingConfig {
  key: string;
  factory: () => Promise<any>;
  ttl?: number;
  priority: number;
  schedule?: string; // Cron expression for automatic warming
  enabled: boolean;
}

@Injectable()
export class CacheWarmingService implements OnModuleInit {
  private readonly logger = new Logger(CacheWarmingService.name);
  private warmingConfigs: CacheWarmingConfig[] = [];

  constructor(
    private readonly cacheService: CacheService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async onModuleInit() {
    // Initialize cache warming configurations
    this.initializeWarmingConfigs();

    // Perform initial cache warming on startup
    await this.performInitialWarming();
  }

  /**
   * Initialize cache warming configurations for frequently accessed data
   */
  private initializeWarmingConfigs(): void {
    this.warmingConfigs = [
      // High priority: Categories (used in almost every calculation)
      {
        key: 'categories:all',
        factory: () => this.warmCategories(),
        ttl: 60 * 60 * 6, // 6 hours
        priority: 10,
        schedule: '0 */6 * * *', // Every 6 hours
        enabled: true,
      },

      // High priority: Cities (used in venue filtering)
      {
        key: 'cities:all',
        factory: () => this.warmCities(),
        ttl: 60 * 60 * 12, // 12 hours
        priority: 9,
        schedule: '0 */12 * * *', // Every 12 hours
        enabled: true,
      },

      // High priority: Currencies (used in all calculations)
      {
        key: 'currencies:all',
        factory: () => this.warmCurrencies(),
        ttl: 60 * 60 * 24, // 24 hours
        priority: 9,
        schedule: '0 0 * * *', // Every day at midnight
        enabled: true,
      },

      // Medium priority: Popular packages (most frequently used)
      {
        key: 'packages:popular',
        factory: () => this.warmPopularPackages(),
        ttl: 60 * 60 * 2, // 2 hours
        priority: 7,
        schedule: '0 */2 * * *', // Every 2 hours
        enabled: true,
      },

      // Medium priority: Divisions
      {
        key: 'divisions:all',
        factory: () => this.warmDivisions(),
        ttl: 60 * 60 * 8, // 8 hours
        priority: 6,
        schedule: '0 */8 * * *', // Every 8 hours
        enabled: true,
      },

      // Lower priority: Venues (location-specific)
      {
        key: 'venues:popular',
        factory: () => this.warmPopularVenues(),
        ttl: 60 * 60 * 4, // 4 hours
        priority: 5,
        schedule: '0 */4 * * *', // Every 4 hours
        enabled: true,
      },

      // Lower priority: Event types
      {
        key: 'event-types:all',
        factory: () => this.warmEventTypes(),
        ttl: 60 * 60 * 12, // 12 hours
        priority: 4,
        schedule: '0 */12 * * *', // Every 12 hours
        enabled: true,
      },
    ];

    this.logger.log(
      `Initialized ${this.warmingConfigs.length} cache warming configurations`,
    );
  }

  /**
   * Perform initial cache warming on application startup
   */
  private async performInitialWarming(): Promise<void> {
    this.logger.log('Starting initial cache warming...');

    const enabledConfigs = this.warmingConfigs.filter(config => config.enabled);
    await this.cacheService.warmCache(enabledConfigs);

    this.logger.log('Initial cache warming completed');
  }

  /**
   * Scheduled cache warming - runs every 2 hours
   * Note: Cron functionality would need @nestjs/schedule package
   */
  async scheduledCacheWarming(): Promise<void> {
    this.logger.log('Starting scheduled cache warming...');

    // Only warm high-priority items during scheduled runs
    const highPriorityConfigs = this.warmingConfigs.filter(
      config => config.enabled && config.priority >= 7,
    );

    await this.cacheService.warmCache(highPriorityConfigs);
    this.logger.log('Scheduled cache warming completed');
  }

  /**
   * Manual cache warming trigger
   */
  async warmCacheManually(keys?: string[]): Promise<void> {
    this.logger.log(
      `Manual cache warming triggered${keys ? ` for keys: ${keys.join(', ')}` : ''}`,
    );

    let configsToWarm = this.warmingConfigs.filter(config => config.enabled);

    if (keys && keys.length > 0) {
      configsToWarm = configsToWarm.filter(config => keys.includes(config.key));
    }

    await this.cacheService.warmCache(configsToWarm);
    this.logger.log('Manual cache warming completed');
  }

  /**
   * Cache warming factory functions
   */
  private async warmCategories(): Promise<any> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from('categories')
      .select('id, name, display_order')
      .order('display_order', { ascending: true });

    if (error) {
      this.logger.error('Error warming categories cache:', error);
      throw error;
    }

    this.logger.debug(
      `Warmed categories cache with ${data?.length || 0} items`,
    );
    return data || [];
  }

  private async warmCities(): Promise<any> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from('cities')
      .select('id, name, country')
      .order('name', { ascending: true });

    if (error) {
      this.logger.error('Error warming cities cache:', error);
      throw error;
    }

    this.logger.debug(`Warmed cities cache with ${data?.length || 0} items`);
    return data || [];
  }

  private async warmCurrencies(): Promise<any> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from('currencies')
      .select('id, code, name, symbol')
      .order('code', { ascending: true });

    if (error) {
      this.logger.error('Error warming currencies cache:', error);
      throw error;
    }

    this.logger.debug(
      `Warmed currencies cache with ${data?.length || 0} items`,
    );
    return data || [];
  }

  private async warmPopularPackages(): Promise<any> {
    const supabase = this.supabaseService.getClient();

    // Get packages that are frequently used in calculations
    const { data, error } = await supabase
      .from('packages')
      .select(
        `
        id, name, description, category_id, division_id,
        categories!inner(name),
        divisions!inner(name)
      `,
      )
      .eq('is_deleted', false)
      .limit(100) // Top 100 most popular packages
      .order('updated_at', { ascending: false });

    if (error) {
      this.logger.error('Error warming popular packages cache:', error);
      throw error;
    }

    this.logger.debug(
      `Warmed popular packages cache with ${data?.length || 0} items`,
    );
    return data || [];
  }

  private async warmDivisions(): Promise<any> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from('divisions')
      .select('id, name, code')
      .order('name', { ascending: true });

    if (error) {
      this.logger.error('Error warming divisions cache:', error);
      throw error;
    }

    this.logger.debug(`Warmed divisions cache with ${data?.length || 0} items`);
    return data || [];
  }

  private async warmPopularVenues(): Promise<any> {
    const supabase = this.supabaseService.getClient();

    // Get venues that are frequently used
    const { data, error } = await supabase
      .from('venues')
      .select(
        `
        id, name, city_id, capacity,
        cities!inner(name)
      `,
      )
      .limit(50) // Top 50 popular venues
      .order('updated_at', { ascending: false });

    if (error) {
      this.logger.error('Error warming popular venues cache:', error);
      throw error;
    }

    this.logger.debug(
      `Warmed popular venues cache with ${data?.length || 0} items`,
    );
    return data || [];
  }

  private async warmEventTypes(): Promise<any> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from('event_types')
      .select('id, name, description')
      .order('name', { ascending: true });

    if (error) {
      this.logger.error('Error warming event types cache:', error);
      throw error;
    }

    this.logger.debug(
      `Warmed event types cache with ${data?.length || 0} items`,
    );
    return data || [];
  }

  /**
   * Get cache warming status
   */
  getWarmingStatus(): {
    totalConfigs: number;
    enabledConfigs: number;
    lastWarming: Date | null;
    nextScheduledWarming: Date | null;
  } {
    return {
      totalConfigs: this.warmingConfigs.length,
      enabledConfigs: this.warmingConfigs.filter(c => c.enabled).length,
      lastWarming: null, // Would need to track this
      nextScheduledWarming: null, // Would need to calculate based on cron
    };
  }

  /**
   * Enable/disable specific cache warming
   */
  toggleCacheWarming(key: string, enabled: boolean): boolean {
    const config = this.warmingConfigs.find(c => c.key === key);
    if (config) {
      config.enabled = enabled;
      this.logger.log(
        `Cache warming for ${key} ${enabled ? 'enabled' : 'disabled'}`,
      );
      return true;
    }
    return false;
  }
}
