import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import { User } from '@supabase/supabase-js';
import { updateProfile } from '@/services/shared/users';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';

// Import schema from centralized location
import { profileFormSchema, type ProfileFormValues } from '../../schemas';

interface ProfileData {
  full_name: string;
  username: string;
  role_name: string;
  phone_number: string;
  address: string;
  city: string;
  company_name: string;
  profile_picture_url?: string | null;
}

interface PersonalInfoSectionProps {
  user: User | null;
  profileData: ProfileData;
  onProfileUpdated: (data: ProfileData) => void;
}

const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  user,
  profileData,
  onProfileUpdated,
}) => {
  // Track submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create form
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      fullName: '',
      username: '',
      phoneNumber: '',
      address: '',
      city: '',
      companyName: '',
    },
  });

  // Update form values when profile data changes
  useEffect(() => {
    if (profileData) {
      console.log('ProfileData changed, updating form values', profileData);

      // Directly set values for each field individually
      form.setValue('fullName', profileData.full_name || '', {
        shouldValidate: false,
        shouldDirty: false,
        shouldTouch: false,
      });

      form.setValue('username', profileData.username || '', {
        shouldValidate: false,
        shouldDirty: false,
        shouldTouch: false,
      });

      form.setValue('phoneNumber', profileData.phone_number || '', {
        shouldValidate: false,
        shouldDirty: false,
        shouldTouch: false,
      });

      form.setValue('address', profileData.address || '', {
        shouldValidate: false,
        shouldDirty: false,
        shouldTouch: false,
      });

      form.setValue('city', profileData.city || '', {
        shouldValidate: false,
        shouldDirty: false,
        shouldTouch: false,
      });

      form.setValue('companyName', profileData.company_name || '', {
        shouldValidate: false,
        shouldDirty: false,
        shouldTouch: false,
      });

      // Then reset the form state to clean
      form.reset(
        {
          fullName: profileData.full_name || '',
          username: profileData.username || '',
          phoneNumber: profileData.phone_number || '',
          address: profileData.address || '',
          city: profileData.city || '',
          companyName: profileData.company_name || '',
        },
        {
          keepValues: true,
          keepDirtyValues: false,
          keepErrors: false,
          keepDirty: false,
          keepIsSubmitted: false,
          keepTouched: false,
          keepIsValid: false,
          keepSubmitCount: false,
        },
      );

      console.log('Form values directly set with new profile data', profileData);
    }
  }, [profileData, form]);

  // Debug effect to monitor form state changes
  useEffect(() => {
    const formState = form.formState;
    console.log('Form state updated:', {
      isDirty: formState.isDirty,
      isSubmitting: formState.isSubmitting,
      isSubmitted: formState.isSubmitted,
      isValid: formState.isValid,
      dirtyFields: Object.keys(formState.dirtyFields || {}),
    });
  }, [form.formState]);

  const onSubmit = async (values: ProfileFormValues) => {
    if (!user) return;

    setIsSubmitting(true);

    try {
      // Create update data object
      const updateData = {
        full_name: values.fullName,
        phone: values.phoneNumber,
        company: values.companyName,
        address: values.address,
        city: values.city,
      };

      console.log('Submitting profile update with data:', updateData);

      // Use only the API service for updates - this will handle both profile and auth updates
      const updatedProfile = await updateProfile(updateData);

      console.log('Received updated profile from API:', updatedProfile);

      // Create updated profile data object from API response or form values
      const updatedProfileData = {
        full_name: updatedProfile?.full_name || values.fullName,
        username: updatedProfile?.username || values.username || '',
        role_name: updatedProfile?.roles?.role_name || profileData.role_name,
        phone_number: updatedProfile?.phone_number || values.phoneNumber || '',
        address: updatedProfile?.address || values.address || '',
        city: updatedProfile?.city || values.city || '',
        company_name: updatedProfile?.company_name || values.companyName || '',
        profile_picture_url: profileData.profile_picture_url, // Preserve this value
      };

      console.log('Created updated profile data:', updatedProfileData);

      // First update the parent component to trigger a re-render
      onProfileUpdated(updatedProfileData);

      // Show success message
      toast.success('Profile updated successfully');

      // We don't need to manually update the form here because:
      // 1. The parent component will re-render with the new profileData
      // 2. Our useEffect hook will detect the new profileData and update the form
      console.log('Form will be updated via useEffect when profileData changes');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className='mb-8'>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
        <CardDescription>Update your personal details</CardDescription>
      </CardHeader>
      <CardContent>
        <Form
          {...form}
          key={`form-${profileData.full_name}-${profileData.phone_number}-${profileData.address}`}
        >
          <form
            id='profile-form'
            key={`form-inner-${profileData.full_name}-${profileData.phone_number}-${profileData.address}`}
            onSubmit={form.handleSubmit(onSubmit)}
            className='space-y-4'
          >
            <FormField
              key={`fullName-${profileData.full_name}`}
              control={form.control}
              name='fullName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              key={`username-${profileData.username}`}
              control={form.control}
              name='username'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Username</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='space-y-2'>
              <FormLabel>Email</FormLabel>
              <Input value={user?.email || ''} disabled />
              <p className='text-sm text-muted-foreground'>Email cannot be changed</p>
            </div>

            <FormField
              key={`phoneNumber-${profileData.phone_number}`}
              control={form.control}
              name='phoneNumber'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              key={`companyName-${profileData.company_name}`}
              control={form.control}
              name='companyName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              key={`address-${profileData.address}`}
              control={form.control}
              name='address'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              key={`city-${profileData.city}`}
              control={form.control}
              name='city'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='space-y-2'>
              <FormLabel>Role</FormLabel>
              <Input value={profileData?.role_name || 'user'} disabled />
            </div>
          </form>
        </Form>
      </CardContent>
      <CardFooter>
        <Button
          type='submit'
          form='profile-form'
          // Only disable the button when actively submitting
          disabled={isSubmitting}
          // Add a key to force re-render when submission state changes
          key={`submit-button-${isSubmitting ? 'submitting' : 'ready'}`}
        >
          {isSubmitting ? (
            <>
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default PersonalInfoSection;
