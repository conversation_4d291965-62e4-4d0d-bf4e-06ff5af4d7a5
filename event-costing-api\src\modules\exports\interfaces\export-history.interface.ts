import { ExportFormat } from '../enums/export-format.enum';
import { ExportStatus } from '../enums/export-status.enum';

// Define a type for the export_history table structure
export interface ExportHistory {
  id: string;
  calculation_id: string;
  created_by: string;
  created_at: string; // Timestamptz comes as string
  export_type: ExportFormat; // Use enum
  status: ExportStatus; // Use enum
  recipient?: string | null; // Optional email recipient
  file_name?: string | null;
  storage_path?: string | null;
  error_message?: string | null;
  completed_at?: string | null; // Timestamptz comes as string
  // Add potential missing fields like file_size_bytes, mime_type if needed
  // file_size_bytes?: number | null;
  // mime_type?: string | null;
}
