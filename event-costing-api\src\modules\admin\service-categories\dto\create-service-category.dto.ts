import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
} from 'class-validator';

export class CreateServiceCategoryDto {
  @ApiProperty({ description: 'Name of the service category', maxLength: 100 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ description: 'Description of the service category' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'ID of the parent category (UUID), if this is a sub-category',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  parent_category_id?: string;
}
