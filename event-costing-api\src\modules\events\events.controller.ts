import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  ParseUUI<PERSON>ipe,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { EventsService } from './events.service';
import { EventDto } from './dto/event.dto';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { EventStatus } from './dto/event-status.enum';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiParam,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';

@ApiTags('Events')
@ApiBearerAuth()
@Controller('events')
@UseGuards(JwtAuthGuard)
export class EventsController {
  private readonly logger = new Logger(EventsController.name);

  constructor(private readonly eventsService: EventsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new event' })
  @ApiCreatedResponse({
    description: 'Event created successfully.',
    type: EventDto,
  })
  @ApiBadRequestResponse({ description: 'Bad Request (Validation Error)' })
  @ApiNotFoundResponse({
    description: 'Referenced client or contact not found',
  })
  async create(@Body() createEventDto: CreateEventDto): Promise<EventDto> {
    this.logger.log(
      `Received request to create event: ${createEventDto.event_name}`,
    );
    return this.eventsService.create(createEventDto);
  }

  @Get()
  @ApiOperation({ summary: 'List all events with optional filtering' })
  @ApiOkResponse({ description: 'List of events.', type: [EventDto] })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for event name or venue details',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: EventStatus,
    description: 'Filter by event status',
  })
  @ApiQuery({
    name: 'clientId',
    required: false,
    type: String,
    format: 'uuid',
    description: 'Filter by associated client ID',
  })
  @ApiQuery({
    name: 'contactId',
    required: false,
    type: String,
    format: 'uuid',
    description: 'Filter by primary contact user ID',
  })
  async findAll(
    @Query('search') search?: string,
    @Query('status') status?: EventStatus,
    @Query('clientId') clientId?: string,
    @Query('contactId') contactId?: string,
  ): Promise<EventDto[]> {
    this.logger.log(
      `Received request to list events ${search ? `with search: '${search}'` : ''} ${status ? `status: ${status}` : ''} ${clientId ? `clientId: ${clientId}` : ''} ${contactId ? `contactId: ${contactId}` : ''}`,
    );
    return this.eventsService.findAll(search, status, clientId, contactId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific event by ID' })
  @ApiOkResponse({ description: 'Event details.', type: EventDto })
  @ApiNotFoundResponse({ description: 'Event not found.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<EventDto> {
    this.logger.log(`Received request to get event ID: ${id}`);
    return this.eventsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an event' })
  @ApiOkResponse({ description: 'Event updated successfully.', type: EventDto })
  @ApiNotFoundResponse({ description: 'Event, client, or contact not found.' })
  @ApiBadRequestResponse({ description: 'Bad Request (Validation Error)' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateEventDto: UpdateEventDto,
  ): Promise<EventDto> {
    this.logger.log(`Received request to update event ID: ${id}`);
    return this.eventsService.update(id, updateEventDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete (soft) an event' })
  @ApiNoContentResponse({ description: 'Event deleted successfully.' })
  @ApiNotFoundResponse({ description: 'Event not found or already deleted.' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Received request to delete event ID: ${id}`);
    await this.eventsService.remove(id);
  }
}
