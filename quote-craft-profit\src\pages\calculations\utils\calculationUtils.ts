/**
 * Utility functions for calculations
 * Consolidated calculation utilities for the calculations feature
 */
import { format, parseISO } from "date-fns";
import { LineItem, QuantityBasisEnum } from "@/types/calculation";

import { FINANCIAL_CONSTANTS } from "../constants";
import { calculateTotalPrice } from "./priceCalculationUtils";

// Re-export from separate utility files
export { formatCurrency, formatDate } from "./formatUtils";
export {
  calculateTotalPrice,
  generatePriceFormula,
  calculatePackagePrice,
} from "./priceCalculationUtils";
export {
  calculateTaxes,
  calculateDiscount,
  calculateFinalTotal,
} from "./taxDiscountUtils";
export type { Tax, Discount } from "./taxDiscountUtils";

/**
 * Calculate financial totals from line items
 * @param lineItems - Array of line items
 * @returns Object with subtotal, totalCost, and profit
 */
export const calculateFinancialTotals = (lineItems: LineItem[]) => {
  if (!lineItems || !Array.isArray(lineItems) || lineItems.length === 0) {
    return { subtotal: 0, totalCost: 0, profit: 0 };
  }

  try {
    const subtotal = lineItems.reduce((sum, item) => {
      // Handle both property naming conventions
      let itemPrice = 0;

      // First check if total_price is available and valid
      if (typeof item.total_price === "number" && !isNaN(item.total_price)) {
        itemPrice = item.total_price;
      }
      // totalPrice property doesn't exist on LineItem interface, skip this check
      // If neither is available, calculate from unit price, quantity, and item_quantity_basis
      else {
        // Calculate if neither property exists
        const unitPrice =
          typeof item.unit_price === "number" && !isNaN(item.unit_price)
            ? item.unit_price
            : 0;

        const quantity = item.quantity || 0;

        // Get item_quantity_basis
        const itemQuantityBasis = item.item_quantity_basis || 1;

        // Get quantity_basis
        const quantityBasis = item.quantity_basis || QuantityBasisEnum.PER_DAY;

        // Calculate based on quantity_basis
        switch (quantityBasis) {
          case QuantityBasisEnum.PER_DAY:
            // For PER_DAY items, multiply by quantity and item_quantity_basis
            itemPrice = unitPrice * quantity * itemQuantityBasis;
            break;
          case QuantityBasisEnum.PER_EVENT:
          case QuantityBasisEnum.PER_ITEM:
            // For PER_EVENT and PER_ITEM items, multiply by quantity only
            itemPrice = unitPrice * quantity;
            break;
          case QuantityBasisEnum.PER_ATTENDEE:
            // For PER_ATTENDEE items, multiply by quantity only
            itemPrice = unitPrice * quantity;
            break;
          case QuantityBasisEnum.PER_ITEM_PER_DAY:
          case QuantityBasisEnum.PER_ATTENDEE_PER_DAY:
            // For PER_ITEM_PER_DAY and PER_ATTENDEE_PER_DAY items, multiply by quantity and item_quantity_basis
            itemPrice = unitPrice * quantity * itemQuantityBasis;
            break;
          default:
            // Default fallback
            itemPrice = unitPrice * quantity * itemQuantityBasis;
        }
      }

      return sum + itemPrice;
    }, 0);

    // Calculate profit using configurable cost ratio
    // In a real app, you'd calculate based on actual costs and other factors
    const totalCost = subtotal * FINANCIAL_CONSTANTS.DEFAULT_COST_RATIO;
    const profit = subtotal - totalCost;

    return { subtotal, totalCost, profit };
  } catch (error) {
    console.error("Error calculating financial totals:", error);
    return { subtotal: 0, totalCost: 0, profit: 0 };
  }
};

/**
 * Calculate the total price for a line item based on its properties
 * @param item - The line item to calculate the total for
 * @returns The calculated total price
 */
export const calculateLineItemTotal = (item: Partial<LineItem>): number => {
  // First check if total_price is available and valid
  if (typeof item.total_price === "number" && !isNaN(item.total_price)) {
    return item.total_price;
  }

  // If not available, calculate from unit price, quantity, and item_quantity_basis
  const unitPrice =
    typeof item.unit_price === "number" && !isNaN(item.unit_price)
      ? item.unit_price
      : 0;
  const quantity = item.quantity || 0;
  const itemQuantityBasis =
    typeof item.item_quantity_basis === "number" &&
    !isNaN(item.item_quantity_basis)
      ? item.item_quantity_basis
      : 1;

  // Get quantity_basis
  const quantityBasis = item.quantity_basis || QuantityBasisEnum.PER_DAY;

  // Calculate based on quantity_basis
  return calculateTotalPrice(
    unitPrice,
    quantity,
    itemQuantityBasis,
    quantityBasis
  );
};
