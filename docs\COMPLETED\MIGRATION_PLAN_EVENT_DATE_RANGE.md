# Event Date Range Migration Plan

## Overview
This document outlines the migration plan to standardize event date handling by converting from separate date pickers to a unified date range picker approach.

## Current State Analysis

### Frontend Date Handling Patterns:
- **Events**: Separate date pickers (`startDate`, `endDate`)
- **Calculations**: Date range picker (`date_range: { from, to }`)
- **Templates**: Date range picker (`dateRange: { from, to }`)

### Backend Expectations:
- **Events**: `event_start_datetime`, `event_end_datetime` (separate fields)
- **Calculations**: `event_start_date`, `event_end_date` (separate fields)
- **Templates**: `template_start_date`, `template_end_date` (separate fields)

## Migration Strategy: Standardize on Date Range Picker

### Benefits:
✅ **UI Consistency**: Unified date selection experience
✅ **Better UX**: Visual feedback for date relationships
✅ **Code Reusability**: Shared components and validation
✅ **Maintainability**: Single pattern to maintain
✅ **Future-Proof**: Easier to extend and modify

## Implementation Phases

### Phase 1: Schema and Type Updates ✅ COMPLETED
- [x] Updated `eventFormSchema` to use `dateRange`
- [x] Added `EventFormData` interface
- [x] Created transformation utilities
- [x] Maintained backward compatibility with legacy schema

### Phase 2: Component Migration ✅ COMPLETED
**Files Updated:**
1. ✅ `EventFormDialog.tsx` - Converted to date range picker
2. ⏳ `EventDetailsPage.tsx` - Update edit mode to use date range (NEXT)
3. ✅ `EventsFilters.tsx` - Already uses date range (Good)

**Changes Completed:**
- ✅ Replaced separate date picker fields with single date range picker
- ✅ Updated form default values and validation
- ✅ Updated form submission logic
- ✅ Updated service layer to support both formats
- ✅ Added transformation functions
- ✅ Maintained backward compatibility

### Phase 3: Service Layer Updates ✅ COMPLETED
**Files Updated:**
1. ✅ `eventService.ts` - Updated to support both EventRequest and Partial<Event>
2. ✅ Added automatic format detection for backward compatibility

### Phase 4: Testing and Validation (CURRENT)
- ⏳ Test event creation with date range picker
- ⏳ Test event editing with date range picker
- ⏳ Verify API integration works correctly
- ⏳ Test edge cases (same day events, multi-day events)
- ⏳ Update EventDetailsPage.tsx edit mode (optional)

## Implementation Details

### New Schema Structure:
```typescript
// New event form schema
export const eventFormSchema = z.object({
  name: formFieldPatterns.name('Event name'),
  clientId: commonValidations.requiredString(1, 'Client'),
  dateRange: dateRangeSchema, // ← Changed from startDate/endDate
  location: commonValidations.optionalString(),
  status: commonValidations.requiredString(1, 'Status'),
  primaryContactId: commonValidations.optionalString(),
  notes: formFieldPatterns.notes(),
});
```

### New Form Interface:
```typescript
export interface EventFormData {
  name: string;
  clientId: string;
  dateRange: { from: Date; to: Date }; // ← New structure
  location?: string;
  status: string;
  primaryContactId?: string;
  notes?: string;
}
```

### Transformation Functions:
- `transformEventFormDataToApiRequest()` - Form data → API request
- `transformEventToFormData()` - Event → Form data (for editing)
- `transformEventToApiRequest()` - Legacy support (maintained)

## Migration Steps

### Step 1: Update EventFormDialog Component
```typescript
// Replace separate date pickers with:
<FormField
  control={form.control}
  name="dateRange"
  render={({ field }) => (
    <FormItem className="flex flex-col">
      <FormLabel>Event Date Range *</FormLabel>
      <Popover>
        <PopoverTrigger asChild>
          <FormControl>
            <Button variant="outline" className={cn(/* ... */)}>
              {field.value?.from && field.value?.to ? (
                <>
                  {format(field.value.from, 'PPP')} - {format(field.value.to, 'PPP')}
                </>
              ) : (
                <span>Select event dates</span>
              )}
              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="range"
            selected={field.value}
            onSelect={field.onChange}
            numberOfMonths={2}
            initialFocus
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )}
/>
```

### Step 2: Update Form Defaults
```typescript
const form = useForm<EventFormValues>({
  resolver: zodResolver(eventFormSchema),
  defaultValues: {
    name: '',
    clientId: '',
    dateRange: {
      from: undefined,
      to: undefined,
    },
    location: '',
    status: 'planning',
    primaryContactId: '',
    notes: '',
  },
});
```

### Step 3: Update Form Submission
```typescript
const onSubmit = async (values: EventFormValues) => {
  try {
    setIsSubmitting(true);

    // Use new transformation function
    const apiEventData = transformEventFormDataToApiRequest(values);

    const result = await createEvent(apiEventData);
    toast.success('Event created successfully');
    onClose();
    navigate(`/events/${result.id}`);
  } catch (error) {
    console.error('Error saving event:', error);
    toast.error('Failed to save event. Please try again.');
  } finally {
    setIsSubmitting(false);
  }
};
```

## Backward Compatibility

### Legacy Support:
- `legacyEventFormSchema` maintained for existing code
- `transformEventToApiRequest()` function preserved
- Gradual migration approach allows testing at each step

### Migration Path:
1. ✅ **Phase 1**: Schema and types (completed)
2. 🔄 **Phase 2**: Component updates (in progress)
3. ⏳ **Phase 3**: Service layer updates
4. ⏳ **Phase 4**: Testing and cleanup

## Testing Strategy

### Test Cases:
1. **Create Event**: New date range picker functionality
2. **Edit Event**: Convert existing dates to date range format
3. **Validation**: Ensure date range validation works
4. **API Integration**: Verify backend receives correct format
5. **Edge Cases**: Same day events, timezone handling

### Rollback Plan:
- Keep legacy schema and components until migration is complete
- Feature flag approach for gradual rollout
- Easy revert to separate date pickers if issues arise

## Next Steps

1. **Implement EventFormDialog updates** (Phase 2)
2. **Update EventDetailsPage edit mode** (Phase 2)
3. **Test event creation and editing** (Phase 4)
4. **Update service layer** (Phase 3)
5. **Remove legacy code** (Phase 4)

## Benefits After Migration

✅ **Consistent UX**: All date selections use the same pattern
✅ **Reduced Code**: Shared validation and components
✅ **Better Maintenance**: Single source of truth for date handling
✅ **Improved Testing**: Unified test patterns
✅ **Enhanced Features**: Easier to add date-related features
