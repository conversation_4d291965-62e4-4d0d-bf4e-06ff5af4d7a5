import { Test, TestingModule } from '@nestjs/testing';
import { PackagesController } from './packages.controller';
import { PackagesService } from './packages.service';
import {
  ListPackageVariationsDto,
  PackageSortField,
  SortDirection,
} from './dto/list-package-variations.dto';
import { PackageVariationDto } from './dto/package-variation.dto';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
import { Logger } from '@nestjs/common';

// Mock the PackagesService
const mockPackagesService = {
  findVariations: jest.fn(),
  findOptions: jest.fn(),
};

describe('PackagesController', () => {
  let controller: PackagesController;
  let service: PackagesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PackagesController],
      providers: [
        {
          provide: PackagesService,
          useValue: mockPackagesService,
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<PackagesController>(PackagesController);
    service = module.get<PackagesService>(PackagesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findVariations', () => {
    it('should return paginated package variations', async () => {
      // Arrange
      const queryDto: ListPackageVariationsDto = {
        currencyId: '123e4567-e89b-12d3-a456-426614174000',
        categoryId: '123e4567-e89b-12d3-a456-426614174001',
        search: 'test',
        sortBy: PackageSortField.NAME,
        sortOrder: SortDirection.ASC,
        limit: 10,
        offset: 0,
      };

      const expectedResult: PaginatedResponseDto<PackageVariationDto> = {
        data: [
          {
            package_id: '123e4567-e89b-12d3-a456-426614174002',
            name: 'Test Package',
            description: 'Test Description',
            category_id: '123e4567-e89b-12d3-a456-426614174001',
            quantity_basis: 'attendees',
            price: 100,
            unit_base_cost: 50,
            is_available_in_city: true,
            is_available_in_venue: true,
            conflicts_with_selection: false,
          },
        ],
        count: 1,
        limit: 10,
        offset: 0,
      };

      // Mock the service method
      mockPackagesService.findVariations.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findVariations(queryDto);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(mockPackagesService.findVariations).toHaveBeenCalledWith(queryDto);
    });

    it('should handle multiple venue IDs', async () => {
      // Arrange
      const queryDto: ListPackageVariationsDto = {
        currencyId: '123e4567-e89b-12d3-a456-426614174000',
        venueIds: [
          '123e4567-e89b-12d3-a456-426614174003',
          '123e4567-e89b-12d3-a456-426614174004',
        ],
      };

      const expectedResult: PaginatedResponseDto<PackageVariationDto> = {
        data: [
          {
            package_id: '123e4567-e89b-12d3-a456-426614174002',
            name: 'Test Package',
            description: 'Test Description',
            category_id: '123e4567-e89b-12d3-a456-426614174001',
            quantity_basis: 'attendees',
            price: 100,
            unit_base_cost: 50,
            is_available_in_city: true,
            is_available_in_venue: true,
            conflicts_with_selection: false,
          },
        ],
        count: 1,
        limit: 20,
        offset: 0,
      };

      // Mock the service method
      mockPackagesService.findVariations.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findVariations(queryDto);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(mockPackagesService.findVariations).toHaveBeenCalledWith(queryDto);
    });
  });
});
