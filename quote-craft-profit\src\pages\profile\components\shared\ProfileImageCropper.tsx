import React, { useState, useRef, useEffect } from 'react';
import <PERSON>actC<PERSON>, { Crop, PixelCrop, centerCrop, makeAspectCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface ProfileImageCropperProps {
  open: boolean;
  onClose: () => void;
  imageFile: File | null;
  onCropComplete: (croppedImageBlob: Blob) => void;
}

// This is to help center and initialize the crop
function centerAspectCrop(mediaWidth: number, mediaHeight: number, aspect: number) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

// Function to create a canvas with the cropped image
function getCroppedImg(
  image: HTMLImageElement,
  crop: PixelCrop,
  fileName: string,
): Promise<Blob> {
  const canvas = document.createElement('canvas');
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  const ctx = canvas.getContext('2d');

  // Set canvas size to match the desired crop size
  canvas.width = crop.width;
  canvas.height = crop.height;

  if (!ctx) {
    throw new Error('No 2d context');
  }

  // Draw the cropped image onto the canvas
  ctx.drawImage(
    image,
    crop.x * scaleX,
    crop.y * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width,
    crop.height,
  );

  // Return a Promise with the canvas as a blob
  return new Promise((resolve, reject) => {
    // Get the file type from the original file name
    const fileType = fileName.split('.').pop()?.toLowerCase();
    let mimeType = 'image/jpeg'; // Default to JPEG

    // Set the appropriate MIME type based on the file extension
    if (fileType === 'png') {
      mimeType = 'image/png';
    } else if (fileType === 'gif') {
      mimeType = 'image/gif';
    } else if (fileType === 'webp') {
      mimeType = 'image/webp';
    }

    canvas.toBlob(
      (blob) => {
        if (!blob) {
          reject(new Error('Canvas is empty'));
          return;
        }
        resolve(blob);
      },
      mimeType,
      1, // Quality
    );
  });
}

const ProfileImageCropper: React.FC<ProfileImageCropperProps> = ({
  open,
  onClose,
  imageFile,
  onCropComplete,
}) => {
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const [imgSrc, setImgSrc] = useState<string>('');

  // When the image file changes, create an object URL for it
  useEffect(() => {
    if (!imageFile) {
      setImgSrc('');
      return;
    }

    const objectUrl = URL.createObjectURL(imageFile);
    setImgSrc(objectUrl);

    // Clean up the object URL when the component unmounts or the file changes
    return () => {
      URL.revokeObjectURL(objectUrl);
    };
  }, [imageFile]);

  // When the image loads, set up the initial crop
  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    // Create a circular crop (aspect ratio 1)
    setCrop(centerAspectCrop(width, height, 1));
  };

  // Handle the crop completion
  const handleCropComplete = async () => {
    if (!imgRef.current || !completedCrop || !imageFile) {
      return;
    }

    setIsLoading(true);
    try {
      const croppedImageBlob = await getCroppedImg(
        imgRef.current,
        completedCrop,
        imageFile.name,
      );
      onCropComplete(croppedImageBlob);
    } catch (error) {
      console.error('Error cropping image:', error);
    } finally {
      setIsLoading(false);
      onClose();
    }
  };

  // Create a preview of the cropped image
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Update the preview when the crop changes
  useEffect(() => {
    if (!completedCrop || !imgRef.current) {
      setPreviewUrl(null);
      return;
    }

    // Create a canvas for the preview
    const canvas = document.createElement('canvas');
    const scaleX = imgRef.current.naturalWidth / imgRef.current.width;
    const scaleY = imgRef.current.naturalHeight / imgRef.current.height;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return;
    }

    // Set canvas dimensions to match the crop size
    canvas.width = completedCrop.width;
    canvas.height = completedCrop.height;

    // Draw the cropped image onto the canvas
    ctx.drawImage(
      imgRef.current,
      completedCrop.x * scaleX,
      completedCrop.y * scaleY,
      completedCrop.width * scaleX,
      completedCrop.height * scaleY,
      0,
      0,
      completedCrop.width,
      completedCrop.height,
    );

    // Create a data URL from the canvas
    const dataUrl = canvas.toDataURL('image/jpeg');
    setPreviewUrl(dataUrl);
  }, [completedCrop]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Crop Profile Picture</DialogTitle>
        </DialogHeader>
        <div className='flex flex-col items-center justify-center p-4'>
          {imgSrc && (
            <ReactCrop
              crop={crop}
              onChange={(c) => setCrop(c)}
              onComplete={(c) => setCompletedCrop(c)}
              aspect={1} // 1:1 aspect ratio for a circle
              circularCrop // Make the crop circular
              className='max-h-[400px] max-w-full'
            >
              <img
                ref={imgRef}
                src={imgSrc}
                alt='Crop me'
                style={{ maxHeight: '400px' }}
                onLoad={onImageLoad}
              />
            </ReactCrop>
          )}

          {/* Preview section */}
          {previewUrl && (
            <div className='mt-4 flex flex-col items-center'>
              <h3 className='text-sm font-medium mb-2'>Preview</h3>
              <div className='w-20 h-20 rounded-full overflow-hidden border-2 border-gray-200'>
                <img
                  src={previewUrl}
                  alt='Preview'
                  className='w-full h-full object-cover'
                />
              </div>
            </div>
          )}

          <p className='text-sm text-muted-foreground mt-4'>
            Drag to adjust the crop. The image will be cropped as a circle.
          </p>
        </div>
        <DialogFooter>
          <Button variant='outline' onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleCropComplete} disabled={isLoading || !completedCrop}>
            {isLoading ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Processing...
              </>
            ) : (
              'Apply'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileImageCropper;
