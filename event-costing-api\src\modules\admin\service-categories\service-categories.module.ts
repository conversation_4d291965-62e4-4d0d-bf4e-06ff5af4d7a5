import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/auth.module';
import { AdminModule } from '../../auth/admin.module';
import { ServiceCategoriesService } from './service-categories.service';
import { ServiceCategoriesController } from './service-categories.controller';

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [ServiceCategoriesController],
  providers: [ServiceCategoriesService],
  exports: [ServiceCategoriesService], // Export if needed by other modules
})
export class ServiceCategoriesModule {}
