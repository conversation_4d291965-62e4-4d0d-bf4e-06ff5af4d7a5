import { useQuery } from '@tanstack/react-query';
import { getVenuesByCity } from '@/services/shared/entities/venues';

interface Venue {
  id: string;
  name: string;
  city_id: string;
  city_name?: string;
}

interface City {
  id: string;
  name: string;
}

/**
 * Optimized hook for fetching venues by city with caching
 * Uses React Query to cache venue data per city and avoid redundant API calls
 */
export const useVenuesByCity = (cityId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['venues', 'by-city', cityId],
    queryFn: () => getVenuesByCity(cityId),
    enabled: enabled && !!cityId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    meta: {
      onError: (error: any) => {
        console.error(`Failed to load venues for city ${cityId}:`, error);
      },
    },
  });
};

/**
 * Hook to fetch venues for multiple cities efficiently
 * Uses a single query to fetch all venues and filters by city
 */
export const useVenuesForCities = (cityIds: string[], cities: City[], enabled: boolean = true) => {
  // Use a single query to fetch all venues for the selected cities
  const { data: allVenuesData, isLoading, isError, isFetching } = useQuery({
    queryKey: ['venues', 'multiple-cities', cityIds.sort()], // Sort for consistent cache key
    queryFn: async () => {
      if (!cityIds.length) return [];

      // Fetch venues for all cities in parallel
      const venuePromises = cityIds.map(cityId => getVenuesByCity(cityId));
      const venueArrays = await Promise.all(venuePromises);

      // Flatten and combine all venues
      return venueArrays.flat();
    },
    enabled: enabled && cityIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    meta: {
      onError: (error: any) => {
        console.error('Failed to load venues for cities:', cityIds, error);
      },
    },
  });

  // Add city names to venues
  const allVenues: Venue[] = (allVenuesData || []).map(venue => ({
    ...venue,
    city_name: cities.find(c => c.id === venue.city_id)?.name || 'Unknown',
  }));

  return {
    venues: allVenues,
    isLoading,
    isError,
    isFetching,
  };
};
