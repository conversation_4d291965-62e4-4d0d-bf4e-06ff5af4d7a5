import { Injectable, Logger } from '@nestjs/common';
import { User } from '@supabase/supabase-js';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { ListTemplatesDto } from './dto/list-templates.dto';
import {
  PaginatedTemplatesResponse,
  TemplateSummaryDto,
  PaginatedAdminTemplatesResponse,
  TemplateDetailDto,
  EnhancedTemplateDetailDto,
} from './dto/template-summary.dto';
import { CreateTemplateFromCalculationDto } from './dto/create-template-from-calculation.dto';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import {
  TemplateCalculationResultDto,
  TemplateCalculationSummaryDto,
} from './dto/template-calculation.dto';
import { ListAdminTemplatesQueryDto } from './dto/list-admin-templates.dto';
import {
  TemplateQueryService,
  TemplateCreationService,
  TemplateDetailService,
  TemplateAdminService,
  TemplateVenueService,
  TemplateCalculationService,
} from './services';

/**
 * Main service for template operations
 * This service delegates to specialized services for different operations
 */
@Injectable()
export class TemplatesService {
  private readonly logger = new Logger(TemplatesService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly templateQueryService: TemplateQueryService,
    private readonly templateCreationService: TemplateCreationService,
    private readonly templateDetailService: TemplateDetailService,
    private readonly templateAdminService: TemplateAdminService,
    private readonly templateCalculationService: TemplateCalculationService,
  ) {}

  // --- User Facing Methods ---

  /**
   * Find templates accessible to a specific user
   */
  async findUserTemplates(
    user: User,
    queryDto: ListTemplatesDto,
  ): Promise<PaginatedTemplatesResponse> {
    return this.templateQueryService.findUserTemplates(user, queryDto);
  }

  /**
   * Create a basic template
   */
  async createTemplate(
    createDto: CreateTemplateDto,
    user: User,
  ): Promise<TemplateSummaryDto> {
    return this.templateCreationService.createTemplate(createDto, user);
  }

  /**
   * Create a template from a calculation
   */
  async createTemplateFromCalculation(
    createDto: CreateTemplateFromCalculationDto,
    user: User,
  ): Promise<TemplateSummaryDto> {
    return this.templateCreationService.createTemplateFromCalculation(
      createDto,
      user,
    );
  }

  /**
   * Find public templates with filtering/pagination
   */
  async findPublicTemplates(
    queryDto: ListTemplatesDto,
  ): Promise<PaginatedTemplatesResponse> {
    return this.templateQueryService.findPublicTemplates(queryDto);
  }

  /**
   * Find a single public template by ID
   */
  async findOnePublic(id: string): Promise<TemplateDetailDto> {
    return this.templateDetailService.findOnePublic(id);
  }

  /**
   * Find a single public template by ID with enhanced package and option names
   */
  async findOnePublicEnhanced(id: string): Promise<EnhancedTemplateDetailDto> {
    return this.templateDetailService.findOnePublicEnhanced(id);
  }

  // --- Admin Methods ---

  /**
   * Find all templates (admin endpoint)
   */
  async findAllAdmin(
    queryDto: ListAdminTemplatesQueryDto,
  ): Promise<PaginatedAdminTemplatesResponse> {
    return this.templateAdminService.findAllAdmin(queryDto);
  }

  /**
   * Find a single template by ID (admin endpoint)
   */
  async findOneAdmin(id: string): Promise<TemplateDetailDto> {
    return this.templateAdminService.findOneAdmin(id);
  }

  /**
   * Update a template
   */
  async updateTemplate(
    id: string,
    updateDto: UpdateTemplateDto,
  ): Promise<TemplateDetailDto> {
    return this.templateAdminService.updateTemplate(id, updateDto);
  }

  /**
   * Update template status (active/inactive)
   */
  async updateTemplateStatus(
    id: string,
    isActive: boolean,
  ): Promise<TemplateDetailDto> {
    return this.templateAdminService.updateTemplateStatus(id, isActive);
  }

  /**
   * Delete a template
   */
  async deleteTemplate(id: string): Promise<void> {
    return this.templateAdminService.deleteTemplate(id);
  }

  // --- Template Calculation Methods ---

  /**
   * Calculate template total value and breakdown
   */
  async calculateTemplateTotal(templateId: string): Promise<TemplateCalculationResultDto> {
    return this.templateCalculationService.calculateTemplateTotal(templateId);
  }

  /**
   * Get template calculation summary
   */
  async getTemplateCalculationSummary(templateId: string): Promise<TemplateCalculationSummaryDto> {
    return this.templateCalculationService.getCalculationSummary(templateId);
  }
}
