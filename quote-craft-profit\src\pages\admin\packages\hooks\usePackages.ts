import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { getAllPackages } from "../../../../services/admin/packages/packageService";
import { PackageFilters } from "../types/filters";
import { toast } from "sonner";
import { QUERY_KEYS } from "@/lib/queryKeys";

/**
 * Custom hook for managing packages list with filtering, pagination, and sorting
 * @param initialFilters - Initial filter values
 * @returns Package data, filters, and filter handling functions
 */
export const usePackages = (initialFilters: Partial<PackageFilters> = {}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [filters, setFilters] = useState<PackageFilters>({
    search: searchParams.get("search") || "",
    categoryId: searchParams.get("categoryId") || "all",
    divisionId: searchParams.get("divisionId") || "all",
    cityId: searchParams.get("cityId") || "all",
    showDeleted: searchParams.get("showDeleted") === "true",
    page: parseInt(searchParams.get("page") || "1"),
    pageSize: parseInt(searchParams.get("pageSize") || "10"),
    sortBy: searchParams.get("sortBy") || "updated_at",
    sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    ...initialFilters,
  });

  // Query for packages with current filters using unified query keys
  const {
    data: packageData,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: QUERY_KEYS.packages.list(filters),
    queryFn: () => getAllPackages(filters),
    meta: {
      onError: () => {
        toast.error("Failed to load packages");
      },
    },
  });

  // Filter handling functions
  const handleFilterChange = (key: string, value: string | boolean) => {
    setFilters((prev) => {
      const newFilters = { ...prev, [key]: value, page: 1 };
      updateSearchParams(newFilters);
      return newFilters;
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev) => {
      const newFilters = { ...prev, search: e.target.value, page: 1 };
      updateSearchParams(newFilters);
      return newFilters;
    });
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => {
      const newFilters = { ...prev, page };
      updateSearchParams(newFilters);
      return newFilters;
    });
  };

  const handlePageSizeChange = (pageSize: number) => {
    setFilters((prev) => {
      const newFilters = { ...prev, pageSize, page: 1 };
      updateSearchParams(newFilters);
      return newFilters;
    });
  };

  const handleSortChange = (field: string, order: "asc" | "desc") => {
    setFilters((prev) => {
      const newFilters = { ...prev, sortBy: field, sortOrder: order };
      updateSearchParams(newFilters);
      return newFilters;
    });
  };

  const clearFilters = () => {
    setFilters({
      search: "",
      categoryId: "all",
      divisionId: "all",
      cityId: "all",
      showDeleted: false,
      page: 1,
      pageSize: filters.pageSize,
      sortBy: "updated_at",
      sortOrder: "desc",
    });
    setSearchParams({});
  };

  // Helper to update URL search params
  const updateSearchParams = (newFilters: PackageFilters) => {
    const params = new URLSearchParams();
    if (newFilters.search) params.set("search", newFilters.search);
    if (newFilters.categoryId !== "all")
      params.set("categoryId", newFilters.categoryId);
    if (newFilters.divisionId !== "all")
      params.set("divisionId", newFilters.divisionId);
    if (newFilters.cityId !== "all") params.set("cityId", newFilters.cityId);
    if (newFilters.showDeleted) params.set("showDeleted", "true");
    if (newFilters.page > 1) params.set("page", newFilters.page.toString());
    if (newFilters.pageSize !== 10)
      params.set("pageSize", newFilters.pageSize.toString());
    if (newFilters.sortBy !== "updated_at")
      params.set("sortBy", newFilters.sortBy);
    if (newFilters.sortOrder !== "desc")
      params.set("sortOrder", newFilters.sortOrder);
    setSearchParams(params);
  };

  return {
    filters,
    packageData,
    isLoading,
    isError,
    refetch,
    handleFilterChange,
    handleSearchChange,
    handlePageChange,
    handlePageSizeChange,
    handleSortChange,
    clearFilters,
  };
};
