import {
  IsNotEmpty,
  <PERSON>String,
  IsU<PERSON>D,
  IsO<PERSON>al,
  <PERSON><PERSON><PERSON>th,
  IsDateString,
  IsInt,
  Min,
  IsArray,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateTemplateFromCalculationDto {
  @ApiProperty({
    description: 'The UUID of the source calculation history record.',
    format: 'uuid',
  })
  @IsNotEmpty()
  @IsUUID()
  calculationId: string;

  @ApiProperty({ description: 'Name for the new template.', maxLength: 255 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: 'Optional description for the template.',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description:
      'Optional event type ID (UUID reference to event_types table).',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  eventTypeId?: string;

  @ApiPropertyOptional({
    description: 'Optional city ID where the template is relevant.',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiPropertyOptional({
    description: 'Optional currency ID.',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  currencyId?: string;

  @ApiPropertyOptional({
    description: 'Optional number of attendees.',
    type: Number,
    minimum: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  attendees?: number;

  @ApiPropertyOptional({
    description: 'Optional start date (YYYY-MM-DD) or empty string.',
    format: 'date',
    nullable: true,
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Start date must be a valid date string or empty' },
  )
  templateStartDate?: string | null;

  @ApiPropertyOptional({
    description: 'Optional end date (YYYY-MM-DD) or empty string.',
    format: 'date',
    nullable: true,
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'End date must be a valid date string or empty' },
  )
  templateEndDate?: string | null;

  @ApiPropertyOptional({
    description:
      'Optional array of venue IDs to associate with the template. Note: Venues can only be set during template creation and cannot be edited afterward.',
    type: [String],
    format: 'uuid',
  })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  venueIds?: string[];

  // category_id (for template_categories) is intentionally omitted here,
  // as linking it likely requires separate logic beyond just copying.
}
