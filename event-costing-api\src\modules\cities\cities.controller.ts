import { Controller, Get, Post, Put, Delete, Logger, UseGuards, Param, ParseUUIDPipe, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { CitiesService } from './cities.service';
import { CityDto } from './dto/city.dto';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
import { ApiTags, ApiOkResponse, ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Cities')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('cities') // Change base path to /cities
export class CitiesController {
  private readonly logger = new Logger(CitiesController.name);

  constructor(private readonly citiesService: CitiesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all cities' })
  @ApiOkResponse({ type: [CityDto] })
  async getCities(): Promise<CityDto[]> {
    this.logger.log('Fetching all cities');
    return this.citiesService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a city by ID' })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: 'City ID',
  })
  @ApiResponse({
    status: 200,
    description: 'City details',
    type: CityDto,
  })
  @ApiResponse({ status: 404, description: 'City not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getCityById(@Param('id', ParseUUIDPipe) id: string): Promise<CityDto> {
    this.logger.log(`Fetching city with ID: ${id}`);
    return this.citiesService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new city' })
  @ApiBody({ type: CreateCityDto })
  @ApiResponse({
    status: 201,
    description: 'City created successfully',
    type: CityDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 409, description: 'City name already exists' })
  async createCity(@Body() createCityDto: CreateCityDto): Promise<CityDto> {
    this.logger.log(`Creating city: ${createCityDto.name}`);
    return this.citiesService.createCity(createCityDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an existing city' })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: 'City ID',
  })
  @ApiBody({ type: UpdateCityDto })
  @ApiResponse({
    status: 200,
    description: 'City updated successfully',
    type: CityDto,
  })
  @ApiResponse({ status: 404, description: 'City not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 409, description: 'City name already exists' })
  async updateCity(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCityDto: UpdateCityDto,
  ): Promise<CityDto> {
    this.logger.log(`Updating city with ID: ${id}`);
    return this.citiesService.updateCity(id, updateCityDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a city' })
  @ApiParam({
    name: 'id',
    type: 'string',
    format: 'uuid',
    description: 'City ID',
  })
  @ApiResponse({ status: 204, description: 'City deleted successfully' })
  @ApiResponse({ status: 404, description: 'City not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCity(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Deleting city with ID: ${id}`);
    await this.citiesService.deleteCity(id);
  }
}
