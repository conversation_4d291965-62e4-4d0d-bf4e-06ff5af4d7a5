import {
  PackageOption,
  PackageOptionDisplay,
  SavePackageOptionData,
} from '../../../pages/admin/packages/types/packageOptions';
import {
  getPackageOptionsFromApi,
  getPackageOptionByIdFromApi,
  savePackageOptionWithApi,
  deletePackageOptionWithApi,
} from './packageOptionApiService';

/**
 * Get all options for a package
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageId - The package ID
 * @param currencyId - Optional currency ID
 * @param venueId - Optional venue ID
 * @returns Promise resolving to an array of package options
 */
export const getPackageOptions = async (
  packageId: string,
  currencyId?: string,
  venueId?: string,
): Promise<PackageOptionDisplay[]> => {
  try {
    // Use the API service to fetch package options with optional parameters
    return await getPackageOptionsFromApi(packageId, currencyId, venueId);
  } catch (error) {
    console.error(`Error in getPackageOptions for package ID ${packageId}:`, error);
    // Return empty array instead of throwing to prevent UI errors
    return [];
  }
};

/**
 * Get a single package option by ID
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageId - The package ID
 * @param optionId - The option ID
 * @returns Promise resolving to a package option or null
 */
export const getPackageOptionById = async (
  packageId: string,
  optionId: string,
): Promise<PackageOptionDisplay | null> => {
  try {
    // Use the API service to fetch the package option
    return await getPackageOptionByIdFromApi(packageId, optionId);
  } catch (error) {
    console.error(`Error in getPackageOptionById for ID ${optionId}:`, error);
    return null;
  }
};

/**
 * Create or update a package option
 * This function now uses the backend API instead of direct Supabase calls
 * @param optionData - The option data to save
 * @returns Promise resolving to the saved option
 */
export const savePackageOption = async (
  optionData: SavePackageOptionData,
): Promise<PackageOption> => {
  try {
    // Use the API service to save the package option
    return await savePackageOptionWithApi(optionData);
  } catch (error) {
    console.error('Error in savePackageOption:', error);
    throw error;
  }
};

/**
 * Delete a package option
 * This function now uses the backend API instead of direct Supabase calls
 * @param packageId - The package ID
 * @param optionId - The option ID to delete
 * @returns Promise resolving to void
 */
export const deletePackageOption = async (
  packageId: string,
  optionId: string,
): Promise<void> => {
  try {
    // Use the API service to delete the package option
    await deletePackageOptionWithApi(packageId, optionId);
  } catch (error) {
    console.error(`Error in deletePackageOption for ID ${optionId}:`, error);
    throw error;
  }
};
