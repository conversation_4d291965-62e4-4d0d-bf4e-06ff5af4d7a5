import React from "react";

interface FormSectionProps {
  title: string;
  stepNumber: number;
  children: React.ReactNode;
  className?: string;
}

/**
 * Reusable form section wrapper component
 * Provides consistent styling and layout for form sections
 */
export const FormSection: React.FC<FormSectionProps> = ({
  title,
  stepNumber,
  children,
  className = "",
}) => {
  return (
    <div
      className={`bg-slate-50 dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700 ${className}`}
    >
      <h3 className="text-lg font-medium mb-4 text-slate-800 dark:text-slate-200 flex items-center">
        <span className="w-6 h-6 bg-primary/10 dark:bg-primary/20 rounded-full mr-2 flex items-center justify-center text-primary dark:text-primary">
          {stepNumber}
        </span>
        {title}
      </h3>
      <div className="space-y-4">{children}</div>
    </div>
  );
};
