/**
 * Performance monitoring hook for calculation features
 * Tracks loading times, query performance, and optimization metrics
 */
import { useEffect, useRef, useState, useMemo, useCallback } from "react";

/**
 * Performance metrics interface
 */
interface PerformanceMetrics {
  loadStartTime: number;
  loadEndTime?: number;
  totalLoadTime?: number;
  queryMetrics: {
    calculation?: number;
    packages?: number;
    lineItems?: number;
    categories?: number;
  };
  parallelLoadingUsed: boolean;
  errorCount: number;
  retryCount: number;
}

/**
 * Performance monitoring configuration
 */
interface PerformanceConfig {
  enabled?: boolean;
  logToConsole?: boolean;
  trackDetailedMetrics?: boolean;
  reportThreshold?: number; // Only report if load time exceeds this (ms)
}

/**
 * Hook for monitoring calculation page performance
 */
export const usePerformanceMonitor = (
  calculationId: string,
  config: PerformanceConfig = {}
) => {
  const {
    enabled = process.env.NODE_ENV === "development",
    logToConsole = true,
    trackDetailedMetrics = true,
    reportThreshold = 1000, // 1 second
  } = config;

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadStartTime: Date.now(),
    queryMetrics: {},
    parallelLoadingUsed: false,
    errorCount: 0,
    retryCount: 0,
  });

  const startTimeRef = useRef<number>(Date.now());
  const queryTimesRef = useRef<Record<string, number>>({});

  /**
   * Start timing a specific query
   */
  const startQueryTimer = useCallback(
    (queryName: string) => {
      if (!enabled) return;
      queryTimesRef.current[queryName] = Date.now();
    },
    [enabled]
  );

  /**
   * End timing a specific query
   */
  const endQueryTimer = useCallback(
    (queryName: string) => {
      if (!enabled) return;
      const startTime = queryTimesRef.current[queryName];
      if (startTime) {
        const duration = Date.now() - startTime;
        setMetrics((prev) => ({
          ...prev,
          queryMetrics: {
            ...prev.queryMetrics,
            [queryName]: duration,
          },
        }));
        delete queryTimesRef.current[queryName];
      }
    },
    [enabled]
  );

  /**
   * Mark the overall loading as complete
   */
  const markLoadComplete = useCallback(
    (parallelLoadingUsed = false) => {
      if (!enabled) return;

      const endTime = Date.now();
      const totalTime = endTime - startTimeRef.current;

      setMetrics((prev) => ({
        ...prev,
        loadEndTime: endTime,
        totalLoadTime: totalTime,
        parallelLoadingUsed,
      }));
    },
    [enabled]
  );

  /**
   * Record an error
   */
  const recordError = useCallback(
    (errorType: string) => {
      if (!enabled) return;
      setMetrics((prev) => ({
        ...prev,
        errorCount: prev.errorCount + 1,
      }));
    },
    [enabled]
  );

  /**
   * Record a retry
   */
  const recordRetry = useCallback(
    (queryType: string) => {
      if (!enabled) return;
      setMetrics((prev) => ({
        ...prev,
        retryCount: prev.retryCount + 1,
      }));
    },
    [enabled]
  );

  /**
   * Get performance summary
   */
  const getPerformanceSummary = useMemo(() => {
    if (!enabled || !metrics.totalLoadTime) return null;

    const {
      totalLoadTime,
      queryMetrics,
      parallelLoadingUsed,
      errorCount,
      retryCount,
    } = metrics;

    return {
      totalLoadTime,
      averageQueryTime:
        Object.values(queryMetrics).length > 0
          ? Object.values(queryMetrics).reduce((a, b) => a + b, 0) /
            Object.values(queryMetrics).length
          : 0,
      slowestQuery: Object.entries(queryMetrics).reduce(
        (slowest, [name, time]) =>
          time > slowest.time ? { name, time } : slowest,
        { name: "", time: 0 }
      ),
      fastestQuery: Object.entries(queryMetrics).reduce(
        (fastest, [name, time]) =>
          time < fastest.time || fastest.time === 0 ? { name, time } : fastest,
        { name: "", time: Infinity }
      ),
      parallelLoadingUsed,
      errorCount,
      retryCount,
      performanceGrade:
        totalLoadTime < 1000
          ? "A"
          : totalLoadTime < 2000
          ? "B"
          : totalLoadTime < 3000
          ? "C"
          : "D",
    };
  }, [metrics, enabled]);

  /**
   * Reset metrics for new measurement
   */
  const resetMetrics = useCallback(() => {
    if (!enabled) return;
    startTimeRef.current = Date.now();
    queryTimesRef.current = {};
    setMetrics({
      loadStartTime: Date.now(),
      queryMetrics: {},
      parallelLoadingUsed: false,
      errorCount: 0,
      retryCount: 0,
    });
  }, [enabled]);

  return useMemo(
    () => ({
      // Core functions
      startQueryTimer,
      endQueryTimer,
      markLoadComplete,
      recordError,
      recordRetry,
      resetMetrics,

      // Data
      metrics,
      performanceSummary: getPerformanceSummary,

      // Utilities
      isEnabled: enabled,
    }),
    [
      startQueryTimer,
      endQueryTimer,
      markLoadComplete,
      recordError,
      recordRetry,
      resetMetrics,
      metrics,
      getPerformanceSummary,
      enabled,
    ]
  );
};

/**
 * Hook for comparing parallel vs sequential loading performance
 */
export const useParallelLoadingComparison = (calculationId: string) => {
  const [comparisonData, setComparisonData] = useState<{
    parallel?: number;
    sequential?: number;
    improvement?: number;
    improvementPercentage?: number;
  }>({});

  const recordLoadTime = (loadTime: number, isParallel: boolean) => {
    setComparisonData((prev) => {
      const newData = {
        ...prev,
        [isParallel ? "parallel" : "sequential"]: loadTime,
      };

      // Calculate improvement if we have both measurements
      if (newData.parallel && newData.sequential) {
        const improvement = newData.sequential - newData.parallel;
        const improvementPercentage = (improvement / newData.sequential) * 100;

        newData.improvement = improvement;
        newData.improvementPercentage = improvementPercentage;
      }

      return newData;
    });
  };

  return {
    comparisonData,
    recordLoadTime,
    hasComparison: !!(comparisonData.parallel && comparisonData.sequential),
  };
};

/**
 * Hook for aggregate performance tracking across multiple calculations
 */
export const useAggregatePerformanceTracking = () => {
  const [aggregateData, setAggregateData] = useState<{
    totalMeasurements: number;
    averageLoadTime: number;
    parallelUsageRate: number;
    errorRate: number;
    measurements: Array<{
      calculationId: string;
      loadTime: number;
      parallelUsed: boolean;
      timestamp: number;
    }>;
  }>({
    totalMeasurements: 0,
    averageLoadTime: 0,
    parallelUsageRate: 0,
    errorRate: 0,
    measurements: [],
  });

  const recordMeasurement = (
    calculationId: string,
    loadTime: number,
    parallelUsed: boolean,
    hasErrors = false
  ) => {
    setAggregateData((prev) => {
      const newMeasurement = {
        calculationId,
        loadTime,
        parallelUsed,
        timestamp: Date.now(),
      };

      const newMeasurements = [...prev.measurements, newMeasurement];
      const totalMeasurements = newMeasurements.length;

      const averageLoadTime =
        newMeasurements.reduce((sum, m) => sum + m.loadTime, 0) /
        totalMeasurements;
      const parallelUsageRate =
        (newMeasurements.filter((m) => m.parallelUsed).length /
          totalMeasurements) *
        100;
      const errorRate = hasErrors
        ? ((prev.errorRate * (totalMeasurements - 1) + 1) / totalMeasurements) *
          100
        : prev.errorRate;

      return {
        totalMeasurements,
        averageLoadTime,
        parallelUsageRate,
        errorRate,
        measurements: newMeasurements.slice(-100), // Keep last 100 measurements
      };
    });
  };

  return {
    aggregateData,
    recordMeasurement,
  };
};
