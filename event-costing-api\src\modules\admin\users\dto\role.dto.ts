import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for role response
 */
export class RoleDto {
  @ApiProperty({
    description: 'Role ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Role name',
    example: 'admin',
  })
  role_name: string;

  @ApiProperty({
    description: 'Role description',
    example: 'Administrator with full access',
  })
  description: string;

  @ApiProperty({
    description: 'Role creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: string;
}
