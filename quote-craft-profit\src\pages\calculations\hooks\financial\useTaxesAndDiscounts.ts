import { useState, useEffect, useCallback, useMemo } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Tax, Discount } from "../../utils/calculationUtils";
import {
  addTaxToCalculation,
  updateCalculationTax,
  removeCalculationTax,
  transformFrontendTaxToBackend,
} from "../../../../services/calculations/taxService";
import { updateCalculation } from "../../../../services/calculations";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { debug } from "@/lib/debugUtils";
import {
  useRenderTracker,
  useObjectReferenceTracker,
} from "@/lib/renderTracker";

/**
 * Custom hook for managing taxes and discounts in calculations
 * PERFORMANCE OPTIMIZED: Fixed object recreation and stabilized dependencies
 *
 * This hook centralizes all tax and discount functionality in one place,
 * reducing complexity and improving performance.
 *
 * @param calculationId - The ID of the calculation
 * @param initialTaxes - Initial taxes from the calculation
 * @param initialDiscount - Initial discount from the calculation
 * @returns Object with taxes, discount, and functions to manage them
 */
export function useTaxesAndDiscounts(
  calculationId: string,
  initialTaxes: Tax[] = [],
  initialDiscount?: Discount | null
) {
  const queryClient = useQueryClient();

  // PERFORMANCE OPTIMIZATION: Memoize default discount to prevent recreation
  const defaultDiscount = useMemo(
    () => ({
      name: "Discount",
      type: "fixed" as const,
      value: 0,
    }),
    []
  );

  // State for taxes and discount
  const [taxes, setTaxes] = useState<Tax[]>(initialTaxes);
  const [discount, setDiscount] = useState<Discount>(
    initialDiscount || defaultDiscount
  );

  // DEBUG: Track renders and object references
  useRenderTracker(
    "useTaxesAndDiscounts",
    {
      calculationId,
      taxesLength: taxes.length,
      discountValue: discount.value,
      hasInitialTaxes: !!initialTaxes?.length,
      hasInitialDiscount: !!initialDiscount,
    },
    { logLevel: "detailed" }
  );

  useObjectReferenceTracker("taxes", taxes);
  useObjectReferenceTracker("discount", discount);

  // PERFORMANCE OPTIMIZATION: Update state when initialTaxes or initialDiscount change
  // Use JSON.stringify for deep comparison to prevent unnecessary updates
  useEffect(() => {
    if (Array.isArray(initialTaxes)) {
      setTaxes((prevTaxes) => {
        // Only update if actually different
        if (JSON.stringify(prevTaxes) !== JSON.stringify(initialTaxes)) {
          return initialTaxes;
        }
        return prevTaxes;
      });
    } else {
      setTaxes([]);
    }
  }, [initialTaxes]);

  useEffect(() => {
    if (initialDiscount) {
      setDiscount((prevDiscount) => {
        // Only update if actually different
        if (JSON.stringify(prevDiscount) !== JSON.stringify(initialDiscount)) {
          return initialDiscount;
        }
        return prevDiscount;
      });
    } else {
      setDiscount((prevDiscount) => {
        // Only update if not already default
        if (JSON.stringify(prevDiscount) !== JSON.stringify(defaultDiscount)) {
          return defaultDiscount;
        }
        return prevDiscount;
      });
    }
  }, [initialDiscount, defaultDiscount]);

  // Mutation for updating taxes and discount
  const updateMutation = useMutation({
    mutationFn: (data: {
      status?: "draft" | "completed" | "canceled";
      taxes: Tax[];
      discount: Discount;
    }) => updateCalculation(calculationId, data),
    onSuccess: () => {
      // Invalidate the calculation query to ensure fresh data
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculation(calculationId),
      });
    },
    onError: (error) => {
      console.error("Error updating taxes and discount:", error);
      toast.error("Failed to save taxes and discount");
    },
  });

  // Add a new tax using Backend API
  const addTaxMutation = useMutation({
    mutationFn: (newTax: Tax) =>
      addTaxToCalculation(calculationId, transformFrontendTaxToBackend(newTax)),
    onSuccess: (addedTax) => {
      setTaxes((prev) => [...prev, addedTax]);
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculation(calculationId),
      });
      toast.success("Tax added successfully");
    },
    onError: (error) => {
      console.error("Error adding tax:", error);
      toast.error("Failed to add tax");
    },
  });

  const addTax = useCallback(
    (newTax: Tax) => {
      addTaxMutation.mutate(newTax);
      return taxes; // Return current taxes for immediate UI update
    },
    [addTaxMutation] // CRITICAL FIX: Remove taxes dependency - it's captured in closure and causes re-renders
  );

  // Remove a tax using Backend API
  const removeTaxMutation = useMutation({
    mutationFn: (taxId: string) => removeCalculationTax(calculationId, taxId),
    onSuccess: (_, taxId) => {
      setTaxes((prev) => prev.filter((tax) => tax.id !== taxId));
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculation(calculationId),
      });
      toast.success("Tax removed successfully");
    },
    onError: (error) => {
      console.error("Error removing tax:", error);
      toast.error("Failed to remove tax");
    },
  });

  const removeTax = useCallback(
    (taxId: string) => {
      removeTaxMutation.mutate(taxId);
      return taxes.filter((tax) => tax.id !== taxId); // Return updated taxes for immediate UI update
    },
    [removeTaxMutation] // CRITICAL FIX: Remove taxes dependency to prevent re-renders
  );

  // Edit a tax using Backend API
  const editTaxMutation = useMutation({
    mutationFn: ({
      taxId,
      updates,
    }: {
      taxId: string;
      updates: Partial<Tax>;
    }) =>
      updateCalculationTax(calculationId, taxId, {
        tax_name: updates.name,
        tax_rate: updates.rate,
        type: updates.type,
        basis: updates.basis,
      }),
    onSuccess: (updatedTax) => {
      setTaxes((prev) =>
        prev.map((tax) => (tax.id === updatedTax.id ? updatedTax : tax))
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculation(calculationId),
      });
      toast.success("Tax updated successfully");
    },
    onError: (error) => {
      console.error("Error updating tax:", error);
      toast.error("Failed to update tax");
    },
  });

  const editTax = useCallback(
    (taxId: string, updates: Partial<Tax>) => {
      editTaxMutation.mutate({ taxId, updates });
      return taxes.map((tax) =>
        tax.id === taxId ? { ...tax, ...updates } : tax
      ); // Return updated taxes for immediate UI update
    },
    [editTaxMutation] // CRITICAL FIX: Remove taxes dependency to prevent re-renders
  );

  // Update discount
  const updateDiscount = useCallback((newDiscount: Discount) => {
    setDiscount(newDiscount);
    return newDiscount;
  }, []);

  // Save taxes and discount to the database
  const saveTaxesAndDiscount = useCallback(
    async (status?: "draft" | "completed" | "canceled") => {
      try {
        const updateData = {
          ...(status ? { status } : {}),
          taxes,
          discount,
        };

        debug("Saving taxes and discount:", updateData);

        await updateMutation.mutateAsync(updateData);
        return true;
      } catch (error) {
        debug("Error saving taxes and discount:", error);
        return false;
      }
    },
    [updateMutation] // CRITICAL FIX: Remove taxes/discount deps - they're captured in closure
  );

  // PERFORMANCE OPTIMIZATION: Memoize return object with minimal dependencies
  // CRITICAL FIX: Remove JSON.stringify and reduce dependency array size
  return useMemo(
    () => ({
      taxes,
      discount,
      addTax,
      editTax,
      removeTax,
      updateDiscount,
      saveTaxesAndDiscount,
      isUpdating:
        updateMutation.isPending ||
        addTaxMutation.isPending ||
        editTaxMutation.isPending ||
        removeTaxMutation.isPending,
    }),
    [
      // CRITICAL FIX: Only depend on essential primitive values
      taxes.length, // Only depend on length for array changes
      discount?.value, // Only depend on value, not object reference
      discount?.type, // Include type for completeness
      // Remove JSON.stringify - it's expensive and causes excessive re-renders
      // Remove callback dependencies - they're already memoized with their own deps
      updateMutation.isPending,
      addTaxMutation.isPending,
      editTaxMutation.isPending,
      removeTaxMutation.isPending,
    ]
  );
}
