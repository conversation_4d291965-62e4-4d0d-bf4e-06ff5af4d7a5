import React, { useState, useEffect } from 'react';
import { AdminSettings, defaultSettings, SettingsContextType } from '@/pages/admin/settings/types';
import { loadSettings, saveSettings } from '@/services/admin/settings';
import { SettingsContext } from './settings-context';

export const SettingsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [settings, setSettings] = useState<AdminSettings>(defaultSettings);

  // Load settings from localStorage on initial render
  useEffect(() => {
    const savedSettings = loadSettings();
    setSettings(savedSettings);
  }, []);

  // Update settings for a specific category
  const handleUpdateSettings = (
    category: keyof AdminSettings,
    values: Partial<AdminSettings[keyof AdminSettings]>,
  ) => {
    const updatedSettings = {
      ...settings,
      [category]: {
        ...settings[category],
        ...values,
      },
    };

    setSettings(updatedSettings);
    saveSettings(updatedSettings);
  };

  // Reset all settings to defaults
  const resetSettings = () => {
    setSettings(defaultSettings);
    saveSettings(defaultSettings);
  };

  return (
    <SettingsContext.Provider
      value={{
        settings,
        updateSettings: handleUpdateSettings,
        resetSettings,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
};
