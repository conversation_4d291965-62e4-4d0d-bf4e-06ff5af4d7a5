# Test Script: Infinite Render Fixes

## 🧪 **Manual Testing Instructions**

### **Prerequisites**
1. Start development server: `npm run dev`
2. Open browser DevTools Console
3. Navigate to a calculation detail page

### **Test 1: Initial Page Load**
**Steps**:
1. Navigate to `/calculations/{any-calculation-id}`
2. Monitor console output for 10 seconds

**Expected Results** ✅:
```
🔄 CalculationDetailPage render #1 - Changed: [id]
🔄 useOptimizedCalculationDetail render #1
🔄 useCalculationDetailUI render #1
🔄 CalculationDetailPage render #2 - Changed: [calculation]
// Should stop here (2-3 renders max)
```

**Failure Signs** ❌:
```
🔄 CalculationDetailPage render #10+
⚠️ CalculationDetailPage has rendered 10+ times - possible infinite loop!
🚨 Query "calculation-detail" invalidated 5+ times
```

### **Test 2: User Interactions**
**Steps**:
1. Change a package quantity
2. Toggle a category expansion
3. Edit calculation name
4. Monitor console after each action

**Expected Results** ✅:
- Each action causes 1-2 renders maximum
- No cascading re-renders
- Console shows specific changed dependencies

**Failure Signs** ❌:
- Actions cause 5+ renders
- Renders continue after action completes
- Same dependencies changing repeatedly

### **Test 3: Background Stability**
**Steps**:
1. Leave page open for 5 minutes
2. Don't interact with page
3. Monitor console for spontaneous renders

**Expected Results** ✅:
- No renders after initial load
- Console remains quiet

**Failure Signs** ❌:
- Periodic renders without user action
- Memory usage increasing over time

### **Test 4: React DevTools Profiler**
**Steps**:
1. Open React DevTools → Profiler tab
2. Click "Record"
3. Perform various interactions
4. Stop recording and analyze

**Expected Results** ✅:
- Render times < 16ms per component
- Reasonable number of components re-rendering
- No excessive render cascades

**Failure Signs** ❌:
- Render times > 50ms
- Hundreds of components re-rendering
- Deep render cascades

## 🔧 **Debugging Commands**

### **Enable Verbose Logging**
Add to browser console:
```javascript
// Enable verbose render tracking
localStorage.setItem('debug-renders', 'verbose');
// Refresh page
```

### **Disable Logging**
```javascript
// Disable render tracking
localStorage.setItem('debug-renders', 'disabled');
// Refresh page
```

### **Check Memory Usage**
```javascript
// Monitor memory usage
setInterval(() => {
  console.log('Memory:', performance.memory?.usedJSHeapSize || 'N/A');
}, 5000);
```

## 📊 **Results Template**

Copy and fill out:

```
## Test Results - [Date/Time]

### Test 1: Initial Page Load
- ✅/❌ Renders stabilized after 2-3 renders
- ✅/❌ No infinite loop warnings
- Notes: 

### Test 2: User Interactions
- ✅/❌ Each action causes ≤2 renders
- ✅/❌ No cascading re-renders
- Notes:

### Test 3: Background Stability
- ✅/❌ No spontaneous renders
- ✅/❌ Memory usage stable
- Notes:

### Test 4: React DevTools Profiler
- ✅/❌ Render times reasonable
- ✅/❌ No excessive cascades
- Notes:

### Overall Assessment
- ✅/❌ Infinite render issue resolved
- ✅/❌ Performance improved
- ✅/❌ User experience smooth

### Next Steps
- [ ] Remove debugging code if successful
- [ ] Implement additional fixes if needed
- [ ] Monitor in production
```

## 🚨 **If Tests Fail**

### **High Render Count (10+ renders)**
1. Check console for which dependencies are changing
2. Look for object reference changes
3. Verify memoization in identified hooks

### **Specific Dependency Issues**
1. **If `state` keeps changing**: Check `useCalculationDetailComplete`
2. **If `actions` keeps changing**: Check `useCalculationActions`
3. **If `uiState` keeps changing**: Check `useCalculationDetailUI`
4. **If timezone functions change**: Check `useTimezoneAwareDates`

### **Cache Invalidation Loops**
1. Look for repeated query invalidation messages
2. Check `useCalculationEditState` for excessive invalidations
3. Consider adding debouncing to cache operations

### **Memory Leaks**
1. Monitor memory usage over time
2. Check for event listeners not being cleaned up
3. Verify useEffect cleanup functions

## 📞 **Escalation**

If tests continue to fail:
1. Document exact console output
2. Record React DevTools Profiler session
3. Note specific user actions that trigger issues
4. Provide browser/OS information
5. Consider implementing Priority 2 fixes from action plan
