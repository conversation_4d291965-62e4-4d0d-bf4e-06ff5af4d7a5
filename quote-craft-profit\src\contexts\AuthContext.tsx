import React, {
  createContext,
  useState,
  useEffect,
  useMemo,
  useCallback,
  ReactNode,
} from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client"; // Assuming this path is correct
import { showSuccess, showError } from "@/lib/notifications";

// Export the interface so it can be imported by useAuth.tsx
export interface AuthContextType {
  session: Session | null;
  user: User | null;
  isAdmin: boolean;
  loading: boolean; // Indicates initial auth session resolution
  isRoleLoading: boolean; // Indicates if the role is currently being fetched/checked
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signUp: (
    email: string,
    password: string,
    fullName: string
  ) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  triggerRefreshSession: () => Promise<void>; // Re-exposed for manual refresh needs
}

// Export the context so it can be imported by useAuth.tsx
export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true); // For initial auth session load
  const [isRoleLoading, setIsRoleLoading] = useState<boolean>(false); // For role checking specifically

  // Test Supabase connection on component mount
  useEffect(() => {
    console.log(`[${new Date().toISOString()}] Testing Supabase connection...`);

    // Simple query to check if Supabase is reachable
    const testConnection = async () => {
      try {
        await supabase.from("profiles").select("count").limit(1);
        console.log(
          `[${new Date().toISOString()}] Supabase connection test successful`
        );
      } catch (error: unknown) {
        console.error(
          `[${new Date().toISOString()}] Supabase connection test failed:`,
          error
        );
        showError(
          "Could not connect to the database. Please check your network connection."
        );
      }
    };

    testConnection();
  }, []);

  // Debug loading state changes and add safety timeout
  useEffect(() => {
    console.log(
      `[${new Date().toISOString()}] AuthProvider loading state changed:`,
      loading
    );

    // Safety timeout to prevent infinite loading
    if (loading) {
      console.log(
        `[${new Date().toISOString()}] Setting safety timeout for loading state`
      );
      const safetyTimer = setTimeout(() => {
        console.warn(
          `[${new Date().toISOString()}] Safety timeout triggered: Force setting loading to false after 10 seconds`
        );
        setLoading(false);
        // Also reset role loading if it's still active
        if (isRoleLoading) {
          console.warn(
            `[${new Date().toISOString()}] Safety timeout also resetting isRoleLoading which was still true`
          );
          setIsRoleLoading(false);
        }
      }, 10000); // 10 seconds

      return () => {
        console.log(
          `[${new Date().toISOString()}] Clearing safety timeout as loading changed to:`,
          !loading
        );
        clearTimeout(safetyTimer);
      };
    }
  }, [loading, isRoleLoading]);

  // Cache for user roles to avoid redundant database queries
  const userRoleCache = useMemo(() => new Map<string, boolean>(), []);

  // Track role check failures to implement fallback strategy
  const [roleCheckFailureCount, setRoleCheckFailureCount] = useState<number>(0);
  const MAX_ROLE_CHECK_FAILURES = 3; // After this many failures, use simplified role checking

  // Function to check user role
  const checkUserRole = useCallback(
    async (userId: string | undefined): Promise<void> => {
      console.log(
        `[${new Date().toISOString()}] checkUserRole called for userId:`,
        userId,
        { failureCount: roleCheckFailureCount }
      );

      if (!userId) {
        console.log(
          `[${new Date().toISOString()}] No userId provided, setting isAdmin=false`
        );
        setIsAdmin(false);
        setIsRoleLoading(false); // Ensure role loading is false if no user
        return;
      }

      // First check if we have the user object with metadata
      if (user && user.user_metadata) {
        console.log(
          `[${new Date().toISOString()}] Checking for role in user metadata:`,
          user.user_metadata
        );

        // Check if role is directly in user_metadata
        if (user.user_metadata.role === "admin") {
          console.log(
            `[${new Date().toISOString()}] Found admin role in user_metadata`
          );
          setIsAdmin(true);
          userRoleCache.set(userId, true);
          setIsRoleLoading(false);
          return;
        }

        // Check if role_name is in user_metadata
        if (user.user_metadata.role_name === "admin") {
          console.log(
            `[${new Date().toISOString()}] Found admin role_name in user_metadata`
          );
          setIsAdmin(true);
          userRoleCache.set(userId, true);
          setIsRoleLoading(false);
          return;
        }

        // Check if there's a roles object in user_metadata
        if (
          user.user_metadata.roles &&
          user.user_metadata.roles.role_name === "admin"
        ) {
          console.log(
            `[${new Date().toISOString()}] Found admin role in user_metadata.roles`
          );
          setIsAdmin(true);
          userRoleCache.set(userId, true);
          setIsRoleLoading(false);
          return;
        }

        console.log(
          `[${new Date().toISOString()}] No role information found in user_metadata`
        );
      }

      // Use simplified role checking if we've had too many failures
      if (roleCheckFailureCount >= MAX_ROLE_CHECK_FAILURES) {
        console.warn(
          `[${new Date().toISOString()}] Using simplified role check after ${roleCheckFailureCount} failures`
        );

        // Simplified approach - just check if the user is in the admin list
        // This is a fallback when database queries are failing
        setIsRoleLoading(true);
        try {
          // You could implement a hardcoded check for known admin emails here
          // For example:
          // Add your email to the list of known admin emails
          const knownAdminEmails = ["<EMAIL>", "<EMAIL>"];
          const isKnownAdmin = user?.email
            ? knownAdminEmails.includes(user.email)
            : false;

          console.log(
            `[${new Date().toISOString()}] Simplified role check result: isAdmin=${isKnownAdmin}`
          );
          setIsAdmin(isKnownAdmin);
          userRoleCache.set(userId, isKnownAdmin);
        } catch (err) {
          console.error(
            `[${new Date().toISOString()}] Error in simplified role check:`,
            err
          );
          setIsAdmin(false);
          userRoleCache.set(userId, false);
        } finally {
          setIsRoleLoading(false);
        }
        return;
      }

      if (userRoleCache.has(userId)) {
        const cachedIsAdmin = userRoleCache.get(userId) || false;
        console.log(
          `[${new Date().toISOString()}] Using cached role for ${userId}: isAdmin=${cachedIsAdmin}`
        );
        setIsAdmin(cachedIsAdmin);
        setIsRoleLoading(false); // Role check complete (from cache)
        return;
      }

      console.log(
        `[${new Date().toISOString()}] Checking role for user ${userId} from database`
      );
      setIsRoleLoading(true);

      const startTime = Date.now();
      try {
        console.log(
          `[${new Date().toISOString()}] Querying profiles table for user ${userId}`
        );

        // Create the role check query promise
        const roleCheckPromise = supabase
          .from("profiles")
          .select("*, roles(role_name)") // Adjust if your schema differs. E.g., .select('role_column_name')
          .eq("id", userId)
          .single();

        // Create a timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error("Role check query timed out after 5 seconds"));
          }, 5000); // 5 second timeout
        });

        // Race the query against the timeout
        console.log(
          `[${new Date().toISOString()}] Starting role check with 5-second timeout`
        );
        const { data, error } = await Promise.race([
          roleCheckPromise,
          timeoutPromise,
        ]);

        const queryTime = Date.now() - startTime;
        console.log(
          `[${new Date().toISOString()}] Profile query completed in ${queryTime}ms`
        );

        if (error) {
          if (error.code === "PGRST116") {
            // "No rows found" - common if profile creation is delayed
            console.warn(
              `[${new Date().toISOString()}] Profile not found for user ${userId} during role check. Defaulting to non-admin.`,
              { error_code: error.code, error_message: error.message }
            );
            setIsAdmin(false);
            userRoleCache.set(userId, false);
          } else {
            console.error(
              `[${new Date().toISOString()}] Database error during role check:`,
              {
                error_code: error.code,
                error_message: error.message,
                details: error.details,
              }
            );
            throw error; // Re-throw other database errors to be caught by the main catch
          }
        } else {
          // Log the profile data for debugging
          console.log(`[${new Date().toISOString()}] Profile data retrieved:`, {
            profile_id: data?.id,
            role_id: data?.role_id,
            role_name: (data?.roles as { role_name: string })?.role_name,
            has_roles_data: !!data?.roles,
          });

          // Adjust this logic based on how your 'role' is stored in 'profiles' or related tables
          const currentIsAdmin =
            (data?.roles as { role_name: string })?.role_name === "admin";
          // Example if 'role' is a direct column in 'profiles': const currentIsAdmin = data?.role === 'admin';

          console.log(
            `[${new Date().toISOString()}] Setting and caching isAdmin=${currentIsAdmin} for user ${userId}`
          );
          setIsAdmin(currentIsAdmin);
          userRoleCache.set(userId, currentIsAdmin);

          // Reset failure counter on successful role check
          if (roleCheckFailureCount > 0) {
            console.log(
              `[${new Date().toISOString()}] Resetting role check failure counter after successful check`
            );
            setRoleCheckFailureCount(0);
          }

          // Store the role in user metadata for faster access in the future
          if (user) {
            try {
              console.log(
                `[${new Date().toISOString()}] Updating user metadata with role information`
              );

              // Update the user metadata with the role information
              await supabase.auth.updateUser({
                data: {
                  role: currentIsAdmin ? "admin" : "user",
                  role_name: currentIsAdmin ? "admin" : "user",
                },
              });

              console.log(
                `[${new Date().toISOString()}] User metadata updated successfully`
              );
            } catch (updateError) {
              console.error(
                `[${new Date().toISOString()}] Error updating user metadata:`,
                updateError
              );
              // Non-critical error, so we don't need to handle it specially
            }
          }
        }
      } catch (err) {
        const errorTime = Date.now() - startTime;

        // Increment the failure counter
        const newFailureCount = roleCheckFailureCount + 1;
        setRoleCheckFailureCount(newFailureCount);

        // Check if this is a timeout error
        const isTimeoutError = (err as Error)?.message?.includes("timed out");

        if (isTimeoutError) {
          console.error(
            `[${new Date().toISOString()}] Role check timed out after ${errorTime}ms (failure #${newFailureCount}):`,
            err
          );
          showError("Role check timed out. Using default permissions.");
        } else {
          console.error(
            `[${new Date().toISOString()}] Error checking user role after ${errorTime}ms (failure #${newFailureCount}):`,
            err
          );
        }

        // Log more details about the error
        console.error(`[${new Date().toISOString()}] Error details:`, {
          name: (err as Error)?.name,
          message: (err as Error)?.message,
          stack: (err as Error)?.stack,
        });

        // If we've reached the max failures, show a more detailed error
        if (newFailureCount >= MAX_ROLE_CHECK_FAILURES) {
          console.warn(
            `[${new Date().toISOString()}] Reached maximum role check failures (${MAX_ROLE_CHECK_FAILURES}). Switching to simplified role checking.`
          );
          showError(
            "Having trouble connecting to the database. Using simplified permissions."
          );
        }

        setIsAdmin(false); // Default to non-admin on error
        userRoleCache.set(userId, false); // Cache false on error
      } finally {
        const totalTime = Date.now() - startTime;
        console.log(
          `[${new Date().toISOString()}] Role check completed in ${totalTime}ms, setting isRoleLoading=false`
        );
        setIsRoleLoading(false);
      }
    },
    [userRoleCache]
  ); // supabase client is stable, userRoleCache is stable

  // Exposed function to manually trigger a session refresh if needed by UI components
  const triggerRefreshSession = useCallback(async () => {
    // console.log('Manually triggered session refresh...');
    try {
      const { error } = await supabase.auth.refreshSession();
      if (error) {
        console.error("Error during manual session refresh:", error);
        showError("Session refresh failed. You might need to sign in again.");
      }
      // onAuthStateChange will handle updating the state if the session changes.
    } catch (e) {
      console.error("Exception during manual session refresh:", e);
      showError("An unexpected error occurred while refreshing session.");
    }
  }, []); // supabase client is stable

  // Effect for initializing auth state and setting up Supabase auth listener
  useEffect(() => {
    const startTime = Date.now();
    console.log(
      `[${new Date().toISOString()}] AuthProvider mounted. Initializing auth and setting up listeners.`
    );
    setLoading(true); // Start loading for initial session check

    // Get current session on initial load
    console.log(
      `[${new Date().toISOString()}] Calling supabase.auth.getSession()`
    );
    supabase.auth
      .getSession()
      .then(async ({ data: { session: currentSession } }) => {
        const sessionTime = Date.now() - startTime;
        console.log(
          `[${new Date().toISOString()}] Initial getSession completed in ${sessionTime}ms:`,
          currentSession
            ? {
                user_id: currentSession.user?.id,
                email: currentSession.user?.email,
                token_length: currentSession.access_token?.length,
                expires_at: new Date(
                  currentSession.expires_at * 1000
                ).toISOString(),
              }
            : "No session"
        );

        setSession(currentSession);
        setUser(currentSession?.user ?? null);

        // IMPORTANT CHANGE: Set main loading to false once session is known.
        // Role check will happen asynchronously and manage `isRoleLoading`.
        console.log(
          `[${new Date().toISOString()}] Setting main loading to false`
        );
        setLoading(false);

        if (currentSession?.user) {
          console.log(
            `[${new Date().toISOString()}] User found, checking role for: ${
              currentSession.user.id
            }`
          );
          try {
            await checkUserRole(currentSession.user.id); // Check role, manages isRoleLoading
            console.log(
              `[${new Date().toISOString()}] Role check completed successfully`
            );
          } catch (roleError) {
            console.error(
              `[${new Date().toISOString()}] Error during role check:`,
              roleError
            );
            // Ensure role loading is reset even if checkUserRole throws
            setIsRoleLoading(false);
          }
        } else {
          console.log(
            `[${new Date().toISOString()}] No user in session, setting isAdmin=false`
          );
          setIsAdmin(false); // No user, so not an admin
          setIsRoleLoading(false); // No role to load
        }
      })
      .catch((error) => {
        console.error(
          `[${new Date().toISOString()}] Error in initial getSession:`,
          error
        );
        // Log more details about the error
        console.error(`[${new Date().toISOString()}] Error details:`, {
          name: error.name,
          message: error.message,
          stack: error.stack,
        });

        setSession(null);
        setUser(null);
        setIsAdmin(false);
        setIsRoleLoading(false);
        setLoading(false); // Ensure loading is false even on critical error during initial fetch
      });

    // Listen for Supabase auth state changes (sign in, sign out, token refresh, etc.)
    console.log(
      `[${new Date().toISOString()}] Setting up auth state change listener`
    );
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        console.log(
          `[${new Date().toISOString()}] Auth state changed: ${event}`,
          newSession
            ? {
                user_id: newSession.user?.id,
                email: newSession.user?.email,
                token_length: newSession.access_token?.length,
                expires_at: newSession.expires_at
                  ? new Date(newSession.expires_at * 1000).toISOString()
                  : "unknown",
              }
            : "No session"
        );

        setSession(newSession);
        setUser(newSession?.user ?? null);

        if (event === "SIGNED_OUT") {
          console.log(
            `[${new Date().toISOString()}] User signed out, clearing admin status and role cache`
          );
          setIsAdmin(false);
          userRoleCache.clear();
          setIsRoleLoading(false);
        } else if (newSession?.user) {
          // For SIGNED_IN, TOKEN_REFRESHED, USER_UPDATED, INITIAL_USER events
          // The INITIAL_USER event is when the user is first set after a sign-in or session restoration.
          console.log(
            `[${new Date().toISOString()}] User session updated (${event}), checking role for: ${
              newSession.user.id
            }`
          );

          // First check if role info is in user metadata
          const userMetadata = newSession.user.user_metadata;
          if (userMetadata) {
            console.log(
              `[${new Date().toISOString()}] Checking user metadata for role:`,
              userMetadata
            );

            // Check various possible locations for role information
            if (
              userMetadata.role === "admin" ||
              userMetadata.role_name === "admin" ||
              (userMetadata.roles && userMetadata.roles.role_name === "admin")
            ) {
              console.log(
                `[${new Date().toISOString()}] Found admin role in user metadata`
              );
              setIsAdmin(true);
              userRoleCache.set(newSession.user.id, true);
              setIsRoleLoading(false);
              return;
            }
          }

          // Check if user email is a known admin email
          if (newSession.user.email) {
            const knownAdminEmails = ["<EMAIL>", "<EMAIL>"];
            if (knownAdminEmails.includes(newSession.user.email)) {
              console.log(
                `[${new Date().toISOString()}] User email ${
                  newSession.user.email
                } is a known admin email`
              );
              setIsAdmin(true);
              userRoleCache.set(newSession.user.id, true);
              setIsRoleLoading(false);

              // Update user metadata with role information
              try {
                await supabase.auth.updateUser({
                  data: {
                    role: "admin",
                    role_name: "admin",
                  },
                });
                console.log(
                  `[${new Date().toISOString()}] Updated user metadata with admin role`
                );
              } catch (updateError) {
                console.error(
                  `[${new Date().toISOString()}] Error updating user metadata:`,
                  updateError
                );
              }
              return;
            }
          }

          // If we couldn't determine role from metadata or email, fall back to database check
          try {
            await checkUserRole(newSession.user.id);
            console.log(
              `[${new Date().toISOString()}] Role check completed for auth state change`
            );
          } catch (roleError) {
            console.error(
              `[${new Date().toISOString()}] Error during role check for auth state change:`,
              roleError
            );
            // Ensure role loading is reset even if checkUserRole throws
            setIsRoleLoading(false);
          }
        } else if (event === "INITIAL_SESSION" && !newSession?.user) {
          // Handles case where initial session is checked by listener but no user
          console.log(
            `[${new Date().toISOString()}] Initial session with no user, setting isAdmin=false`
          );
          setIsAdmin(false);
          setIsRoleLoading(false);
        }
        // Note: The main `loading` state is primarily controlled by the getSession() promise chain above
        // for the *initial* load. Subsequent auth events update session/user/role but don't
        // toggle the main `loading` flag, as the app is already considered "loaded".
      }
    );

    // Cleanup listener on component unmount
    return () => {
      console.log(
        `[${new Date().toISOString()}] AuthProvider unmounting. Unsubscribing auth listener.`
      );
      authListener?.subscription?.unsubscribe();
    };
  }, [checkUserRole, userRoleCache]); // Dependencies for the main auth effect

  // Effect for scheduling automatic token refresh before expiration
  useEffect(() => {
    let refreshTimer: NodeJS.Timeout | null = null;
    if (session?.expires_at) {
      const expiresAtMs = session.expires_at * 1000;
      const nowMs = Date.now();
      const timeUntilExpiryMs = expiresAtMs - nowMs;
      const refreshThresholdMs = 5 * 60 * 1000; // 5 minutes

      if (timeUntilExpiryMs > 0) {
        const refreshInMs = Math.max(timeUntilExpiryMs - refreshThresholdMs, 0);
        refreshTimer = setTimeout(() => {
          triggerRefreshSession();
        }, refreshInMs);
      }
    }
    return () => {
      if (refreshTimer) clearTimeout(refreshTimer);
    };
  }, [session, triggerRefreshSession]); // Re-run if session or refresh function changes

  // Effect for handling tab visibility changes to ensure session freshness
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        console.log(
          `[${new Date().toISOString()}] Tab became visible, checking session`
        );

        // Calling getSession() prompts Supabase client to check its state,
        // which can trigger onAuthStateChange if the state has changed.
        supabase.auth
          .getSession()
          .then(({ data: { session: currentSupabaseSession } }) => {
            console.log(
              `[${new Date().toISOString()}] Visibility change getSession result:`,
              currentSupabaseSession
                ? {
                    user_id: currentSupabaseSession.user?.id,
                    token_length: currentSupabaseSession.access_token?.length,
                    expires_at: currentSupabaseSession.expires_at
                      ? new Date(
                          currentSupabaseSession.expires_at * 1000
                        ).toISOString()
                      : "unknown",
                  }
                : "No session"
            );

            // Compare currentSupabaseSession with React state session for debugging
            if (
              currentSupabaseSession?.access_token !== session?.access_token
            ) {
              console.warn(
                `[${new Date().toISOString()}] Session mismatch detected on visibility change.`,
                {
                  current_token_length: session?.access_token?.length,
                  new_token_length:
                    currentSupabaseSession?.access_token?.length,
                  loading,
                  isRoleLoading,
                }
              );
            }
          })
          .catch((error) => {
            console.error(
              `[${new Date().toISOString()}] Error checking session on visibility change:`,
              error
            );
          });
      } else {
        console.log(`[${new Date().toISOString()}] Tab became hidden`);
      }
    };

    console.log(
      `[${new Date().toISOString()}] Setting up visibility change listener`
    );
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      console.log(
        `[${new Date().toISOString()}] Removing visibility change listener`
      );
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [session, loading, isRoleLoading]); // Dependencies for visibility effect

  // Auth action: Sign in
  const signIn = useCallback(async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      // onAuthStateChange will handle setting user, session, and triggering role check
      return { error: null };
    } catch (err) {
      console.error("Error signing in:", err);
      showError(
        (err as Error).message ||
          "Sign in failed. Please check your credentials."
      );
      return { error: err as Error };
    }
  }, []);

  // Auth action: Sign up
  const signUp = useCallback(
    async (email: string, password: string, fullName: string) => {
      try {
        const { error } = await supabase.auth.signUp({
          email,
          password,
          options: { data: { full_name: fullName } },
        });
        if (error) throw error;
        showSuccess(
          "Sign up successful! Please check your email to verify your account."
        );
        return { error: null };
      } catch (err) {
        console.error("Error signing up:", err);
        showError(
          (err as Error).message || "Sign up failed. Please try again."
        );
        return { error: err as Error };
      }
    },
    []
  );

  // Auth action: Sign out
  const signOut = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      // onAuthStateChange will handle setting user/session to null and isAdmin to false.
    } catch (err) {
      console.error("Error signing out:", err);
      showError(
        (err as Error).message || "Failed to sign out. Please try again."
      );
    }
  }, []);

  // Memoize the context value to prevent unnecessary re-renders of consumers
  const contextValue = useMemo(
    () => ({
      session,
      user,
      isAdmin,
      loading, // For initial session load
      isRoleLoading, // For async role checking
      signIn,
      signUp,
      signOut,
      triggerRefreshSession,
    }),
    [
      session,
      user,
      isAdmin,
      loading,
      isRoleLoading,
      signIn,
      signUp,
      signOut,
      triggerRefreshSession,
    ]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

// The useAuth hook has been moved to useAuth.tsx
