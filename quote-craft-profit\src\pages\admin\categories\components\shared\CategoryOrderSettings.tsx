import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { GripVertical, Save, Undo2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useCategoryOrder } from '../../hooks/useCategoryOrder';
import { Category, CategoryOrderItem } from '../../types';

/**
 * Component for managing category display order with drag and drop
 */
export function CategoryOrderSettings() {
  const { categories, isLoading, updateOrder, isUpdating } = useCategoryOrder();
  const [localCategories, setLocalCategories] = useState<Category[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // Initialize local categories when data is loaded
  useEffect(() => {
    if (categories.length > 0) {
      setLocalCategories([...categories]);
    }
  }, [categories]);

  // Handle drag end
  const onDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    // Return if dropped outside the list or at the same position
    if (
      !destination ||
      (destination.droppableId === source.droppableId &&
        destination.index === source.index)
    ) {
      return;
    }

    // Reorder the list
    const items = Array.from(localCategories);
    const [reorderedItem] = items.splice(source.index, 1);
    items.splice(destination.index, 0, reorderedItem);

    // Update display_order values
    const updatedItems = items.map((item, index) => ({
      ...item,
      display_order: index + 1,
    }));

    setLocalCategories(updatedItems);
    setHasChanges(true);
  };

  // Save changes
  const saveChanges = () => {
    const orderItems: CategoryOrderItem[] = localCategories.map((cat, index) => ({
      id: cat.id,
      display_order: index + 1,
    }));

    updateOrder(orderItems);
    setHasChanges(false);
  };

  // Reset changes
  const resetChanges = () => {
    setLocalCategories([...categories]);
    setHasChanges(false);
  };

  // Check for unsaved changes before navigating away
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasChanges) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasChanges]);

  if (isLoading) {
    return (
      <div className='space-y-4'>
        <div className='flex justify-between items-center'>
          <h3 className='text-lg font-medium'>Category Order</h3>
        </div>
        <div className='space-y-2'>
          {[1, 2, 3, 4, 5].map((i) => (
            <Card key={i} className='p-3'>
              <Skeleton className='h-6 w-full' />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <h3 className='text-lg font-medium'>Category Order</h3>
        <div className='space-x-2'>
          <Button
            variant='outline'
            onClick={resetChanges}
            disabled={!hasChanges || isUpdating}
            className='flex items-center'
          >
            <Undo2 className='h-4 w-4 mr-2' />
            Reset
          </Button>
          <Button
            onClick={saveChanges}
            disabled={!hasChanges || isUpdating}
            className='flex items-center'
          >
            <Save className='h-4 w-4 mr-2' />
            {isUpdating ? 'Saving...' : 'Save Order'}
          </Button>
        </div>
      </div>

      <p className='text-sm text-muted-foreground'>
        Drag and drop categories to change their display order. Categories will be
        displayed in this order throughout the application.
      </p>

      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId='categories'>
          {(provided) => (
            <ul
              {...provided.droppableProps}
              ref={provided.innerRef}
              className='space-y-2'
            >
              {localCategories.map((category, index) => (
                <Draggable key={category.id} draggableId={category.id} index={index}>
                  {(provided) => (
                    <li
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className='p-3 bg-card border rounded-md flex items-center justify-between'
                    >
                      <div className='flex items-center'>
                        <div
                          {...provided.dragHandleProps}
                          className='mr-3 text-muted-foreground cursor-grab'
                        >
                          <GripVertical className='h-5 w-5' />
                        </div>
                        <span>{category.name}</span>
                      </div>
                      <span className='text-muted-foreground text-sm'>
                        Order: {index + 1}
                      </span>
                    </li>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </ul>
          )}
        </Droppable>
      </DragDropContext>

      {/* Confirmation dialog for unsaved changes */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Unsaved Changes</AlertDialogTitle>
            <AlertDialogDescription>
              You have unsaved changes to the category order. Do you want to save them
              before leaving?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setShowConfirmDialog(false);
                // Continue with navigation or action
              }}
            >
              Discard
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                saveChanges();
                setShowConfirmDialog(false);
                // Continue with navigation or action
              }}
            >
              Save Changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
