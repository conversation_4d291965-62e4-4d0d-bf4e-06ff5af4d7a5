import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Loader2 } from "lucide-react";
import { getAllCities } from "@/services/shared/entities/cities";
import { WizardState } from "./WizardContainer";
import { toast } from "sonner";

interface CitySelectorProps {
  wizardState: WizardState;
  updateWizardState: (updates: Partial<WizardState>) => void;
  onNext: () => void;
}

export const CitySelector: React.FC<CitySelectorProps> = ({
  wizardState,
  updateWizardState,
  onNext,
}) => {
  // Fetch cities using existing service
  const { data: cities, isLoading, isError } = useQuery({
    queryKey: ['cities'],
    queryFn: getAllCities,
    meta: {
      onError: (error: Error) => {
        toast.error(`Failed to load cities: ${error.message}`);
      },
    },
  });

  const handleCitySelect = (cityId: string) => {
    updateWizardState({ cityId, venueId: '' }); // Reset venue when city changes
    // Auto-advance to next step after selection
    setTimeout(() => {
      onNext();
    }, 300);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Where is your event located?
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Choose the city where your event will take place
          </p>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-600 dark:text-gray-300">Loading cities...</span>
          </div>
        </div>
      </div>
    );
  }

  if (isError || !cities) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Where is your event located?
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Choose the city where your event will take place
          </p>
        </div>

        <div className="text-center py-12">
          <div className="text-red-600 dark:text-red-400 mb-4">
            <MapPin className="h-12 w-12 mx-auto mb-2" />
            <p className="text-lg font-medium">Unable to load cities</p>
            <p className="text-sm">Please try refreshing the page</p>
          </div>
        </div>
      </div>
    );
  }

  const selectedCity = cities.find(city => city.id === wizardState.cityId);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          Where is your event located?
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Choose the city where your event will take place
        </p>
      </div>

      {cities.length === 0 ? (
        <div className="text-center py-12">
          <MapPin className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-lg font-medium text-gray-600 dark:text-gray-300">
            No cities available
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Please contact your administrator to add cities
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {cities.map((city) => {
            const isSelected = wizardState.cityId === city.id;

            return (
              <Card
                key={city.id}
                className={`cursor-pointer transition-all duration-200 hover:scale-105 ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-200 dark:ring-blue-800'
                    : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/10'
                }`}
                onClick={() => handleCitySelect(city.id)}
              >
                <CardContent className="p-6 text-center">
                  <div className={`w-12 h-12 rounded-full mx-auto mb-4 flex items-center justify-center ${
                    isSelected
                      ? 'bg-blue-500 text-white'
                      : 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                  }`}>
                    <MapPin className="h-6 w-6" />
                  </div>
                  <h3 className={`font-semibold text-lg ${
                    isSelected
                      ? 'text-blue-700 dark:text-blue-300'
                      : 'text-gray-800 dark:text-white'
                  }`}>
                    {city.name}
                  </h3>
                  {isSelected && (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                        Selected
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {selectedCity && (
        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Selected: <span className="font-medium">{selectedCity.name}</span>
          </p>
        </div>
      )}
    </div>
  );
};
