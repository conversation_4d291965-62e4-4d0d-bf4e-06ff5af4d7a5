import React, { useState, useMemo, useCallback } from "react";
import { Search, X, SlidersHorizontal } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PackageWithOptions } from "@/types/calculation";

interface CompactPackageSearchProps {
  packages: PackageWithOptions[];
  categories: Array<{ id: string; name: string }>;
  onFilteredPackagesChange: (packages: PackageWithOptions[]) => void;
  onViewModeChange: (mode: "category" | "list") => void;
}

const CompactPackageSearch: React.FC<CompactPackageSearchProps> = ({
  packages,
  categories,
  onFilteredPackagesChange,
  onViewModeChange,
}) => {
  const [search, setSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [priceRange, setPriceRange] = useState<
    "all" | "low" | "medium" | "high"
  >("all");

  // Filtered packages
  const filteredPackages = useMemo(() => {
    let result = [...packages];

    // Search filter
    if (search) {
      const searchLower = search.toLowerCase();
      result = result.filter(
        (pkg) =>
          pkg.name.toLowerCase().includes(searchLower) ||
          pkg.description?.toLowerCase().includes(searchLower)
      );
    }

    // Category filter
    if (selectedCategory !== "all") {
      result = result.filter((pkg) => pkg.category_id === selectedCategory);
    }

    // Price range filter
    if (priceRange !== "all") {
      switch (priceRange) {
        case "low":
          result = result.filter((pkg) => Number(pkg.price) < 500000);
          break;
        case "medium":
          result = result.filter((pkg) => {
            const price = Number(pkg.price);
            return price >= 500000 && price < 2000000;
          });
          break;
        case "high":
          result = result.filter((pkg) => Number(pkg.price) >= 2000000);
          break;
      }
    }

    return result;
  }, [packages, search, selectedCategory, priceRange]);

  // Update parent when filtered packages change
  React.useEffect(() => {
    onFilteredPackagesChange(filteredPackages);
    // Switch to list view when filtering is active
    const isFiltering =
      search || selectedCategory !== "all" || priceRange !== "all";
    onViewModeChange(isFiltering ? "list" : "category");
  }, [
    search,
    selectedCategory,
    priceRange,
    filteredPackages,
    onFilteredPackagesChange,
    onViewModeChange,
  ]);

  const clearFilters = useCallback(() => {
    setSearch("");
    setSelectedCategory("all");
    setPriceRange("all");
  }, []);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (search) count++;
    if (selectedCategory !== "all") count++;
    if (priceRange !== "all") count++;
    return count;
  }, [search, selectedCategory, priceRange]);

  return (
    <div className="space-y-3">
      {/* Main Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-4 w-4" />
        <Input
          placeholder="Search packages by name or description..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pl-10 pr-4"
        />
        {search && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={() => setSearch("")}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Quick Filters Row */}
      <div className="flex items-center gap-3 flex-wrap">
        <div className="flex items-center gap-2">
          <SlidersHorizontal className="h-4 w-4 text-gray-500 dark:text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            Quick filters:
          </span>
        </div>

        {/* Category Filter */}
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-40 h-8">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Price Range Filter */}
        <Select
          value={priceRange}
          onValueChange={(value) =>
            setPriceRange(value as "all" | "low" | "medium" | "high")
          }
        >
          <SelectTrigger className="w-32 h-8">
            <SelectValue placeholder="All Prices" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Prices</SelectItem>
            <SelectItem value="low">Under Rp 500K</SelectItem>
            <SelectItem value="medium">Rp 500K - 2M</SelectItem>
            <SelectItem value="high">Over Rp 2M</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters */}
        {activeFilterCount > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            className="h-8"
          >
            Clear ({activeFilterCount})
          </Button>
        )}

        {/* Results Count */}
        <div className="text-sm text-gray-500 dark:text-gray-400 ml-auto">
          {filteredPackages.length} of {packages.length} packages
        </div>
      </div>

      {/* Active Filters */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {search && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: "{search}"
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setSearch("")}
              />
            </Badge>
          )}
          {selectedCategory !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {categories.find((c) => c.id === selectedCategory)?.name}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setSelectedCategory("all")}
              />
            </Badge>
          )}
          {priceRange !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {priceRange === "low"
                ? "Under Rp 500K"
                : priceRange === "medium"
                ? "Rp 500K - 2M"
                : "Over Rp 2M"}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setPriceRange("all")}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

export default CompactPackageSearch;
