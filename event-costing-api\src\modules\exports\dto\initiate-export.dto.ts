import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

// Define supported export formats
export enum ExportFormat {
  CSV = 'csv',
  // PDF = 'pdf', // Add later if needed
}

export class InitiateExportDto {
  @ApiProperty({
    enum: ExportFormat,
    description: 'The desired format for the export file.',
    example: ExportFormat.CSV,
    default: ExportFormat.CSV,
  })
  @IsEnum(ExportFormat)
  @IsNotEmpty()
  format: ExportFormat;

  // Add other options later if needed, e.g.:
  // @ApiProperty({ required: false, description: 'Email address to send the export to.' })
  // @IsOptional()
  // @IsEmail()
  // recipientEmail?: string;
}
