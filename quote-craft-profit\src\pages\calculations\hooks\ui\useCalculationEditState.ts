/**
 * Hook for managing calculation edit state
 */
import { useState, useEffect, useMemo, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { toast } from "sonner";
import { DateRange } from "react-day-picker";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { updateCalculation } from "../../../../services/calculations";
import { CalculationDetails } from "@/types/calculations";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

/**
 * Hook for managing calculation edit state
 *
 * @param calculationId - The ID of the calculation
 * @param calculation - The calculation data
 * @returns State and functions for editing calculation details
 */
export function useCalculationEditState(
  calculationId: string,
  calculation: CalculationDetails | undefined
) {
  const queryClient = useQueryClient();
  const { convertDatabaseToRange, convertRangeForSubmission } =
    useTimezoneAwareDates();

  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedName, setEditedName] = useState("");
  const [editedEventType, setEditedEventType] = useState("");
  const [editedNotes, setEditedNotes] = useState("");
  const [editedAttendees, setEditedAttendees] = useState<number>(0);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [isSaving, setIsSaving] = useState(false);

  // Reset form when calculation changes
  useEffect(() => {
    if (calculation) {
      setEditedName(calculation.name || "");
      setEditedEventType(calculation.event_type_id || ""); // Updated to use event_type_id
      setEditedNotes(calculation.notes || "");
      setEditedAttendees(calculation.attendees || 0);

      if (calculation.event_start_date && calculation.event_end_date) {
        const dateRange = convertDatabaseToRange(
          calculation.event_start_date,
          calculation.event_end_date
        );
        if (dateRange.from && dateRange.to) {
          setDateRange({ from: dateRange.from, to: dateRange.to });
        }
      }
    }
  }, [calculation, convertDatabaseToRange]);

  // Update calculation mutation
  const updateMutation = useMutation({
    mutationFn: (data: { id: string; updateData: any }) =>
      updateCalculation(data.id, data.updateData),
    onSuccess: () => {
      toast.success("Calculation details updated successfully");
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculation(calculationId),
      });
      setIsEditMode(false);
      setIsSaving(false);
    },
    onError: (error: any) => {
      console.error("Update mutation error:", error);

      // Extract more detailed error message if available
      let errorMessage = "Failed to update calculation";

      if (error.response?.data?.message) {
        // API error with message
        errorMessage += `: ${error.response.data.message}`;
      } else if (error.message) {
        // Standard error object
        errorMessage += `: ${error.message}`;
      } else if (typeof error === "string") {
        // String error
        errorMessage += `: ${error}`;
      }

      toast.error(errorMessage);
      setIsSaving(false);
    },
  });

  // Toggle edit mode
  const handleToggleEditMode = useCallback(() => {
    if (isEditMode) {
      // Cancel edit mode
      setIsEditMode(false);
    } else {
      // Enter edit mode and initialize form values
      setEditedName(calculation?.name || "");
      setEditedEventType(calculation?.event_type_id || ""); // Updated to use event_type_id
      setEditedNotes(calculation?.notes || "");
      setEditedAttendees(calculation?.attendees || 0);

      // Initialize date range with timezone awareness
      if (calculation?.event_start_date && calculation?.event_end_date) {
        const dateRange = convertDatabaseToRange(
          calculation.event_start_date,
          calculation.event_end_date
        );
        if (dateRange.from && dateRange.to) {
          setDateRange({ from: dateRange.from, to: dateRange.to });
        }
      }

      setIsEditMode(true);
    }
  }, [isEditMode, calculation, convertDatabaseToRange]);

  // Save changes
  const handleSaveChanges = useCallback(() => {
    if (!calculationId || !calculation) return;

    setIsSaving(true);

    // Create update data with only the fields we want to change
    const submissionDates = convertRangeForSubmission({
      from: dateRange?.from,
      to: dateRange?.to,
    });
    const startDate = submissionDates.startDate;
    const endDate = submissionDates.endDate;

    const updateData = {
      name: editedName,
      event_type_id: editedEventType, // Updated to use event_type_id
      notes: editedNotes || "", // API might require non-null value
      attendees: editedAttendees,
      event_start_date: startDate || calculation.event_start_date,
      event_end_date: endDate || calculation.event_end_date,
      venue_ids: calculation.venues?.map((venue: any) => venue.id) || [],
    };

    // Check if there are changes using timezone-aware comparison
    const hasChanges =
      editedName !== calculation.name ||
      editedEventType !== calculation.event_type_id || // Updated to use event_type_id
      editedNotes !== calculation.notes ||
      editedAttendees !== calculation.attendees ||
      startDate !== calculation.event_start_date ||
      endDate !== calculation.event_end_date;

    // Check if attendees changed - we'll need to reload packages if so
    const attendeesChanged = editedAttendees !== calculation.attendees;
    const datesChanged =
      startDate !== calculation.event_start_date ||
      endDate !== calculation.event_end_date;

    if (hasChanges) {
      updateMutation.mutate(
        {
          id: calculationId,
          updateData: updateData,
        },
        {
          onSuccess: () => {
            // If attendees or dates changed, we need to reload packages
            if (attendeesChanged || datesChanged) {
              // Invalidate packages query to force reload
              queryClient.invalidateQueries({
                queryKey: QUERY_KEYS.packagesByCategory(calculationId),
              });
            }
          },
        }
      );
    } else {
      // No changes to save
      setIsEditMode(false);
      setIsSaving(false);
    }
  }, [
    calculationId,
    calculation,
    convertRangeForSubmission,
    dateRange,
    editedName,
    editedEventType,
    editedNotes,
    editedAttendees,
    updateMutation,
    queryClient,
  ]);

  return useMemo(
    () => ({
      isEditMode,
      isSaving,
      editedName,
      editedEventType,
      editedNotes,
      editedAttendees,
      dateRange,
      setEditedName,
      setEditedEventType,
      setEditedNotes,
      setEditedAttendees,
      setDateRange,
      handleToggleEditMode,
      handleSaveChanges,
    }),
    [
      isEditMode,
      isSaving,
      editedName,
      editedEventType,
      editedNotes,
      editedAttendees,
      dateRange,
      setEditedName,
      setEditedEventType,
      setEditedNotes,
      setEditedAttendees,
      setDateRange,
      handleToggleEditMode,
      handleSaveChanges,
    ]
  );
}
