import { z } from 'zod';
import { commonValidations, formFieldPatterns, dateRangeSchema } from '@/schemas/common';

/**
 * Event-related validation schemas
 * Centralized schemas for all event forms and components
 */

// Event form schema with date range picker (updated for consistency)
export const eventFormSchema = z.object({
  name: formFieldPatterns.name('Event name'),
  clientId: commonValidations.requiredString(1, 'Client'),
  dateRange: dateRangeSchema,
  location: commonValidations.optionalString(),
  status: commonValidations.requiredString(1, 'Status'),
  // Custom validation for primaryContactId to handle 'none' values
  primaryContactId: z.string().optional().or(z.literal('none')).transform((val) => val === 'none' ? undefined : val),
  notes: formFieldPatterns.notes(),
});

// Type exports for TypeScript inference
export type EventFormValues = z.infer<typeof eventFormSchema>;
