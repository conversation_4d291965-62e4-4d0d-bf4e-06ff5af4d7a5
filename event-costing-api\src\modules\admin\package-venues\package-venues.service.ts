import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PackageVenueDto } from './dto/package-venue.dto';

// Define the expected structure for the query result
interface PackageVenueQueryResult {
  venue: {
    id: string;
    name: string;
    address?: string;
    city_id?: string;
  } | null;
}

@Injectable()
export class PackageVenuesService {
  private readonly logger = new Logger(PackageVenuesService.name);
  private readonly PACKAGE_VENUES_TABLE = 'package_venues';
  private readonly VENUES_TABLE = 'venues';
  private readonly PACKAGES_TABLE = 'packages';

  constructor(private readonly supabaseService: SupabaseService) {}

  async addVenueToPackage(
    packageId: string,
    venueId: string,
  ): Promise<{ id: string }> {
    this.logger.log(`Attempting to add venue ${venueId} to package ${packageId}`);
    const supabase = this.supabaseService.getClient();

    // Create the association
    const { data, error } = await supabase
      .from(this.PACKAGE_VENUES_TABLE)
      .insert({ package_id: packageId, venue_id: venueId })
      .select('id'); // Select only the ID

    const typedData = data as { id: string }[] | null;

    if (error) {
      this.logger.error(
        `Error adding venue ${venueId} to package ${packageId}: ${error.message}`,
      );

      if (error.code === '23505') {
        // Unique constraint violation
        throw new ConflictException(
          'This venue is already associated with the package.',
        );
      }

      if (error.code === '23503') {
        // Foreign key constraint violation
        throw new NotFoundException('Package or Venue not found.');
      }

      throw new InternalServerErrorException('Failed to add venue to package.');
    }

    return { id: typedData![0].id };
  }

  async listVenuesForPackage(packageId: string): Promise<PackageVenueDto[]> {
    this.logger.log(`Fetching venues for package ${packageId}`);
    const supabase = this.supabaseService.getClient();

    // Query the junction table and join with the venues table (only active associations)
    const { data, error } = await supabase
      .from(this.PACKAGE_VENUES_TABLE)
      .select(
        `
        venue: ${this.VENUES_TABLE} (
          id,
          name,
          address,
          city_id
        )
      `,
      )
      .eq('package_id', packageId)
      .eq('is_deleted', false)
      .returns<PackageVenueQueryResult[]>();

    if (error) {
      this.logger.error(
        `Error fetching venues for package ${packageId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Error fetching venues for package.',
      );
    }

    // Transform the data
    const venues: PackageVenueDto[] =
      data
        ?.map(item => item.venue)
        .filter((venue): venue is { id: string; name: string; address?: string; city_id?: string } => venue !== null)
        .map(venue => ({
          id: venue.id,
          name: venue.name,
          address: venue.address,
          city_id: venue.city_id
        })) || [];

    if (venues.length === 0) {
      // Check if the package itself exists
      const { count: packageCount } = await supabase
        .from(this.PACKAGES_TABLE)
        .select('*', { count: 'exact', head: true })
        .eq('id', packageId);

      if (packageCount === 0) {
        throw new NotFoundException(`Package with ID ${packageId} not found.`);
      }
    }

    return venues;
  }

  async removeVenueFromPackage(packageId: string, venueId: string): Promise<void> {
    this.logger.log(`Removing venue ${venueId} from package ${packageId}`);
    const supabase = this.supabaseService.getClient();

    const { error } = await supabase
      .from(this.PACKAGE_VENUES_TABLE)
      .delete()
      .eq('package_id', packageId)
      .eq('venue_id', venueId);

    if (error) {
      this.logger.error(
        `Error removing venue ${venueId} from package ${packageId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Failed to remove venue from package.',
      );
    }

    this.logger.log(`Successfully removed venue ${venueId} from package ${packageId}`);
  }
}
