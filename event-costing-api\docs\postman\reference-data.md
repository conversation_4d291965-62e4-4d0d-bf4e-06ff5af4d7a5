## Reference Data (Public / User-Specific)

These endpoints are primarily used to fetch data for populating UI elements like dropdowns.

### Cities (`/cities`)

#### 1. Get Cities

- **Method:** `GET`
- **URL:** `/cities`
- **Headers:** None required (Public)
- **Description:** Retrieves a list of all available cities, ordered by name.
- **Success Response (200 OK):**
  ```json
  [
    {
      "id": "city-uuid-1",
      "name": "Amsterdam"
    },
    {
      "id": "city-uuid-2",
      "name": "New York"
    }
    // ... other cities
  ]
  ```
- **Error Response (500 Internal Server Error):** If database query fails.

---

### Currencies (`/currencies`)

#### 1. Get Currencies

- **Method:** `GET`
- **URL:** `/currencies`
- **Headers:** None required (Public)
- **Description:** Retrieves a list of all available currencies, ordered by code.
- **Success Response (200 OK):**
  ```json
  [
    {
      "id": "currency-uuid-eur",
      "code": "EUR",
      "name": "Euro"
    },
    {
      "id": "currency-uuid-usd",
      "code": "USD",
      "name": "United States Dollar"
    }
    // ... other currencies
  ]
  ```
- **Error Response (500 Internal Server Error):** If database query fails.

---

### Categories (`/categories`)

#### 1. Get Categories

- **Method:** `GET`
- **URL:** `/categories`
- **Headers:** None required (Public)
- **Description:** Retrieves a list of all available package/template categories, ordered by name.
- **Success Response (200 OK):**
  ```json
  [
    {
      "id": "category-uuid-catering",
      "name": "Catering",
      "description": "Food and beverage services"
    },
    {
      "id": "category-uuid-venue",
      "name": "Venue",
      "description": null
    }
    // ... other categories
  ]
  ```
- **Error Response (500 Internal Server Error):** If database query fails.

---

### Clients (`/clients`)

#### 1. Get My Clients

- **Method:** `GET`
- **URL:** `/clients`
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Description:** Retrieves a list of clients created by the authenticated user, ordered by name.
- **Success Response (200 OK):**
  ```json
  [
    {
      "id": "client-uuid-1",
      "client_name": "Acme Corp",
      "contact_person": "John Doe",
      "email": "<EMAIL>",
      "phone_number": "************",
      "created_at": "timestamp"
    }
    // ... other clients
  ]
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (500 Internal Server Error):** If database query fails.

---

### Events (`/events`)

#### 1. Get My Events

- **Method:** `GET`
- **URL:** `/events`
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Description:** Retrieves a list of events created by the authenticated user, ordered by start date.
- **Success Response (200 OK):**
  ```json
  [
    {
      "id": "event-uuid-1",
      "event_name": "Summer Gala 2025",
      "event_start_datetime": "2025-07-15T18:00:00Z",
      "event_end_datetime": "2025-07-15T23:00:00Z",
      "location": "Grand Ballroom",
      "notes": "Annual fundraiser event.",
      "created_at": "timestamp"
    }
    // ... other events
  ]
  ```
- **Error Response (401 Unauthorized):** If token is missing or invalid.
- **Error Response (500 Internal Server Error):** If database query fails.
