import React, { useMemo } from "react";
import { Link, useLocation } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/contexts/useAuth";
import { Button } from "@/components/ui/button";
import { ThemeToggleSimple } from "@/components/ui/theme-toggle";
import {
  LayoutDashboard,
  Users,
  Package,
  FileText,
  Settings,
  ChevronDown,
  LogOut,
  User,
  Building2,
  Map,
  Layers,
  ShieldCheck,
  ArrowLeft,
  Loader2,
  Calendar,
} from "lucide-react";

const AdminNavbar: React.FC = () => {
  const location = useLocation();
  const { user, signOut } = useAuth();

  // Improved active path detection - memoize to prevent recreation on each render
  const isActive = useMemo(() => {
    return (path: string) => {
      return (
        location.pathname === path || location.pathname.startsWith(`${path}/`)
      );
    };
  }, [location.pathname]);

  // Get user information for display
  const userInitial = user?.email ? user.email[0].toUpperCase() : "A";
  const userFullName = user?.user_metadata?.full_name || user?.email || "Admin";

  return (
    <header className="bg-gray-900 dark:bg-gray-950 border-b border-gray-800 dark:border-gray-700 sticky top-0 z-10 shadow-md">
      <div className="container mx-auto px-4">
        <div className="h-16 flex items-center justify-between">
          <div className="flex items-center">
            {/* Mobile Navigation - Only visible on mobile */}
            <div className="flex md:hidden"></div>

            {/* Logo and Brand */}
            <Link to="/admin" className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-eventcost-primary text-white flex items-center justify-center rounded">
                <ShieldCheck className="h-5 w-5" />
              </div>
              <span className="font-bold text-xl tracking-tight text-white">
                Admin Portal
              </span>
            </Link>

            {/* Main Navigation - Desktop */}
            <nav className="ml-10 hidden md:flex items-center space-x-1">
              {/* Dashboard */}
              <Link
                to="/admin"
                className={`px-3 py-2 rounded-md ${
                  isActive("/admin") && location.pathname === "/admin"
                    ? "bg-gray-800 text-eventcost-primary font-medium"
                    : "text-gray-300 hover:text-white hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <LayoutDashboard className="h-4 w-4 mr-1" />
                <span>Dashboard</span>
              </Link>

              {/* Users */}
              <Link
                to="/admin/users"
                className={`px-3 py-2 rounded-md ${
                  isActive("/admin/users")
                    ? "bg-gray-800 text-eventcost-primary font-medium"
                    : "text-gray-300 hover:text-white hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <Users className="h-4 w-4 mr-1" />
                <span>Users</span>
              </Link>

              {/* Catalogue Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant={
                      isActive("/admin/catalogue") ||
                      isActive("/admin/packages") ||
                      isActive("/admin/categories") ||
                      isActive("/admin/divisions") ||
                      isActive("/admin/cities") ||
                      isActive("/admin/venues") ||
                      isActive("/admin/event-types")
                        ? "secondary"
                        : "ghost"
                    }
                    className={`px-3 py-2 h-auto ${
                      isActive("/admin/catalogue") ||
                      isActive("/admin/packages") ||
                      isActive("/admin/categories") ||
                      isActive("/admin/divisions") ||
                      isActive("/admin/cities") ||
                      isActive("/admin/venues") ||
                      isActive("/admin/event-types")
                        ? "bg-gray-800 text-eventcost-primary"
                        : "text-gray-300 hover:text-white hover:bg-gray-800"
                    }`}
                  >
                    <Package className="h-4 w-4 mr-1" />
                    <span>Catalogue</span>
                    <ChevronDown className="h-4 w-4 ml-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="start"
                  className="w-56 bg-gray-900 border-gray-800"
                >
                  <DropdownMenuItem asChild>
                    <Link
                      to="/admin/catalogue"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <Package className="h-4 w-4 mr-2" />
                      <span>All Catalogue</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      to="/admin/packages"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <Package className="h-4 w-4 mr-2" />
                      <span>Packages</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      to="/admin/categories"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <Layers className="h-4 w-4 mr-2" />
                      <span>Categories</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      to="/admin/divisions"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <Layers className="h-4 w-4 mr-2" />
                      <span>Divisions</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-gray-700" />
                  <DropdownMenuLabel className="text-gray-400 text-xs">
                    Locations
                  </DropdownMenuLabel>
                  <DropdownMenuItem asChild>
                    <Link
                      to="/admin/cities"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <Map className="h-4 w-4 mr-2" />
                      <span>Cities</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      to="/admin/venues"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <Building2 className="h-4 w-4 mr-2" />
                      <span>Venues</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      to="/admin/event-types"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>Event Types</span>
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Templates */}
              <Link
                to="/admin/templates"
                className={`px-3 py-2 rounded-md ${
                  isActive("/admin/templates")
                    ? "bg-gray-800 text-eventcost-primary font-medium"
                    : "text-gray-300 hover:text-white hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <FileText className="h-4 w-4 mr-1" />
                <span>Templates</span>
              </Link>

              {/* Settings */}
              <Link
                to="/admin/settings"
                className={`px-3 py-2 rounded-md ${
                  isActive("/admin/settings")
                    ? "bg-gray-800 text-eventcost-primary font-medium"
                    : "text-gray-300 hover:text-white hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <Settings className="h-4 w-4 mr-1" />
                <span>Settings</span>
              </Link>
            </nav>
          </div>

          <div className="flex items-center">
            {/* Theme Toggle */}
            <div className="mr-2">
              <ThemeToggleSimple />
            </div>

            {/* Back to App Button */}
            <Link
              to="/"
              className="mr-4 px-3 py-2 text-sm bg-gray-700 hover:bg-gray-600 text-white rounded flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">Back to App</span>
            </Link>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center space-x-2 focus:outline-none">
                <Avatar className="h-8 w-8 bg-gray-700">
                  <AvatarImage src={null} />
                  <AvatarFallback>{userInitial}</AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium text-white hidden md:block">
                  {userFullName}
                </span>
                <ChevronDown className="h-4 w-4 text-gray-400" />
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-56 bg-gray-900 border-gray-800"
              >
                <DropdownMenuLabel className="text-gray-300">
                  Admin Account
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-gray-700" />
                <DropdownMenuGroup>
                  <DropdownMenuItem asChild>
                    <Link
                      to="/profile"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <User className="h-4 w-4 mr-2" />
                      <span>My Profile</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      to="/admin/settings"
                      className="flex items-center text-gray-300 hover:text-white"
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      <span>Admin Settings</span>
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator className="bg-gray-700" />
                <DropdownMenuItem
                  onClick={() => {
                    // Store current URL before signing out
                    signOut();
                  }}
                  className="flex items-center text-red-400 hover:text-red-300 hover:bg-gray-800"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
};

export default AdminNavbar;
