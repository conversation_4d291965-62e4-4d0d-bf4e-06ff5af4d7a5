import { Module } from '@nestjs/common';
import { PackagePricesService } from './package-prices.service';
import { PackagePricesController } from './package-prices.controller';
import { AuthModule } from '../../auth/auth.module';
import { AdminModule } from '../../auth/admin.module';

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [PackagePricesController],
  providers: [PackagePricesService],
})
export class PackagePricesModule {}
