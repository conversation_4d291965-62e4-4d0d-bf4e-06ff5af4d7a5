import { ApiProperty } from '@nestjs/swagger';

// DTO representing a city associated with a package
export class PackageCityDto {
  @ApiProperty({ description: 'City UUID', format: 'uuid' })
  id: string;

  @ApiProperty({ description: 'City Name', example: 'Jakarta' })
  name: string;

  // We might not need package_id here as it's implicit in the context
  // Add other relevant city fields if needed (e.g., country)
}
