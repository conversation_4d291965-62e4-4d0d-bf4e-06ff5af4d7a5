import { useCallback, useMemo } from "react";
import { useUserPreferences } from "./useUserPreferences";
import {
  formatDateForDisplay,
  formatDateForSubmission,
  formatDateTimeForSubmission,
  convertDateRangeForSubmission,
  convertDatabaseDatesToRange,
  transformSeparateDatesToDateRange,
  transformSeparateDatetimesToDateRange,
  validateDateRange,
  getDateRangeErrorMessage,
  isDateRangeComplete,
  formatDateRangeForDisplay,
} from "@/lib/timezone-utils";

/**
 * Custom hook for timezone-aware date operations
 * Provides centralized access to timezone-aware date formatting and conversion functions
 *
 * @returns Object with timezone and utility functions
 */
export const useTimezoneAwareDates = () => {
  const { timezone } = useUserPreferences();

  // CRITICAL FIX: Memoize all functions to prevent infinite re-renders
  const formatForDisplay = useCallback(
    (dateString: string, pattern?: string) =>
      formatDateForDisplay(dateString, timezone, pattern),
    [timezone]
  );

  const formatForSubmission = useCallback(
    (date: Date) => formatDateForSubmission(date, timezone),
    [timezone]
  );

  const formatDateTimeForSubmissionMemo = useCallback(
    (date: Date) => formatDateTimeForSubmission(date, timezone),
    [timezone]
  );

  const convertRangeForSubmission = useCallback(
    (dateRange: { from?: Date; to?: Date }) =>
      convertDateRangeForSubmission(dateRange, timezone),
    [timezone]
  );

  const convertDatabaseToRange = useCallback(
    (startDate: string, endDate: string) =>
      convertDatabaseDatesToRange(startDate, endDate, timezone),
    [timezone]
  );

  const transformSeparateDatesToRange = useCallback(
    (startDate?: string | null, endDate?: string | null) =>
      transformSeparateDatesToDateRange(startDate, endDate, timezone),
    [timezone]
  );

  const transformSeparateDatetimesToRange = useCallback(
    (startDatetime?: string | null, endDatetime?: string | null) =>
      transformSeparateDatetimesToDateRange(
        startDatetime,
        endDatetime,
        timezone
      ),
    [timezone]
  );

  const validateDateRangeMemo = useCallback(
    (dateRange?: { from?: Date; to?: Date }) => validateDateRange(dateRange),
    [] // No dependencies - pure function
  );

  const getDateRangeErrorMessageMemo = useCallback(
    (dateRange?: { from?: Date; to?: Date }) =>
      getDateRangeErrorMessage(dateRange),
    [] // No dependencies - pure function
  );

  const isDateRangeCompleteMemo = useCallback(
    (dateRange?: { from?: Date; to?: Date }) => isDateRangeComplete(dateRange),
    [] // No dependencies - pure function
  );

  const formatDateRangeForDisplayMemo = useCallback(
    (dateRange?: { from?: Date; to?: Date }, formatPattern?: string) =>
      formatDateRangeForDisplay(dateRange, timezone, formatPattern),
    [timezone]
  );

  // CRITICAL FIX: Return memoized object to prevent infinite re-renders
  return useMemo(
    () => ({
      timezone,
      formatForDisplay,
      formatForSubmission,
      formatDateTimeForSubmission: formatDateTimeForSubmissionMemo,
      convertRangeForSubmission,
      convertDatabaseToRange,
      transformSeparateDatesToRange,
      transformSeparateDatetimesToRange,
      validateDateRange: validateDateRangeMemo,
      getDateRangeErrorMessage: getDateRangeErrorMessageMemo,
      isDateRangeComplete: isDateRangeCompleteMemo,
      formatDateRangeForDisplay: formatDateRangeForDisplayMemo,
    }),
    [
      timezone,
      formatForDisplay,
      formatForSubmission,
      formatDateTimeForSubmissionMemo,
      convertRangeForSubmission,
      convertDatabaseToRange,
      transformSeparateDatesToRange,
      transformSeparateDatetimesToRange,
      validateDateRangeMemo,
      getDateRangeErrorMessageMemo,
      isDateRangeCompleteMemo,
      formatDateRangeForDisplayMemo,
    ]
  );
};

export default useTimezoneAwareDates;
