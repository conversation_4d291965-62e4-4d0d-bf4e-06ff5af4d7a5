/**
 * Template calculation services
 *
 * Unified exports for all template calculation functionality
 */

// Export specific functions to avoid conflicts
export {
  calculateTemplateTotal,
  calculateTemplateFromObject,
  getTemplateCalculationSummary,
  getTemplateCalculationSummaryFromResult,
  formatCurrency,
  validateTemplatePricing,
  getTemplatePricingIssues,
  createCalculationFromTemplate,
  getCalculationSuggestions,
  validateTemplateCustomization,
  getTemplateCalculationLimits,
} from './templateCalculationService';

// Re-export with legacy names for backward compatibility
export {
  getTemplateCalculationSummaryFromResult as getCalculationSummary,
} from './templateCalculationService';

// Export specific functions with different names to avoid conflicts
export {
  formatCurrency as formatCurrencyAPI,
} from './templateCalculationService';

// Export types separately to fix isolatedModules issue
export type {
  TemplateCalculationResult,
  TemplateCalculationSummary,
  CalculationBreakdown,
  CreateCalculationFromTemplateRequest,
  CreateCalculationFromTemplateResponse,
} from './templateCalculationService';
