# Backend Plan v2: Refactoring and Enhancements

## Phase 1: Core Setup & Package Management (Mostly Done)

- [x] Basic NestJS Project Structure
- [x] Supabase Integration (`SupabaseModule`, `SupabaseService`)
- [x] Config Module (`@nestjs/config`)
- [x] Authentication (`AuthModule`, JWT Guard, User Decorator)
- [x] Redis/BullMQ Setup for Background Jobs - Added April 21, 2025
- [x] Basic CRUD for `Packages`
- [x] Basic CRUD for `PackageOptions`
- [x] Basic CRUD for `PackagePrices`
- [x] Basic CRUD for `Categories`
- [x] Basic CRUD for `Divisions`
- [x] Basic CRUD for `Cities`
- [x] Basic CRUD for `Currencies`
- [x] Add `quantity_basis` to Packages
- [x] Add Cost Tracking (`unit_base_cost`, `cost_adjustment`)
- [x] Swagger API Documentation Setup
- [ ] Implement PUT/POST for `PackageCities` (Relate packages to cities)

## Phase 2: Calculation Engine & Templates (In Progress)

- [x] `calculation_history` table schema
- [x] `calculation_line_items` table schema (Normalized)
- [x] `calculation_line_item_options` table schema (Normalized)
- [x] `calculation_custom_items` table schema
- [x] `Templates` table schema (Blueprint storage)
- [x] `TemplateCategories` table schema
- [x] Basic CRUD for `Calculations` (Initial `CalculationsService`) - Review Complete April 21, 2025
- [x] Basic CRUD for `Calculation Custom Items` (`CalculationItemsService`)
- [x] Add Package Line Item Logic (`CalculationItemsService`, including snapshotting, quantity/duration determination)
- [x] Add/Delete Line Item Options (`CalculationItemsService`)
- [x] Populate Calculation Items from Template Blueprint (`CalculationItemsService` using `populate_items_from_template_blueprint` RPC) - Fixed user ID issue April 22, 2025
- [x] **Refactor `Calculations` Module (Done - April 19, 2025)**
  - [x] Moved internal interfaces to `src/modules/calculations/interfaces/`.
  - [x] Created `CalculationLogicService` for totals/tax/discount calculation.
  - [x] Created `CalculationTemplateService` for `createFromTemplate` logic.
  - [x] Refactored `CalculationsService` to handle core CRUD and ownership, delegate other tasks.
  - [x] Updated `CalculationsModule` with new providers/exports.
  - [x] Updated `CalculationsController` to use new services, removed item routes.
- [x] **Create `CalculationItemsController` (Done - April 19, 2025)**
  - [x] Created controller in `src/modules/calculation-items`.
  - [x] Moved item add/delete routes (`/calculations/:calcId/items/*`) here.
  - [x] Implemented ownership checks via `CalculationsService`.
  - [x] Updated `CalculationItemsModule`.
  - [x] Moved relevant DTOs (`AddPackageLineItemDto`, `AddCustomLineItemDto`) to `calculation-items/dto`.
- [x] CRUD for Templates (`TemplatesService`, `TemplatesController`, `AdminTemplatesController`) - Implemented April 19, 2025
  - [x] Create Template (Admin only via `POST /admin/templates/from-calculation`)
  - [x] List/Search Templates (Publicly Accessible via `GET /templates`)
  - [x] Get Template by ID (Publicly Accessible via `GET /templates/:id`)
  - [x] Update Template (Admin only via `PUT /admin/templates/:id`)
  - [x] Delete Template (Admin only via `DELETE /admin/templates/:id`)
  - [x] List/Search Templates (Admin view via `GET /admin/templates`)
  - [x] Get Template by ID (Admin view via `GET /admin/templates/:id`)
- [ ] Implement `recalculate_calculation_totals` Supabase Function (RPC)
  - [x] Basic structure created.
  - [x] Ensure correct calculation of subtotal, tax amount (based on JSON), discount amount (based on JSON), total, total_cost, estimated_profit.
  - [x] Ensure it updates the `calculation_history` row.
  - Note: Function finalized and considered complete.
- [ ] Implement `add_package_item_and_recalculate` Supabase Function (RPC) - _Needs Review/Refinement_
  - [x] Basic structure created.
  - [x] Verify ownership.
  - [x] Fetch package & price/cost details.
  - [x] Fetch option details & calculate adjustments/summary.
  - [x] Determine quantity/duration based on `quantity_basis`.
  - [x] Calculate line totals/costs.
  - [x] Insert line item & options with snapshots.
  - [x] Call `recalculate_calculation_totals`.
  - Note: Function reviewed and considered complete.
- [x] Implement Line Item Deletion Logic via RPC (`delete_line_item_and_recalculate`) - Implemented April 19, 2025
- [x] Implement Calculation Status Transitions via dedicated endpoint (`PATCH /calculations/:id/status`) with validation - Implemented April 19, 2025
- [x] Implement Tax/Discount Management via `UpdateCalculationDto` and `PUT /calculations/:id` endpoint. Relies on `recalculate_calculation_totals` RPC for processing. - Implemented April 19, 2025

## Phase 3: Clients, Events & Exporting (Next)

- [x] `Clients` table schema
- [x] `Events` table schema
- [x] `ExportHistory` table schema
- [x] CRUD for `Clients` (`ClientsService`, `ClientsController`) - Implemented April 19, 2025
  - [x] POST /clients (Create Client)
  - [x] GET /clients (List Clients w/ Search/Filtering)
  - [x] GET /clients/:id (Get Client Details)
  - [x] PATCH /clients/:id (Update Client) - Switched PUT to PATCH
  - [x] DELETE /clients/:id (Delete Client - Hard Delete)
- [x] CRUD for `Events` (`EventsService`, `EventsController`) - Implemented April 19, 2025
  - [x] POST /events (Create Event)
  - [x] GET /events (List Events w/ Search/Filtering)
  - [x] GET /events/:id (Get Event Details)
  - [x] PATCH /events/:id (Update Event) - Switched PUT to PATCH
  - [x] DELETE /events/:id (Delete Event - Soft Delete)
  - [x] Link Events to Calculations/Clients (Handled via `client_id` FK and potential logic in Calculation/Event services)
  - [x] Event Status management (Handled via `status` field in CRUD)
- [~] Calculation Export Functionality (`ExportsModule`, `ExportsService`, `ExportsController`) - File Gen Added
  - [~] Basic structure created (Module, Service, Controller, DTOs) - April 19, 2025
  - [x] `POST /exports` endpoint initiates export, creates `ExportHistory` record.
  - [x] Implement file generation logic (CSV, PDF, ) in service methods.
  - [x] Implement background job processing for generation/upload. (Processor implemented, Service modified to queue job)
  - [x] Implement storage upload (optional). (Implemented in Processor)
  - [ ] Implement email notification (optional).
  - [x] Add status tracking/retrieval endpoint (e.g., `GET /exports/:id/status`). (Implemented in Controller/Service)
  - [x] Schema: Add `status`, `storage_path`, `file_name`, `error_message`, `completed_at` etc. to `export_history` table. (Migration 0001 created)

## Phase 4: User Profiles & Permissions (Future)

- [x] `Profiles` table schema
- [x] `Roles` table schema
- [x] Trigger for profile creation
- [ ] User Profile Management API (`ProfilesService`, `ProfilesController`)
- [ ] Role-Based Access Control (RBAC) Enhancements (Guards, Decorators)
  - [ ] Differentiate Admin vs User capabilities.

## Phase 5: Testing & Deployment (Ongoing)

- [ ] Unit Tests (Services, Controllers)
- [ ] Integration Tests (Modules)
- [ ] E2E Tests (API Endpoints)
- [ ] Database Migration Strategy (Supabase CLI)
- [ ] Environment Configuration (`.env` variables for dev/prod)
- [ ] Deployment Setup (e.g., Docker, Cloud Provider)

## Notes & Considerations

- **Error Handling:** Standardize error responses using Exception Filters.
- **Validation:** Use DTOs and `class-validator` Pipes consistently.
- **Logging:** Implement robust logging across services.
- **Security:** Input validation, auth checks, dependency updates (`npm audit`).
- **Transactions:** Use Supabase RPC functions for atomic operations spanning multiple tables (like adding line items and recalculating).
- **Refactoring:** Continuously review and refactor code for clarity and maintainability.
- **Documentation:** Keep Swagger and `*.md` files updated.
- **CalculationItemsController:** Controller created and routes moved.
