import React, { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { formatCurrencyInput, parseIDRNumber } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface CurrencyInputProps {
  value?: number | string;
  onChange?: (value: number) => void;
  onValueChange?: (value: number) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  currencySymbol?: string;
  showSymbol?: boolean;
  id?: string;
  name?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
  'aria-invalid'?: boolean;
}

/**
 * Enhanced currency input component with automatic Indonesian thousand separators
 * Formats input as: 1.000.000 for one million rupiah
 * Maintains numeric value for API calls
 */
export const CurrencyInput = React.forwardRef<HTMLInputElement, CurrencyInputProps>(
  (
    {
      value = '',
      onChange,
      onValueChange,
      placeholder = '0',
      className,
      disabled = false,
      currencySymbol = 'Rp',
      showSymbol = true,
      id,
      name,
      ...props
    },
    ref
  ) => {
    // Convert initial value to display format
    const getInitialDisplayValue = useCallback(() => {
      if (value === '' || value === null || value === undefined) return '';
      const numValue = typeof value === 'string' ? parseIDRNumber(value) : value;
      return numValue > 0 ? formatCurrencyInput(numValue.toString()).display : '';
    }, [value]);

    const [displayValue, setDisplayValue] = useState(() => getInitialDisplayValue());

    // Update display value when external value changes
    useEffect(() => {
      const newDisplayValue = getInitialDisplayValue();
      if (newDisplayValue !== displayValue) {
        setDisplayValue(newDisplayValue);
      }
    }, [value, displayValue, getInitialDisplayValue]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;

      // Allow empty input
      if (inputValue === '') {
        setDisplayValue('');
        const numericValue = 0;
        onChange?.(numericValue);
        onValueChange?.(numericValue);
        return;
      }

      // Format the input value
      const { display, numeric } = formatCurrencyInput(inputValue);

      // Update display value
      setDisplayValue(display);

      // Call onChange callbacks with numeric value
      onChange?.(numeric);
      onValueChange?.(numeric);
    };

    const handleBlur = () => {
      // Clean up display on blur - remove leading zeros, etc.
      if (displayValue) {
        const { display } = formatCurrencyInput(displayValue);
        setDisplayValue(display);
      }
    };

    return (
      <div className="relative">
        {showSymbol && (
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 pointer-events-none">
            {currencySymbol}
          </span>
        )}
        <Input
          ref={ref}
          type="text"
          inputMode="numeric"
          value={displayValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={cn(showSymbol && 'pl-8', className)}
          disabled={disabled}
          id={id}
          name={name}
          {...props}
        />
      </div>
    );
  }
);

CurrencyInput.displayName = 'CurrencyInput';

export default CurrencyInput;
