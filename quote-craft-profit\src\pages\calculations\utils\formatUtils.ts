/**
 * Formatting utility functions for calculations
 */
import { format, parseISO } from "date-fns";
import { formatDateForDisplay, DEFAULT_TIMEZONE } from "@/lib/timezone-utils";

/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - Currency code (defaults to 'IDR')
 * @param compact - Whether to use compact notation (defaults to false)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number,
  currency: string = "IDR",
  compact: boolean = false
) => {
  const formatter = new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    notation: compact ? "compact" : "standard",
  });

  return formatter.format(amount).replace("IDR", "Rp");
};

/**
 * Format a date string with timezone awareness
 * @param dateString - ISO date string or YYYY-MM-DD format
 * @param timezone - User's timezone (optional, defaults to DEFAULT_TIMEZONE)
 * @returns Formatted date string
 */
export const formatDate = (
  dateString: string,
  timezone: string = DEFAULT_TIMEZONE
) => {
  try {
    return formatDateForDisplay(dateString, timezone, "dd MMM yyyy");
  } catch (error) {
    return "Invalid date";
  }
};

/**
 * Legacy formatDate function for backward compatibility
 * @deprecated Use formatDate with timezone parameter instead
 */
export const formatDateLegacy = (dateString: string) => {
  try {
    return format(parseISO(dateString), "dd MMM yyyy");
  } catch (error) {
    return "Invalid date";
  }
};
