import { z } from "zod";

// Venue classification types
export type VenueClassification =
  | "outdoor"
  | "hotel"
  | "indoor"
  | "premium"
  | "luxury";

// Interface for venue data from the database
export interface Venue {
  id: string;
  name: string;
  description: string | null;
  address: string | null;
  city_id: string | null;
  city_name?: string; // For display purposes
  classification?: VenueClassification | null;
  capacity?: number | null;
  image_url?: string | null;
  features?: string[] | null;
  is_active: boolean;
  is_deleted: boolean;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
}

// Interface for frontend display with formatted data
export interface VenueDisplay extends Venue {
  city_name: string;
}

// Data for creating or updating a venue
export interface SaveVenueData {
  id?: string;
  name: string;
  description?: string | null;
  address?: string | null;
  city_id: string;
  classification?: VenueClassification | null;
  capacity?: number | null;
  image_url?: string | null;
  features?: string[] | null;
  is_active?: boolean;
}

// Form values for venue form
export interface VenueFormValues {
  name: string;
  description: string;
  address: string;
  city_id: string;
  classification?: VenueClassification;
  capacity?: number;
  image_url?: string;
  features?: string[];
}

// Venue filtering options for Dashboard V2
export interface VenueFilters {
  cityId?: string;
  active?: boolean;
  classification?: VenueClassification;
  minCapacity?: number;
  maxCapacity?: number;
}

// Zod schema for venue validation
export const venueSchema = z.object({
  name: z.string().min(1, "Venue name is required"),
  description: z.string().optional(),
  address: z.string().optional(),
  city_id: z.string().min(1, "City is required"),
  classification: z
    .enum(["outdoor", "hotel", "indoor", "premium", "luxury"])
    .optional(),
  capacity: z.number().min(1, "Capacity must be at least 1").optional(),
  image_url: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  features: z.array(z.string()).optional(),
});

// Interface for package venue relationship
export interface PackageVenue {
  id: string;
  package_id: string;
  venue_id: string;
  is_deleted: boolean;
  deleted_at: string | null;
  created_at: string;
}

// Admin-specific venue types (consolidated from admin pages)
export type VenueFormMode = "create" | "edit";
export type VenueStatus = "active" | "inactive";

// Venue classification constants and utilities
export const VENUE_CLASSIFICATIONS: Record<
  VenueClassification,
  { label: string; color: string; icon: string }
> = {
  outdoor: {
    label: "Outdoor",
    color: "green",
    icon: "trees",
  },
  hotel: {
    label: "Hotel",
    color: "blue",
    icon: "building",
  },
  indoor: {
    label: "Indoor",
    color: "gray",
    icon: "home",
  },
  premium: {
    label: "Premium",
    color: "purple",
    icon: "star",
  },
  luxury: {
    label: "Luxury",
    color: "yellow",
    icon: "crown",
  },
};

// Helper function to get venue classification info
export const getVenueClassificationInfo = (
  classification?: VenueClassification | null
) => {
  if (!classification) return null;
  return VENUE_CLASSIFICATIONS[classification];
};

// Helper function to format venue capacity
export const formatVenueCapacity = (
  capacity?: number | null,
  attendees?: number
) => {
  if (!capacity) return "Capacity not specified";

  if (attendees) {
    const percentage = Math.round((attendees / capacity) * 100);
    if (percentage > 100) {
      return `${capacity} guests (${percentage}% - Over capacity!)`;
    } else if (percentage > 80) {
      return `${capacity} guests (${percentage}% - Near capacity)`;
    } else {
      return `${capacity} guests (${percentage}% capacity)`;
    }
  }

  return `Up to ${capacity} guests`;
};
