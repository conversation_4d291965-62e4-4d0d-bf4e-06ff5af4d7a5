import React, { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { PlusCircle, Search, X } from "lucide-react";
import AdminLayout from "@/components/layout/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { getAllVenues } from "@/services/shared/entities/venues";
import { getAllCities } from "@/services/shared/entities/cities";
import { City } from "@/types/types";
import { VenueForm, VenuesList } from "./components";
import DataPagination from "@/components/ui/data-pagination";

const VenuesPage: React.FC = () => {
  // Get the query client for cache invalidation
  const queryClient = useQueryClient();

  // State for filters and pagination
  const [filters, setFilters] = useState({
    search: "",
    cityId: "all",
    showDeleted: false,
    page: 1,
    pageSize: 10,
    sortBy: "name",
    sortOrder: "asc" as "asc" | "desc",
  });

  // State for venue form
  const [isAddVenueOpen, setIsAddVenueOpen] = useState(false);
  const [isEditVenueOpen, setIsEditVenueOpen] = useState(false);
  const [editingVenueId, setEditingVenueId] = useState<string | null>(null);

  // Fetch venues with filters
  const {
    data: venuesResult,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["venues", filters],
    queryFn: () => getAllVenues(filters),
    meta: {
      onError: () => {
        toast.error("Failed to load venues");
      },
    },
  });

  // Fetch cities for filtering using the service layer
  const { data: cities = [] } = useQuery({
    queryKey: ["cities"],
    queryFn: getAllCities,
    meta: {
      onError: () => {
        toast.error("Failed to load cities");
      },
    },
  });

  // Handle filter changes
  const handleFilterChange = (key: string, value: string | boolean) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev) => ({ ...prev, search: e.target.value, page: 1 }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      ...filters,
      cityId: "all",
      search: "",
      showDeleted: false,
      page: 1,
    });
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setFilters({
      ...filters,
      page,
    });
  };

  // Handle venue edit
  const handleEditVenue = (venueId: string) => {
    setEditingVenueId(venueId);
    setIsEditVenueOpen(true);
  };

  // Handle venue form close
  const handleVenueFormClose = async (shouldRefresh: boolean = false) => {
    setIsAddVenueOpen(false);
    setIsEditVenueOpen(false);
    setEditingVenueId(null);

    if (shouldRefresh) {
      try {
        // Invalidate the venues query cache to ensure fresh data
        await queryClient.invalidateQueries({ queryKey: ["venues"] });

        // Explicitly refetch the current query with filters
        await refetch();

        // Show success message
        toast.success("Venue list updated successfully");
      } catch (error) {
        console.error("Error refreshing venues:", error);
        toast.error("Failed to refresh venues. Please reload the page.");
      }
    }
  };

  // Check if any filters are active
  const hasActiveFilters =
    filters.search !== "" || filters.cityId !== "all" || filters.showDeleted;

  return (
    <AdminLayout title="Manage Venues">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/catalogue">Catalogue</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Venues</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="mb-6 text-muted-foreground">
        Create and manage venues where your services are available.
      </div>

      <div className="flex justify-between items-center mb-6">
        <Button onClick={() => setIsAddVenueOpen(true)}>
          <PlusCircle className="h-4 w-4 mr-2" />
          Add New Venue
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
            <Input
              placeholder="Search venues..."
              className="pl-8"
              value={filters.search}
              onChange={handleSearchChange}
            />
            {filters.search && (
              <button
                onClick={() => handleFilterChange("search", "")}
                className="absolute right-2.5 top-2.5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          <Select
            value={filters.cityId}
            onValueChange={(value) => handleFilterChange("cityId", value)}
          >
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="City" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Cities</SelectItem>
              {cities.map((city) => (
                <SelectItem key={city.id} value={city.id}>
                  {city.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="showDeleted"
              checked={filters.showDeleted}
              onCheckedChange={(checked) =>
                handleFilterChange("showDeleted", checked === true)
              }
            />
            <label
              htmlFor="showDeleted"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-700 dark:text-gray-300"
            >
              Show Inactive Venues
            </label>
          </div>
        </div>

        {/* Active filters display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap items-center gap-2 mt-2">
            {filters.search && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: {filters.search}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => handleFilterChange("search", "")}
                />
              </Badge>
            )}
            {filters.cityId !== "all" && (
              <Badge variant="secondary" className="flex items-center gap-1">
                City:{" "}
                {cities.find((c) => c.id === filters.cityId)?.name ||
                  filters.cityId}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => handleFilterChange("cityId", "all")}
                />
              </Badge>
            )}
            {filters.showDeleted && (
              <Badge
                variant="secondary"
                className="flex items-center gap-1 bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 hover:bg-amber-200 dark:hover:bg-amber-900/30"
              >
                Including Inactive Venues
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => handleFilterChange("showDeleted", false)}
                />
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={clearFilters}
              className="ml-auto"
            >
              Clear All Filters
            </Button>
          </div>
        )}
      </div>

      {/* Venues List */}
      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      ) : isError ? (
        <div className="text-center py-8 text-red-500 dark:text-red-400">
          Failed to load venues. Please try again.
        </div>
      ) : (
        <>
          <VenuesList
            venues={venuesResult?.data || []}
            onEdit={handleEditVenue}
            onRefresh={refetch}
          />

          {/* Pagination */}
          {venuesResult && (
            <DataPagination
              currentPage={filters.page}
              totalPages={venuesResult.totalPages}
              totalItems={venuesResult.totalCount}
              pageSize={filters.pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={(pageSize) =>
                setFilters((prev) => ({ ...prev, pageSize, page: 1 }))
              }
              showPageSizeSelector={true}
              pageSizeOptions={[10, 25, 50, 100]}
              className="mt-4"
            />
          )}
        </>
      )}

      {/* Add Venue Dialog */}
      <VenueForm
        open={isAddVenueOpen}
        onOpenChange={handleVenueFormClose}
        mode="create"
      />

      {/* Edit Venue Dialog */}
      {editingVenueId && (
        <VenueForm
          open={isEditVenueOpen}
          onOpenChange={handleVenueFormClose}
          mode="edit"
          venueId={editingVenueId}
        />
      )}
    </AdminLayout>
  );
};

export default VenuesPage;
