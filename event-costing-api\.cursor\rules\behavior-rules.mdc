---
description: 
globs: *.tsx,*.ts,src/modules/**/*.ts,src/modules/**/**/*.ts
alwaysApply: false
---

# NestJS Backend: Project Rules and Guidelines (Concise)

Core rules and conventions for the NestJS/Supabase backend (using `@supabase/supabase-js`). Adherence ensures consistency and quality.

## Your Role

-   Expert backend developer: TypeScript, NestJS, Supabase (Auth, DB Client/PostgREST, Storage, RPC), PostgreSQL, REST API design, Jest testing.

## Rules and Guidelines

### Code Style and Structure

-   **TypeScript:** Use strong typing effectively; avoid `any`. Consider `supabase gen types typescript` for DB types.
-   **Architecture:** Use NestJS Feature Modules (`src/modules/`), standard Layers (Controller, Service, Provider), and Dependency Injection (DI). Keep Controllers thin.
-   **Folder Structure:** Follow the established project structure (`src/modules`, `src/core/supabase`, `src/shared`, `src/config`). Example snippet:
    ```
    src/
    ├── modules/
    │   ├── auth/
    │   │   ├── auth.module.ts
    │   │   ├── auth.controller.ts
    │   │   ├── auth.service.ts
    │   │   ├── guards/
    │   │   │   └── jwt-auth.guard.ts
    │   │   └── strategies/ # Optional: for Passport.js if not solely relying on Supabase client
    │   │       └── jwt.strategy.ts
    │   └── ... # Other feature modules
    ├── core/
    │   ├── supabase/
    │   │   └── supabase.module.ts # Setup for injectable Supabase client
    │   │   └── supabase.service.ts
    │   └── ... # Filters, Pipes, etc.
    └── ...
    ```

### API Design & Controllers

-   **REST & URLs:** Follow REST principles. Use `kebab-case`, plural nouns for URLs (e.g., `/api/v1/calculation-histories`).
-   **DTOs:** Use `class-validator` DTOs for all API I/O (in `dtos/` folders).
-   **interfaces:** Use `class-validator` DTOs for all API I/O (in `dtos/` folders).
-   **Controller Logic:** Handle HTTP req/res, validation via Pipes/DTOs, delegate business logic to Services.

### Services & Business Logic

-   **Service Logic:** Encapsulate business logic, interact with the database via injected Supabase client (e.g., through `SupabaseService`).
-   **Transactions:** For atomic multi-write operations, use **Supabase Database Functions (RPC)**. Avoid complex multi-step operations directly in service methods if atomicity is critical.

### Database Interaction (Supabase)

-   **Supabase Client:** Use the central configured `@supabase/supabase-js` client (e.g., via `SupabaseService`) for all DB interactions (queries, inserts, updates, deletes, RPC calls).
    - Use the **Service Role Key** (`SUPABASE_SERVICE_ROLE_KEY` environment variable) for backend client initialization to bypass RLS when necessary.
-   **Schema Workflow:** **MANDATORY:** Define schema changes in `/supabase/migrations/*.sql` and apply them directly to the Supabase database (e.g., via Supabase CLI or MCP). SQL is the source of truth.
-   **RPC:** Use `supabase.rpc()` for complex queries or transaction-like operations.

### NestJS Features

-   **Guards:** Implement authorization (roles, ownership) using Supabase Auth context.
-   **Pipes:** Use global `ValidationPipe` for DTO validation; custom pipes for transformation.
-   **Exception Filters:** Use global filters for standardized error responses.
-   **Middleware:** Use for request logging, CORS, security headers (`helmet`). Avoid business logic.

### Naming Conventions

-   **TypeScript Code:** `PascalCase` (Classes, Interfaces, Types, Enums), `camelCase` (variables, functions, methods), `UPPER_SNAKE_CASE` (constants).
-   **Files:** `kebab-case.suffix.ts` (e.g., `calculations.service.ts`).
-   **Folders:** `lowercase`, `kebab-case` (e.g., `calculation-history`).
-   **Database:** `snake_case` (tables, columns). Be mindful of this when using the Supabase client.

### Configuration & Environment

-   **Config:** Use `@nestjs/config` and `.env` files. Access Supabase URL/Keys via typed service.
-   **Secrets:** Never commit `.env` or hardcode secrets. Use `.env.example` with `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY` placeholders.

### Input Validation

-   **Method:** Use DTOs with `class-validator` decorators and the global `ValidationPipe`.
-   **Errors:** Validation failures result in structured 400 responses via Exception Filter.

### Error Handling

-   **Method:** Throw NestJS standard (`BadRequestException`, etc.) or custom `HttpException`s.
-   **Handling:** Rely on global Exception Filters for consistent JSON error responses.

### API Documentation (Swagger/OpenAPI)

-   **Method:** Use `@nestjs/swagger` decorators extensively.
-   **Maintenance:** **ALWAYS** keep Swagger docs updated with API changes. Provide clear descriptions.

### Testing

-   **Method:** Write Jest tests: Unit (`.spec.ts`), Integration, E2E (`supertest`).
-   **Goal:** Aim for meaningful test coverage.

### Security

-   **Essentials:** Enforce strict Input Validation (DTOs/Pipes), robust Authorization (Guards), keep Dependencies updated (`npm audit`), use `helmet`.
-   **Consider:** Rate Limiting (`@nestjs/throttler`).

### Key Technical Reminders

-   Implement correct Price/Cost Snapshotting logic.
-   Base Calculation Logic strictly on snapshot values.
-   Implement Quantity/Duration determination logic carefully.
-   Enforce Package Dependencies checks.
-   Design Admin APIs to support complex UI needs.

### Version Control (Git)

-   Follow project's Branching Strategy (e.g., Gitflow).
-   Write descriptive Commit Messages. Use reviewed Pull Requests.

### General Preferences

-   Adhere to Requirements. Write Functional, Readable, Complete code.
-   Prioritize Readability & Maintainability. Be Concise.
-   Communicate Clearly if blocked or unsure (Honesty).

## IMPORTANT

-   **Update All Documentation** (these rules, README, Backend Plan v1, Technical Plan, etc.) after significant changes potentially `docs/`.
