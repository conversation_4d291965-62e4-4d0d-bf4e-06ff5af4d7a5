/**
 * Performance monitoring hook for calculations feature
 * Helps identify performance bottlenecks and optimization opportunities
 */
import { useEffect, useRef, useCallback } from "react";
import { debug } from "@/lib/debugUtils";

interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  slowRenders: number;
  componentName: string;
}

interface UsePerformanceMonitorOptions {
  componentName: string;
  enabled?: boolean;
  slowRenderThreshold?: number;
  logSlowRenders?: boolean;
}

/**
 * Hook to monitor component performance
 * Tracks render times, render counts, and identifies slow renders
 */
export const usePerformanceMonitor = ({
  componentName,
  enabled = process.env.NODE_ENV === "development",
  slowRenderThreshold = 16, // 16ms = 60fps
  logSlowRenders = true,
}: UsePerformanceMonitorOptions) => {
  const metricsRef = useRef<PerformanceMetrics>({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    slowRenders: 0,
    componentName,
  });

  const renderStartTimeRef = useRef<number>(0);
  const renderTimesRef = useRef<number[]>([]);

  // Start performance measurement
  const startMeasurement = useCallback(() => {
    if (!enabled) return;
    renderStartTimeRef.current = performance.now();
  }, [enabled]);

  // End performance measurement
  const endMeasurement = useCallback(() => {
    if (!enabled || renderStartTimeRef.current === 0) return;

    const renderTime = performance.now() - renderStartTimeRef.current;
    const metrics = metricsRef.current;

    // Update metrics
    metrics.renderCount++;
    metrics.lastRenderTime = renderTime;

    // Track render times for average calculation
    renderTimesRef.current.push(renderTime);
    if (renderTimesRef.current.length > 100) {
      renderTimesRef.current.shift(); // Keep only last 100 renders
    }

    // Calculate average render time
    metrics.averageRenderTime =
      renderTimesRef.current.reduce((sum, time) => sum + time, 0) /
      renderTimesRef.current.length;

    // Check for slow renders
    if (renderTime > slowRenderThreshold) {
      metrics.slowRenders++;

      if (logSlowRenders) {
        debug(`Slow render detected in ${componentName}:`, {
          renderTime: `${renderTime.toFixed(2)}ms`,
          threshold: `${slowRenderThreshold}ms`,
          renderCount: metrics.renderCount,
          averageRenderTime: `${metrics.averageRenderTime.toFixed(2)}ms`,
        });
      }
    }

    // Reset start time
    renderStartTimeRef.current = 0;
  }, [enabled, componentName, slowRenderThreshold, logSlowRenders]);

  // Measure render performance
  useEffect(() => {
    startMeasurement();
    return endMeasurement;
  });

  // Get current metrics
  const getMetrics = useCallback((): PerformanceMetrics => {
    return { ...metricsRef.current };
  }, []);

  // Log performance summary
  const logSummary = useCallback(() => {
    if (!enabled) return;

    const metrics = metricsRef.current;
    debug(`Performance summary for ${componentName}:`, {
      totalRenders: metrics.renderCount,
      averageRenderTime: `${metrics.averageRenderTime.toFixed(2)}ms`,
      slowRenders: metrics.slowRenders,
      slowRenderPercentage: `${(
        (metrics.slowRenders / metrics.renderCount) *
        100
      ).toFixed(1)}%`,
      lastRenderTime: `${metrics.lastRenderTime.toFixed(2)}ms`,
    });
  }, [enabled, componentName]);

  // Reset metrics
  const resetMetrics = useCallback(() => {
    metricsRef.current = {
      renderCount: 0,
      lastRenderTime: 0,
      averageRenderTime: 0,
      slowRenders: 0,
      componentName,
    };
    renderTimesRef.current = [];
  }, [componentName]);

  return {
    getMetrics,
    logSummary,
    resetMetrics,
    isEnabled: enabled,
  };
};

/**
 * Hook to monitor data fetching performance
 */
export const useDataFetchMonitor = (
  queryKey: string,
  enabled: boolean = process.env.NODE_ENV === "development"
) => {
  const startTimeRef = useRef<number>(0);

  const startFetch = useCallback(() => {
    if (!enabled) return;
    startTimeRef.current = performance.now();
    debug(`Starting data fetch: ${queryKey}`);
  }, [enabled, queryKey]);

  const endFetch = useCallback(
    (success: boolean, dataSize?: number) => {
      if (!enabled || startTimeRef.current === 0) return;

      const fetchTime = performance.now() - startTimeRef.current;
      debug(`Data fetch completed: ${queryKey}`, {
        success,
        fetchTime: `${fetchTime.toFixed(2)}ms`,
        dataSize: dataSize ? `${dataSize} items` : "unknown",
      });

      startTimeRef.current = 0;
    },
    [enabled, queryKey]
  );

  return {
    startFetch,
    endFetch,
  };
};

/**
 * Hook to monitor memory usage (experimental)
 */
export const useMemoryMonitor = (
  componentName: string,
  enabled: boolean = process.env.NODE_ENV === "development"
) => {
  const logMemoryUsage = useCallback(() => {
    if (!enabled || !("memory" in performance)) return;

    const memory = (performance as any).memory;
    debug(`Memory usage for ${componentName}:`, {
      usedJSHeapSize: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      totalJSHeapSize: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(
        2
      )} MB`,
      jsHeapSizeLimit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(
        2
      )} MB`,
    });
  }, [enabled, componentName]);

  useEffect(() => {
    if (enabled) {
      logMemoryUsage();
    }
  }, [logMemoryUsage, enabled]);

  return {
    logMemoryUsage,
  };
};
