import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import {
  getPackageDependencies,
  savePackageDependency,
  deletePackageDependency,
  checkDependencyExists,
} from "../../../../services/admin/packages/packageDependencyService";
import {
  SavePackageDependencyData,
  PackageDependencyFormValues,
} from "../types/packageDependencies";
import { showSuccess, showError } from "@/lib/notifications";
import { QUERY_KEYS } from "@/lib/queryKeys";

/**
 * Custom hook for managing package dependencies
 * @param packageId - The package ID
 * @returns Package dependencies data, loading state, and CRUD functions
 */
export const usePackageDependencies = (packageId: string) => {
  const queryClient = useQueryClient();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedDependencyId, setSelectedDependencyId] = useState<
    string | null
  >(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [dependencyToDelete, setDependencyToDelete] = useState<string | null>(
    null
  );

  // Fetch package dependencies
  const {
    data: dependencies = [],
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: QUERY_KEYS.packages.dependencies(packageId),
    queryFn: () => getPackageDependencies(packageId),
    enabled: !!packageId,
    meta: {
      onError: () => {
        showError("Failed to load package dependencies");
      },
    },
  });

  // Save package dependency mutation
  const saveDependencyMutation = useMutation({
    mutationFn: (data: SavePackageDependencyData) =>
      savePackageDependency(data),
    onSuccess: () => {
      showSuccess(
        `Dependency ${
          selectedDependencyId ? "updated" : "created"
        } successfully`
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.packageDependencies(packageId),
      });
      setIsFormOpen(false);
      setSelectedDependencyId(null);
    },
    onError: (error) => {
      showError(
        `Failed to ${selectedDependencyId ? "update" : "create"} dependency: ${
          error.message
        }`
      );
    },
  });

  // Delete package dependency mutation
  const deleteDependencyMutation = useMutation({
    mutationFn: (dependencyId: string) =>
      deletePackageDependency(packageId, dependencyId),
    onSuccess: () => {
      showSuccess("Dependency deleted successfully");
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.packageDependencies(packageId),
      });
      setIsDeleteDialogOpen(false);
      setDependencyToDelete(null);
    },
    onError: (error) => {
      showError(`Failed to delete dependency: ${error.message}`);
    },
  });

  // Check if dependency exists
  const checkDependencyExistsMutation = useMutation({
    mutationFn: (data: { packageId: string; dependentPackageId: string }) =>
      checkDependencyExists(data.packageId, data.dependentPackageId),
  });

  // Handle form submission
  const handleSaveDependency = async (values: PackageDependencyFormValues) => {
    // Check if dependency already exists
    if (!selectedDependencyId) {
      const exists = await checkDependencyExistsMutation.mutateAsync({
        packageId,
        dependentPackageId: values.dependent_package_id,
      });

      if (exists) {
        showError("A dependency with this package already exists");
        return;
      }
    }

    saveDependencyMutation.mutate({
      id: selectedDependencyId || undefined,
      package_id: packageId,
      dependent_package_id: values.dependent_package_id,
      dependency_type: values.dependency_type,
      description: values.description || null,
    });
  };

  // Handle dependency deletion
  const handleDeleteDependency = () => {
    if (dependencyToDelete) {
      deleteDependencyMutation.mutate(dependencyToDelete);
    }
  };

  return {
    dependencies,
    isLoading,
    isError,
    isFormOpen,
    setIsFormOpen,
    selectedDependencyId,
    setSelectedDependencyId,
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    dependencyToDelete,
    setDependencyToDelete,
    handleSaveDependency,
    handleDeleteDependency,
    refetch,
  };
};
