import React from "react";
import { Search, EyeIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { SortMenu } from "./SortMenu";

// Import types from the feature's types directory
interface Category {
  id: string;
  name: string;
}

interface Division {
  id: string;
  name: string;
}

interface City {
  id: string;
  name: string;
}

interface PackageFiltersProps {
  filters: {
    categoryId: string;
    divisionId: string;
    cityId: string;
    search: string;
    showDeleted: boolean;
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
  categories: Category[];
  divisions: Division[];
  cities: City[];
  onFilterChange: (key: string, value: string | boolean) => void;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClearFilters: () => void;
  onSortChange?: (field: string, order: "asc" | "desc") => void;
}

export const PackageFilters: React.FC<PackageFiltersProps> = ({
  filters,
  categories,
  divisions,
  cities,
  onFilterChange,
  onSearchChange,
  onClearFilters,
  onSortChange,
}) => {
  const hasActiveFilters =
    filters.search !== "" ||
    filters.categoryId !== "all" ||
    filters.divisionId !== "all" ||
    filters.cityId !== "all" ||
    filters.showDeleted;

  return (
    <>
      {/* Action and Filter Bar Block */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1 flex flex-col md:flex-row gap-2">
          <div className="relative w-full md:max-w-[300px]">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search packages..."
              className="pl-8"
              value={filters.search}
              onChange={onSearchChange}
            />
          </div>

          <Select
            value={filters.categoryId}
            onValueChange={(value) => onFilterChange("categoryId", value)}
          >
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.divisionId}
            onValueChange={(value) => onFilterChange("divisionId", value)}
          >
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Division" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Divisions</SelectItem>
              {divisions.map((division) => (
                <SelectItem key={division.id} value={division.id}>
                  {division.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.cityId}
            onValueChange={(value) => onFilterChange("cityId", value)}
          >
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="City" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Cities</SelectItem>
              {cities.map((city) => (
                <SelectItem key={city.id} value={city.id}>
                  {city.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={onClearFilters}
              className="w-full md:w-auto"
            >
              Clear Filters
            </Button>
          )}
        </div>

        {/* Sort Menu */}
        {onSortChange && (
          <div className="flex items-center">
            <SortMenu
              sortBy={filters.sortBy}
              sortOrder={filters.sortOrder}
              onSortChange={onSortChange}
            />
          </div>
        )}

        {/* Status Filter Toggle */}
        <div className="flex items-center space-x-2 bg-slate-50 dark:bg-slate-800 p-2 rounded-md border border-slate-200 dark:border-slate-700">
          <Switch
            id="show-inactive"
            checked={filters.showDeleted}
            onCheckedChange={(checked) =>
              onFilterChange("showDeleted", checked)
            }
          />
          <Label
            htmlFor="show-inactive"
            className="cursor-pointer flex items-center gap-1"
          >
            <EyeIcon className="h-4 w-4" />
            {filters.showDeleted
              ? "Showing All Packages"
              : "Showing Active Only"}
          </Label>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 mb-4">
          <div className="text-sm text-muted-foreground mr-2 py-1">
            Active filters:
          </div>
          {filters.search && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: {filters.search}
            </Badge>
          )}
          {filters.categoryId && filters.categoryId !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Category:{" "}
              {categories.find((c) => c.id === filters.categoryId)?.name ||
                filters.categoryId}
            </Badge>
          )}
          {filters.divisionId && filters.divisionId !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Division:{" "}
              {divisions.find((d) => d.id === filters.divisionId)?.name ||
                filters.divisionId}
            </Badge>
          )}
          {filters.cityId && filters.cityId !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              City:{" "}
              {cities.find((c) => c.id === filters.cityId)?.name ||
                filters.cityId}
            </Badge>
          )}
          {filters.showDeleted && (
            <Badge
              variant="secondary"
              className="flex items-center gap-1 bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 hover:bg-amber-200 dark:hover:bg-amber-900/30"
            >
              Including Inactive Packages
            </Badge>
          )}
        </div>
      )}
    </>
  );
};
