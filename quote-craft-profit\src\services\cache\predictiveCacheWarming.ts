/**
 * Predictive Cache Warming Service
 * 
 * PHASE 2: Implements intelligent cache warming based on user behavior patterns
 * Analyzes navigation patterns and preloads likely-to-be-accessed data
 */

import { QueryClient } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/queryKeys';

export interface NavigationPattern {
  fromRoute: string;
  toRoute: string;
  frequency: number;
  lastAccessed: Date;
  averageTimeSpent: number;
  dataRequirements: string[]; // Query keys typically needed for this route
}

export interface UserBehaviorData {
  userId?: string;
  sessionId: string;
  patterns: NavigationPattern[];
  currentRoute: string;
  routeStartTime: Date;
  totalSessions: number;
  averageSessionDuration: number;
}

export interface PredictiveWarmingConfig {
  enabled: boolean;
  confidenceThreshold: number; // Minimum confidence to trigger warming (0-1)
  maxPredictions: number; // Maximum number of predictions to act on
  warmingDelay: number; // Delay before warming in ms
  patternRetentionDays: number; // How long to keep patterns
}

class PredictiveCacheWarmingService {
  private queryClient: QueryClient;
  private userBehavior: UserBehaviorData;
  private config: PredictiveWarmingConfig = {
    enabled: true,
    confidenceThreshold: 0.6,
    maxPredictions: 3,
    warmingDelay: 1000, // 1 second delay
    patternRetentionDays: 30,
  };

  private routeDataMap = new Map<string, string[]>([
    // Define what data each route typically needs
    ['/calculations', [
      JSON.stringify(QUERY_KEYS.calculations.lists()),
      JSON.stringify(QUERY_KEYS.packages.lists()),
      JSON.stringify(QUERY_KEYS.categories.all()),
    ]],
    ['/calculations/new', [
      JSON.stringify(QUERY_KEYS.packages.lists()),
      JSON.stringify(QUERY_KEYS.categories.all()),
      JSON.stringify(QUERY_KEYS.clients.all()),
      JSON.stringify(QUERY_KEYS.cities.all()),
    ]],
    ['/calculations/:id', [
      JSON.stringify(QUERY_KEYS.packages.lists()),
      JSON.stringify(QUERY_KEYS.categories.all()),
    ]],
    ['/admin/packages', [
      JSON.stringify(QUERY_KEYS.packages.lists()),
      JSON.stringify(QUERY_KEYS.categories.all()),
      JSON.stringify(QUERY_KEYS.divisions.all()),
    ]],
    ['/admin/categories', [
      JSON.stringify(QUERY_KEYS.categories.all()),
    ]],
    ['/clients', [
      JSON.stringify(QUERY_KEYS.clients.all()),
    ]],
    ['/dashboard', [
      JSON.stringify(QUERY_KEYS.calculations.lists()),
      JSON.stringify(QUERY_KEYS.clients.all()),
    ]],
  ]);

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
    this.userBehavior = this.initializeUserBehavior();
    this.loadStoredPatterns();
    this.setupRouteTracking();
  }

  /**
   * Initialize user behavior tracking
   */
  private initializeUserBehavior(): UserBehaviorData {
    return {
      sessionId: this.generateSessionId(),
      patterns: [],
      currentRoute: window.location.pathname,
      routeStartTime: new Date(),
      totalSessions: 1,
      averageSessionDuration: 0,
    };
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Load stored navigation patterns from localStorage
   */
  private loadStoredPatterns(): void {
    try {
      const stored = localStorage.getItem('predictive_cache_patterns');
      if (stored) {
        const data = JSON.parse(stored);
        this.userBehavior.patterns = data.patterns || [];
        this.userBehavior.totalSessions = (data.totalSessions || 0) + 1;
        this.userBehavior.averageSessionDuration = data.averageSessionDuration || 0;
        
        // Clean up old patterns
        this.cleanupOldPatterns();
        
        console.debug('📊 Loaded', this.userBehavior.patterns.length, 'navigation patterns');
      }
    } catch (error) {
      console.error('Error loading navigation patterns:', error);
    }
  }

  /**
   * Save navigation patterns to localStorage
   */
  private savePatterns(): void {
    try {
      const dataToStore = {
        patterns: this.userBehavior.patterns,
        totalSessions: this.userBehavior.totalSessions,
        averageSessionDuration: this.userBehavior.averageSessionDuration,
        lastUpdated: new Date().toISOString(),
      };
      localStorage.setItem('predictive_cache_patterns', JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Error saving navigation patterns:', error);
    }
  }

  /**
   * Clean up patterns older than retention period
   */
  private cleanupOldPatterns(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.patternRetentionDays);
    
    const initialCount = this.userBehavior.patterns.length;
    this.userBehavior.patterns = this.userBehavior.patterns.filter(
      pattern => new Date(pattern.lastAccessed) > cutoffDate
    );
    
    const removedCount = initialCount - this.userBehavior.patterns.length;
    if (removedCount > 0) {
      console.debug(`🧹 Cleaned up ${removedCount} old navigation patterns`);
    }
  }

  /**
   * Setup route change tracking
   */
  private setupRouteTracking(): void {
    // Track route changes
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = (...args) => {
      originalPushState.apply(history, args);
      this.handleRouteChange();
    };

    history.replaceState = (...args) => {
      originalReplaceState.apply(history, args);
      this.handleRouteChange();
    };

    // Handle browser back/forward
    window.addEventListener('popstate', () => {
      this.handleRouteChange();
    });

    // Save patterns before page unload
    window.addEventListener('beforeunload', () => {
      this.savePatterns();
    });
  }

  /**
   * Handle route change and update patterns
   */
  private handleRouteChange(): void {
    const newRoute = window.location.pathname;
    const previousRoute = this.userBehavior.currentRoute;
    const timeSpent = Date.now() - this.userBehavior.routeStartTime.getTime();

    if (previousRoute !== newRoute) {
      // Update navigation pattern
      this.updateNavigationPattern(previousRoute, newRoute, timeSpent);
      
      // Predict and warm next likely routes
      this.predictAndWarmNextRoutes(newRoute);
      
      // Update current route tracking
      this.userBehavior.currentRoute = newRoute;
      this.userBehavior.routeStartTime = new Date();
    }
  }

  /**
   * Update navigation pattern based on route transition
   */
  private updateNavigationPattern(fromRoute: string, toRoute: string, timeSpent: number): void {
    // Find existing pattern or create new one
    let pattern = this.userBehavior.patterns.find(
      p => p.fromRoute === fromRoute && p.toRoute === toRoute
    );

    if (pattern) {
      // Update existing pattern
      pattern.frequency++;
      pattern.lastAccessed = new Date();
      pattern.averageTimeSpent = (pattern.averageTimeSpent + timeSpent) / 2;
    } else {
      // Create new pattern
      pattern = {
        fromRoute,
        toRoute,
        frequency: 1,
        lastAccessed: new Date(),
        averageTimeSpent: timeSpent,
        dataRequirements: this.routeDataMap.get(toRoute) || [],
      };
      this.userBehavior.patterns.push(pattern);
    }

    console.debug(`📈 Updated navigation pattern: ${fromRoute} → ${toRoute} (frequency: ${pattern.frequency})`);
  }

  /**
   * Predict next likely routes and warm their data
   */
  private predictAndWarmNextRoutes(currentRoute: string): void {
    if (!this.config.enabled) return;

    // Get patterns from current route
    const relevantPatterns = this.userBehavior.patterns
      .filter(p => p.fromRoute === currentRoute)
      .sort((a, b) => b.frequency - a.frequency);

    if (relevantPatterns.length === 0) return;

    // Calculate confidence scores
    const totalFrequency = relevantPatterns.reduce((sum, p) => sum + p.frequency, 0);
    const predictions = relevantPatterns
      .map(pattern => ({
        route: pattern.toRoute,
        confidence: pattern.frequency / totalFrequency,
        dataRequirements: pattern.dataRequirements,
        pattern,
      }))
      .filter(pred => pred.confidence >= this.config.confidenceThreshold)
      .slice(0, this.config.maxPredictions);

    if (predictions.length > 0) {
      console.debug(`🔮 Predictions for route ${currentRoute}:`, 
        predictions.map(p => `${p.route} (${(p.confidence * 100).toFixed(1)}%)`));

      // Warm cache for predicted routes with delay
      setTimeout(() => {
        this.warmPredictedRoutes(predictions);
      }, this.config.warmingDelay);
    }
  }

  /**
   * Warm cache for predicted routes
   */
  private async warmPredictedRoutes(predictions: Array<{
    route: string;
    confidence: number;
    dataRequirements: string[];
  }>): Promise<void> {
    for (const prediction of predictions) {
      try {
        console.debug(`🔥 Warming cache for predicted route: ${prediction.route} (confidence: ${(prediction.confidence * 100).toFixed(1)}%)`);
        
        for (const queryKeyStr of prediction.dataRequirements) {
          try {
            const queryKey = JSON.parse(queryKeyStr);
            
            // Check if data is already cached and fresh
            const queryState = this.queryClient.getQueryState(queryKey);
            const isStale = !queryState || 
              !queryState.dataUpdatedAt || 
              Date.now() - queryState.dataUpdatedAt > 60000; // 1 minute staleness threshold

            if (isStale) {
              // Prefetch the data
              await this.queryClient.prefetchQuery({
                queryKey,
                queryFn: () => this.getQueryFunction(queryKey),
                staleTime: 5 * 60 * 1000, // 5 minutes
              });
              
              console.debug(`✅ Warmed cache for query:`, queryKey);
            }
          } catch (error) {
            console.error('Error warming cache for query:', queryKeyStr, error);
          }
        }
      } catch (error) {
        console.error('Error warming cache for route:', prediction.route, error);
      }
    }
  }

  /**
   * Get appropriate query function for a query key
   * This is a simplified implementation - in practice, you'd map to actual API functions
   */
  private async getQueryFunction(queryKey: any[]): Promise<any> {
    // This is a placeholder - replace with actual query functions
    console.debug('Fetching data for query key:', queryKey);
    return Promise.resolve([]);
  }

  /**
   * Manually trigger prediction for current route
   */
  triggerPrediction(): void {
    this.predictAndWarmNextRoutes(this.userBehavior.currentRoute);
  }

  /**
   * Get navigation patterns statistics
   */
  getStats(): {
    totalPatterns: number;
    totalSessions: number;
    averageSessionDuration: number;
    topPatterns: Array<{ from: string; to: string; frequency: number; confidence: number }>;
    currentRoute: string;
  } {
    const totalFrequency = this.userBehavior.patterns.reduce((sum, p) => sum + p.frequency, 0);
    const topPatterns = this.userBehavior.patterns
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 10)
      .map(p => ({
        from: p.fromRoute,
        to: p.toRoute,
        frequency: p.frequency,
        confidence: totalFrequency > 0 ? p.frequency / totalFrequency : 0,
      }));

    return {
      totalPatterns: this.userBehavior.patterns.length,
      totalSessions: this.userBehavior.totalSessions,
      averageSessionDuration: this.userBehavior.averageSessionDuration,
      topPatterns,
      currentRoute: this.userBehavior.currentRoute,
    };
  }

  /**
   * Configure predictive warming
   */
  configure(newConfig: Partial<PredictiveWarmingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.debug('🔧 Predictive cache warming configured:', this.config);
  }

  /**
   * Clear all navigation patterns
   */
  clearPatterns(): void {
    this.userBehavior.patterns = [];
    localStorage.removeItem('predictive_cache_patterns');
    console.debug('🧹 Navigation patterns cleared');
  }

  /**
   * Export patterns for analysis
   */
  exportPatterns(): string {
    return JSON.stringify({
      patterns: this.userBehavior.patterns,
      stats: this.getStats(),
      config: this.config,
      exportDate: new Date().toISOString(),
    }, null, 2);
  }
}

// Export singleton instance
let predictiveCacheWarmingInstance: PredictiveCacheWarmingService | null = null;

export const createPredictiveCacheWarming = (queryClient: QueryClient): PredictiveCacheWarmingService => {
  if (!predictiveCacheWarmingInstance) {
    predictiveCacheWarmingInstance = new PredictiveCacheWarmingService(queryClient);
  }
  return predictiveCacheWarmingInstance;
};

export const getPredictiveCacheWarming = (): PredictiveCacheWarmingService | null => {
  return predictiveCacheWarmingInstance;
};
