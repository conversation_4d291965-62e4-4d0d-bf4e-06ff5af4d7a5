import React from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Edit, Calendar } from "lucide-react";
import { Link } from "react-router-dom";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { useSettings } from "@/hooks/useSettings";
import { TableColumnSettingsType } from "@/pages/admin/settings/types";
import { Package } from "../../types/package";
import { togglePackageStatus } from "../../../../../services/admin/packages/packageService";
import {
  getQuantityBasisLabel,
  formatCurrency,
} from "../..//utils/packageUtils";
import { TableSkeleton } from "../shared";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

interface PackagesTableProps {
  packages: Package[];
  isLoading: boolean;
  isError: boolean;
  onStatusToggle?: () => void; // Callback to refresh the list after status change
  onEdit?: (packageId: string) => void; // Callback to edit a package
  columnSettings?: TableColumnSettingsType; // Optional column visibility settings
}

export const PackagesTable: React.FC<PackagesTableProps> = React.memo(
  ({
    packages,
    isLoading,
    isError,
    onStatusToggle,
    onEdit,
    columnSettings,
  }) => {
    // Get column settings from context if not provided as props
    const { settings } = useSettings();
    const columns = columnSettings || settings.packageTable;
    const { formatForDisplay } = useTimezoneAwareDates();

    const handleStatusToggle = async (
      packageId: string,
      newIsActive: boolean
    ) => {
      try {
        // Pass the new isActive state to the toggle function
        await togglePackageStatus(packageId, newIsActive);

        // Show success message
        toast.success(
          `Package ${newIsActive ? "enabled" : "disabled"} successfully`
        );

        // Call the onStatusToggle callback to refresh the list if provided
        if (onStatusToggle) {
          onStatusToggle();
        }
      } catch (error) {
        toast.error("Failed to update package status");
        console.error(error);
      }
    };

    if (isLoading) {
      return <TableSkeleton rows={5} columns={8} />;
    }

    if (isError) {
      return (
        <div className="border rounded-lg p-8">
          <div className="flex justify-center items-center h-32">
            <p className="text-lg text-red-500">
              Failed to load packages. Please try again.
            </p>
          </div>
        </div>
      );
    }

    if (packages.length === 0) {
      return (
        <div className="border rounded-lg p-8">
          <div className="flex flex-col justify-center items-center h-32 space-y-4">
            <p className="text-lg text-muted-foreground">No packages found</p>
            <p className="text-sm text-muted-foreground">
              Create your first package to get started
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-muted text-muted-foreground text-sm">
                {columns.number && (
                  <th className="text-left py-3 px-4 w-16">#</th>
                )}
                {columns.name && <th className="text-left py-3 px-4">Name</th>}
                {columns.category && (
                  <th className="text-left py-3 px-4">Category</th>
                )}
                {columns.division && (
                  <th className="text-left py-3 px-4">Division</th>
                )}
                {columns.cities && (
                  <th className="text-left py-3 px-4">Cities</th>
                )}
                {columns.quantityBasis && (
                  <th className="text-left py-3 px-4">Quantity Basis</th>
                )}
                {columns.price && (
                  <th className="text-left py-3 px-4">Price (Rp)</th>
                )}
                {columns.lastModified && (
                  <th className="text-left py-3 px-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>Last Modified</span>
                    </div>
                  </th>
                )}
                {columns.status && (
                  <th className="text-left py-3 px-4">Status</th>
                )}
                {columns.actions && (
                  <th className="text-right py-3 px-4">Actions</th>
                )}
              </tr>
            </thead>
            <tbody>
              {packages.map((pkg, index) => (
                <tr key={pkg.id} className="border-t hover:bg-muted/50">
                  {columns.number && (
                    <td className="py-3 px-4 text-muted-foreground">
                      {index + 1}
                    </td>
                  )}
                  {columns.name && (
                    <td className="py-3 px-4">
                      <Link
                        to={`/admin/packages/${pkg.id}`}
                        className="hover:underline"
                      >
                        <div className="font-medium text-blue-600">
                          {pkg.name}
                        </div>
                      </Link>
                    </td>
                  )}
                  {columns.category && (
                    <td className="py-3 px-4">{pkg.categoryName}</td>
                  )}
                  {columns.division && (
                    <td className="py-3 px-4">{pkg.divisionName}</td>
                  )}
                  {columns.cities && (
                    <td className="py-3 px-4">
                      {pkg.cityNames && pkg.cityNames.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {pkg.cityNames.map((city, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800"
                            >
                              {city}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">
                          No cities
                        </span>
                      )}
                    </td>
                  )}
                  {columns.quantityBasis && (
                    <td className="py-3 px-4">
                      {pkg.quantityBasis
                        ? getQuantityBasisLabel(pkg.quantityBasis)
                        : "Not set"}
                    </td>
                  )}
                  {columns.price && (
                    <td className="py-3 px-4">
                      {pkg.unitBaseCost ? (
                        <div className="font-medium">
                          {formatCurrency(pkg.unitBaseCost, pkg.currencySymbol)}
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">
                          Not set
                        </span>
                      )}
                    </td>
                  )}
                  {columns.lastModified && (
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <span className="text-sm text-muted-foreground">
                          {pkg.updatedAt
                            ? formatForDisplay(
                                pkg.updatedAt,
                                "MMM d, yyyy h:mm a"
                              )
                            : "N/A"}
                        </span>
                      </div>
                    </td>
                  )}
                  {columns.status && (
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2 bg-slate-50 dark:bg-slate-800 p-1 px-2 rounded-md border border-slate-200 dark:border-slate-700">
                          <Switch
                            checked={!pkg.isDeleted} // Convert from isDeleted to isActive
                            onCheckedChange={(isActive) =>
                              handleStatusToggle(pkg.id, isActive)
                            }
                            aria-label={`Toggle ${pkg.name} status`}
                          />
                          <span className="text-sm font-medium">
                            {pkg.isDeleted === true ? (
                              <span className="text-red-600">Inactive</span>
                            ) : (
                              <span className="text-green-600">Active</span>
                            )}
                          </span>
                        </div>
                      </div>
                    </td>
                  )}
                  {columns.actions && (
                    <td className="py-3 px-4 text-right">
                      {onEdit ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 px-2"
                          onClick={() => onEdit(pkg.id)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      ) : (
                        <Link to={`/admin/packages/${pkg.id}`}>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 px-2"
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        </Link>
                      )}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }
);
