/**
 * Constants for the templates module
 */
export class TemplateConstants {
  /**
   * The name of the templates table in the database
   */
  static readonly TABLE_NAME = 'templates';

  /**
   * The fields to select for a summary view of a template
   */
  static readonly SUMMARY_SELECT_FIELDS = `
    id, name, description, event_type_id, city_id, attendees,
    template_start_date, template_end_date, category_id,
    created_at, updated_at, created_by, is_public, is_deleted,
    currency_id, taxes, discount
  `;

  /**
   * The fields to select for a detailed view of a template
   */
  static readonly DETAIL_SELECT_FIELDS = `${TemplateConstants.SUMMARY_SELECT_FIELDS}, package_selections, custom_items`;
}
