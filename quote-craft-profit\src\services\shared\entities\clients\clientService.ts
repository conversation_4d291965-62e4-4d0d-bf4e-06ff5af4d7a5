import { getAuthenticatedApiClient, API_ENDPOINTS } from '@/integrations/api';
import { Client } from '@/types/types';
import { PaginatedResult } from '@/types/pagination';
import axios from 'axios';

// Interface for client API response
export interface ClientApiResponse {
  id: string;
  client_name: string;
  contact_person: string | null;
  email: string | null;
  phone: string | null;
  address: string | null;
  company_name: string | null;
  created_at: string;
  updated_at: string;
}

// Interface for client filters
export interface ClientFilters {
  search?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Interface for client creation/update
export interface ClientFormData {
  client_name: string;
  contact_person?: string;
  email?: string;
  phone: string; // Now required
  address?: string;
  company_name: string; // Now required
}

/**
 * Transform API client response to frontend Client type
 */
const transformApiClient = (apiClient: ClientApiResponse): Client => {
  return {
    id: apiClient.id,
    name: apiClient.client_name,
    email: apiClient.email || '',
    phone: apiClient.phone || undefined,
    company: apiClient.company_name || undefined,
    address: apiClient.address || undefined,
    city: undefined, // API doesn't have city field
  };
};

/**
 * Get all clients with optional filtering
 */
export const getAllClients = async (
  filters: ClientFilters = {}
): Promise<PaginatedResult<Client>> => {
  try {
    console.log('Fetching clients with filters:', filters);

    // Default pagination values
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;

    // Build query parameters
    const params: Record<string, string | number> = {
      page,
      limit: pageSize,
    };

    if (filters.search) {
      params.search = filters.search;
    }

    if (filters.sortBy) {
      params.sort_by = filters.sortBy;
      params.sort_order = filters.sortOrder || 'asc';
    }

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.CLIENTS.LIST, { params });

    // Handle different response formats
    let clients: ClientApiResponse[] = [];
    let totalCount = 0;

    if (Array.isArray(response.data)) {
      clients = response.data;
      totalCount = response.data.length;
    } else if (response.data && typeof response.data === 'object') {
      if (Array.isArray(response.data.data)) {
        clients = response.data.data;
        totalCount = response.data.total || response.data.data.length;
      } else if (response.data.clients && Array.isArray(response.data.clients)) {
        clients = response.data.clients;
        totalCount = response.data.total || response.data.clients.length;
      }
    }

    // Transform API clients to frontend Client type
    const transformedClients = clients.map(transformApiClient);

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      data: transformedClients,
      totalCount,
      page,
      pageSize,
      totalPages,
    };
  } catch (error) {
    console.error('Error fetching clients:', error);
    throw error;
  }
};

/**
 * Get client by ID
 */
export const getClientById = async (id: string): Promise<Client> => {
  try {
    console.log(`Fetching client with ID: ${id}`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(API_ENDPOINTS.CLIENTS.GET_BY_ID(id));

    // Transform API client to frontend Client type
    return transformApiClient(response.data);
  } catch (error) {
    console.error(`Error fetching client with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new client
 */
export const createClient = async (clientData: ClientFormData): Promise<Client> => {
  try {
    console.log('Creating new client:', clientData);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.post(API_ENDPOINTS.CLIENTS.CREATE, clientData);

    // Transform API client to frontend Client type
    return transformApiClient(response.data);
  } catch (error) {
    console.error('Error creating client:', error);

    if (axios.isAxiosError(error)) {
      console.error('API error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    throw error;
  }
};

/**
 * Update an existing client
 */
export const updateClient = async (id: string, clientData: ClientFormData): Promise<Client> => {
  try {
    console.log(`Updating client with ID ${id}:`, clientData);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.patch(API_ENDPOINTS.CLIENTS.UPDATE(id), clientData);

    // Transform API client to frontend Client type
    return transformApiClient(response.data);
  } catch (error) {
    console.error(`Error updating client with ID ${id}:`, error);

    if (axios.isAxiosError(error)) {
      console.error('API error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    throw error;
  }
};

/**
 * Delete a client
 */
export const deleteClient = async (id: string): Promise<void> => {
  try {
    console.log(`Deleting client with ID: ${id}`);

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.delete(API_ENDPOINTS.CLIENTS.DELETE(id));

    console.log('Client deleted successfully');
  } catch (error) {
    console.error(`Error deleting client with ID ${id}:`, error);

    if (axios.isAxiosError(error)) {
      console.error('API error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    throw error;
  }
};
