import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
  homeHref?: string;
  showHome?: boolean;
}

/**
 * Breadcrumbs component for navigation context
 * 
 * @example
 * ```tsx
 * <Breadcrumbs
 *   items={[
 *     { label: 'Admin', href: '/admin' },
 *     { label: 'Users', href: '/admin/users' },
 *     { label: 'Edit User', href: '/admin/users/123' },
 *   ]}
 * />
 * ```
 */
export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  items,
  className,
  homeHref = '/',
  showHome = true,
}) => {
  const location = useLocation();
  
  // Add home item if showHome is true
  const allItems = showHome 
    ? [{ label: 'Home', href: homeHref, icon: <Home className="h-4 w-4" /> }, ...items]
    : items;

  return (
    <nav className={cn('flex items-center space-x-1 text-sm', className)}>
      <ol className="flex items-center space-x-1">
        {allItems.map((item, index) => {
          const isLast = index === allItems.length - 1;
          const isActive = location.pathname === item.href;
          
          return (
            <li key={item.href} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground" />
              )}
              
              {isLast || isActive ? (
                <span 
                  className={cn(
                    "font-medium",
                    isActive ? "text-primary" : "text-foreground"
                  )}
                >
                  {item.icon && (
                    <span className="mr-1 inline-flex items-center">{item.icon}</span>
                  )}
                  {item.label}
                </span>
              ) : (
                <Link
                  to={item.href}
                  className={cn(
                    "hover:text-foreground text-muted-foreground transition-colors",
                    "inline-flex items-center"
                  )}
                >
                  {item.icon && (
                    <span className="mr-1 inline-flex items-center">{item.icon}</span>
                  )}
                  {item.label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
