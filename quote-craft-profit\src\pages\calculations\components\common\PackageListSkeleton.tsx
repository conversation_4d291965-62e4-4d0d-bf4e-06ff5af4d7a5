/**
 * Skeleton loading component for package lists
 */
import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

interface PackageListSkeletonProps {
  count?: number;
  showSearch?: boolean;
  variant?: "card" | "list" | "accordion";
}

const PackageListSkeleton: React.FC<PackageListSkeletonProps> = ({
  count = 5,
  showSearch = true,
  variant = "card",
}) => {
  const renderCardSkeleton = (index: number) => (
    <Card key={index} className="animate-pulse">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="space-y-2 flex-1">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-4 w-12" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-24" />
          </div>
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-6 w-24" />
            </div>
            <Skeleton className="h-9 w-28" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderListSkeleton = (index: number) => (
    <div
      key={index}
      className="flex items-center justify-between p-4 border dark:border-gray-700 rounded-lg animate-pulse"
    >
      <div className="flex items-center space-x-4 flex-1">
        <Skeleton className="h-12 w-12 rounded" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-1/3" />
          <Skeleton className="h-3 w-1/4" />
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <div className="text-right space-y-1">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-3 w-12" />
        </div>
        <Skeleton className="h-8 w-20" />
      </div>
    </div>
  );

  const renderAccordionSkeleton = (index: number) => (
    <div
      key={index}
      className="border dark:border-gray-700 rounded-lg animate-pulse"
    >
      <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-5 w-32" />
        </div>
        <Skeleton className="h-4 w-16" />
      </div>
      <div className="p-4 space-y-3">
        {Array.from({ length: 2 }).map((_, subIndex) => (
          <div
            key={subIndex}
            className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded"
          >
            <div className="space-y-2">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-3 w-24" />
            </div>
            <div className="flex items-center space-x-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSkeleton = (index: number) => {
    switch (variant) {
      case "list":
        return renderListSkeleton(index);
      case "accordion":
        return renderAccordionSkeleton(index);
      case "card":
      default:
        return renderCardSkeleton(index);
    }
  };

  return (
    <div className="space-y-4">
      {showSearch && (
        <div className="space-y-3">
          <Skeleton className="h-10 w-full" />
          <div className="flex gap-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-28" />
          </div>
        </div>
      )}

      <div className={variant === "list" ? "space-y-2" : "space-y-4"}>
        {Array.from({ length: count }).map((_, index) => renderSkeleton(index))}
      </div>
    </div>
  );
};

export default PackageListSkeleton;
