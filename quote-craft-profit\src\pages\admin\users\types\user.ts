/**
 * Types for admin users feature
 */

/**
 * Admin user interface - matches the backend AdminUserDto
 */
export interface AdminUser {
  id: string;
  email: string;
  username: string | null;
  full_name: string | null;
  role_name: string | null;
  created_at: string;
  last_sign_in_at: string | null;
  profile_picture_url: string | null;
  status: 'ACTIVE' | 'INACTIVE';
}

/**
 * Role interface for user management
 */
export interface Role {
  id: number;
  role_name: string;
  description: string;
  created_at: string;
}

/**
 * Form data for creating a new user
 */
export interface CreateUserFormData {
  email: string;
  password?: string;
  full_name: string;
  username: string;
  role_id: number;
}

/**
 * Form data for updating an existing user
 */
export interface UpdateUserFormData {
  email?: string;
  password?: string;
  full_name?: string;
  username?: string;
  role_id?: number;
}

/**
 * User status options
 */
export type UserStatus = 'ACTIVE' | 'INACTIVE';

/**
 * User form mode
 */
export type UserFormMode = 'add' | 'edit';
