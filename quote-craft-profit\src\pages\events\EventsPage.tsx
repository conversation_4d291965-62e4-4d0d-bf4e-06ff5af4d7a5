import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { EventsHeader, EventsFilters, EventsList } from './components';
import { useQuery } from '@tanstack/react-query';
import { EventData } from '@/types/types';
import { Event } from '@/types/events';
import { getAllEvents } from '@/services/shared/entities/events';
import { toast } from 'sonner';
import { useDateRange } from '@/hooks/useDateRange';

// Convert API Event to EventData for compatibility with existing components
const convertToEventData = (event: Event): EventData => {
  return {
    id: event.id,
    name: event.name,
    clientName: event.clientName || 'No Client', // Ensure clientName is never empty
    startDate: event.startDate,
    endDate: event.endDate,
    location: event.location || 'No Location', // Ensure location is never empty
    status: event.status || 'draft', // Ensure status is never empty
    // Removed attendees field - this information is tracked in calculation history
    primaryContact: event.primaryContactName || 'No Contact', // Ensure primaryContact is never empty
    notes: event.notes,
  };
};

const EventsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [filterClient, setFilterClient] = useState<string | null>(null);
  const [filterContact, setFilterContact] = useState<string | null>(null);

  // Use centralized date range hook
  const { dateRange: filterDateRange, setDateRange: setFilterDateRange } = useDateRange();

  // Fetch events from the API
  const {
    data: apiEvents = [],
    isLoading,
  } = useQuery({
    queryKey: ['events'],
    queryFn: getAllEvents,
    meta: {
      onError: (error: Error) => {
        console.error('Failed to fetch events:', error);
        toast.error('Failed to load events. Please try again.');
      },
    },
  });

  // Convert API events to EventData format for compatibility with existing components
  const events: EventData[] = apiEvents.map(convertToEventData);

  // Filter events based on search and filters
  const filteredEvents = events.filter((event) => {
    // Search term filter
    if (
      searchTerm &&
      !event.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !event.clientName.toLowerCase().includes(searchTerm.toLowerCase())
    ) {
      return false;
    }

    // Status filter
    if (filterStatus && event.status !== filterStatus) {
      return false;
    }

    // Client filter
    if (filterClient && event.clientName !== filterClient) {
      return false;
    }

    // Contact filter
    if (filterContact && event.primaryContact !== filterContact) {
      return false;
    }

    // Date range filter
    if (filterDateRange?.from || filterDateRange?.to) {
      const eventDate = new Date(event.startDate);

      if (filterDateRange.from && eventDate < filterDateRange.from) {
        return false;
      }

      if (filterDateRange.to && eventDate > filterDateRange.to) {
        return false;
      }
    }

    return true;
  });

  // Get unique clients and contacts for filter options, filtering out empty values
  const clientOptions = [
    ...new Set(events.map((event) => event.clientName).filter(Boolean)),
  ];
  const contactOptions = [
    ...new Set(events.map((event) => event.primaryContact).filter(Boolean)),
  ];
  const statusOptions = [...new Set(events.map((event) => event.status).filter(Boolean))];

  return (
    <MainLayout>
      <div className='space-y-6'>
        <EventsHeader />

        <EventsFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filterStatus={filterStatus}
          setFilterStatus={setFilterStatus}
          filterClient={filterClient}
          setFilterClient={setFilterClient}
          filterContact={filterContact}
          setFilterContact={setFilterContact}
          filterDateRange={filterDateRange}
          setFilterDateRange={setFilterDateRange}
          statusOptions={statusOptions}
          clientOptions={clientOptions}
          contactOptions={contactOptions}
        />

        <EventsList events={filteredEvents} isLoading={isLoading} />
      </div>
    </MainLayout>
  );
};

export default EventsPage;
