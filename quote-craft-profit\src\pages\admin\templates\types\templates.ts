import { z } from "zod";
import { optionalDateRangeSchema } from "@/schemas/common";
import {
  formatDateTimeForSubmission,
  DEFAULT_TIMEZONE,
} from "@/lib/timezone-utils";

// Package selection within a template
export interface PackageSelection {
  package_id: string;
  option_ids: string[];
  item_quantity: Record<string, number>;
  duration_days: Record<string, number>;
}

// Template data structure from API (aligned with backend DTOs)
export interface Template {
  id: string;
  name: string;
  description: string | null;
  event_type_id: string | null; // Updated to use event_type_id
  city_id: string | null;
  currency_id: string | null;
  attendees: number | null;
  template_start_date: string | null;
  template_end_date: string | null;
  category_id: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  is_public: boolean;
  is_deleted: boolean;
  package_selections: PackageSelection[];
  venue_ids?: string[];
  total_price?: number; // Calculated total price (frontend only)
  taxes?: unknown; // Tax configuration from source calculation
  discount?: unknown; // Discount configuration from source calculation
}

// Request payload for creating a template from a calculation
export interface CreateTemplateFromCalculationRequest {
  calculationId: string;
  name: string;
  description: string;
  eventTypeId: string; // Changed from eventType to eventTypeId
  cityId: string;
  currencyId: string;
  attendees: number;
  templateStartDate?: string;
  templateEndDate?: string;
  venueIds?: string[]; // Added venue IDs
}

// Request payload for updating a template
export interface UpdateTemplateRequest {
  name: string;
  description: string | null;
  event_type_id: string | null; // Changed from event_type to event_type_id
  attendees?: number;
  template_start_date: string;
  template_end_date: string;
  is_public: boolean;
}

// Zod schema for template creation form validation
export const createTemplateSchema = z.object({
  name: z.string().min(2, "Template name must be at least 2 characters"),
  description: z.string().optional(),
  eventType: z.string().optional(),
  cityId: z.string().uuid("Invalid city ID").optional(),
  currencyId: z.string().uuid("Invalid currency ID").optional(),
  attendees: z.number().int().min(1, "Attendees must be at least 1").optional(),
  templateStartDate: z
    .string()
    .regex(
      /^(\d{4}-\d{2}-\d{2})?$/,
      "Date must be in YYYY-MM-DD format or empty"
    )
    .optional(),
  templateEndDate: z
    .string()
    .regex(
      /^(\d{4}-\d{2}-\d{2})?$/,
      "Date must be in YYYY-MM-DD format or empty"
    )
    .optional(),
  venueIds: z.array(z.string().uuid()).optional(),
});

// New template form schema with date range picker
export const templateFormSchema = z.object({
  name: z.string().min(2, "Template name must be at least 2 characters"),
  description: z.string().nullable(),
  event_type_id: z.string().uuid("Invalid event type").nullable(), // Updated to use event_type_id
  attendees: z.number().int().min(1, "Attendees must be at least 1").optional(),
  dateRange: optionalDateRangeSchema,
  is_public: z.boolean(),
});

// Legacy template form schema (for backward compatibility - can be removed after migration)
export const updateTemplateSchema = templateFormSchema;

// Template form data interface
export interface TemplateFormData {
  name: string;
  description?: string | null;
  event_type_id?: string | null; // Updated to use event_type_id
  attendees?: number;
  dateRange?: { from?: Date; to?: Date };
  is_public: boolean;
}

// Transformation utilities for template date range handling

// Function to transform TemplateFormData (with date range) to API request
export const transformTemplateFormDataToApiRequest = (
  templateData: Partial<TemplateFormData>,
  timezone: string = DEFAULT_TIMEZONE
): UpdateTemplateRequest => {
  return {
    name: templateData.name || "",
    description: templateData.description || null,
    event_type_id: templateData.event_type_id || null, // Updated to use event_type_id
    attendees: templateData.attendees,
    template_start_date: templateData.dateRange?.from
      ? formatDateTimeForSubmission(templateData.dateRange.from, timezone)
      : "",
    template_end_date: templateData.dateRange?.to
      ? formatDateTimeForSubmission(templateData.dateRange.to, timezone)
      : "",
    is_public: templateData.is_public || false,
  };
};

// Function to transform Template to TemplateFormData (for editing)
export const transformTemplateToFormData = (
  template: Template
): TemplateFormData => {
  return {
    name: template.name,
    description: template.description,
    event_type_id: template.event_type_id, // Updated to use event_type_id
    attendees: template.attendees || undefined,
    dateRange: {
      from: template.template_start_date
        ? new Date(template.template_start_date)
        : undefined,
      to: template.template_end_date
        ? new Date(template.template_end_date)
        : undefined,
    },
    is_public: template.is_public,
  };
};
