import { Module } from '@nestjs/common';
import { ClientsService } from './clients.service';
import { ClientsController } from './clients.controller';
import { AuthModule } from '../auth/auth.module'; // Needed for JwtAuthGuard

@Module({
  imports: [AuthModule], // Import AuthModule to enable @UseGuards(JwtAuthGuard)
  controllers: [ClientsController],
  providers: [ClientsService],
})
export class ClientsModule {}
