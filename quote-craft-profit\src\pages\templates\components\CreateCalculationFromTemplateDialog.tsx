import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import {
  showSuccess,
  showError,
  showLoading,
  dismissToast,
} from "@/lib/notifications";
import {
  Loader2,
  FileText,
  Calendar as CalendarIcon,
  Users,
  MapPin,
  PlusCircle,
  Building2,
  CalendarDays,
} from "lucide-react";
import { format } from "date-fns";
import {
  createCalculationFromTemplate,
  getCalculationSuggestions,
  validateTemplateCustomization,
  CreateCalculationFromTemplateRequest,
} from "@/services/calculations";
import { TemplateDetailDto } from "@/services/templates";
import { getAllClients } from "@/services/shared/entities/clients";
import { getAllEvents } from "@/services/shared/entities/events";
import { getAllCities } from "@/services/shared/entities/cities";
import { getVenuesByCity } from "@/services/shared/entities/venues";
import { ClientFormDialog } from "@/pages/clients/components";
import { EventFormDialog } from "@/pages/events/components";
import { useEntityCreationCallbacks } from "@/hooks/useEntityCreationCallbacks";

interface CreateCalculationFromTemplateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  template: TemplateDetailDto | null;
}

// Validation schema
const createCalculationSchema = z
  .object({
    name: z.string().min(2, "Calculation name must be at least 2 characters"),
    dateRange: z
      .object({
        from: z.date().optional(),
        to: z.date().optional(),
      })
      .optional(),
    attendees: z
      .number()
      .int()
      .min(1, "Attendees must be at least 1")
      .optional(),
    clientId: z.string().optional(),
    eventId: z.string().optional(),
    cityId: z.string().optional(),
    venueIds: z.array(z.string()).optional(),
    notes: z
      .string()
      .max(1000, "Notes must be less than 1000 characters")
      .optional(),
  })
  .refine(
    (data) => {
      if (data.dateRange?.from && data.dateRange?.to) {
        return data.dateRange.from <= data.dateRange.to;
      }
      return true;
    },
    {
      message: "Event start date must be before end date",
      path: ["dateRange"],
    }
  );

type FormValues = z.infer<typeof createCalculationSchema>;

const CreateCalculationFromTemplateDialog: React.FC<
  CreateCalculationFromTemplateDialogProps
> = ({ isOpen, onClose, template }) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isClientFormOpen, setIsClientFormOpen] = useState(false);
  const [isEventFormOpen, setIsEventFormOpen] = useState(false);
  const [selectedCityId, setSelectedCityId] = useState<string>("");

  // Fetch clients
  const { data: clientsResult } = useQuery({
    queryKey: ["clients"],
    queryFn: () => getAllClients(),
    meta: {
      onError: () => {
        showError("Failed to load clients");
      },
    },
  });

  // Fetch events
  const { data: events = [] } = useQuery({
    queryKey: ["events"],
    queryFn: getAllEvents,
    meta: {
      onError: () => {
        toast.error("Failed to load events");
      },
    },
  });

  // Fetch cities
  const { data: cities = [] } = useQuery({
    queryKey: ["cities"],
    queryFn: getAllCities,
    meta: {
      onError: () => {
        toast.error("Failed to load cities");
      },
    },
  });

  // Fetch venues based on selected city
  const { data: venues = [] } = useQuery({
    queryKey: ["venues", selectedCityId],
    queryFn: () => getVenuesByCity(selectedCityId),
    enabled: !!selectedCityId,
    meta: {
      onError: () => {
        toast.error("Failed to load venues");
      },
    },
  });

  // Extract clients array from the paginated result
  const clients = clientsResult?.data || [];

  const form = useForm<FormValues>({
    resolver: zodResolver(createCalculationSchema),
    defaultValues: {
      name: "",
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
      attendees: undefined,
      clientId: "none",
      eventId: "none",
      cityId: "",
      venueIds: [],
      notes: "",
    },
  });

  // Use centralized entity creation callbacks
  const { handleClientCreated, handleEventCreated } =
    useEntityCreationCallbacks({
      form,
      clientFieldName: "clientId",
      eventFieldName: "eventId",
    });

  // Update form with template suggestions when template changes
  useEffect(() => {
    if (template && isOpen) {
      const suggestions = getCalculationSuggestions(template);
      const today = new Date();

      form.reset({
        name: suggestions.name,
        dateRange: {
          from: today,
          to: today,
        },
        attendees: suggestions.attendees,
        clientId: "",
        eventId: "",
        cityId: template.city_id || "",
        venueIds: template.venue_ids || [],
        notes: suggestions.notes,
      });

      // Set selected city for venue fetching
      if (template.city_id) {
        setSelectedCityId(template.city_id);
      }
    }
  }, [template, isOpen, form]);

  const onSubmit = async (values: FormValues) => {
    if (!template) return;

    setIsSubmitting(true);
    try {
      // Prepare the request data
      const requestData: CreateCalculationFromTemplateRequest = {
        name: values.name,
        eventStartDate: values.dateRange?.from
          ? format(values.dateRange.from, "yyyy-MM-dd")
          : undefined,
        eventEndDate: values.dateRange?.to
          ? format(values.dateRange.to, "yyyy-MM-dd")
          : undefined,
        attendees: values.attendees,
        clientId:
          values.clientId && values.clientId !== "none"
            ? values.clientId
            : undefined,
        eventId:
          values.eventId && values.eventId !== "none"
            ? values.eventId
            : undefined,
        cityId: values.cityId || undefined,
        venueIds:
          values.venueIds && values.venueIds.length > 0
            ? values.venueIds
            : undefined,
        notes: values.notes || undefined,
      };

      // Validate the data
      const validation = validateTemplateCustomization(requestData);
      if (!validation.isValid) {
        showError(`Validation failed`, {
          category: "template",
          description: validation.errors.join(", "),
        });
        return;
      }

      // Create the calculation
      const result = await createCalculationFromTemplate(
        template.id,
        requestData
      );

      showSuccess("Calculation created successfully!", {
        category: "template",
        description: `New calculation "${requestData.name}" has been created from template.`,
      });
      onClose();

      // Navigate to the new calculation
      navigate(`/calculations/${result.id}`);
    } catch (error) {
      console.error("Error creating calculation from template:", error);
      showError("Failed to create calculation", {
        category: "template",
        description: "Please check your inputs and try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Create Calculation from Template
          </DialogTitle>
          <DialogDescription>
            {template ? (
              <>
                Customize your calculation based on the template "
                {template.name}"
              </>
            ) : (
              "Create a new calculation from the selected template"
            )}
          </DialogDescription>
        </DialogHeader>

        {template && (
          <ScrollArea className="flex-1 overflow-y-auto">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4 px-1 pb-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Calculation Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter calculation name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dateRange"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>
                        <CalendarIcon className="h-4 w-4 inline mr-1" />
                        Event Date Range
                      </FormLabel>
                      <FormControl>
                        <DateRangePicker
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Pick a date range"
                          numberOfMonths={2}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="attendees"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <Users className="h-4 w-4 inline mr-1" />
                        Number of Attendees (From Template)
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          value={field.value || template?.attendees || ""}
                          disabled
                          className="bg-muted"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Client Selection */}
                <FormField
                  control={form.control}
                  name="clientId"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between items-center">
                        <FormLabel>
                          <Building2 className="h-4 w-4 inline mr-1" />
                          Client (Optional)
                        </FormLabel>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setIsClientFormOpen(true)}
                          className="h-8 px-2 text-xs"
                        >
                          <PlusCircle className="h-3.5 w-3.5 mr-1" />
                          Add New
                        </Button>
                      </div>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a client" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {clients
                            .filter(
                              (client) => client.id && client.id.trim() !== ""
                            ) // Filter out clients with empty IDs
                            .map((client) => (
                              <SelectItem key={client.id} value={client.id}>
                                {client.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Event Selection */}
                <FormField
                  control={form.control}
                  name="eventId"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between items-center">
                        <FormLabel>
                          <CalendarDays className="h-4 w-4 inline mr-1" />
                          Associated Event (Optional)
                        </FormLabel>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setIsEventFormOpen(true)}
                          className="h-8 px-2 text-xs"
                        >
                          <PlusCircle className="h-3.5 w-3.5 mr-1" />
                          Add New
                        </Button>
                      </div>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an event" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {events
                            .filter(
                              (event) => event.id && event.id.trim() !== ""
                            ) // Filter out events with empty IDs
                            .map((event) => (
                              <SelectItem key={event.id} value={event.id}>
                                {event.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* City Selection - Read Only */}
                <FormField
                  control={form.control}
                  name="cityId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City (From Template)</FormLabel>
                      <FormControl>
                        <Input
                          value={
                            cities.find((city) => city.id === field.value)
                              ?.name ||
                            template?.city_name ||
                            "Not specified"
                          }
                          disabled
                          className="bg-muted"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Venue Selection - Read Only */}
                {template &&
                  template.venue_ids &&
                  template.venue_ids.length > 0 && (
                    <div className="space-y-2">
                      <FormLabel>Venues (From Template)</FormLabel>
                      <div className="p-3 bg-muted rounded-md border">
                        <div className="flex items-center text-sm font-medium mb-2">
                          <MapPin className="h-4 w-4 mr-1" />
                          Associated Venues ({template.venue_ids.length})
                        </div>
                        <div className="text-xs text-muted-foreground">
                          These venues are inherited from the template and
                          cannot be modified.
                        </div>
                      </div>
                    </div>
                  )}

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Add any additional notes for this calculation..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </ScrollArea>
        )}

        {template && (
          <DialogFooter className="flex-shrink-0 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              onClick={form.handleSubmit(onSubmit)}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Create Calculation
                </>
              )}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>

      {/* Client Form Dialog */}
      <ClientFormDialog
        open={isClientFormOpen}
        onOpenChange={(open) => {
          setIsClientFormOpen(open);
          if (!open) {
            // Refetch clients when dialog is closed
            queryClient.invalidateQueries({ queryKey: ["clients"] });
          }
        }}
        mode="create"
        onClientCreated={handleClientCreated}
      />

      {/* Event Form Dialog */}
      <EventFormDialog
        isOpen={isEventFormOpen}
        onClose={() => {
          setIsEventFormOpen(false);
          // Refetch events when dialog is closed
          queryClient.invalidateQueries({ queryKey: ["events"] });
        }}
        disableNavigation={true}
        onEventCreated={handleEventCreated}
      />
    </Dialog>
  );
};

export default CreateCalculationFromTemplateDialog;
