/**
 * Query keys for calculation-related queries
 */

export const CALCULATION_QUERY_KEYS = {
  /**
   * Key for a specific calculation
   * @param id - The calculation ID
   * @returns Query key for the calculation
   */
  calculation: (id: string) => ['calculation', id],

  /**
   * Key for all calculations
   * @returns Query key for all calculations
   */
  calculations: () => ['calculations'],

  /**
   * Key for packages by category
   * @param calculationId - The calculation ID
   * @returns Query key for packages by category
   */
  packagesByCategory: (calculationId: string) => ['packagesByCategory', calculationId],

  /**
   * Key for line items
   * @param calculationId - The calculation ID
   * @returns Query key for line items
   */
  lineItems: (calculationId: string) => ['lineItems', calculationId],

  /**
   * Key for a specific line item
   * @param calculationId - The calculation ID
   * @param lineItemId - The line item ID
   * @returns Query key for the line item
   */
  lineItem: (calculationId: string, lineItemId: string) => ['lineItem', calculationId, lineItemId],

  /**
   * Key for line item options
   * @param calculationId - The calculation ID
   * @param lineItemId - The line item ID
   * @returns Query key for line item options
   */
  lineItemOptions: (calculationId: string, lineItemId: string) => ['lineItemOptions', calculationId, lineItemId],
};
