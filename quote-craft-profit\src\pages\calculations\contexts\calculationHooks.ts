/**
 * Custom hooks for calculation context
 * Separated from CalculationContext.tsx to satisfy React Fast Refresh requirements
 */
import { useContext, useMemo } from "react";
import { CalculationContext, CalculationContextValue } from "./context";
import { CALCULATION_CONTEXT_ERROR_MESSAGE } from "./calculationConstants";
import { CalculationDetailState } from "../types/calculationState";

/**
 * Hook to access calculation context
 * Provides type-safe access to calculation state and actions
 */
export const useCalculationContext = (): CalculationContextValue => {
  const context = useContext(CalculationContext);

  if (!context) {
    throw new Error(CALCULATION_CONTEXT_ERROR_MESSAGE);
  }

  return context;
};

/**
 * Hook to access only calculation state
 * Useful for components that only need read access
 */
export const useCalculationState = (): CalculationDetailState => {
  const { state } = useCalculationContext();
  return state;
};

// PHASE 5 CLEANUP: Removed deprecated useCalculationActions hook
// Use useCalculationFunctions instead

/**
 * Hook to access calculation ID
 * Useful for components that only need the calculation ID
 */
export const useCalculationId = (): string => {
  const { calculationId } = useCalculationContext();
  return calculationId;
};

/**
 * PHASE 1 OPTIMIZATION: Consolidated hooks for better developer experience
 * These new hooks reduce the number of hooks from 13 to 5 while maintaining granular access
 */

/**
 * Consolidated hook for UI-related data (merges UIState, EditState, LoadingState)
 * Reduces cognitive load by grouping related UI state together
 */
export const useCalculationUIData = () => {
  const { state } = useCalculationContext();

  return useMemo(
    () => ({
      // From useCalculationUIState
      expandedCategories: state.expandedCategories,
      packageForms: state.packageForms,
      isEditMode: state.isEditMode,
      isSaving: state.isSaving,
      isAddingCustomItem: state.isAddingCustomItem,
      isEditingLineItem: state.isEditingLineItem,
      currentEditingLineItem: state.currentEditingLineItem,
      isDeleting: state.isDeleting,

      // From useCalculationEditState
      editedName: state.editedName,
      editedEventType: state.editedEventType,
      editedNotes: state.editedNotes,
      editedAttendees: state.editedAttendees,
      dateRange: state.dateRange,

      // From useCalculationLoadingState
      isLoading: state.isLoading,
      isLoadingPackages: state.isLoadingPackages,
      isError: state.isError,
      isPackagesError: state.isPackagesError,
    }),
    [
      // UI State dependencies
      state.expandedCategories,
      state.packageForms,
      state.isEditMode,
      state.isSaving,
      state.isAddingCustomItem,
      state.isEditingLineItem,
      state.currentEditingLineItem,
      state.isDeleting,
      // Edit State dependencies
      state.editedName,
      state.editedEventType,
      state.editedNotes,
      state.editedAttendees,
      state.dateRange,
      // Loading State dependencies
      state.isLoading,
      state.isLoadingPackages,
      state.isError,
      state.isPackagesError,
    ]
  );
};

/**
 * Consolidated hook for data and utilities (merges FinancialData, Utils)
 * Groups financial data with utility functions for convenience
 */
export const useCalculationDataAndUtils = () => {
  const { state } = useCalculationContext();

  return useMemo(
    () => ({
      // From useCalculationFinancialData
      taxes: state.taxes,
      discount: state.discount,
      financialCalculations: state.financialCalculations,

      // From useCalculationUtils
      formatCurrency: state.formatCurrency,
      formatDate: state.formatDate,
    }),
    [
      state.taxes,
      state.discount,
      state.financialCalculations,
      state.formatCurrency,
      state.formatDate,
    ]
  );
};

/**
 * Consolidated hook for all function-based operations
 * Organizes functions by domain for better discoverability
 */
export const useCalculationFunctions = () => {
  const { state, actions } = useCalculationContext();

  return useMemo(
    () => ({
      // State setters (from useCalculationStateSetters)
      setters: {
        setIsAddingCustomItem: state.setIsAddingCustomItem,
        setIsEditingLineItem: state.setIsEditingLineItem,
        setEditedName: state.setEditedName,
        setEditedEventType: state.setEditedEventType,
        setEditedNotes: state.setEditedNotes,
        setEditedAttendees: state.setEditedAttendees,
        setDateRange: state.setDateRange,
      },

      // Package functions (from useCalculationPackageFunctions)
      packages: {
        toggleCategory: state.toggleCategory,
        handleQuantityChange: state.handleQuantityChange,
        handleItemQuantityBasisChange: state.handleItemQuantityBasisChange,
        handleOptionToggle: state.handleOptionToggle,
        cleanupPackageForm: state.cleanupPackageForm,
        cleanupMultiplePackageForms: state.cleanupMultiplePackageForms,
        resetAllPackageForms: state.resetAllPackageForms,
        getPackageFormData: state.getPackageFormData,
        handleAddToCalculation: state.handleAddToCalculation,
      },

      // Line item functions (from useCalculationLineItemFunctions)
      lineItems: {
        handleAddCustomItem: state.handleAddCustomItem,
        handleEditLineItem: state.handleEditLineItem,
        handleUpdateLineItem: state.handleUpdateLineItem,
        handleRemoveLineItem: state.handleRemoveLineItem,
      },

      // Edit functions (from useCalculationEditFunctions)
      editing: {
        handleToggleEditMode: state.handleToggleEditMode,
        handleSaveChanges: state.handleSaveChanges,
      },

      // Financial functions (from useCalculationTaxDiscountFunctions)
      financial: {
        addTax: state.addTax,
        updateDiscount: state.updateDiscount,
      },

      // Actions (from useCalculationActions)
      actions: {
        handleStatusChange: actions.handleStatusChange,
        handleDelete: actions.handleDelete,
        handleNavigateBack: actions.handleNavigateBack,
        handleNavigateToList: actions.handleNavigateToList,
      },
    }),
    [
      // State setters dependencies
      state.setIsAddingCustomItem,
      state.setIsEditingLineItem,
      state.setEditedName,
      state.setEditedEventType,
      state.setEditedNotes,
      state.setEditedAttendees,
      state.setDateRange,
      // Package functions dependencies
      state.toggleCategory,
      state.handleQuantityChange,
      state.handleItemQuantityBasisChange,
      state.handleOptionToggle,
      state.cleanupPackageForm,
      state.cleanupMultiplePackageForms,
      state.resetAllPackageForms,
      state.getPackageFormData,
      state.handleAddToCalculation,
      // Line item functions dependencies
      state.handleAddCustomItem,
      state.handleEditLineItem,
      state.handleUpdateLineItem,
      state.handleRemoveLineItem,
      // Edit functions dependencies
      state.handleToggleEditMode,
      state.handleSaveChanges,
      // Financial functions dependencies
      state.addTax,
      state.updateDiscount,
      // Actions dependencies
      actions.handleStatusChange,
      actions.handleDelete,
      actions.handleNavigateBack,
      actions.handleNavigateToList,
    ]
  );
};

/**
 * LEGACY HOOKS: Maintained for backward compatibility during Phase 1
 * These hooks will be marked as deprecated and removed in Phase 4
 *
 * Selective state hooks for specific parts of the state
 * These hooks help prevent unnecessary re-renders by only subscribing to specific state slices
 */

/**
 * Hook for calculation core data
 * NOTE: This hook is kept unchanged as specified in Phase 1 requirements
 */
export const useCalculationCoreData = () => {
  const { state } = useCalculationContext();

  return useMemo(() => {
    return {
      calculation: state.calculation,
      lineItems: state.lineItems,
      categories: state.categories,
      packagesByCategory: state.packagesByCategory,
    };
  }, [
    state.calculation,
    state.lineItems,
    state.categories,
    state.packagesByCategory,
  ]);
};

// PHASE 5 CLEANUP: Removed deprecated hooks
// - useCalculationUIState (use useCalculationUIData instead)
// - useCalculationEditState (use useCalculationUIData instead)

// PHASE 5 CLEANUP: Removed deprecated hooks
// - useCalculationLoadingState (use useCalculationUIData instead)
// - useCalculationFinancialData (use useCalculationDataAndUtils instead)
// - useCalculationUtils (use useCalculationDataAndUtils instead)

// PHASE 5 CLEANUP: Removed deprecated hooks
// - useCalculationStateSetters (use useCalculationFunctions instead)
// - useCalculationPackageFunctions (use useCalculationFunctions instead)

// PHASE 5 CLEANUP: Removed deprecated hooks
// - useCalculationLineItemFunctions (use useCalculationFunctions instead)
// - useCalculationEditFunctions (use useCalculationFunctions instead)
// - useCalculationTaxDiscountFunctions (use useCalculationFunctions instead)

// End of file
