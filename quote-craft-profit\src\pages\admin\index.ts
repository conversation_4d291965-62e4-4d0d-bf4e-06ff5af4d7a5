/**
 * Admin Feature
 * 
 * This module provides admin functionality including user management,
 * package management, category management, and system settings.
 */

// Export main page components
export { default as AdminDashboardPage } from './dashboard/AdminDashboardPage';
export { default as CataloguePage } from './CataloguePage';

// Export feature modules
export * from './categories';
export * from './cities';
export * from './divisions';
export * from './packages';
export * from './settings';
export * from './templates';
export * from './users';
export * from './venues';
