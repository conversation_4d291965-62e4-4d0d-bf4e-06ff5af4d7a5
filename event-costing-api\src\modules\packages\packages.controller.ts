// src/modules/packages/packages.controller.ts
import {
  Controller,
  Get,
  Query,
  Param,
  ParseUUIDPipe,
  ParseArrayPipe,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { PackagesService } from './packages.service';
import { ListPackageVariationsDto } from './dto/list-package-variations.dto';
import { PackageVariationDto } from './dto/package-variation.dto';
import { ListPackageOptionsDto } from './dto/list-package-options.dto';
import { PackageOptionDetailDto } from './dto/package-option-detail.dto';
import { BatchPackageOptionsResponseDto } from './dto/batch-package-options-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
import {
  ApiTags,
  ApiOkResponse,
  ApiBearerAuth,
  ApiParam,
  <PERSON>pi<PERSON><PERSON><PERSON>,
  <PERSON>pi<PERSON>peration,
} from '@nestjs/swagger';

@ApiTags('Packages Catalogue')
@ApiBearerAuth()
@Controller('packages') // Base path for package browsing
@UseGuards(JwtAuthGuard) // Protect catalogue browsing endpoints
export class PackagesController {
  private readonly logger = new Logger(PackagesController.name);

  constructor(private readonly packagesService: PackagesService) {}

  // Endpoint changed to align with plan: /packages/variations
  @Get('variations')
  @ApiOkResponse({
    description: 'Returns a paginated list of package variations',
    type: () => PaginatedResponseDto<PackageVariationDto>,
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    type: String,
    description: 'Filter by category ID',
  })
  @ApiQuery({
    name: 'cityId',
    required: false,
    type: String,
    description: 'Filter by city ID',
  })
  @ApiQuery({
    name: 'venueId',
    required: false,
    type: String,
    description: 'Filter by venue ID',
  })
  @ApiQuery({
    name: 'venueIds',
    required: false,
    type: [String],
    isArray: true,
    description: 'Filter by multiple venue IDs',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for package name',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['name', 'price', 'category'],
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort direction',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items to return',
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Number of items to skip',
  })
  async findVariations(
    @Query() queryDto: ListPackageVariationsDto,
  ): Promise<PaginatedResponseDto<PackageVariationDto>> {
    this.logger.log(
      `Fetching package variations with query: ${JSON.stringify(queryDto)}`,
    );
    return this.packagesService.findVariations(queryDto);
  }

  // Endpoint changed to align with plan: /packages/variations/{packageId}/options
  @Get('variations/:packageId/options')
  @ApiParam({ name: 'packageId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ type: [PackageOptionDetailDto] })
  async findOptions(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Query() queryDto: ListPackageOptionsDto, // Query DTO for currencyId
  ): Promise<PackageOptionDetailDto[]> {
    this.logger.log(
      `Fetching options for package ${packageId}, currency ${queryDto.currencyId}`,
    );
    return this.packagesService.findOptions(packageId, queryDto.currencyId);
  }

  // New endpoint to handle direct package options requests with optional parameters
  @Get(':packageId/options')
  @ApiParam({ name: 'packageId', type: 'string', format: 'uuid' })
  @ApiOkResponse({ type: [PackageOptionDetailDto] })
  async findPackageOptions(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Query('currencyId') currencyId?: string,
    @Query('venueId') venueId?: string,
  ): Promise<PackageOptionDetailDto[]> {
    this.logger.log(
      `Fetching options for package ${packageId}${currencyId ? `, currency ${currencyId}` : ''}${venueId ? `, venue ${venueId}` : ''} (direct endpoint)`,
    );
    return this.packagesService.findOptions(packageId, currencyId, venueId);
  }

  // Endpoint for batch package options
  @Get('batch-options')
  @ApiOperation({
    summary: 'Get options for multiple packages in a single request',
  })
  @ApiQuery({
    name: 'packageIds',
    type: [String],
    isArray: true,
    description: 'Array of package IDs to get options for',
  })
  @ApiQuery({
    name: 'currencyId',
    required: true,
    type: String,
    description: 'Currency ID for pricing',
  })
  @ApiQuery({
    name: 'venueId',
    required: false,
    type: String,
    description: 'Optional venue ID for venue-specific options',
  })
  @ApiOkResponse({ type: BatchPackageOptionsResponseDto })
  async getBatchPackageOptions(
    @Query('packageIds', new ParseArrayPipe({ items: String, separator: ',' }))
    packageIds: string[],
    @Query('currencyId') currencyId: string,
    @Query('venueId') venueId?: string,
  ): Promise<BatchPackageOptionsResponseDto> {
    this.logger.log(
      `Getting batch options for ${packageIds.length} packages, currency ${currencyId}, venue ${venueId || 'any'}`,
    );
    return this.packagesService.getBatchPackageOptions(
      packageIds,
      currencyId,
      venueId,
    );
  }
}
