/**
 * Hook for managing package form state
 */
import { useState } from 'react';
import { PackageFormState, QuantityBasisEnum } from '@/types/calculation';
import { calculatePackagePrice } from '@/pages/calculations/utils/calculationUtils';
import { debug } from '@/lib/debugUtils';

/**
 * Hook for managing package form state (quantity, item_quantity_basis, options)
 *
 * @returns State and functions for managing package forms
 */
export function usePackageForms() {
  const [packageForms, setPackageForms] = useState<PackageFormState>({});

  // Handle quantity changes
  const handleQuantityChange = (packageId: string, value: number) => {
    setPackageForms((prev) => ({
      ...prev,
      [packageId]: {
        ...(prev[packageId] || {
          quantity: 1,
          days: 1,
          item_quantity_basis: 1,
          selectedOptions: [],
        }),
        quantity: Math.max(1, value),
      },
    }));
  };

  // Handle item_quantity_basis changes (second input field)
  const handleItemQuantityBasisChange = (packageId: string, value: number) => {
    setPackageForms((prev) => ({
      ...prev,
      [packageId]: {
        ...(prev[packageId] || {
          quantity: 1,
          days: 1,
          item_quantity_basis: 1,
          selectedOptions: [],
        }),
        // Update both days (for backward compatibility) and item_quantity_basis
        days: Math.max(1, value),
        item_quantity_basis: Math.max(1, value),
      },
    }));
  };

  // handleDaysChange function has been removed in favor of handleItemQuantityBasisChange

  // Handle option toggling
  const handleOptionToggle = (
    packageId: string,
    optionId: string,
    isSelected: boolean,
  ) => {
    setPackageForms((prev) => {
      const currentPackage = prev[packageId] || {
        quantity: 1,
        days: 1,
        item_quantity_basis: 1,
        selectedOptions: [],
      };
      const currentSelectedOptions = currentPackage.selectedOptions || [];

      let newSelectedOptions: string[];
      if (isSelected) {
        // Add option if it's not already selected
        newSelectedOptions = [...currentSelectedOptions, optionId];
      } else {
        // Remove option if it's selected
        newSelectedOptions = currentSelectedOptions.filter((id) => id !== optionId);
      }

      return {
        ...prev,
        [packageId]: {
          ...currentPackage,
          selectedOptions: newSelectedOptions,
        },
      };
    });
  };

  // Calculate total price for a package
  const calculatePackageTotalPrice = (
    packageId: string,
    basePrice: string | number,
    quantityBasis: QuantityBasisEnum | undefined,
    packagesByCategory: any[],
  ) => {
    const {
      quantity = 1,
      item_quantity_basis = 1,
      selectedOptions = [],
    } = packageForms[packageId] || {};

    // Find the package in packagesByCategory to get its options
    let packageOptions: any[] = [];
    let currencySymbol = 'Rp';

    if (packagesByCategory) {
      // Find the package and its options
      for (const category of packagesByCategory) {
        const pkg = category.packages.find((p: any) => p.id === packageId);
        if (pkg) {
          if (pkg.options) {
            packageOptions = pkg.options;
          }
          if (pkg.currencySymbol) {
            currencySymbol = pkg.currencySymbol;
          }
          break;
        }
      }
    }

    // Use the utility function to calculate the total price
    const selectedPackageOptions = packageOptions.filter((opt) =>
      selectedOptions.includes(opt.id),
    );
    const { totalPrice } = calculatePackagePrice(
      basePrice,
      quantity,
      item_quantity_basis,
      quantityBasis,
      selectedPackageOptions,
      currencySymbol,
    );

    return totalPrice;
  };

  return {
    packageForms,
    handleQuantityChange,
    handleItemQuantityBasisChange,
    handleOptionToggle,
    calculatePackageTotalPrice,
  };
}
