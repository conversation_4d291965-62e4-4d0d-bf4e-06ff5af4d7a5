import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import {
  ChevronLeft,
  Save,
  Package as PackageIcon,
  GitFork,
} from "lucide-react";
import AdminLayout from "@/components/layout/AdminLayout";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";

// Import components from the feature's components directory
import { PackageDetailsForm } from "./components/form/PackageDetailsForm";
import { PackageOptions } from "./components/detail/PackageOptions";
import { PackageDependencies } from "./components/detail/PackageDependencies";
import { PackageErrorBoundary, FormSectionSkeleton } from "./components/shared";

// Import types and services from the feature's directories
import { PackageFormValues } from "./types/package";
import {
  getPackageById,
  savePackage,
} from "../../../services/admin/packages/packageService";
import { usePackageForm } from "./hooks";

const EditPackagePage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("details");
  const [isSaving, setIsSaving] = useState(false);

  // Fetch package details with caching
  const {
    data: packageData,
    isLoading: isLoadingPackage,
    isError,
  } = useQuery({
    queryKey: ["package", id],
    queryFn: () => getPackageById(id || ""),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes (renamed from cacheTime in newer versions)
    meta: {
      onError: () => {
        toast.error("Failed to load package details");
        navigate("/admin/packages");
      },
    },
  });

  // Use the package form hook with package data
  const {
    form,
    categories,
    divisions,
    cities,
    currencies,
    isLoading: isLoadingFormData,
  } = usePackageForm({
    packageData,
    enabled: !!id,
  });

  const isLoading = isLoadingPackage || isLoadingFormData;

  const handleSubmit = async (values: PackageFormValues) => {
    try {
      if (!id) return;

      // Set saving state to true
      setIsSaving(true);

      // Show loading toast
      const loadingToastId = toast.loading("Updating package...");

      await savePackage({
        id,
        name: values.name,
        description: values.description,
        categoryId: values.categoryId || undefined,
        divisionId: values.divisionId || undefined,
        cityIds: values.cityIds || [],
        venueIds: values.enableVenues ? values.venueIds || [] : [],
        enableVenues: values.enableVenues,
        quantityBasis: values.quantityBasis,
        isDeleted: !values.isActive, // Convert from isActive to isDeleted
        price: values.price ? parseFloat(values.price) : undefined,
        unitBaseCost: values.unitBaseCost
          ? parseFloat(values.unitBaseCost)
          : undefined,
        currencyId: values.currencyId || undefined,
      });

      // Dismiss loading toast
      toast.dismiss(loadingToastId);

      // Show success toast with action to view package list
      toast.success("Package updated successfully", {
        action: {
          label: "View All",
          onClick: () => navigate("/admin/packages"),
        },
        duration: 5000,
      });
    } catch (error) {
      toast.error("Failed to update package");
      console.error(error);
    } finally {
      // Reset saving state
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Loading Package...">
        <PackageErrorBoundary>
          <div className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <div className="space-y-2">
                <div className="h-4 w-32 bg-slate-200 rounded animate-pulse"></div>
                <div className="h-6 w-48 bg-slate-200 rounded animate-pulse"></div>
              </div>
              <div className="space-y-2">
                <div className="h-4 w-24 bg-slate-200 rounded animate-pulse"></div>
                <div className="flex space-x-2">
                  <div className="h-6 w-16 bg-slate-200 rounded animate-pulse"></div>
                  <div className="h-6 w-20 bg-slate-200 rounded animate-pulse"></div>
                  <div className="h-6 w-20 bg-slate-200 rounded animate-pulse"></div>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <FormSectionSkeleton title="Basic Information" fieldCount={2} />
              <FormSectionSkeleton title="Classification" fieldCount={3} />
              <FormSectionSkeleton title="Pricing Information" fieldCount={4} />
              <FormSectionSkeleton title="Venue Exclusive" fieldCount={2} />
              <FormSectionSkeleton title="Status" fieldCount={1} />
            </div>
          </div>
        </PackageErrorBoundary>
      </AdminLayout>
    );
  }

  if (isError || !packageData) {
    return (
      <AdminLayout title="Package Not Found">
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="text-lg text-red-500 font-medium">
            The requested package could not be found.
          </div>
          <div className="text-sm text-red-400 max-w-md text-center">
            This could be because the package has been deleted, or there was an
            error loading the data. Please try again or contact support if the
            problem persists.
          </div>
          <div className="flex space-x-4 mt-4">
            <Button onClick={() => navigate("/admin/packages")}>
              Return to Packages
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`Edit Package: ${packageData.name}`}>
      <PackageErrorBoundary>
        <div className="flex items-center justify-between mb-6">
          <div>
            <Link
              to="/admin/packages"
              className="flex items-center text-muted-foreground hover:text-foreground transition-colors mb-2"
            >
              <ChevronLeft className="w-4 h-4 mr-1" /> Back to All Packages
            </Link>
            <h1 className="text-xl font-semibold">{packageData.name}</h1>
          </div>
          <div className="flex flex-col items-end">
            <div className="text-sm text-muted-foreground mb-2">
              ID: <span className="font-mono text-xs">{id}</span>
            </div>
            <div className="flex space-x-2">
              <Badge
                variant={packageData.isDeleted ? "destructive" : "success"}
              >
                {packageData.isDeleted ? "Inactive" : "Active"}
              </Badge>
              <Badge variant="outline">{packageData.categoryName}</Badge>
              <Badge variant="outline">{packageData.divisionName}</Badge>
            </div>
          </div>
        </div>

        <div className="space-y-8">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="options" title="Package Options">
                <PackageIcon className="w-4 h-4 mr-2" /> Options
              </TabsTrigger>
              <TabsTrigger value="dependencies" title="Package Dependencies">
                <GitFork className="w-4 h-4 mr-2" /> Dependencies
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border dark:border-gray-700">
                <h2 className="text-lg font-medium mb-4 dark:text-white">
                  Core Package Information
                </h2>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(handleSubmit)}
                    className="space-y-4"
                  >
                    <PackageDetailsForm
                      key={`package-form-${id}-${packageData?.name}-${packageData?.categoryId}-${packageData?.divisionId}`}
                      form={form}
                      categories={categories}
                      divisions={divisions}
                      cities={cities}
                      currencies={currencies}
                      isLoading={isLoadingFormData}
                    />

                    {/* Hidden field for isActive to ensure it gets submitted */}
                    <input type="hidden" {...form.register("isActive")} />

                    <div className="flex justify-end pt-4">
                      <Button
                        type="submit"
                        className="flex items-center"
                        disabled={isSaving}
                      >
                        {isSaving ? (
                          <>
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="w-4 h-4 mr-2" /> Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </div>
            </TabsContent>

            <TabsContent value="options">
              <PackageOptions packageId={id || ""} />
            </TabsContent>

            <TabsContent value="dependencies">
              <PackageDependencies packageId={id || ""} />
            </TabsContent>
          </Tabs>
        </div>
      </PackageErrorBoundary>
    </AdminLayout>
  );
};

export default EditPackagePage;
