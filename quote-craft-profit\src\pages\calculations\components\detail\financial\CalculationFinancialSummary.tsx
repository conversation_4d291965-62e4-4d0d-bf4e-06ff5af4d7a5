import React, { memo } from "react";
import { Tax, Discount } from "../../../utils/calculationUtils";
import { useFinancialSummaryCalculations } from "../../../hooks/financial/useFinancialCalculations";
import { useFinancialActions } from "../../../hooks/financial/useFinancialActions";
import { useTaxDiscountManagement } from "../../../hooks/financial/useTaxDiscountManagement";
import { FinancialSummaryDisplay } from "./FinancialSummaryDisplay";
import { TaxManagement } from "./TaxManagement";
import { DiscountManagement } from "./DiscountManagement";
import { CalculationActions } from "../actions/CalculationActions";

interface CalculationFinancialSummaryProps {
  total: number;
  formatCurrency: (amount: number) => string;
  calculationId?: string;
  status?: "draft" | "completed" | "canceled";
  initialTaxes?: Tax[]; // Initial taxes from the database
  initialDiscount?: Discount; // Initial discount from the database
  onStatusChange?: (
    status: "draft" | "completed" | "canceled",
    taxes: Tax[],
    discount: Discount
  ) => Promise<void>;
  onDelete?: () => Promise<void>;
  onNavigateToList?: () => void; // New prop for navigation
  onTaxesChange?: (taxes: Tax[]) => void; // Callback when taxes change
  onDiscountChange?: (discount: Discount) => void; // Callback when discount changes
}

const CalculationFinancialSummary: React.FC<
  CalculationFinancialSummaryProps
> = ({
  total,
  formatCurrency,
  calculationId,
  status = "draft",
  initialTaxes = [],
  initialDiscount,
  onStatusChange,
  onDelete,
  onNavigateToList,
  onTaxesChange,
  onDiscountChange,
}) => {
  // Initialize tax and discount management
  const {
    taxes,
    isAddingTax,
    newTaxName,
    newTaxPercentage,
    setIsAddingTax,
    setNewTaxName,
    setNewTaxPercentage,
    handleAddTax,
    handleEditTax,
    handleRemoveTax,
    discountObj,
    isAddingDiscount,
    discountAmount,
    setIsAddingDiscount,
    setDiscountAmount,
    handleAddDiscount,
    handleEditDiscount,
    handleRemoveDiscount,
  } = useTaxDiscountManagement({
    initialTaxes,
    initialDiscount,
    onTaxesChange,
    onDiscountChange,
  });

  // Initialize financial calculations
  const { validTotal, financialTotals, finalTotal } =
    useFinancialSummaryCalculations(total, taxes, discountObj);

  // Initialize financial actions
  const {
    isProcessing,
    isConfirmingDelete,
    setIsConfirmingDelete,
    handleStatusChange,
    handleDelete,
  } = useFinancialActions({
    calculationId,
    onStatusChange,
    onDelete,
    onNavigateToList,
  });

  // Action handlers that use the hooks
  const handleSaveDraft = () => handleStatusChange("draft", taxes, discountObj);
  const handleComplete = () =>
    handleStatusChange("completed", taxes, discountObj);
  const handleStartDelete = () => setIsConfirmingDelete(true);
  const handleCancelDelete = () => setIsConfirmingDelete(false);

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border dark:border-gray-700">
      <h2 className="text-xl font-bold mb-4 dark:text-white">
        Financial Summary
      </h2>
      <div className="space-y-4">
        <FinancialSummaryDisplay
          validTotal={validTotal}
          finalTotal={finalTotal}
          formatCurrency={formatCurrency}
        />

        <TaxManagement
          taxes={taxes}
          calculatedTaxes={financialTotals.calculatedTaxes}
          isAddingTax={isAddingTax}
          newTaxName={newTaxName}
          newTaxPercentage={newTaxPercentage}
          formatCurrency={formatCurrency}
          onTaxNameChange={setNewTaxName}
          onTaxPercentageChange={setNewTaxPercentage}
          onAddTax={handleAddTax}
          onEditTax={handleEditTax}
          onRemoveTax={handleRemoveTax}
          onStartAddingTax={() => setIsAddingTax(true)}
          onCancelAddingTax={() => setIsAddingTax(false)}
        />

        <DiscountManagement
          discountObj={discountObj}
          discountAmount={financialTotals.discountAmount}
          isAddingDiscount={isAddingDiscount}
          newDiscountAmount={discountAmount}
          formatCurrency={formatCurrency}
          onDiscountAmountChange={setDiscountAmount}
          onAddDiscount={handleAddDiscount}
          onEditDiscount={handleEditDiscount}
          onRemoveDiscount={handleRemoveDiscount}
          onStartAddingDiscount={() => setIsAddingDiscount(true)}
          onCancelAddingDiscount={() => setIsAddingDiscount(false)}
        />

        <CalculationActions
          calculationId={calculationId}
          status={status}
          isProcessing={isProcessing}
          isConfirmingDelete={isConfirmingDelete}
          onSaveDraft={handleSaveDraft}
          onComplete={handleComplete}
          onStartDelete={handleStartDelete}
          onCancelDelete={handleCancelDelete}
          onConfirmDelete={handleDelete}
        />
      </div>
    </div>
  );
};

// Wrap with memo to prevent unnecessary re-renders when props haven't changed
export default memo(CalculationFinancialSummary);
