import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { UserWithProfile } from '../services/jwt-validation.service';

// Define an interface for the request object with the enriched user type
// Consider moving this to a shared types file.
interface RequestWithUserProfile extends Request {
  user?: UserWithProfile; // Make user optional as it might not be populated yet
}

@Injectable()
export class AdminRoleGuard implements CanActivate {
  private readonly logger = new Logger(AdminRoleGuard.name);

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<RequestWithUserProfile>(); // Use the correct request type
    const user = request.user;

    // Check if the user object exists and has the profile and role info
    if (!user) {
      this.logger.warn(
        'AdminRoleGuard requires a user object on the request. Ensure JwtAuthGuard runs first.',
      );
      // If JwtAuthGuard failed or didn't run, user won't be populated.
      throw new ForbiddenException(
        'Access Denied: User context not available.',
      );
    }

    // Directly check the role from the user object populated by JwtAuthGuard
    const roleName = user.profile?.roles?.role_name;

    this.logger.debug(
      `Checking admin role for user ${user.id}. Role found: ${roleName ?? '[No Role Found]'}`,
    );

    if (roleName === 'admin') {
      return true; // Allow access
    }

    // If role is not admin
    this.logger.warn(
      `User ${user.id} denied access. Role: ${roleName ?? '[No Role Found]'}`,
    );
    throw new ForbiddenException('Access Denied: Requires admin privileges.');
  }
}
