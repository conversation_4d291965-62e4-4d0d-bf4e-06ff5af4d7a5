import { Modu<PERSON> } from '@nestjs/common';
import { CategoriesService } from './categories.service';
import { CategoriesController } from './categories.controller';
import { AdminCategoriesController } from './admin-categories.controller';
import { AuthModule } from '../auth/auth.module';
import { AdminModule } from '../auth/admin.module';

@Module({
  imports: [AuthModule, AdminModule],
  controllers: [CategoriesController, AdminCategoriesController],
  providers: [CategoriesService],
  exports: [CategoriesService],
})
export class CategoriesModule {}
