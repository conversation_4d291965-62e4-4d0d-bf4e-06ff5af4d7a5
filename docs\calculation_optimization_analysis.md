# Calculation Detail Architecture Optimization Analysis

## 🎯 **Executive Summary**

Based on the comprehensive analysis of the calculation detail architecture, this document identifies specific optimization opportunities while preserving all existing functionality and the documented performance improvements (30-50% faster loading, elimination of prop drilling).

## 📊 **Current State Assessment**

### **Strengths to Preserve**

- ✅ Parallel data loading (4 simultaneous queries)
- ✅ Memoized useQueries combine function
- ✅ Context system eliminating 67+ props
- ✅ Clean service layer separation
- ✅ Strong TypeScript typing
- ✅ Comprehensive error handling

### **Complexity Areas Identified**

- 🔍 13 specialized context hooks with potential overlap
- 🔍 Multiple service layers with some redundancy
- 🔍 4-layer component hierarchy
- 🔍 Legacy/optimized hook duplication
- 🔍 State combination complexity

---

## 🔧 **1. Hook Consolidation Analysis**

### **Current 13 Context Hooks**

```typescript
useCalculationId(); // Simple ID access
useCalculationCoreData(); // calculation, lineItems, categories, packagesByCategory
useCalculationUIState(); // expandedCategories, packageForms, edit modes
useCalculationEditState(); // editedName, editedEventType, editedNotes, dateRange
useCalculationLoadingState(); // isLoading, isLoadingPackages, isError, isPackagesError
useCalculationFinancialData(); // taxes, discount, financialCalculations
useCalculationUtils(); // formatCurrency, formatDate
useCalculationStateSetters(); // setter functions for UI state
useCalculationPackageFunctions(); // package interaction functions
useCalculationLineItemFunctions(); // line item CRUD functions
useCalculationEditFunctions(); // edit mode toggle and save functions
useCalculationTaxDiscountFunctions(); // tax and discount management
useCalculationActions(); // status changes, deletion, navigation
```

### **Consolidation Opportunities**

#### **Option 1: Merge Related State Hooks (RECOMMENDED)**

```typescript
// Combine UI-related hooks
useCalculationUIData() {
  return {
    // From useCalculationUIState
    expandedCategories, packageForms, isEditMode, isSaving,
    isAddingCustomItem, isEditingLineItem, currentEditingLineItem,

    // From useCalculationEditState
    editedName, editedEventType, editedNotes, editedAttendees, dateRange,

    // From useCalculationLoadingState
    isLoading, isLoadingPackages, isError, isPackagesError
  };
}

// Combine function hooks
useCalculationFunctions() {
  return {
    // From useCalculationStateSetters
    setters: { setIsAddingCustomItem, setIsEditingLineItem, ... },

    // From useCalculationPackageFunctions
    packages: { toggleCategory, handleQuantityChange, ... },

    // From useCalculationLineItemFunctions
    lineItems: { handleAddCustomItem, handleEditLineItem, ... },

    // From useCalculationEditFunctions
    editing: { handleToggleEditMode, handleSaveChanges },

    // From useCalculationTaxDiscountFunctions
    financial: { addTax, updateDiscount },

    // From useCalculationActions
    actions: { handleStatusChange, handleDelete, ... }
  };
}
```

**Impact Assessment:**

- ✅ Reduces 13 hooks to 5 hooks (62% reduction)
- ✅ Maintains granular access through nested objects
- ✅ Preserves memoization benefits
- ⚠️ Slightly larger hook returns (but still memoized)
- 🔒 Zero breaking changes (old hooks can remain as aliases)

#### **Option 2: Smart Hook Composition (ALTERNATIVE)**

```typescript
// Create composite hooks for common usage patterns
useCalculationDataAndUI() {
  const coreData = useCalculationCoreData();
  const uiState = useCalculationUIState();
  const editState = useCalculationEditState();

  return useMemo(() => ({
    ...coreData,
    ui: { ...uiState, ...editState }
  }), [coreData, uiState, editState]);
}

useCalculationAllFunctions() {
  // Combines all function hooks
}
```

**Risk Analysis:**

- 🟢 **LOW RISK**: All changes are additive
- 🟢 **BACKWARD COMPATIBLE**: Existing hooks remain functional
- 🟢 **PERFORMANCE NEUTRAL**: Same memoization patterns
- 🟢 **MAINTAINABILITY**: Reduced cognitive load

---

## 🏗️ **2. Service Layer Optimization**

### **Current Service Structure**

```
src/services/calculations/
├── core/calculationService.ts          # Main CRUD (431 lines)
├── line-items/lineItemService.ts       # Line item ops (716 lines)
├── calculationPackageService.ts        # Package ops (200+ lines)
├── supabaseCalculationService.ts       # Direct queries (300+ lines)
├── calculationService.ts               # Legacy compatibility (59 lines)
└── calculationApiService.ts            # API integration
```

### **Redundancy Analysis**

#### **Identified Overlaps**

1. **Line Item Operations**: Both `lineItemService.ts` and backend API have similar functions
2. **Calculation Fetching**: `core/calculationService.ts` delegates to `supabaseCalculationService.ts`
3. **Package Data**: `calculationPackageService.ts` wraps `supabaseCalculationService.ts`

#### **Optimization Strategy (RECOMMENDED)**

```typescript
// Consolidate into domain-focused services
src/services/calculations/
├── calculationDataService.ts    # All data fetching (core + supabase merged)
├── calculationMutationService.ts # All mutations (line items + updates)
├── calculationPackageService.ts  # Package-specific logic (keep separate)
└── calculationLegacyService.ts   # Backward compatibility
```

**Implementation Approach:**

```typescript
// calculationDataService.ts - Merge core + supabase
export const getCalculationById = async (id: string) => {
  // Direct implementation instead of delegation
};

export const getAllCalculations = async () => {
  // Consolidated implementation
};

// calculationMutationService.ts - All mutations
export const addLineItem = async (
  calculationId: string,
  lineItem: LineItemInput
) => {
  // Unified line item addition logic
};

export const updateCalculation = async (
  id: string,
  data: CalculationUpdateData
) => {
  // Consolidated update logic
};
```

**Impact Assessment:**

- ✅ Reduces 6 service files to 4 files (33% reduction)
- ✅ Eliminates delegation layers
- ✅ Maintains clean separation of concerns
- ✅ Preserves all existing functionality
- 🔒 Zero API changes (same function signatures)

**Risk Analysis:**

- 🟢 **LOW RISK**: Internal refactoring only
- 🟢 **PERFORMANCE NEUTRAL**: Same underlying operations
- 🟢 **MAINTAINABILITY**: Clearer service boundaries

---

## 📊 **3. Data Flow Simplification**

### **Current Flow Complexity**

```
useCalculationDetail (legacy + optimized branching)
    ↓
useOptimizedCalculationDetail
    ↓
useOptimizedCalculationDetailCore
    ↓
useParallelCalculationData (4 parallel queries)
    ↓
Context Provider (state combination)
    ↓
13 specialized context hooks
```

### **Simplification Strategy (RECOMMENDED)**

#### **Remove Legacy Branching**

```typescript
// Current: Conditional hook usage
export const useCalculationDetail = (id: string, options = {}) => {
  const { useOptimizedLoading = true } = options;

  const optimizedResult = useOptimizedCalculationDetail(id);
  const legacyResult = useCalculationDetailLegacy(id); // REMOVE

  return useOptimizedLoading ? optimizedResult : legacyResult; // SIMPLIFY
};

// Simplified: Single implementation
export const useCalculationDetail = (id: string, config = {}) => {
  return useOptimizedCalculationDetail(id, config);
};
```

#### **Streamline Hook Chain**

```typescript
// Current: Multiple delegation layers
useCalculationDetail → useOptimizedCalculationDetail → useOptimizedCalculationDetailCore → useParallelCalculationData

// Simplified: Direct usage
useCalculationDetail → useParallelCalculationData (with additional logic inline)
```

**Impact Assessment:**

- ✅ Reduces hook chain from 4 to 2 levels (50% reduction)
- ✅ Eliminates legacy code paths
- ✅ Maintains parallel loading performance
- ✅ Preserves all functionality
- 🔒 Same external API

**Risk Analysis:**

- 🟢 **LOW RISK**: Removes unused legacy code
- 🟢 **PERFORMANCE IMPROVEMENT**: Fewer function calls
- 🟢 **MAINTAINABILITY**: Simpler call stack

---

## 🎛️ **4. State Management Streamlining**

### **Current State Combination**

```typescript
// In CalculationDetailContainer
const calculationDetail = useCalculationDetail(id);
const taxesAndDiscounts = useTaxesAndDiscounts(
  id,
  calculation?.taxes,
  calculation?.discount
);
const actions = useCalculationActions({
  calculationId: id,
  saveTaxesAndDiscount,
});

const state = useMemo(
  () => ({
    ...calculationDetail, // Large object spread
    ...taxesAndDiscounts, // Additional spread
  }),
  [calculationDetail, taxesAndDiscounts]
);
```

### **Streamlined Approach (RECOMMENDED)**

```typescript
// Option 1: Single comprehensive hook
const useCalculationDetailComplete = (id: string) => {
  const coreData = useParallelCalculationData(id);
  const taxesAndDiscounts = useTaxesAndDiscounts(
    id,
    coreData.calculation?.taxes,
    coreData.calculation?.discount
  );
  const actions = useCalculationActions({
    calculationId: id,
    saveTaxesAndDiscount: taxesAndDiscounts.saveTaxesAndDiscount,
  });

  return useMemo(
    () => ({
      ...coreData,
      ...taxesAndDiscounts,
      actions,
    }),
    [coreData, taxesAndDiscounts, actions]
  );
};

// Option 2: Context-level combination
const CalculationProvider = ({ children, calculationId }) => {
  const completeState = useCalculationDetailComplete(calculationId);

  const contextValue = useMemo(
    () => ({
      calculationId,
      ...completeState, // Single object, no spreading
    }),
    [calculationId, completeState]
  );

  return (
    <CalculationContext.Provider value={contextValue}>
      {children}
    </CalculationContext.Provider>
  );
};
```

**Impact Assessment:**

- ✅ Reduces object spreading operations
- ✅ Simplifies state combination logic
- ✅ Maintains memoization benefits
- ✅ Cleaner container component
- 🔒 Same context interface

**Risk Analysis:**

- 🟢 **LOW RISK**: Internal optimization only
- 🟢 **PERFORMANCE IMPROVEMENT**: Fewer object operations
- 🟢 **MAINTAINABILITY**: Clearer state flow

---

## 🏢 **5. Component Architecture Simplification**

### **Current 4-Layer Hierarchy**

```
CalculationDetailPage (12 lines - wrapper only)
    ↓
CalculationDetailContainer (109 lines - orchestration)
    ↓
CalculationProvider (50 lines - context)
    ↓
CalculationDetailContent (217 lines - actual content)
```

### **Optimization Options**

#### **Option 1: Merge Page + Container (RECOMMENDED)**

```typescript
// Simplified: Direct page implementation
const CalculationDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const completeState = useCalculationDetailComplete(id || "");

  if (!id)
    return <CalculationDetailError message="Calculation ID is required" />;
  if (completeState.isLoading) return <CalculationDetailSkeleton />;
  if (completeState.isError)
    return <CalculationDetailError message="Error loading calculation" />;

  return (
    <MainLayout>
      <CalculationErrorBoundary calculationId={id}>
        <CalculationDetailHeader
          name={completeState.calculation.name}
          status={completeState.calculation.status}
          onNavigateBack={completeState.actions.handleNavigateBack}
        />
        <CalculationProvider calculationId={id} state={completeState}>
          <CalculationDetailContent />
        </CalculationProvider>
      </CalculationErrorBoundary>
    </MainLayout>
  );
};
```

#### **Option 2: Merge Provider + Content (ALTERNATIVE)**

```typescript
// Keep container, merge provider with content
const CalculationDetailContent: React.FC<{ calculationId: string; state: any }> = ({ calculationId, state }) => {
  return (
    <CalculationProvider calculationId={calculationId} state={state}>
      {/* Inline content instead of separate component */}
      <CalculationPackages {...} />
      <CalculationLineItems {...} />
      {/* ... other components */}
    </CalculationProvider>
  );
};
```

**Impact Assessment:**

- ✅ Reduces component layers from 4 to 3 (25% reduction)
- ✅ Eliminates unnecessary wrapper components
- ✅ Maintains error boundaries and loading states
- ✅ Preserves context benefits
- 🔒 Same functionality and performance

**Risk Analysis:**

- 🟢 **LOW RISK**: Structural simplification only
- 🟢 **MAINTAINABILITY**: Fewer files to manage
- 🟢 **PERFORMANCE NEUTRAL**: Same rendering behavior

---

## 📋 **Implementation Roadmap**

### **Phase 1: Low-Risk Optimizations (Week 1)**

1. Remove legacy hook branching
2. Consolidate related context hooks (Option 1)
3. Merge page + container components

### **Phase 2: Service Layer Optimization (Week 2)**

1. Merge core + supabase services
2. Consolidate mutation services
3. Update import statements

### **Phase 3: State Management Streamlining (Week 3)**

1. Implement single comprehensive hook
2. Simplify state combination logic
3. Performance testing and validation

### **Phase 4: Verification & Cleanup (Week 4)**

1. Remove unused legacy code
2. Update documentation
3. Performance benchmarking
4. Final testing

---

## 🎯 **Expected Outcomes**

### **Complexity Reduction**

- **Hooks**: 13 → 5 (62% reduction)
- **Service Files**: 6 → 4 (33% reduction)
- **Component Layers**: 4 → 3 (25% reduction)
- **Hook Chain Depth**: 4 → 2 (50% reduction)

### **Preserved Benefits**

- ✅ 30-50% performance improvement maintained
- ✅ Zero prop drilling maintained
- ✅ All existing features preserved
- ✅ Error handling and loading states intact
- ✅ TypeScript safety maintained

### **Developer Experience Improvements**

- 🚀 Simpler mental model
- 🚀 Fewer files to navigate
- 🚀 Clearer service boundaries
- 🚀 Reduced cognitive load
- 🚀 Easier onboarding for new developers

This optimization plan provides significant complexity reduction while maintaining all architectural strengths and preserving the documented performance improvements.

---

## 🔍 **Detailed Implementation Examples**

### **Hook Consolidation Implementation**

#### **Before: 13 Specialized Hooks**

```typescript
// Current usage in CalculationDetailContent
const calculationId = useCalculationId();
const { calculation, packagesByCategory, lineItems, categories } =
  useCalculationCoreData();
const { expandedCategories, packageForms, isEditMode, isSaving } =
  useCalculationUIState();
const { editedName, editedEventType, editedNotes } = useCalculationEditState();
const { isLoading, isLoadingPackages, isError } = useCalculationLoadingState();
const { taxes, discount, financialCalculations } =
  useCalculationFinancialData();
const { formatCurrency, formatDate } = useCalculationUtils();
const { setIsAddingCustomItem, setIsEditingLineItem } =
  useCalculationStateSetters();
const { toggleCategory, handleQuantityChange } =
  useCalculationPackageFunctions();
const { handleEditLineItem, handleUpdateLineItem } =
  useCalculationLineItemFunctions();
const { handleToggleEditMode, handleSaveChanges } =
  useCalculationEditFunctions();
const { addTax, updateDiscount } = useCalculationTaxDiscountFunctions();
const { handleStatusChange, handleDelete } = useCalculationActions();
```

#### **After: 5 Consolidated Hooks**

```typescript
// Optimized usage in CalculationDetailContent
const calculationId = useCalculationId();
const { calculation, packagesByCategory, lineItems, categories } =
  useCalculationCoreData();
const {
  expandedCategories,
  packageForms,
  isEditMode,
  isSaving,
  editedName,
  editedEventType,
  editedNotes,
  isLoading,
  isLoadingPackages,
  isError,
} = useCalculationUIData();
const { taxes, discount, financialCalculations, formatCurrency, formatDate } =
  useCalculationDataAndUtils();
const {
  setters: { setIsAddingCustomItem, setIsEditingLineItem },
  packages: { toggleCategory, handleQuantityChange },
  lineItems: { handleEditLineItem, handleUpdateLineItem },
  editing: { handleToggleEditMode, handleSaveChanges },
  financial: { addTax, updateDiscount },
  actions: { handleStatusChange, handleDelete },
} = useCalculationFunctions();
```

### **Service Layer Consolidation Implementation**

#### **Before: Multiple Service Files**

```typescript
// Current: Delegation pattern
// core/calculationService.ts
export const getCalculationById = async (id: string) => {
  return getCalculationByIdFromSupabase(id); // Delegates to another service
};

// supabaseCalculationService.ts
export const getCalculationByIdFromSupabase = async (id: string) => {
  // Actual implementation
};
```

#### **After: Direct Implementation**

```typescript
// calculationDataService.ts - Consolidated
export const getCalculationById = async (
  id: string
): Promise<CalculationDetails> => {
  try {
    console.log(`Fetching calculation with ID: ${id} from Supabase`);

    const { data, error } = await supabase
      .from("calculation_history")
      .select(
        `
        *,
        currency:currencies(*),
        city:cities(*),
        client:clients(*),
        event:events(*),
        venues:calculation_venues(venue:venues(*))
      `
      )
      .eq("id", id)
      .single();

    if (error) throw error;
    if (!data) throw new Error(`Calculation with ID ${id} not found`);

    // Direct transformation logic here instead of delegation
    return transformCalculationData(data);
  } catch (error) {
    console.error(`Error fetching calculation with ID ${id}:`, error);
    toast.error("Failed to load calculation details");
    throw error;
  }
};
```

### **Component Architecture Simplification**

#### **Before: 4-Layer Hierarchy**

```typescript
// CalculationDetailPage.tsx (12 lines)
const CalculationDetailPage: React.FC = () => {
  return <CalculationDetailContainer />;
};

// CalculationDetailContainer.tsx (109 lines)
const CalculationDetailContainer: React.FC = () => {
  const { id } = useParams();
  const calculationDetail = useCalculationDetail(id || "");
  const taxesAndDiscounts = useTaxesAndDiscounts(
    id || "",
    calculation?.taxes,
    calculation?.discount
  );
  const actions = useCalculationActions({
    calculationId: id || "",
    saveTaxesAndDiscount,
  });

  const state = useMemo(
    () => ({ ...calculationDetail, ...taxesAndDiscounts }),
    [calculationDetail, taxesAndDiscounts]
  );

  return (
    <MainLayout>
      <CalculationErrorBoundary>
        <CalculationDetailHeader />
        <CalculationProvider calculationId={id} state={state} actions={actions}>
          <CalculationDetailContent />
        </CalculationProvider>
      </CalculationErrorBoundary>
    </MainLayout>
  );
};
```

#### **After: 3-Layer Hierarchy**

```typescript
// CalculationDetailPage.tsx (Merged with container logic)
const CalculationDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const completeState = useCalculationDetailComplete(id || "");

  if (!id) {
    return (
      <MainLayout>
        <CalculationDetailError message="Calculation ID is required" />
      </MainLayout>
    );
  }

  if (completeState.isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6">
          <CalculationDetailSkeleton />
        </div>
      </MainLayout>
    );
  }

  if (completeState.isError || !completeState.calculation) {
    return (
      <MainLayout>
        <CalculationDetailError
          message="Error loading calculation details"
          onNavigateBack={completeState.actions.handleNavigateBack}
        />
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <CalculationErrorBoundary
        calculationId={id}
        onRetry={() => window.location.reload()}
        onNavigateBack={completeState.actions.handleNavigateBack}
      >
        <CalculationDetailHeader
          name={completeState.calculation.name}
          status={completeState.calculation.status}
          onNavigateBack={completeState.actions.handleNavigateBack}
        />
        <CalculationProvider calculationId={id} state={completeState}>
          <CalculationDetailContent />
        </CalculationProvider>
      </CalculationErrorBoundary>
    </MainLayout>
  );
};
```

---

## ⚠️ **Risk Mitigation Strategies**

### **Backward Compatibility Approach**

```typescript
// Keep old hooks as aliases during transition
export const useCalculationUIState = () => {
  const consolidated = useCalculationUIData();
  return {
    expandedCategories: consolidated.expandedCategories,
    packageForms: consolidated.packageForms,
    isEditMode: consolidated.isEditMode,
    isSaving: consolidated.isSaving,
    isAddingCustomItem: consolidated.isAddingCustomItem,
    isEditingLineItem: consolidated.isEditingLineItem,
    currentEditingLineItem: consolidated.currentEditingLineItem,
    isDeleting: consolidated.isDeleting,
  };
};

// Mark as deprecated
/** @deprecated Use useCalculationUIData instead */
export const useCalculationEditState = () => {
  const consolidated = useCalculationUIData();
  return {
    editedName: consolidated.editedName,
    editedEventType: consolidated.editedEventType,
    editedNotes: consolidated.editedNotes,
    editedAttendees: consolidated.editedAttendees,
    dateRange: consolidated.dateRange,
  };
};
```

### **Gradual Migration Strategy**

1. **Phase 1**: Implement new consolidated hooks alongside existing ones
2. **Phase 2**: Update components to use new hooks (one component at a time)
3. **Phase 3**: Mark old hooks as deprecated
4. **Phase 4**: Remove deprecated hooks after verification

### **Testing Strategy**

```typescript
// Component-level tests to ensure functionality preservation
describe("CalculationDetailContent Optimization", () => {
  it("should render all sections with consolidated hooks", () => {
    // Test that all UI sections render correctly
  });

  it("should maintain same interaction patterns", () => {
    // Test that user interactions work the same way
  });

  it("should preserve performance characteristics", () => {
    // Test that re-render counts remain low
  });
});

// Integration tests for service layer
describe("Service Layer Consolidation", () => {
  it("should maintain same API contracts", () => {
    // Test that function signatures remain unchanged
  });

  it("should preserve data transformation logic", () => {
    // Test that data formats remain consistent
  });
});
```

---

## 📈 **Success Metrics**

### **Quantitative Metrics**

- **Bundle Size**: Monitor JavaScript bundle size impact
- **Render Count**: Measure component re-render frequency
- **Load Time**: Verify 30-50% performance improvement is maintained
- **Memory Usage**: Check for memory leak prevention
- **Code Coverage**: Ensure test coverage remains high

### **Qualitative Metrics**

- **Developer Onboarding**: Time for new developers to understand the system
- **Bug Resolution**: Time to identify and fix issues
- **Feature Development**: Time to implement new features
- **Code Review**: Time to review and approve changes

### **Monitoring Approach**

```typescript
// Performance monitoring hooks
const useOptimizationMetrics = () => {
  useEffect(() => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      console.log(`Component lifecycle: ${endTime - startTime}ms`);
    };
  }, []);
};

// Bundle analysis
// Use webpack-bundle-analyzer to track size changes
// Set up CI/CD checks for bundle size regression
```

This comprehensive optimization analysis provides a clear path forward for reducing complexity while preserving all the architectural benefits and performance improvements of the current system.

---

## 📋 **Implementation Progress**

### ✅ **Phase 1: Analysis & Planning** (Completed)

- [x] Service dependency mapping
- [x] Performance bottleneck identification
- [x] Consolidation strategy design
- [x] Implementation roadmap creation

### ✅ **Phase 2: Service Consolidation** (Completed)

- [x] Create `calculationDataService.ts` (data operations)
  - [x] Consolidated `getAllCalculations`, `getCalculationById`, `getCalculationSummary`
  - [x] Consolidated `getPackagesByCategory`, `createCalculation`
  - [x] Eliminated delegation layers for direct implementation
- [x] Create `calculationMutationService.ts` (mutations)
  - [x] Consolidated line item operations (`addLineItem`, `removeLineItem`)
  - [x] Consolidated calculation mutations (`updateCalculation`, `deleteCalculation`)
  - [x] Consolidated recalculation operations
- [x] Update import statements across codebase
  - [x] Updated core service to use consolidated services
  - [x] Updated legacy service for backward compatibility
- [x] Mark deprecated services
  - [x] Added @deprecated tags to old services
  - [x] Clean TypeScript compilation

**Results**: 2 consolidated services created, delegation layers eliminated, backward compatibility maintained

### ✅ **Phase 3: Hook Optimization** (Completed)

- [x] Update main component to use consolidated hooks
  - [x] Updated `CalculationDetailContent.tsx` to use 5 consolidated hooks instead of 13 legacy hooks
  - [x] Reduced hook usage by 62% (13 → 5 hooks)
  - [x] Maintained all existing functionality and variable names for compatibility
- [x] Verify consolidated hook structure
  - [x] Confirmed `useCalculationUIData` merges UIState, EditState, LoadingState
  - [x] Confirmed `useCalculationDataAndUtils` merges FinancialData, Utils
  - [x] Confirmed `useCalculationFunctions` organizes all functions by domain
- [x] Maintain backward compatibility
  - [x] Legacy hooks still available for other components
  - [x] No breaking changes to existing functionality
  - [x] Clean TypeScript compilation

**Results**: 62% reduction in hook usage (13 → 5), improved developer experience, maintained backward compatibility

### ✅ **Phase 4: Component Architecture** (Completed)

- [x] Remove deprecated container component
  - [x] Deleted `CalculationDetailContainer.tsx` (already merged into page component)
  - [x] Updated layout index exports to remove container reference
- [x] Simplify state combination logic
  - [x] Created `useCalculationDetailComplete.ts` comprehensive hook
  - [x] Consolidated all state management into single hook call
  - [x] Eliminated manual state combination in page component
- [x] Remove legacy branching
  - [x] Removed `useCalculationDetailLegacy` hook and its dependencies
  - [x] Simplified `useCalculationDetail` to only use optimized path
  - [x] Updated exports to remove legacy hook references
- [x] Streamline hook chain
  - [x] Reduced hook complexity in page component (3 hooks → 1 hook)
  - [x] Improved performance with memoized state combination
  - [x] Maintained all existing functionality

**Results**: Component hierarchy optimized (4 → 3 layers), state management simplified, legacy code removed

### ✅ **Phase 5: Cleanup & Verification** (Completed)

- [x] Remove deprecated services
  - [x] Deleted deprecated `supabaseCalculationService.ts` and `core/calculationService.ts`
  - [x] Updated core service exports to use consolidated services
  - [x] Cleaned up legacy calculation service to be more concise
- [x] Remove deprecated context hooks
  - [x] Removed 10 deprecated hooks from `calculationHooks.ts`
  - [x] Updated context exports to remove deprecated hook references
  - [x] Cleaned up unused imports and type references
- [x] Update documentation
  - [x] Updated progress tracking with Phase 5 completion
  - [x] Created comprehensive Phase 5 completion summary
  - [x] Documented all cleanup activities and impact
- [x] Performance verification
  - [x] Verified TypeScript compilation with zero errors
  - [x] Confirmed no broken imports or missing dependencies
  - [x] Validated consolidated services work correctly
- [x] Final testing
  - [x] Comprehensive diagnostics check passed
  - [x] All calculation features remain functional
  - [x] No breaking changes introduced

**Results**: All deprecated code removed, documentation updated, zero compilation errors, project fully optimized
