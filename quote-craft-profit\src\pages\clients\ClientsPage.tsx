import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Client } from "@/types/types";
import {
  getAllClients,
  ClientFilters,
} from "@/services/shared/entities/clients";
import { ClientFormDialog, DeleteClientDialog } from "./components";
import { useDebounce } from "@/hooks/useDebounce";
import {
  PlusIcon,
  SearchIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
} from "lucide-react";

const ClientsPage: React.FC = () => {
  const navigate = useNavigate();

  // State for search and pagination
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 500);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // State for client form dialog
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | undefined>(
    undefined
  );
  const [formMode, setFormMode] = useState<"create" | "edit">("create");

  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [clientToDelete, setClientToDelete] = useState<Client | null>(null);

  // Reset page when search changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearch]);

  // Prepare filters for API call
  const filters: ClientFilters = {
    search: debouncedSearch,
    page,
    pageSize,
    sortBy: "client_name",
    sortOrder: "asc",
  };

  // Fetch clients data
  const { data, isLoading, isError } = useQuery({
    queryKey: ["clients", filters],
    queryFn: () => getAllClients(filters),
  });

  // Handle opening the create client dialog
  const handleAddClient = () => {
    setSelectedClient(undefined);
    setFormMode("create");
    setFormDialogOpen(true);
  };

  // Handle opening the edit client dialog
  const handleEditClient = (client: Client) => {
    setSelectedClient(client);
    setFormMode("edit");
    setFormDialogOpen(true);
  };

  // Handle opening the delete confirmation dialog
  const handleDeleteClient = (client: Client) => {
    setClientToDelete(client);
    setDeleteDialogOpen(true);
  };

  // Handle viewing client details
  const handleViewClient = (client: Client) => {
    navigate(`/clients/${client.id}`);
  };

  // Handle pagination
  const handlePreviousPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (data && page < data.totalPages) {
      setPage(page + 1);
    }
  };

  return (
    <MainLayout>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            Clients
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Manage your clients and their information
          </p>
        </div>
        <div>
          <Button onClick={handleAddClient}>
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Client
          </Button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="flex-1 relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search clients..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left bg-gray-50 dark:bg-gray-800">
                <th className="px-6 py-3 font-medium text-gray-600 dark:text-gray-300">
                  Name
                </th>
                <th className="px-6 py-3 font-medium text-gray-600 dark:text-gray-300">
                  Company
                </th>
                <th className="px-6 py-3 font-medium text-gray-600 dark:text-gray-300">
                  Email
                </th>
                <th className="px-6 py-3 font-medium text-gray-600 dark:text-gray-300">
                  Phone
                </th>
                <th className="px-6 py-3 font-medium text-gray-600 dark:text-gray-300">
                  Address
                </th>
                <th className="px-6 py-3 font-medium text-gray-600 dark:text-gray-300"></th>
              </tr>
            </thead>
            <tbody>
              {isLoading ? (
                // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <tr
                    key={`skeleton-${index}`}
                    className="border-t border-gray-200 dark:border-gray-700"
                  >
                    <td className="px-6 py-4">
                      <Skeleton className="h-6 w-32" />
                    </td>
                    <td className="px-6 py-4">
                      <Skeleton className="h-6 w-32" />
                    </td>
                    <td className="px-6 py-4">
                      <Skeleton className="h-6 w-32" />
                    </td>
                    <td className="px-6 py-4">
                      <Skeleton className="h-6 w-32" />
                    </td>
                    <td className="px-6 py-4">
                      <Skeleton className="h-6 w-32" />
                    </td>
                    <td className="px-6 py-4">
                      <Skeleton className="h-6 w-24" />
                    </td>
                  </tr>
                ))
              ) : isError ? (
                // Error state
                <tr className="border-t border-gray-200 dark:border-gray-700">
                  <td
                    colSpan={6}
                    className="px-6 py-4 text-center text-red-500"
                  >
                    Error loading clients. Please try again.
                  </td>
                </tr>
              ) : data?.data.length === 0 ? (
                // Empty state
                <tr className="border-t border-gray-200 dark:border-gray-700">
                  <td
                    colSpan={6}
                    className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                  >
                    No clients found.{" "}
                    {search
                      ? "Try a different search term."
                      : "Add a new client to get started."}
                  </td>
                </tr>
              ) : (
                // Client data
                data?.data.map((client) => (
                  <tr
                    key={client.id}
                    className="border-t border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <td className="px-6 py-4 font-medium">
                      <button
                        onClick={() => handleViewClient(client)}
                        className="hover:text-blue-600 dark:hover:text-blue-400 hover:underline text-left text-gray-900 dark:text-white"
                      >
                        {client.name}
                      </button>
                    </td>
                    <td className="px-6 py-4 text-gray-900 dark:text-gray-300">
                      {client.company || "-"}
                    </td>
                    <td className="px-6 py-4 text-gray-900 dark:text-gray-300">
                      {client.email || "-"}
                    </td>
                    <td className="px-6 py-4 text-gray-900 dark:text-gray-300">
                      {client.phone || "-"}
                    </td>
                    <td className="px-6 py-4 text-gray-900 dark:text-gray-300">
                      {client.address || "-"}
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewClient(client)}
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteClient(client)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                        >
                          <TrashIcon className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {data && data.totalCount > 0 ? (
              <>
                Showing{" "}
                <span className="font-medium">{(page - 1) * pageSize + 1}</span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(page * pageSize, data.totalCount)}
                </span>{" "}
                of <span className="font-medium">{data.totalCount}</span>{" "}
                clients
              </>
            ) : (
              "No clients found"
            )}
          </div>
          <div className="flex space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousPage}
              disabled={isLoading || page <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={isLoading || !data || page >= data.totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      </div>

      {/* Client Form Dialog */}
      <ClientFormDialog
        open={formDialogOpen}
        onOpenChange={setFormDialogOpen}
        client={selectedClient}
        mode={formMode}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteClientDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        client={clientToDelete}
      />
    </MainLayout>
  );
};

export default ClientsPage;
