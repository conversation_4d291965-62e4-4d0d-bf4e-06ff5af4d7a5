import React from "react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

type DateRangePreset = {
  label: string;
  getValue: () => DateRange;
  description?: string;
};

interface DateRangePickerProps {
  value?: DateRange;
  onChange?: (range: DateRange | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  disablePastDates?: boolean;
  className?: string;
  numberOfMonths?: number;
  showPresets?: boolean;
  presets?: DateRangePreset[];
}

export const DateRangePicker = React.forwardRef<
  HTMLDivElement,
  DateRangePickerProps
>(
  (
    {
      value,
      onChange,
      placeholder = "Select start and end dates",
      disabled = false,
      disablePastDates = false,
      className,
      numberOfMonths = 2,
      showPresets = false,
      presets = [],
    },
    ref
  ) => {
    return (
      <div ref={ref} className={cn("grid gap-2", className)}>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              id="date"
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                (!value?.from || !value?.to) && "text-muted-foreground"
              )}
              disabled={disabled}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {value?.from && value?.to ? (
                <>
                  {format(value.from, "PPP")} - {format(value.to, "PPP")}
                </>
              ) : value?.from ? (
                <>
                  {format(value.from, "PPP")} -{" "}
                  <span className="text-red-500">Select end date</span>
                </>
              ) : (
                placeholder
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="flex">
              {showPresets && presets.length > 0 && (
                <div className="border-r p-3 space-y-1">
                  <div className="text-sm font-medium mb-2">Quick Select</div>
                  {presets.map((preset) => (
                    <Button
                      key={preset.label}
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-left h-auto p-2"
                      onClick={() => onChange?.(preset.getValue())}
                    >
                      <div>
                        <div className="font-medium">{preset.label}</div>
                        {preset.description && (
                          <div className="text-xs text-muted-foreground">
                            {preset.description}
                          </div>
                        )}
                      </div>
                    </Button>
                  ))}
                </div>
              )}
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={value?.from}
                selected={value}
                onSelect={onChange}
                numberOfMonths={numberOfMonths}
                disabled={
                  disablePastDates
                    ? (date) => {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return date < today;
                      }
                    : undefined
                }
                className="pointer-events-auto"
              />
            </div>
          </PopoverContent>
        </Popover>
        {value?.from && !value?.to && (
          <p className="text-xs text-orange-600 font-medium">
            ⚠️ Please select an end date to complete the date range
          </p>
        )}
        {!value?.from && (
          <p className="text-xs text-muted-foreground">
            Select both start and end dates
          </p>
        )}
      </div>
    );
  }
);

DateRangePicker.displayName = "DateRangePicker";

export default DateRangePicker;
