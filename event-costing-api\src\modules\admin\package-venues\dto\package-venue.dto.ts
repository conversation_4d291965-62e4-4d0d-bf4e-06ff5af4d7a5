import { ApiProperty } from '@nestjs/swagger';

// DTO representing a venue associated with a package
export class PackageVenueDto {
  @ApiProperty({ description: 'Venue UUID', format: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Venue Name', example: 'Grand Ballroom' })
  name: string;

  @ApiProperty({ description: 'Venue Address', example: '123 Main St' })
  address?: string;

  @ApiProperty({ description: 'City ID', format: 'uuid' })
  city_id?: string;

  @ApiProperty({ description: 'City Name', example: 'Jakarta' })
  city_name?: string;
}
