/**
 * Specialized Error Boundary for calculation-related components
 */
import React, { ReactNode } from "react";
import ErrorBoundary from "@/components/error/ErrorBoundary";
import { AlertTriangle, Calculator, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface CalculationErrorBoundaryProps {
  children: ReactNode;
  calculationId?: string;
  onRetry?: () => void;
  onNavigateBack?: () => void;
}

const CalculationErrorFallback: React.FC<{
  calculationId?: string;
  onRetry?: () => void;
  onNavigateBack?: () => void;
}> = ({ calculationId, onRetry, onNavigateBack }) => {
  return (
    <div className="min-h-[300px] flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
            <Calculator className="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
            Calculation Error
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-300">
            There was an issue loading or processing this calculation.
            {calculationId && (
              <span className="block mt-2 text-sm">
                Calculation ID:{" "}
                <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded">
                  {calculationId}
                </code>
              </span>
            )}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-3">
          <div className="flex flex-col gap-2">
            {onRetry && (
              <Button
                onClick={onRetry}
                className="flex items-center justify-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Retry Loading
              </Button>
            )}
            {onNavigateBack && (
              <Button
                variant="outline"
                onClick={onNavigateBack}
                className="flex items-center justify-center gap-2"
              >
                ← Back to Calculations
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const CalculationErrorBoundary: React.FC<CalculationErrorBoundaryProps> = ({
  children,
  calculationId,
  onRetry,
  onNavigateBack,
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // In a real app, send to error tracking service with calculation context
    // Example: Sentry.captureException(error, {
    //   tags: { section: 'calculations', calculationId },
    //   contexts: { react: errorInfo }
    // });
  };

  return (
    <ErrorBoundary
      onError={handleError}
      resetKeys={[calculationId]}
      resetOnPropsChange={true}
      fallback={
        <CalculationErrorFallback
          calculationId={calculationId}
          onRetry={onRetry}
          onNavigateBack={onNavigateBack}
        />
      }
    >
      {children}
    </ErrorBoundary>
  );
};

export default CalculationErrorBoundary;
