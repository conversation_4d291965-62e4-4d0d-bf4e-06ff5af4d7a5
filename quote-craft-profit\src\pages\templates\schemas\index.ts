import { z } from "zod";
import { commonValidations, formFieldPatterns } from "@/schemas/common";

/**
 * Template-related validation schemas
 * Centralized schemas for all template forms and components
 */

// Date format validation for template dates
const templateDateFormat = z
  .string()
  .regex(/^(\d{4}-\d{2}-\d{2})?$/, "Date must be in YYYY-MM-DD format or empty")
  .optional();

// Template creation schema (consolidated from multiple sources)
export const createTemplateSchema = z.object({
  name: formFieldPatterns.name("Template name"),
  description: formFieldPatterns.description(),
  eventTypeId: z.string().uuid("Invalid event type").optional(),
  cityId: z.string().uuid("Invalid city ID").optional(),
  currencyId: z.string().uuid("Invalid currency ID").optional(),
  attendees: z.number().int().min(1, "Attendees must be at least 1").optional(),
  templateStartDate: templateDateFormat,
  templateEndDate: templateDateFormat,
  venueIds: z.array(z.string().uuid()).optional(),
});

// Template update schema (from admin templates)
export const updateTemplateSchema = z.object({
  name: formFieldPatterns.name("Template name"),
  description: z.string().nullable(),
  event_type_id: z.string().uuid("Invalid event type").nullable(),
  attendees: z.number().int().min(1, "Attendees must be at least 1").optional(),
  template_start_date: templateDateFormat,
  template_end_date: templateDateFormat,
  is_public: z.boolean(),
});

// Simple template form schema (extracted from TemplateFormDialog.tsx)
export const templateFormSchema = z.object({
  name: formFieldPatterns.name("Template name"),
  description: formFieldPatterns.description(),
  event_type_id: z.string().uuid("Invalid event type").optional(),
  attendees: z.number().int().min(1, "Attendees must be at least 1").optional(),
  is_public: commonValidations.booleanWithDefault(false),
});

// Create template from calculation schema (extracted from CreateTemplateFromCalculationDialog.tsx)
export const createTemplateFromCalculationSchema = z.object({
  name: formFieldPatterns.name("Template name"),
  description: formFieldPatterns.description(),
  eventTypeId: z.string().uuid("Invalid event type").optional(),
});

// Type exports for TypeScript inference
export type CreateTemplateValues = z.infer<typeof createTemplateSchema>;
export type UpdateTemplateValues = z.infer<typeof updateTemplateSchema>;
export type TemplateFormValues = z.infer<typeof templateFormSchema>;
export type CreateTemplateFromCalculationValues = z.infer<
  typeof createTemplateFromCalculationSchema
>;
