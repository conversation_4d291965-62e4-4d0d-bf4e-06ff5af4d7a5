## Project Brief: Dynamic Event Costing Platform

**(Based on Schema & Features Finalized as of April 18, 2025)**

**1. Vision**

To develop a web platform for event planning businesses to streamline event cost calculations, generate accurate quotes rapidly, manage a detailed service catalogue, and track calculation profitability.

**2. Target Audience**

- **Primary Users:** Event Planning Companies / Agencies, including:
  - **Administrators:** Manage catalogue, settings, users, templates.
  - **Event Planners / Sales Staff:** Create calculations/quotes for clients/events.
- **Secondary Users (Implied):** Event clients receiving the generated outputs.

**3. Core Capabilities (Development Focus)**

- **Manage Service Catalogue:** Provide functionality (primarily via Admin interfaces) to define packages including variations, configurable options, multi-currency pricing & costs, city-based availability, categories/divisions, and package dependencies (requires/conflicts).
- **Generate Calculations:** Allow users to initiate calculations from scratch or pre-filled templates. Capture essential event details (dates, attendees, city, currency) and optionally link the calculation to `Client` and `Event` records. Support guest/unlinked calculations.
- **Build Estimates:** Enable users to browse the package catalogue (filtered by category/city, showing conflicts based on dependencies) and add items to the calculation. Support adding both standard catalogue items and one-off custom line items. Automatically calculate the effective quantity for standard items based on package rules (`quantity_basis`: per-day, per-attendee, etc.) and event inputs. Dynamically display and allow configuration of applicable package options.
- **Realtime Totals:** Display running subtotals that update as items are added/modified. Calculate and display final totals including manually entered/configured tax and discount parameters (applying service charges before tax if configured).
- **Ensure Accuracy (Price Locking):** Implement logic to automatically capture price and cost snapshots for each line item (`calculation_line_items`, `calculation_line_item_options`) when the calculation is saved, ensuring historical estimates remain consistent. Recalculations must use these snapshots.
- **Track Profitability:** Implement backend logic to automatically calculate the estimated `total_cost` and `estimated_profit` for each `calculation_history` record based on the snapshotted costs of its line items.
- **Manage Access & Workflow:** Enforce role-based permissions (Admin vs. Planner). Support saving calculations as 'draft' and marking them as 'completed'.
- **Export Results:** Provide functionality to generate calculation summary documents (e.g., PDF, xlsx) based on the saved calculation data and log these export events (`export_history`).

**4. Technical Approach**

- Built on a robust PostgreSQL backend (Supabase compatible) using a normalized structure for core calculation data (`calculation_line_items`, etc.).
- An API layer handles complex business logic (quantity calculations, price snapshotting, dependency checks, profitability calculation, final total calculation).
