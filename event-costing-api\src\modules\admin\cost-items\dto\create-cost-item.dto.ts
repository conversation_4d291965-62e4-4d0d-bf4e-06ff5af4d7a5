import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  IsNumber,
  Min,
  MaxLength,
  IsBoolean,
} from 'class-validator';

export class CreateCostItemDto {
  @ApiProperty({
    description: 'Unique code for the cost item',
    example: 'LABOR-SN',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  item_code: string;

  @ApiProperty({
    description: 'Name of the cost item',
    example: 'Senior Technician Labor',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Detailed description',
    required: false,
    example: 'Hourly rate for senior tech',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Unit of measure', example: 'Hour' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  unit_of_measure: string;

  @ApiProperty({ description: 'Default price/cost per unit', example: 75.5 })
  @IsNumber()
  @Min(0)
  default_price: number;

  @ApiProperty({
    description: 'ID of the currency for the default price',
    example: '...',
    format: 'uuid',
  })
  @IsUUID()
  @IsNotEmpty()
  currency_id: string;

  @ApiProperty({
    description: 'ID of the category this item belongs to',
    example: '...',
    format: 'uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  category_id?: string;

  @ApiProperty({
    description: 'ID of the default supplier',
    example: '...',
    format: 'uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  supplier_id?: string;

  @ApiProperty({
    description: 'Whether the cost item is active',
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean = true; // Default value handled here or in DB
}
