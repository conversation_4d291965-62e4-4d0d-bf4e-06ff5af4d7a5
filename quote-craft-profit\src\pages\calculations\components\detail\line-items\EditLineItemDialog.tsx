import React, { useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { CurrencyInput } from "@/components/ui/currency-input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LineItem, LineItemInput } from "@/types/calculation";
import { useCustomItems } from "@/hooks/useCustomItems";
import { useLineItemOptions } from "../../../hooks/data/useLineItems";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "../../../utils/formatUtils";
import { useCalculationId, useCalculationCoreData } from "../../../contexts";

// Define the form schema
const editItemSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
  item_quantity_basis: z.coerce
    .number()
    .min(0, "Days/Units cannot be negative"),
  unit_price: z.coerce.number().min(0, "Price cannot be negative"),
  category_id: z.string().min(1, "Please select a category"),
});

type EditItemFormValues = z.infer<typeof editItemSchema>;

interface EditLineItemDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateLineItem: (
    lineItemId: string,
    updates: Partial<LineItemInput>
  ) => void;
  lineItem: LineItem | null;
}

const EditLineItemDialog: React.FC<EditLineItemDialogProps> = ({
  isOpen,
  onClose,
  onUpdateLineItem,
  lineItem,
}) => {
  // Get data from context
  const calculationId = useCalculationId();
  const { categories } = useCalculationCoreData();

  // Get custom items hook for custom item updates
  const { updateCustomItem, isUpdatingCustomItem } =
    useCustomItems(calculationId);

  // Fetch line item options for package-based items (only if not custom)
  const shouldFetchOptions = !!lineItem?.id && !lineItem?.is_custom;
  const { data: lineItemOptions = [], isLoading: isLoadingOptions } =
    useLineItemOptions(calculationId, shouldFetchOptions ? lineItem.id : "");

  // Initialize form
  const form = useForm<EditItemFormValues>({
    resolver: zodResolver(editItemSchema),
    defaultValues: {
      name: "",
      description: "",
      quantity: 1,
      item_quantity_basis: 1,
      unit_price: 0,
      category_id: "",
    },
  });

  // Update form values when lineItem changes
  useEffect(() => {
    if (lineItem) {
      form.reset({
        name: lineItem.name,
        description: lineItem.description || "",
        quantity: lineItem.quantity,
        item_quantity_basis: lineItem.item_quantity_basis || 1,
        unit_price: lineItem.unit_price || 0,
        category_id: lineItem.category_id || "",
      });
    }
  }, [lineItem, form]);

  // Handle form submission
  const onSubmit = (values: EditItemFormValues) => {
    if (!lineItem) return;

    if (lineItem.is_custom) {
      // Handle custom item update using the new service
      const customItemUpdates = {
        itemName: values.name,
        description: values.description,
        quantity: values.quantity,
        unitPrice: values.unit_price,
        itemQuantityBasis: values.item_quantity_basis,
        categoryId: values.category_id,
      };

      updateCustomItem({ itemId: lineItem.id, updates: customItemUpdates });
      onClose();
    } else {
      // Handle package item update using the existing method
      const totalPrice =
        values.unit_price * values.quantity * values.item_quantity_basis;

      const updates: Partial<LineItemInput> = {
        name: values.name,
        description: values.description,
        quantity: values.quantity,
        item_quantity_basis: values.item_quantity_basis,
        unit_price: values.unit_price,
        total_price: totalPrice,
        category_id: values.category_id,
      };

      onUpdateLineItem(lineItem.id, updates);
    }
  };

  // If no line item is provided, don't render the dialog
  if (!lineItem) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-[500px]"
        aria-describedby="edit-item-description"
      >
        <DialogHeader>
          <DialogTitle>Edit Item</DialogTitle>
          <DialogDescription id="edit-item-description">
            Update the details of this item
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Item Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter item name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter item description"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity</FormLabel>
                    <FormControl>
                      <Input type="number" min={1} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="item_quantity_basis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Days/Units</FormLabel>
                    <FormControl>
                      <Input type="number" min={0} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="unit_price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit Price</FormLabel>
                    {lineItem?.is_custom ? (
                      // Editable unit price for custom items
                      <FormControl>
                        <CurrencyInput
                          value={field.value || 0}
                          onChange={(numericValue) =>
                            field.onChange(numericValue)
                          }
                          placeholder="Enter unit price"
                          showSymbol={true}
                        />
                      </FormControl>
                    ) : (
                      // Read-only unit price for package items
                      <FormControl>
                        <div className="p-2 text-sm bg-gray-50 border border-gray-200 rounded-md">
                          <span className="text-gray-700">
                            {field.value
                              ? `Rp ${field.value.toLocaleString("id-ID")}`
                              : "No price set"}
                          </span>
                          <div className="text-xs text-gray-500 mt-1">
                            Unit price is inherited from the original package
                          </div>
                        </div>
                      </FormControl>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    {lineItem?.is_custom ? (
                      // Editable category for custom items
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.isArray(categories) &&
                          categories.length > 0 ? (
                            categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="__no_categories__" disabled>
                              No categories available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    ) : (
                      // Read-only category for package items
                      <FormControl>
                        <div className="p-2 text-sm bg-gray-50 border border-gray-200 rounded-md">
                          <span className="text-gray-700">
                            {categories.find((cat) => cat.id === field.value)
                              ?.name || "No category assigned"}
                          </span>
                          <div className="text-xs text-gray-500 mt-1">
                            Category is inherited from the original package
                          </div>
                        </div>
                      </FormControl>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Package Options Section - Only for Package Items */}
              {!lineItem?.is_custom && (
                <div className="space-y-4">
                  <div className="border-t pt-4">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="secondary" className="text-xs">
                        Package Options
                      </Badge>
                      {isLoadingOptions && (
                        <div className="text-xs text-gray-500">
                          Loading options...
                        </div>
                      )}
                    </div>

                    {lineItemOptions.length > 0 ? (
                      <div className="space-y-3">
                        {lineItemOptions.map((option) => (
                          <div
                            key={option.id}
                            className="flex items-start space-x-3 p-3 bg-gray-50 rounded-md"
                          >
                            <Checkbox
                              id={`option-${option.id}`}
                              checked={true} // For now, we'll assume all fetched options are selected
                              onCheckedChange={(checked) => {
                                // Handle option toggle - we'll implement this next
                                console.log(
                                  "Toggle option:",
                                  option.id,
                                  checked
                                );
                              }}
                              className="mt-0.5"
                            />
                            <div className="flex-1 min-w-0">
                              <label
                                htmlFor={`option-${option.id}`}
                                className="text-sm font-medium text-gray-900 cursor-pointer"
                              >
                                {option.name}
                              </label>
                              {option.description && (
                                <p className="text-xs text-gray-600 mt-1">
                                  {option.description}
                                </p>
                              )}
                              <div className="flex items-center gap-2 mt-1">
                                <span className="text-xs text-gray-500">
                                  Price Adjustment:{" "}
                                  {formatCurrency(option.price_adjustment)}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : !isLoadingOptions ? (
                      <div className="text-sm text-gray-500 p-3 bg-gray-50 rounded-md">
                        No package options available for this item.
                      </div>
                    ) : null}
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={lineItem?.is_custom ? isUpdatingCustomItem : false}
              >
                {lineItem?.is_custom && isUpdatingCustomItem
                  ? "Updating..."
                  : "Update Item"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditLineItemDialog;
