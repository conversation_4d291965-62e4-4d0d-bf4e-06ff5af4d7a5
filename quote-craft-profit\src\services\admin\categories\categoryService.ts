import { Category } from '@/types/types';
import { CategoryOrderItem } from '@/pages/admin/categories/types';
import {
  getAllCategoriesFromApi,
  getCategoryByIdFromApi,
  updateCategoryOrderFromApi,
  createCategoryFromApi,
  updateCategoryFromApi,
  deleteCategoryFromApi,
} from './categoryApiService';

/**
 * Fetches all categories from the API
 * This function now uses the backend API instead of direct Supabase calls
 * @returns Promise resolving to an array of categories
 */
export const getAllCategories = async (): Promise<Category[]> => {
  try {
    // Use the API service to fetch categories
    return await getAllCategoriesFromApi();
  } catch (error) {
    console.error('Error in getAllCategories:', error);
    throw error;
  }
};

/**
 * Get a category by ID
 * This function uses the backend API instead of direct Supabase calls
 * @param id - The category ID
 * @returns Promise resolving to a category
 */
export const getCategoryById = async (id: string): Promise<Category> => {
  try {
    // Use the API service to fetch the category
    return await getCategoryByIdFromApi(id);
  } catch (error) {
    console.error(`Error in getCategoryById for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new category
 * This function uses the backend API instead of direct Supabase calls
 * @param categoryData - The category data to create
 * @returns Promise resolving to the created category
 */
export const createCategory = async (categoryData: {
  name: string;
  description?: string;
  display_order?: number;
  is_active?: boolean;
}): Promise<Category> => {
  try {
    // Use the API service to create the category
    return await createCategoryFromApi(categoryData);
  } catch (error) {
    console.error('Error in createCategory:', error);
    throw error;
  }
};

/**
 * Update a category
 * This function uses the backend API instead of direct Supabase calls
 * @param id - The category ID
 * @param categoryData - The category data to update
 * @returns Promise resolving to the updated category
 */
export const updateCategory = async (
  id: string,
  categoryData: {
    name?: string;
    description?: string;
    display_order?: number;
    is_active?: boolean;
  },
): Promise<Category> => {
  try {
    // Use the API service to update the category
    return await updateCategoryFromApi(id, categoryData);
  } catch (error) {
    console.error(`Error in updateCategory for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a category
 * This function uses the backend API instead of direct Supabase calls
 * @param id - The category ID
 * @returns Promise resolving when the category is deleted
 */
export const deleteCategory = async (id: string): Promise<void> => {
  try {
    // Use the API service to delete the category
    await deleteCategoryFromApi(id);
  } catch (error) {
    console.error(`Error in deleteCategory for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Updates the display order of multiple categories
 * This function now uses the backend API instead of direct API calls
 * @param categories Array of category order items with id and display_order
 * @returns Promise resolving to the updated categories
 */
export const updateCategoryOrder = async (
  categories: CategoryOrderItem[],
): Promise<Category[]> => {
  try {
    // Use the API service to update category order
    return await updateCategoryOrderFromApi(categories);
  } catch (error) {
    console.error('Error in updateCategoryOrder:', error);
    throw error;
  }
};
