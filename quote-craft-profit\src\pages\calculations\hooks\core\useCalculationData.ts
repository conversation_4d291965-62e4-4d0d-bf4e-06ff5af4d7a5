import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { getAllClients } from "@/services/shared/entities/clients";
import { getAllEvents } from "@/services/shared/entities/events";
import { getVenuesByCity } from "@/services/shared/entities/venues";
import { getAllCities } from "@/services/shared/entities/cities";

export const useCalculationData = () => {
  const [isClientFormOpen, setIsClientFormOpen] = useState(false);
  const [isEventFormOpen, setIsEventFormOpen] = useState(false);
  const [selectedCityId, setSelectedCityId] = useState<string>("");

  // Fetch cities using the service layer with optimized caching
  const { data: cities = [] } = useQuery({
    queryKey: QUERY_KEYS.cities.all(),
    queryFn: getAllCities,
    staleTime: 15 * 60 * 1000, // 15 minutes - cities rarely change
    gcTime: 30 * 60 * 1000, // 30 minutes cache retention
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    meta: {
      onError: () => {
        toast.error("Failed to load cities");
      },
    },
  });

  // Fetch clients with optimized caching
  const { data: clientsResult } = useQuery({
    queryKey: QUERY_KEYS.clients.all(),
    queryFn: () => getAllClients(),
    staleTime: 5 * 60 * 1000, // 5 minutes - clients change more frequently
    gcTime: 15 * 60 * 1000, // 15 minutes cache retention
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    meta: {
      onError: () => {
        toast.error("Failed to load clients");
      },
    },
  });

  // Fetch events with optimized caching
  const { data: events = [] } = useQuery({
    queryKey: QUERY_KEYS.events.all(),
    queryFn: getAllEvents,
    staleTime: 5 * 60 * 1000, // 5 minutes - events change moderately
    gcTime: 15 * 60 * 1000, // 15 minutes cache retention
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    meta: {
      onError: () => {
        toast.error("Failed to load events");
      },
    },
  });

  // Fetch venues based on selected city with optimized caching
  const { data: venues = [] } = useQuery({
    queryKey: QUERY_KEYS.venues.byCity(selectedCityId),
    queryFn: () => getVenuesByCity(selectedCityId),
    enabled: !!selectedCityId,
    staleTime: 10 * 60 * 1000, // 10 minutes - venues change occasionally
    gcTime: 20 * 60 * 1000, // 20 minutes cache retention
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    meta: {
      onError: () => {
        toast.error("Failed to load venues");
      },
    },
  });

  // Extract clients array from the paginated result
  const clients = clientsResult?.data || [];

  return {
    cities,
    clients,
    events,
    venues,
    isClientFormOpen,
    isEventFormOpen,
    selectedCityId,
    setIsClientFormOpen,
    setIsEventFormOpen,
    setSelectedCityId,
  };
};
