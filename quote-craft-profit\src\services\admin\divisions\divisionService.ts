import { Division } from '@/types/types';
import {
  getAllDivisionsFromApi,
  getDivisionByIdFromApi,
  createDivisionFromApi,
  updateDivisionFromApi,
  deleteDivisionFromApi,
  CreateDivisionRequest,
  UpdateDivisionRequest
} from './divisionApiService';

/**
 * Get all divisions
 * This function now uses the backend API instead of direct Supabase calls
 * @param active - Optional filter for active status
 * @returns Promise resolving to an array of divisions
 */
export const getAllDivisions = async (active?: boolean): Promise<Division[]> => {
  try {
    // Use the API service to fetch divisions
    return await getAllDivisionsFromApi(active);
  } catch (error) {
    console.error('Error in getAllDivisions:', error);
    throw error;
  }
};

/**
 * Get a division by ID
 * @param id - The division ID
 * @returns Promise resolving to a division
 */
export const getDivisionById = async (id: string): Promise<Division> => {
  try {
    // Use the API service to fetch a division by ID
    return await getDivisionByIdFromApi(id);
  } catch (error) {
    console.error(`Error in getDivisionById for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new division
 * @param data - The division data to create
 * @returns Promise resolving to the created division
 */
export const createDivision = async (data: CreateDivisionRequest): Promise<Division> => {
  try {
    return await createDivisionFromApi(data);
  } catch (error) {
    console.error('Error in createDivision:', error);
    throw error;
  }
};

/**
 * Update a division
 * @param id - The division ID
 * @param data - The division data to update
 * @returns Promise resolving to the updated division
 */
export const updateDivision = async (id: string, data: UpdateDivisionRequest): Promise<Division> => {
  try {
    return await updateDivisionFromApi(id, data);
  } catch (error) {
    console.error(`Error in updateDivision for ID ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a division
 * @param id - The division ID
 * @returns Promise resolving when the division is deleted
 */
export const deleteDivision = async (id: string): Promise<void> => {
  try {
    return await deleteDivisionFromApi(id);
  } catch (error) {
    console.error(`Error in deleteDivision for ID ${id}:`, error);
    throw error;
  }
};
