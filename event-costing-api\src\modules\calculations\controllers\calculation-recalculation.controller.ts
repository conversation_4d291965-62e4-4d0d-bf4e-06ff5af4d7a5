import {
  Controller,
  Post,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
  Inject,
  forwardRef,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiParam,
  ApiOkResponse,
  ApiResponse,
  ApiOperation,
} from '@nestjs/swagger';
import { CalculationCrudService } from '../services/calculation-crud.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';

/**
 * Response DTO for recalculation endpoint
 */
export class RecalculationResponseDto {
  success: boolean;
  message: string;
  timestamp: string;
}

/**
 * Controller for calculation recalculation operations
 * Implements the standardized recalculation endpoint from the API Architecture Migration Plan
 * Replaces the mixed Supabase RPC + API approach with a single unified endpoint
 */
@ApiTags('Calculation Recalculation')
@ApiBearerAuth()
@Controller('calculations/:calculationId')
@UseGuards(JwtAuthGuard)
export class CalculationRecalculationController {
  private readonly logger = new Logger(CalculationRecalculationController.name);

  constructor(
    @Inject(forwardRef(() => CalculationCrudService))
    private readonly calculationCrudService: CalculationCrudService,
  ) {}

  /**
   * Validate calculation ownership
   */
  private async validateCalculationOwnership(
    calculationId: string,
    user: User,
  ): Promise<void> {
    this.logger.debug(
      `Validating ownership for calculation: ${calculationId}, user: ${user.id}`,
    );

    try {
      // This will throw NotFoundException if calculation doesn't exist or user doesn't own it
      await this.calculationCrudService.findCalculationRawById(
        calculationId,
        user,
      );
      this.logger.debug(
        `Ownership validated for calculation: ${calculationId}`,
      );
    } catch (error) {
      this.logger.warn(
        `Ownership validation failed for calculation: ${calculationId}, user: ${user.id}`,
      );
      throw error;
    }
  }

  /**
   * Recalculate calculation totals
   * Standardized endpoint that replaces the mixed Supabase RPC + API approach
   */
  @Post('recalculate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Recalculate calculation totals',
    description:
      'Triggers recalculation of all totals, taxes, and profit for the calculation. Replaces the mixed Supabase RPC + API approach with a unified endpoint.',
  })
  @ApiParam({ name: 'calculationId', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Calculation recalculated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Calculation recalculated successfully',
        },
        timestamp: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to recalculate calculation totals.',
  })
  async recalculateCalculation(
    @Param('calculationId', ParseUUIDPipe) calculationId: string,
    @GetCurrentUser() user: User,
  ): Promise<RecalculationResponseDto> {
    this.logger.log(
      `User ${user.email} requesting recalculation for calculation ${calculationId}`,
    );

    await this.validateCalculationOwnership(calculationId, user);

    const startTime = Date.now();

    try {
      // Trigger the recalculation using the existing service method
      await this.calculationCrudService.triggerRecalculation(calculationId);

      const duration = Date.now() - startTime;

      this.logger.log(
        `Calculation ${calculationId} recalculated successfully in ${duration}ms`,
      );

      return {
        success: true,
        message: 'Calculation recalculated successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(
        `Failed to recalculate calculation ${calculationId} after ${duration}ms: ${error.message}`,
        error.stack,
      );

      throw error; // Let NestJS handle the error response
    }
  }
}
