import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import {
  PackageOptionsService,
  PackageOptionListQueryDto,
} from './package-options.service';
import { CreatePackageOptionDto } from './dto/create-package-option.dto';
import { UpdatePackageOptionDto } from './dto/update-package-option.dto';
import { PackageOptionDto } from './dto/package-option.dto';
import { PaginatedResponseDto } from 'src/shared/dtos/paginated-response.dto';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from 'src/modules/auth/guards/admin-role.guard';

@ApiTags('Admin | Packages / Options')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AdminRoleGuard)
@Controller('admin/packages/:packageId/options')
export class PackageOptionsController {
  constructor(private readonly packageOptionsService: PackageOptionsService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new package option for a specific package',
  })
  @ApiParam({
    name: 'packageId',
    description: 'Parent Package UUID',
    type: String,
  })
  @ApiResponse({
    status: 201,
    description: 'The package option has been successfully created.',
    type: PackageOptionDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Parent package not found.' })
  create(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Body() createDto: CreatePackageOptionDto,
  ): Promise<PackageOptionDto> {
    return this.packageOptionsService.create(packageId, createDto);
  }

  @Get()
  @ApiOperation({
    summary: 'List package options for a specific package',
  })
  @ApiParam({
    name: 'packageId',
    description: 'Parent Package UUID',
    type: String,
  })
  @ApiQuery({ type: PackageOptionListQueryDto })
  @ApiResponse({
    status: 200,
    description: 'List of package options.',
    type: PaginatedResponseDto<PackageOptionDto>,
  })
  @ApiResponse({ status: 404, description: 'Parent package not found.' })
  findAll(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Query() queryDto: PackageOptionListQueryDto,
  ): Promise<PaginatedResponseDto<PackageOptionDto>> {
    return this.packageOptionsService.findAll(packageId, queryDto);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Retrieve a specific package option by ID within a package',
  })
  @ApiParam({ name: 'packageId', description: 'The ID of the parent package' })
  @ApiParam({ name: 'id', description: 'The ID of the package option' })
  @ApiResponse({ status: 200, description: 'Detail', type: PackageOptionDto })
  @ApiResponse({ status: 404, description: 'Not Found' })
  findOne(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<PackageOptionDto> {
    return this.packageOptionsService.findOne(packageId, id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a package option' })
  @ApiParam({
    name: 'packageId',
    description: 'Parent Package UUID',
    type: String,
  })
  @ApiParam({
    name: 'id',
    description: 'Package Option UUID',
    type: String,
  })
  @ApiResponse({ status: 200, description: 'Updated', type: PackageOptionDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  update(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdatePackageOptionDto,
  ): Promise<PackageOptionDto> {
    return this.packageOptionsService.update(packageId, id, updateDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Soft-delete a package option' })
  @ApiParam({ name: 'packageId', description: 'ID of the parent package' })
  @ApiParam({
    name: 'id',
    description: 'ID of the package option to delete',
  })
  @ApiResponse({
    status: 204,
    description: 'Package option soft-deleted successfully.',
  })
  @ApiResponse({ status: 404, description: 'Package or Option not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async remove(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<void> {
    await this.packageOptionsService.remove(packageId, id);
  }
}
