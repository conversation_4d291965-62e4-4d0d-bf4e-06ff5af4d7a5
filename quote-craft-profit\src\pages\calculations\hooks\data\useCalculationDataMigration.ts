/**
 * Migration hook for calculation data fetching
 * 
 * This hook provides a smooth transition from the old 4-API-call approach
 * to the new consolidated endpoint approach. It includes feature detection,
 * fallback logic, and performance monitoring.
 */

import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { 
  useConsolidatedCalculationData, 
  useConsolidatedEndpointAvailability,
  UseConsolidatedCalculationDataOptions,
  CalculationCompleteData,
  isConsolidatedDataLoaded,
} from './useConsolidatedCalculationData';

// Import existing hooks for fallback
import { useCalculationDetail } from './useCalculationDetail';
import { useCalculationLineItems } from './useCalculationLineItems';
import { usePackagesByCategory } from './usePackagesByCategory';
import { useCategories } from './useCategories';

/**
 * Migration strategy options
 */
export type MigrationStrategy = 
  | 'consolidated-only'     // Use only the new consolidated endpoint
  | 'legacy-only'          // Use only the old separate endpoints
  | 'auto-detect'          // Automatically detect and use the best approach
  | 'performance-test';    // Test both approaches and compare performance

/**
 * Migration hook options
 */
export interface UseCalculationDataMigrationOptions extends UseConsolidatedCalculationDataOptions {
  /**
   * Migration strategy to use
   * @default 'auto-detect'
   */
  strategy?: MigrationStrategy;
  
  /**
   * Whether to log performance metrics
   * @default false
   */
  enablePerformanceLogging?: boolean;
  
  /**
   * Whether to enable fallback to legacy endpoints on error
   * @default true
   */
  enableFallback?: boolean;
}

/**
 * Result type for the migration hook
 */
export interface CalculationDataMigrationResult {
  // Data
  calculation: any;
  lineItems: any[];
  packagesByCategory: any[];
  categories: any[];
  
  // Loading states
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  
  // Migration metadata
  strategy: MigrationStrategy;
  usingConsolidated: boolean;
  usingFallback: boolean;
  performanceMetrics?: {
    loadTime: number;
    endpointCount: number;
    cacheHits: number;
  };
  
  // Utility functions
  refetch: () => void;
  invalidate: () => void;
}

/**
 * Hook that provides intelligent migration between old and new data fetching approaches
 * 
 * @param calculationId - The calculation ID
 * @param options - Migration options
 * @returns Unified calculation data with migration metadata
 */
export const useCalculationDataMigration = (
  calculationId: string | undefined,
  options: UseCalculationDataMigrationOptions = {}
): CalculationDataMigrationResult => {
  const {
    strategy = 'auto-detect',
    enablePerformanceLogging = false,
    enableFallback = true,
    ...consolidatedOptions
  } = options;

  // Check if consolidated endpoint is available
  const { data: isConsolidatedAvailable, isLoading: isCheckingAvailability } = 
    useConsolidatedEndpointAvailability();

  // Determine which strategy to actually use
  const effectiveStrategy = determineEffectiveStrategy(
    strategy, 
    isConsolidatedAvailable, 
    isCheckingAvailability
  );

  // Use consolidated endpoint
  const consolidatedQuery = useConsolidatedCalculationData(
    calculationId,
    {
      ...consolidatedOptions,
      enabled: effectiveStrategy === 'consolidated-only' && !!calculationId,
    }
  );

  // Use legacy endpoints (for fallback or legacy-only strategy)
  const legacyQueries = useLegacyCalculationData(
    calculationId,
    {
      enabled: (effectiveStrategy === 'legacy-only' || 
                (enableFallback && consolidatedQuery.isError)) && !!calculationId,
    }
  );

  // Determine which data to use
  const usingConsolidated = effectiveStrategy === 'consolidated-only' && 
                           !consolidatedQuery.isError;
  const usingFallback = enableFallback && consolidatedQuery.isError && 
                       !legacyQueries.isLoading && !legacyQueries.isError;

  // Select the appropriate data source
  const selectedData = usingConsolidated ? consolidatedQuery : legacyQueries;

  // Extract data based on source
  const result: CalculationDataMigrationResult = {
    // Data
    calculation: usingConsolidated 
      ? consolidatedQuery.data?.calculation 
      : legacyQueries.calculation,
    lineItems: usingConsolidated 
      ? consolidatedQuery.data?.lineItems || []
      : legacyQueries.lineItems,
    packagesByCategory: usingConsolidated 
      ? consolidatedQuery.data?.packages || []
      : legacyQueries.packagesByCategory,
    categories: usingConsolidated 
      ? consolidatedQuery.data?.categories || []
      : legacyQueries.categories,
    
    // Loading states
    isLoading: selectedData.isLoading || isCheckingAvailability,
    isError: selectedData.isError,
    error: selectedData.error,
    
    // Migration metadata
    strategy: effectiveStrategy,
    usingConsolidated,
    usingFallback,
    performanceMetrics: usingConsolidated && consolidatedQuery.data?.metadata ? {
      loadTime: consolidatedQuery.data.metadata.loadTime,
      endpointCount: 1,
      cacheHits: 0, // Could be enhanced to track cache hits
    } : undefined,
    
    // Utility functions
    refetch: () => {
      if (usingConsolidated) {
        consolidatedQuery.refetch();
      } else {
        legacyQueries.refetch();
      }
    },
    invalidate: () => {
      // This would need to be implemented with queryClient
      console.log('[Migration] Invalidate called - implement with queryClient');
    },
  };

  // Performance logging
  if (enablePerformanceLogging && result.performanceMetrics) {
    console.log('[Migration] Performance metrics:', {
      strategy: effectiveStrategy,
      usingConsolidated,
      usingFallback,
      ...result.performanceMetrics,
    });
  }

  return result;
};

/**
 * Determine the effective strategy based on availability and user preference
 */
function determineEffectiveStrategy(
  strategy: MigrationStrategy,
  isConsolidatedAvailable: boolean | undefined,
  isCheckingAvailability: boolean
): MigrationStrategy {
  if (isCheckingAvailability) {
    return 'legacy-only'; // Use legacy while checking
  }

  switch (strategy) {
    case 'consolidated-only':
      return isConsolidatedAvailable ? 'consolidated-only' : 'legacy-only';
    
    case 'legacy-only':
      return 'legacy-only';
    
    case 'auto-detect':
      return isConsolidatedAvailable ? 'consolidated-only' : 'legacy-only';
    
    case 'performance-test':
      // For now, default to consolidated if available
      return isConsolidatedAvailable ? 'consolidated-only' : 'legacy-only';
    
    default:
      return 'legacy-only';
  }
}

/**
 * Hook for legacy calculation data fetching (separate endpoints)
 */
function useLegacyCalculationData(
  calculationId: string | undefined,
  options: { enabled: boolean }
) {
  // These hooks would need to be imported from existing implementation
  // For now, providing a mock structure
  
  const calculationQuery = useQuery({
    queryKey: ['calculation', calculationId],
    queryFn: () => {
      // Mock implementation - replace with actual useCalculationDetail
      throw new Error('Legacy hooks not yet implemented in this migration');
    },
    enabled: options.enabled,
  });

  const lineItemsQuery = useQuery({
    queryKey: ['lineItems', calculationId],
    queryFn: () => {
      // Mock implementation - replace with actual useCalculationLineItems
      throw new Error('Legacy hooks not yet implemented in this migration');
    },
    enabled: options.enabled,
  });

  const packagesQuery = useQuery({
    queryKey: ['packages', calculationId],
    queryFn: () => {
      // Mock implementation - replace with actual usePackagesByCategory
      throw new Error('Legacy hooks not yet implemented in this migration');
    },
    enabled: options.enabled,
  });

  const categoriesQuery = useQuery({
    queryKey: ['categories'],
    queryFn: () => {
      // Mock implementation - replace with actual useCategories
      throw new Error('Legacy hooks not yet implemented in this migration');
    },
    enabled: options.enabled,
  });

  return {
    calculation: calculationQuery.data,
    lineItems: lineItemsQuery.data || [],
    packagesByCategory: packagesQuery.data || [],
    categories: categoriesQuery.data || [],
    isLoading: calculationQuery.isLoading || lineItemsQuery.isLoading || 
               packagesQuery.isLoading || categoriesQuery.isLoading,
    isError: calculationQuery.isError || lineItemsQuery.isError || 
             packagesQuery.isError || categoriesQuery.isError,
    error: calculationQuery.error || lineItemsQuery.error || 
           packagesQuery.error || categoriesQuery.error,
    refetch: () => {
      calculationQuery.refetch();
      lineItemsQuery.refetch();
      packagesQuery.refetch();
      categoriesQuery.refetch();
    },
  };
}
