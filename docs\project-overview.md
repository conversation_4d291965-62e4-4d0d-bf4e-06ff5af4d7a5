# Quote Craft Profit - Project Overview

## Project Purpose

Quote Craft Profit is a comprehensive web application designed for event planning professionals to streamline the process of creating detailed cost calculations and generating accurate quotes for events. The application addresses several key challenges faced by event planning businesses:

1. **Complex Pricing Models**: Event services often have different pricing models (per event, per day, per attendee, etc.)
2. **Dynamic Calculations**: Event costs vary based on multiple factors (duration, attendee count, location)
3. **Profitability Tracking**: Businesses need to track estimated profit margins across different events
4. **Quote Generation**: Creating professional quotes quickly and accurately is essential for winning business
5. **Service Catalog Management**: Maintaining a comprehensive catalog of services with pricing variations

## Project Vision

To develop a web platform for event planning businesses to streamline event cost calculations, generate accurate quotes rapidly, manage a detailed service catalogue, and track calculation profitability.

## Project Components

The project consists of two main components:

1. **Frontend Application (quote-craft-profit)**

   - React-based web application with TypeScript
   - User interface for all application features
   - Direct integration with Supabase for authentication and some database operations
   - Communication with the backend API for advanced features

2. **Backend API (event-costing-api)**
   - NestJS-based RESTful API
   - Handles complex business logic
   - Manages template operations
   - Provides advanced calculation features
   - Integrates with Supabase for data storage

## Key Features

### Service Catalog Management

- Define packages with variations and configurable options
- Set up different pricing models (per event, per day, per attendee)
- Configure multi-currency pricing and costs
- Manage city-based availability and venue-specific offerings
- Organize packages by categories and divisions
- Define package dependencies (requires/conflicts)

### Dynamic Calculations

- Create calculations from scratch or templates
- Capture essential event details (dates, attendees, city, currency)
- Link calculations to clients and events
- Browse and filter the package catalog
- Add standard catalog items and custom line items
- Automatically calculate quantities based on package rules and event inputs

### Real-time Totals and Profitability

- Display running subtotals that update as items are added/modified
- Calculate final totals including taxes and discounts
- Implement price locking to ensure historical estimates remain consistent
- Track estimated profit based on costs and prices

### User and Access Management

- Role-based permissions (Admin vs. Planner)
- User authentication and authorization
- Save calculations as drafts or mark them as completed
- Export calculation summaries in various formats

### Client and Event Management

- Maintain a database of clients
- Track events with detailed information
- Associate calculations with specific clients and events
- View calculation history by client or event

### Template Management

- Save common configurations as templates
- Create new calculations from templates
- Manage public and private templates
- Version control for templates

## Target Users

1. **Event Planning Companies / Agencies**

   - **Administrators**: Manage catalog, settings, users, templates
   - **Event Planners / Sales Staff**: Create calculations/quotes for clients/events

2. **Secondary Users**
   - **Event clients**: Receive the generated quotes and summaries

## Architecture Overview

### Frontend Architecture

The frontend follows a modern React application architecture:

- **Component-Based Structure**: UI elements are broken down into reusable components
- **Context-Based State Management**: Global state is managed through React Context
- **Query-Based Data Fetching**: Server state is managed with React Query
- **Route-Based Code Organization**: Features are organized by routes/pages

### Backend Architecture

The backend follows a modular NestJS architecture:

- **Module-Based Structure**: Features are organized into cohesive modules
- **Service-Controller Pattern**: Business logic is separated from request handling
- **DTO-Based Validation**: Data transfer objects validate incoming requests
- **Repository Pattern**: Data access is abstracted through repositories

### Database Schema

The database is designed around several core entities:

- **Users**: Application users with roles and permissions
- **Packages**: Service offerings with pricing and options
- **Calculations**: Event cost calculations with line items
- **Clients**: Customer information
- **Events**: Event details and metadata
- **Templates**: Reusable calculation templates

## Folder Structure

### Frontend Structure (quote-craft-profit)

```
src/
├── components/         # Reusable UI components
│   ├── ui/             # Base UI components from shadcn
│   ├── layout/         # Layout components (Navbar, MainLayout, etc.)
│   ├── admin/          # Admin-specific components
│   ├── auth/           # Authentication components
│   ├── calculations/   # Calculation-related components
│   ├── clients/        # Client management components
│   ├── dashboard/      # Dashboard components
│   └── events/         # Event management components
├── contexts/           # React Context providers
├── hooks/              # Custom React hooks
├── integrations/       # Third-party integrations
│   ├── supabase/       # Supabase client and types
│   └── api/            # External API client and configuration
├── lib/                # Utility functions and helpers
├── pages/              # Page components
│   ├── admin/          # Admin pages
│   ├── auth/           # Authentication pages
│   ├── calculations/   # Calculation pages
│   ├── clients/        # Client management pages
│   ├── events/         # Event management pages
│   ├── profile/        # User profile pages
│   └── reports/        # Reporting pages
├── services/           # API service functions
└── types/              # TypeScript type definitions
```

### Backend Structure (event-costing-api)

```
src/
├── main.ts             # Application entry point
├── app.module.ts       # Root module
├── core/               # Core functionality
│   ├── filters/        # Global exception filters
│   ├── supabase/       # Supabase integration
│   └── database/       # Database configuration
├── modules/            # Feature modules
│   ├── auth/           # Authentication module
│   ├── users/          # User management module
│   ├── calculations/   # Calculation module
│   ├── templates/      # Template management module
│   ├── clients/        # Client management module
│   ├── packages/       # Package catalog module
│   ├── categories/     # Category management module
│   ├── cities/         # City management module
│   └── events/         # Event management module
└── shared/             # Shared DTOs and interfaces
```

## Integration Points

### Supabase Integration

Both the frontend and backend integrate with Supabase:

1. **Authentication**: User login, registration, and session management
2. **Database**: PostgreSQL database for data storage
3. **Storage**: File storage for documents and exports

### Frontend-Backend Integration

The frontend communicates with the backend through a RESTful API:

1. **API Client**: Axios-based client for making HTTP requests
2. **Authentication**: JWT tokens for secure communication
3. **Data Transformation**: Mapping between API and UI data structures

## Deployment Strategy

The application is deployed using modern cloud platforms:

1. **Frontend**: Deployed on Vercel for global CDN distribution
2. **Backend**: Deployed on Railway for scalable API hosting
3. **Database**: Hosted on Supabase's managed PostgreSQL service

## Future Roadmap

The project has several planned enhancements:

1. **Advanced Reporting**: Comprehensive reporting and analytics
2. **Mobile Application**: Native mobile experience for on-the-go access
3. **Integration with CRM Systems**: Connect with popular CRM platforms
4. **AI-Powered Recommendations**: Suggest packages based on event requirements
5. **Multi-Language Support**: Localization for international users
