import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getPackageById, savePackage } from '../../../../services/admin/packages/packageService';
import { PackageSaveData } from '../types/package';
import { toast } from 'sonner';
import { QUERY_KEYS } from '../constants';

/**
 * Custom hook for managing package details
 * @param packageId - The package ID (null for new package)
 * @returns Package data, loading state, and save function
 */
export const usePackageDetail = (packageId: string | null) => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('details');

  // Fetch package details
  const {
    data: packageData,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: packageId ? QUERY_KEYS.package(packageId) : ['new-package'],
    queryFn: () => (packageId ? getPackageById(packageId) : null),
    enabled: !!packageId,
    meta: {
      onError: () => {
        toast.error('Failed to load package details');
      },
    },
  });

  // Save package mutation
  const savePackageMutation = useMutation({
    mutationFn: (data: PackageSaveData) => savePackage(data),
    onSuccess: (savedPackage) => {
      toast.success(`Package ${packageId ? 'updated' : 'created'} successfully`);
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.packages() });

      if (packageId) {
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.package(packageId) });
      } else if (savedPackage?.id) {
        // If creating a new package, navigate to the edit page
        navigate(`/admin/packages/${savedPackage.id}`);
      }
    },
    onError: (error) => {
      toast.error(
        `Failed to ${packageId ? 'update' : 'create'} package: ${error.message}`,
      );
    },
    onSettled: () => {
      setIsSaving(false);
    },
  });

  // Handle form submission
  const handleSave = async (data: PackageSaveData) => {
    setIsSaving(true);
    savePackageMutation.mutate({
      ...data,
      id: packageId || undefined,
    });
  };

  return {
    packageData,
    isLoading,
    isError,
    isSaving,
    activeTab,
    setActiveTab,
    handleSave,
    refetch,
  };
};
