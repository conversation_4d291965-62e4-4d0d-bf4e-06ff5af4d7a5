import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export interface ActivityItemProps {
  type: string;
  message: string;
  timestamp: string;
  user?: {
    name: string;
    email: string;
    avatarUrl?: string;
  };
}

const ActivityItem: React.FC<ActivityItemProps> = ({ type, message, timestamp, user }) => {
  const getActivityIcon = () => {
    switch (type) {
      case "user":
        return (
          <div className="p-1 rounded-full bg-blue-100 text-blue-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
        );
      case "package":
        return (
          <div className="p-1 rounded-full bg-green-100 text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M16 16v2a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v4.34"></path>
              <rect x="14" y="12" width="8" height="8" rx="2"></rect>
            </svg>
          </div>
        );
      case "template":
        return (
          <div className="p-1 rounded-full bg-purple-100 text-purple-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
            </svg>
          </div>
        );
      case "calculation":
        return (
          <div className="p-1 rounded-full bg-amber-100 text-amber-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M2 9a3 3 0 0 1 0-6h9a3 3 0 0 1 0 6H2Z"></path>
              <path d="M2 15a3 3 0 0 1 0-6h9a3 3 0 0 1 0 6H2Z"></path>
              <path d="M2 21a3 3 0 0 1 0-6h9a3 3 0 0 1 0 6H2Z"></path>
              <path d="M17 6V3h5v3"></path>
              <path d="M17 12v-3h5v3"></path>
              <path d="M17 18v-3h5v3"></path>
            </svg>
          </div>
        );
      default:
        return (
          <div className="p-1 rounded-full bg-gray-100 text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 8v4l3 3"></path>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
          </div>
        );
    }
  };

  return (
    <div className="flex items-start gap-4 py-3">
      {getActivityIcon()}
      <div className="flex-1 space-y-1">
        <p className="text-sm">{message}</p>
        <div className="flex items-center gap-2">
          {user && (
            <Avatar className="h-5 w-5">
              <AvatarImage src={user.avatarUrl || undefined} alt={user.name} />
              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
            </Avatar>
          )}
          <span className="text-xs text-muted-foreground">{timestamp}</span>
        </div>
      </div>
    </div>
  );
};

export default ActivityItem;
