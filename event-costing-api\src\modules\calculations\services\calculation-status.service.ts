import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { UpdateCalculationStatusDto } from '../dto/update-calculation-status.dto';
import { CalculationStatus } from '../enums/calculation-status.enum';

/**
 * Service responsible for calculation status management
 * Extracted from the main CalculationsService for better separation of concerns
 */
@Injectable()
export class CalculationStatusService {
  private readonly logger = new Logger(CalculationStatusService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Update calculation status with validation
   */
  async updateStatus(
    id: string,
    updateStatusDto: UpdateCalculationStatusDto,
    userId: string,
  ): Promise<void> {
    const newStatus = updateStatusDto.status;
    const allowedStatuses = [
      CalculationStatus.DRAFT,
      CalculationStatus.COMPLETED,
      CalculationStatus.CANCELED,
    ];

    if (!allowedStatuses.includes(newStatus)) {
      this.logger.warn(
        `Invalid status ${newStatus} attempted for calculation ${id}`,
      );
      throw new BadRequestException(`Invalid status: ${newStatus}`);
    }

    this.logger.log(
      `User ${userId} attempting to update status of calculation ${id} to ${newStatus}`,
    );
    const supabase = this.supabaseService.getClient();

    // 1. Fetch current calculation and check ownership
    const { data: currentCalc, error: fetchError } = await supabase
      .from('calculation_history')
      .select('status, created_by')
      .eq('id', id)
      .eq('is_deleted', false)
      .single();

    if (fetchError) {
      this.logger.error(
        `Error fetching calculation ${id} for status update: ${fetchError.message}`,
      );
      if (fetchError.code === 'PGRST116') {
        throw new NotFoundException(`Calculation with ID ${id} not found.`);
      }
      throw new InternalServerErrorException('Could not retrieve calculation.');
    }

    if (!currentCalc) {
      throw new NotFoundException(`Calculation with ID ${id} not found.`);
    }

    if (currentCalc.created_by !== userId) {
      this.logger.warn(
        `User ${userId} attempted to update status on unowned calc ${id}`,
      );
      throw new ForbiddenException('Access denied to this calculation.');
    }

    // 2. Validate status transition
    if (!this.isValidStatusTransition(currentCalc.status, newStatus)) {
      throw new BadRequestException(
        `Invalid status transition from ${currentCalc.status} to ${newStatus}`,
      );
    }

    // 3. Update Status
    const { error: updateError } = await supabase
      .from('calculation_history')
      .update({ status: newStatus })
      .eq('id', id);

    if (updateError) {
      this.logger.error(
        `Failed to update status for calculation ${id}: ${updateError.message}`,
        updateError.stack,
      );
      throw new InternalServerErrorException(
        'Could not update calculation status.',
      );
    }

    this.logger.log(
      `Successfully updated status of calculation ${id} to ${newStatus}`,
    );
  }

  /**
   * Get current status of a calculation
   */
  async getCurrentStatus(calculationId: string): Promise<string> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('calculation_history')
      .select('status')
      .eq('id', calculationId)
      .eq('is_deleted', false)
      .single();

    if (error) {
      this.logger.error(
        `Error fetching status for calculation ${calculationId}: ${error.message}`,
      );
      throw new NotFoundException(
        `Calculation with ID ${calculationId} not found.`,
      );
    }

    return data.status;
  }

  /**
   * Check if a status transition is valid
   */
  private isValidStatusTransition(
    currentStatus: string,
    newStatus: string,
  ): boolean {
    // Define valid status transitions
    const validTransitions: Record<string, string[]> = {
      [CalculationStatus.DRAFT]: [
        CalculationStatus.COMPLETED,
        CalculationStatus.CANCELED,
        CalculationStatus.DRAFT, // Allow staying in draft
      ],
      [CalculationStatus.COMPLETED]: [
        CalculationStatus.DRAFT, // Allow reopening completed calculations
        CalculationStatus.CANCELED,
      ],
      [CalculationStatus.CANCELED]: [
        CalculationStatus.DRAFT, // Allow reopening canceled calculations
      ],
    };

    const allowedTransitions = validTransitions[currentStatus] || [];
    return allowedTransitions.includes(newStatus);
  }

  /**
   * Get all possible next statuses for a calculation
   */
  async getPossibleNextStatuses(calculationId: string): Promise<string[]> {
    const currentStatus = await this.getCurrentStatus(calculationId);

    const validTransitions: Record<string, string[]> = {
      [CalculationStatus.DRAFT]: [
        CalculationStatus.COMPLETED,
        CalculationStatus.CANCELED,
      ],
      [CalculationStatus.COMPLETED]: [
        CalculationStatus.DRAFT,
        CalculationStatus.CANCELED,
      ],
      [CalculationStatus.CANCELED]: [CalculationStatus.DRAFT],
    };

    return validTransitions[currentStatus] || [];
  }

  /**
   * Bulk update status for multiple calculations
   */
  async bulkUpdateStatus(
    calculationIds: string[],
    newStatus: CalculationStatus,
    userId: string,
  ): Promise<{ success: string[]; failed: string[] }> {
    const results = {
      success: [] as string[],
      failed: [] as string[],
    };

    for (const id of calculationIds) {
      try {
        await this.updateStatus(id, { status: newStatus }, userId);
        results.success.push(id);
      } catch (error) {
        this.logger.error(
          `Failed to update status for calculation ${id}: ${error.message}`,
        );
        results.failed.push(id);
      }
    }

    return results;
  }

  /**
   * Get calculations by status for a user
   */
  async getCalculationsByStatus(
    userId: string,
    status: string,
    limit: number = 20,
    offset: number = 0,
  ): Promise<any[]> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('calculation_history')
      .select('id, name, status, created_at, updated_at, total')
      .eq('created_by', userId)
      .eq('status', status)
      .eq('is_deleted', false)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      this.logger.error(
        `Error fetching calculations by status ${status} for user ${userId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve calculations by status.',
      );
    }

    return data || [];
  }

  /**
   * Get status statistics for a user
   */
  async getStatusStatistics(userId: string): Promise<Record<string, number>> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('calculation_history')
      .select('status')
      .eq('created_by', userId)
      .eq('is_deleted', false);

    if (error) {
      this.logger.error(
        `Error fetching status statistics for user ${userId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve status statistics.',
      );
    }

    // Count by status
    const statistics: Record<string, number> = {};
    data.forEach(calc => {
      statistics[calc.status] = (statistics[calc.status] || 0) + 1;
    });

    return statistics;
  }

  /**
   * Check if calculation can be modified based on status
   */
  canModifyCalculation(status: string): boolean {
    // Only draft calculations can be modified
    return status === CalculationStatus.DRAFT;
  }

  /**
   * Check if calculation can be deleted based on status
   */
  canDeleteCalculation(status: string): boolean {
    // Can delete draft and canceled calculations
    return [CalculationStatus.DRAFT, CalculationStatus.CANCELED].includes(
      status as CalculationStatus,
    );
  }

  /**
   * Auto-transition status based on business rules
   */
  async autoTransitionStatus(calculationId: string): Promise<string | null> {
    // Example: Auto-complete calculations with all required data
    // This is a placeholder for business logic

    const currentStatus = await this.getCurrentStatus(calculationId);

    if (currentStatus === CalculationStatus.DRAFT) {
      // Check if calculation has all required data for completion
      // This would involve checking line items, totals, etc.
      // For now, just return null (no auto-transition)
    }

    return null;
  }
}
