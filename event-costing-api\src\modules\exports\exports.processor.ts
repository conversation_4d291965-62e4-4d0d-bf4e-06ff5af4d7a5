import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger, InternalServerErrorException } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { CalculationsService } from '../calculations/calculations.service';
import { ExportFormat } from './enums/export-format.enum';
import { ExportsService } from './exports.service'; // Keep for status updates
import { ExportGenerationService } from './services/export-generation.service';
import { ExportStorageService } from './services/export-storage.service';
import { ExportJobData } from './interfaces/export-job-data.interface';
import { ExportStatus } from './enums/export-status.enum';

@Processor('exportQueue')
export class ExportsProcessor extends WorkerHost {
  private readonly logger = new Logger(ExportsProcessor.name);

  constructor(
    private readonly calculationsService: CalculationsService,
    private readonly exportsService: ExportsService, // Keep for status updates
    private readonly generationService: ExportGenerationService,
    private readonly storageService: ExportStorageService,
  ) {
    super();
  }

  async process(job: Job<ExportJobData>): Promise<void> {
    const { exportHistoryId, calculationId, format, userId } = job.data;
    this.logger.log(
      `Processing job ${job.id} for export history ${exportHistoryId} (Format: ${format})`,
    );

    let tempFilePath: string | undefined = undefined;
    let generatedFileName: string = '';
    let storagePath: string | undefined = undefined;

    try {
      // 1. Update status to PROCESSING
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.PROCESSING,
      );

      // 2. Fetch calculation data (includes ownership check)
      // Use the dedicated export method that handles ownership and User object creation
      const calculationData =
        await this.calculationsService.findCalculationForExport(
          calculationId,
          userId,
        );

      if (!calculationData) {
        throw new InternalServerErrorException(
          `Calculation data not found for ID: ${calculationId}`,
        );
      }

      // 3. Transform data
      const transformedData =
        this.generationService.transformCalculationData(calculationData);

      // 4. Generate file (buffer or temporary file path depending on format)
      const sanitizedCalcName = calculationData.name
        .replace(/[^a-z0-9]/gi, '_')
        .toLowerCase();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      let fileBuffer: Buffer | undefined = undefined;
      let mimeType: string = '';

      if (format === ExportFormat.XLSX) {
        generatedFileName = `${sanitizedCalcName}_${timestamp}.xlsx`;
        mimeType =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        fileBuffer =
          await this.generationService.generateXlsxBuffer(transformedData);
      } else if (format === ExportFormat.PDF) {
        generatedFileName = `${sanitizedCalcName}_${timestamp}.pdf`;
        mimeType = 'application/pdf';
        tempFilePath = path.join(os.tmpdir(), generatedFileName); // Define temp path
        await this.generationService.generatePdfToFile(
          transformedData,
          tempFilePath,
        );
        fileBuffer = fs.readFileSync(tempFilePath); // Read buffer from temp file
      } else if (format === ExportFormat.CSV) {
        generatedFileName = `${sanitizedCalcName}_${timestamp}.csv`;
        mimeType = 'text/csv';
        fileBuffer =
          await this.generationService.generateCsvBuffer(transformedData);
      } else {
        throw new Error(`Unsupported export format: ${format}`);
      }

      if (!fileBuffer) {
        throw new InternalServerErrorException('File buffer not generated.');
      }

      this.logger.log(`Generated file buffer for ${generatedFileName}`);

      // 5. Upload file to storage
      storagePath = await this.storageService.uploadExportFile(
        userId,
        generatedFileName,
        fileBuffer,
        mimeType,
      );

      // 6. Update status to COMPLETED
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.COMPLETED,
        {
          storagePath: storagePath,
          fileName: generatedFileName,
          fileSize: fileBuffer.length,
          mimeType: mimeType,
        },
      );

      this.logger.log(
        `Job ${job.id} completed successfully for export history ${exportHistoryId}`,
      );
    } catch (error: unknown) {
      this.logger.error(
        `Job ${job.id} failed for export history ${exportHistoryId}: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      // 7. Update status to FAILED
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.FAILED,
        {
          error: error instanceof Error ? error.message : String(error),
          // Include fileName/path if generated before failure for context
          fileName: generatedFileName || undefined,
          storagePath: storagePath || undefined,
        },
      );
      throw error; // Re-throw to let BullMQ handle retries/failure state
    } finally {
      // 8. Clean up temporary file if created
      if (tempFilePath) {
        try {
          fs.unlinkSync(tempFilePath);
          this.logger.log(`Temporary file deleted: ${tempFilePath}`);
        } catch (unlinkErr: any) {
          this.logger.warn(
            `Could not delete temporary file ${tempFilePath}: ${unlinkErr instanceof Error ? unlinkErr.message : String(unlinkErr)}`,
          );
        }
      }
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: any) {
    this.logger.log(
      `Job ${job.id} has completed! Result: ${JSON.stringify(result)}`,
    );
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(
      `Job ${job.id} has failed! Error: ${err.message}`,
      err.stack,
    );
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`Job ${job.id} has started`);
  }
}
