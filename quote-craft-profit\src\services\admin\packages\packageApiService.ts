/**
 * Package API Service
 *
 * This service provides methods for interacting with the packages API endpoints.
 * It replaces direct Supabase calls with backend API calls.
 */

import { Package } from '../../../pages/admin/packages/types/package';
import type { PackageFilters } from '../../../pages/admin/packages/types/filters';
import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';
import { PaginatedResult } from '@/types/pagination';

// Interface for package variation
export interface PackageVariation {
  package_id: string;
  name: string;
  description: string | null;
  category_id: string;
  quantity_basis: string;
  price: number;
  unit_base_cost: number;
  is_available_in_city: boolean;
  is_available_in_venue: boolean;
  conflicts_with_selection: boolean;
}

// Interface for package option
export interface PackageOption {
  id: string;
  option_name: string;
  description: string | null;
  price_adjustment: number;
  cost_adjustment: number;
}

/**
 * Get all packages from the backend API with optional filtering
 * @param filters - Optional filters for packages
 * @returns Promise resolving to a paginated result of packages
 */
export const getAllPackagesFromApi = async (
  filters: PackageFilters = {},
): Promise<PaginatedResult<Package>> => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (filters.search) {
      // Map 'search' to 'name' parameter expected by the backend
      queryParams.append('name', filters.search);
    }
    if (filters.categoryId && filters.categoryId !== 'all') {
      queryParams.append('categoryId', filters.categoryId);
    }
    if (filters.divisionId && filters.divisionId !== 'all') {
      queryParams.append('divisionId', filters.divisionId);
    }
    if (filters.cityId && filters.cityId !== 'all') {
      queryParams.append('cityId', filters.cityId);
    }
    if (filters.venueIds && filters.venueIds.length > 0) {
      filters.venueIds.forEach((venueId) => {
        queryParams.append('venueIds', venueId);
      });
    }
    if (filters.excludeId) {
      queryParams.append('excludeId', filters.excludeId);
    }
    // Map showDeleted to isDeleted parameter expected by the backend
    if (filters.showDeleted !== undefined) {
      queryParams.append('isDeleted', filters.showDeleted.toString());
    }
    if (filters.page) {
      queryParams.append('page', filters.page.toString());
    }
    if (filters.pageSize) {
      queryParams.append('pageSize', filters.pageSize.toString());
    }
    if (filters.sortBy) {
      queryParams.append('sortBy', filters.sortBy);
    }
    if (filters.sortOrder) {
      queryParams.append('sortOrder', filters.sortOrder);
    }

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(
      `${API_ENDPOINTS.PACKAGES.GET_ALL}${
        queryParams.toString() ? `?${queryParams.toString()}` : ''
      }`,
    );

    // Check if the response has the expected structure
    if (response.data && typeof response.data === 'object') {
      // If the response is not already in the PaginatedResult format, transform it
      if (!response.data.data && Array.isArray(response.data)) {
        return {
          data: response.data,
          totalCount: response.data.length,
          page: filters.page || 1,
          pageSize: filters.pageSize || 10,
          totalPages: Math.ceil(response.data.length / (filters.pageSize || 10)),
        };
      }

      // Map the backend response to ensure field names match frontend expectations
      if (response.data.data && Array.isArray(response.data.data)) {
        console.log('Raw API response packages:', response.data.data);
        const mappedPackages = response.data.data.map((pkg: any) => {
          console.log('Package before mapping:', pkg);
          const mappedPkg = {
            ...pkg,
            isDeleted: pkg.is_deleted, // Ensure is_deleted is mapped to isDeleted
            currencyId: pkg.currency_id || undefined, // Map currency_id to currencyId if it exists
            updatedAt: pkg.updated_at, // Map updated_at to updatedAt
            createdAt: pkg.created_at, // Map created_at to createdAt
            quantityBasis: pkg.quantity_basis, // Explicitly map quantity_basis to quantityBasis
          };
          console.log('Package after mapping:', mappedPkg);
          return mappedPkg;
        });

        // Transform backend pagination format to frontend format
        const currentPage = filters.page || 1;
        const currentPageSize = filters.pageSize || 10;
        const totalCount = response.data.count || 0;
        const totalPages = Math.ceil(totalCount / currentPageSize);

        return {
          data: mappedPackages,
          totalCount: totalCount,
          page: currentPage,
          pageSize: currentPageSize,
          totalPages: totalPages,
        };
      }

      return response.data;
    }

    throw new Error('Invalid response format from API');
  } catch (error) {
    console.error('Error fetching packages from backend API:', error.message);

    // Return empty result on error
    return {
      data: [],
      totalCount: 0,
      page: filters.page || 1,
      pageSize: filters.pageSize || 10,
      totalPages: 0,
    };
  }
};

/**
 * Get a package by ID from the backend API
 * @param id - The package ID
 * @returns Promise resolving to a package or null
 */
export const getPackageByIdFromApi = async (id: string): Promise<Package | null> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request for package data
    const response = await authClient.get(API_ENDPOINTS.PACKAGES.GET_BY_ID(id));

    // Check if the response has the expected structure
    if (response.data) {
      // Fetch city IDs separately
      let cityIds: string[] = [];
      try {
        const citiesResponse = await authClient.get(API_ENDPOINTS.PACKAGES.CITIES.GET_ALL(id));
        cityIds = citiesResponse.data.map((city: any) => city.id);
      } catch (cityError) {
        console.warn('Failed to fetch city IDs for package:', cityError);
        cityIds = [];
      }

      // Fetch venue IDs separately (if endpoint exists)
      let venueIds: string[] = [];
      try {
        const venuesResponse = await authClient.get(API_ENDPOINTS.PACKAGES.VENUES.GET_ALL(id));
        venueIds = venuesResponse.data.map((venue: any) => venue.id);
      } catch (venueError) {
        console.warn('Failed to fetch venue IDs for package:', venueError);
        venueIds = [];
      }

      // Handle currency ID mapping
      let currencyId = response.data.currency_id;
      if (!currencyId && response.data.currencySymbol) {
        // If no currency_id but we have currencySymbol, try to fetch currencies to map
        try {
          const currenciesResponse = await authClient.get('/currencies');
          const currencies = currenciesResponse.data;
          const matchingCurrency = currencies.find((c: any) => c.code === response.data.currencySymbol);
          if (matchingCurrency) {
            currencyId = matchingCurrency.id;
          }
        } catch (currencyError) {
          console.warn('Failed to fetch currencies for mapping:', currencyError);
        }
      }

      // Map backend field names to frontend field names
      const mappedData = {
        ...response.data,
        // Map ID fields
        categoryId: response.data.category_id || '',
        divisionId: response.data.division_id || '',
        currencyId: currencyId || undefined,
        // Use fetched city and venue IDs
        cityIds: cityIds,
        venueIds: venueIds,
        // Ensure isDeleted is correctly mapped
        isDeleted: response.data.is_deleted,
        // Map date fields
        updatedAt: response.data.updated_at,
        createdAt: response.data.created_at,
        // Explicitly map quantity_basis to quantityBasis
        quantityBasis: response.data.quantity_basis,
      };

      return mappedData;
    }

    throw new Error('Invalid response format from API');
  } catch (error) {
    console.error(`Error fetching package with ID ${id}:`, error.message);
    return null;
  }
};

/**
 * Create or update a package using the backend API
 * @param packageData - The package data to save
 * @returns Promise resolving to the saved package
 */
export const savePackageWithApi = async (packageData: any): Promise<Package> => {
  try {
    // Map frontend field names to backend field names
    const mappedData = {
      id: packageData.id,
      name: packageData.name,
      description: packageData.description,
      category_id: packageData.categoryId,
      division_id: packageData.divisionId,
      quantity_basis: packageData.quantityBasis, // Map quantityBasis to quantity_basis
      is_deleted: packageData.isDeleted,
      // Include city, venue, and price data directly in the API payload
      city_ids: packageData.cityIds,
      enable_venues: packageData.enableVenues,
      venue_ids: packageData.enableVenues ? packageData.venueIds : [],
      price: packageData.price,
      unit_base_cost: packageData.unitBaseCost,
      currency_id: packageData.currencyId,
    };

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    if (packageData.id) {
      // Update existing package
      const response = await authClient.patch(
        API_ENDPOINTS.PACKAGES.UPDATE(packageData.id),
        mappedData,
      );

      return response.data;
    } else {
      // Create new package
      const response = await authClient.post(API_ENDPOINTS.PACKAGES.CREATE, mappedData);

      return response.data;
    }
  } catch (error) {
    console.error('Error saving package with backend API:', error);
    throw error;
  }
};

/**
 * Toggle package status (active/deleted) using the backend API
 * @param packageId - The package ID
 * @param isActive - Whether the package should be active
 * @returns Promise resolving to the updated package
 */
export const togglePackageStatusWithApi = async (
  packageId: string,
  isActive: boolean,
): Promise<Package> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.patch(
      API_ENDPOINTS.PACKAGES.UPDATE_STATUS(packageId),
      { isActive },
    );

    // Return the package data
    return response.data;
  } catch (error) {
    console.error(
      `Error toggling package status for ID ${packageId} using backend API:`,
      error,
    );
    throw error;
  }
};

/**
 * Delete a package using the backend API
 * @param packageId - The ID of the package to delete
 * @returns Promise resolving to void
 */
export const deletePackageWithApi = async (packageId: string): Promise<void> => {
  try {
    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    await authClient.delete(API_ENDPOINTS.PACKAGES.DELETE(packageId));
  } catch (error) {
    console.error(
      `Error deleting package with ID ${packageId} using backend API:`,
      error,
    );
    throw error;
  }
};
