import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js'; // Assuming the Guard passes the Supabase User object

// Define an interface for the expected profile data structure
interface Profile {
  id: string; // Should match User ID
  username?: string;
  full_name?: string;
  phone_number?: string;
  address?: string;
  profile_picture_url?: string;
  company_name?: string;
  preferences?: Record<string, any>; // Adjust if preferences have a stricter type
  city?: string;
  roles?: { role_name: string } | null; // Adjust based on actual roles relation
}

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  async findMyProfile(user: User): Promise<any> {
    if (!user || !user.id) {
      this.logger.error('findMyProfile called with invalid user object', user);
      throw new InternalServerErrorException('Invalid user data received.');
    }
    this.logger.log(`Fetching profile for user ID: ${user.id}`);
    this.logger.debug(`Querying profiles table with user ID: ${user.id}`);
    const supabase = this.supabaseService.getClient();

    // Fetch profile data, type will be inferred or cast later
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const { data, error: profileError } = await supabase
      .from('profiles')
      .select(
        `
        *,
        roles ( role_name )
      `,
      )
      .eq('id', user.id)
      .maybeSingle();

    if (profileError) {
      this.logger.error(
        `Failed to fetch profile for user ${user.id}: ${profileError.message}`,
        profileError.stack,
      );
      throw new InternalServerErrorException(
        'Could not retrieve user profile.',
      );
    }

    if (!data) {
      this.logger.warn(`Profile not found for user ID: ${user.id}`);
      throw new NotFoundException(`Profile not found for user.`);
    }

    // Explicitly cast data to Profile after the null check
    const profileData = data as Profile;

    this.logger.log(`Profile fetched successfully for user ID: ${user.id}`);

    // Combine Supabase auth user info with profile data
    return {
      id: user.id,
      email: user.email,
      lastSignInAt: user.last_sign_in_at,
      createdAt: user.created_at,
      // Access profile fields safely
      username: profileData.username,
      fullName: profileData.full_name,
      phoneNumber: profileData.phone_number,
      address: profileData.address,
      profilePictureUrl: profileData.profile_picture_url,
      companyName: profileData.company_name,
      preferences: profileData.preferences,
      city: profileData.city,
      role: profileData.roles?.role_name || null,
    };
  }
}
