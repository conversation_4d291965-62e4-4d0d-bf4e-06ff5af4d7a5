import React from "react";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatRupiah } from "@/lib/utils";
import { PackageFormValues } from "../../../types/package";
import { QUANTITY_BASIS_OPTIONS } from "../../../constants";
import { FormSection, PriceInput, CurrencySelector } from "../../shared";

interface Currency {
  id: string;
  code: string;
  description: string;
}

interface PricingSectionProps {
  form: UseFormReturn<PackageFormValues>;
  currencies?: Currency[];
  isLoading?: boolean;
}

export const PricingSection: React.FC<PricingSectionProps> = ({
  form,
  currencies = [],
  isLoading = false,
}) => {
  return (
    <FormSection title="Pricing Information" stepNumber={3}>
      <FormField
        control={form.control}
        name="quantityBasis"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="font-medium">Quantity Basis *</FormLabel>
            <Select
              onValueChange={field.onChange as (value: string) => void}
              value={field.value || "PER_EVENT"}
              defaultValue={field.value || "PER_EVENT"}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select quantity basis" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {QUANTITY_BASIS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormDescription>
              Determines how this package is calculated in an event budget
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <CurrencySelector
        form={form}
        name="currencyId"
        currencies={currencies}
        isLoading={isLoading}
        required
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <PriceInput
          form={form}
          name="price"
          label="Base Price"
          description="The base price for this package - Required"
          required
        />

        <PriceInput
          form={form}
          name="unitBaseCost"
          label="Base Cost"
          description="Your internal cost for this package (not visible to clients) - Required"
          required
        />
      </div>

      {form.watch("price") && form.watch("unitBaseCost") && (
        <div className="mt-3 p-3 bg-white dark:bg-gray-800 rounded-md border border-slate-200 dark:border-gray-700">
          <div className="text-sm dark:text-gray-200">
            <span className="font-medium dark:text-white">Profit Margin: </span>
            {(() => {
              const price = parseInt(form.watch("price") || "0");
              const cost = parseInt(form.watch("unitBaseCost") || "0");
              const profit = price - cost;
              const margin = price > 0 ? (profit / price) * 100 : 0;

              return (
                <>
                  {formatRupiah(profit)} ({margin.toFixed(1)}%)
                </>
              );
            })()}
          </div>
        </div>
      )}
    </FormSection>
  );
};
