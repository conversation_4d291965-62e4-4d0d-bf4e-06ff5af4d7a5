/**
 * Core hook for calculation detail data fetching
 * Focused on data fetching and basic state management
 */
import { useCalculation } from "./useCalculation";
import { usePackagesByCategory } from "../data/usePackagesByCategory";
import { useLineItems } from "../data/useLineItems";
import { useQuery } from "@tanstack/react-query";
import { getAllCategories } from "@/services/admin/categories";

/**
 * Core calculation detail data hook
 * Handles the basic data fetching for calculation detail page
 *
 * @param id - The calculation ID
 * @returns Core calculation data and loading states
 */
export const useCalculationDetailCore = (id: string) => {
  // Fetch calculation data
  const {
    data: calculation,
    isLoading: isLoadingCalculation,
    isError: isErrorCalculation,
  } = useCalculation(id);

  // Fetch packages by category
  const {
    data: packagesByCategory = [],
    isLoading: isLoadingPackages,
    isError: isPackagesError,
  } = usePackagesByCategory(id);

  // Fetch line items
  const {
    data: lineItemsData,
    isLoading: isLoadingLineItems,
    isError: isErrorLineItems,
  } = useLineItems(id);

  // Fetch categories for dialogs
  const {
    data: categories = [],
    isLoading: isLoadingCategories,
    isError: isErrorCategories,
  } = useQuery({
    queryKey: ["categories"],
    queryFn: getAllCategories,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Aggregate loading and error states
  const isLoading =
    isLoadingCalculation ||
    isLoadingPackages ||
    isLoadingLineItems ||
    isLoadingCategories;
  const isError = isErrorCalculation || isErrorLineItems || isErrorCategories;

  return {
    // Core data
    calculation,
    packagesByCategory,
    lineItems: lineItemsData || [],
    categories,

    // Loading and error states
    isLoading,
    isLoadingCalculation,
    isLoadingPackages,
    isLoadingLineItems,
    isLoadingCategories,
    isError,
    isErrorCalculation,
    isErrorLineItems,
    isErrorCategories,
    isPackagesError,
  };
};
