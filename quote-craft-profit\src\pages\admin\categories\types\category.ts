/**
 * Category types for the admin categories feature
 */

import { Category as GlobalCategory } from '@/types/types';

// Re-export the global Category interface
export interface Category extends GlobalCategory {}

/**
 * Interface for category response from the API
 */
export interface CategoryResponse {
  /**
   * Unique identifier for the category
   */
  id: string;

  /**
   * Code identifier for the category
   */
  code: string;

  /**
   * Name of the category
   */
  name: string;

  /**
   * Optional description of the category
   */
  description: string | null;

  /**
   * Optional icon identifier for the category
   */
  icon: string | null;

  /**
   * Order in which the category should be displayed (lower values first)
   */
  display_order: number;

  /**
   * Timestamp when the category was created
   */
  created_at: string;

  /**
   * Timestamp when the category was last updated
   */
  updated_at: string;
}

/**
 * Interface for category order item used when updating category order
 */
export interface CategoryOrderItem {
  /**
   * Unique identifier for the category
   */
  id: string;

  /**
   * New display order for the category
   */
  display_order: number;
}
