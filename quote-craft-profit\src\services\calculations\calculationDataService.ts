/**
 * Calculation Data Service
 * PHASE 2 OPTIMIZATION: Consolidated service merging core/calculationService.ts and supabaseCalculationService.ts
 *
 * This service handles all calculation data operations:
 * - Fetching calculations (single and multiple)
 * - Creating calculations
 * - Updating calculations
 * - Deleting calculations
 * - Package data fetching
 * - Calculation summaries
 *
 * Eliminates delegation layers for better performance and maintainability
 */

import { supabase } from "@/integrations/supabase/client";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { toast } from "sonner";
import {
  ApiCalculation,
  CalculationFormData,
  CalculationDetails,
} from "@/types/calculations";
import { CategoryWithPackages, PackageOption } from "@/types/calculation";

/**
 * Fetch all calculations directly from Supabase
 * @returns List of calculations
 */
export const getAllCalculations = async (): Promise<ApiCalculation[]> => {
  try {
    // Get the current user to filter calculations by ownership
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error(
        "User not authenticated. Please sign in to view calculations."
      );
    }

    // Fetch calculations from Supabase with venue information
    // Filter by current user and exclude soft-deleted calculations
    const { data, error } = await supabase
      .from("calculation_history")
      .select(
        `
        *,
        currency:currencies(code),
        city:cities(name),
        client:clients(client_name),
        event:events(event_name),
        calculation_venues(
          venue_id,
          venues(id, name)
        )
      `
      )
      .eq("is_deleted", false)
      .eq("created_by", user.id) // Only fetch calculations created by the current user
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching calculations from Supabase:", error);
      throw error;
    }

    // Transform the data to match the expected format
    const calculations: ApiCalculation[] = data.map((calc) => ({
      id: calc.id,
      name: calc.name,
      status: calc.status,
      attendees: calc.attendees || 0,
      currency: {
        id: calc.currency_id,
        code: calc.currency?.code || "IDR",
        symbol: "Rp", // Hardcoded for now since the currencies table doesn't have a symbol column
      },
      city: {
        id: calc.city_id,
        name: calc.city?.name || "Unknown",
      },
      client: calc.client
        ? {
            id: calc.client_id,
            name: calc.client?.client_name || "Unknown",
          }
        : null,
      event: calc.event
        ? {
            id: calc.event_id,
            name: calc.event?.event_name || "Unknown",
          }
        : null,
      venues: calc.calculation_venues
        ? calc.calculation_venues
            .filter((cv: any) => cv.venues) // Filter out any null venues
            .map((cv: any) => ({
              id: cv.venues.id,
              name: cv.venues.name,
            }))
        : [],
      event_start_date: calc.event_start_date,
      event_end_date: calc.event_end_date,
      created_at: calc.created_at,
      updated_at: calc.updated_at,
      total: calc.total || 0,
      total_cost: calc.total_cost || 0,
      estimated_profit: calc.estimated_profit || 0,
      currency_id: calc.currency_id,
    }));

    // Return the transformed calculations
    return calculations;
  } catch (error) {
    console.error("Error fetching calculations from Supabase:", error);
    toast.error("Failed to load calculations");
    throw error;
  }
};

/**
 * Fetch a calculation by ID directly from Supabase
 * CONSOLIDATED: Direct implementation instead of delegation
 * @param id - The calculation ID
 * @returns The calculation data
 */
export const getCalculationById = async (
  id: string
): Promise<CalculationDetails> => {
  try {
    console.log(`Fetching calculation with ID: ${id} from Supabase`);

    // Fetch the calculation with all related data
    const { data, error } = await supabase
      .from("calculation_history")
      .select(
        `
        *,
        currency:currencies(*),
        city:cities(*),
        client:clients(*),
        event:events(*),
        venues:calculation_venues(
          venue:venues(*)
        )
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      console.error(`Error fetching calculation with ID ${id}:`, error);
      throw error;
    }

    if (!data) {
      throw new Error(`Calculation with ID ${id} not found`);
    }

    // Fetch line items and custom items separately
    const [lineItemsData, customItemsData] = await Promise.all([
      supabase
        .from("calculation_line_items")
        .select("id, item_name_snapshot, package_id")
        .eq("calculation_id", id),
      supabase
        .from("calculation_custom_items")
        .select("id, item_name, description")
        .eq("calculation_id", id),
    ]);

    // Transform the data to match the expected format
    const calculation: CalculationDetails = {
      id: data.id,
      name: data.name,
      status: data.status,
      currency: data.currency,
      city: data.city,
      client: data.client,
      event: data.event,
      venues: data.venues?.map((v: any) => v.venue) || [],
      event_start_date: data.event_start_date,
      event_end_date: data.event_end_date,
      attendees: data.attendees,
      event_type_id: data.event_type_id,
      notes: data.notes,
      total: data.total,
      // Add line items data
      line_items:
        lineItemsData.data?.map((item) => ({
          id: item.id,
          name: item.item_name_snapshot || "Unnamed Item",
          isCustom: false,
        })) || [],
      // Add custom items data
      custom_items:
        customItemsData.data?.map((item) => ({
          id: item.id,
          item_name: item.item_name,
          description: item.description,
        })) || [],
      // Use simplified tax format - ensure each tax has an id
      taxes: Array.isArray(data.taxes)
        ? data.taxes.map((tax: any) => ({
            id:
              tax.id ||
              `tax-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            name: tax.name || "Tax",
            rate: typeof tax.rate === "number" ? tax.rate : 0,
          }))
        : [],
      // Use simplified discount format
      discount: data.discount
        ? (() => {
            try {
              const discountObj = data.discount as any;
              return {
                name:
                  typeof discountObj.name === "string"
                    ? discountObj.name
                    : "Discount",
                type: "fixed" as const,
                value:
                  typeof discountObj.value === "number" ? discountObj.value : 0,
              };
            } catch (error) {
              return {
                name: "Discount",
                type: "fixed" as const,
                value: 0,
              };
            }
          })()
        : null,
    };

    console.log("Calculation data received from Supabase:", calculation);
    return calculation;
  } catch (error) {
    console.error(
      `Error fetching calculation with ID ${id} from Supabase:`,
      error
    );
    toast.error("Failed to load calculation details");
    throw error;
  }
};

/**
 * Fetch calculation summary for template creation
 * @param id - The calculation ID
 * @returns The calculation summary with package and custom item counts
 */
export const getCalculationSummary = async (id: string): Promise<any> => {
  try {
    console.log(`Fetching calculation summary for ID: ${id}`);

    // Use authenticated API client like other services
    const authClient = await getAuthenticatedApiClient();
    const response = await authClient.get(`/calculations/${id}/summary`);

    console.log("Calculation summary response:", response.data);
    return response.data;
  } catch (error) {
    console.error(`Error fetching calculation summary for ID ${id}:`, error);
    toast.error("Failed to load calculation summary");
    throw error;
  }
};

/**
 * Get packages organized by category for a calculation directly from Supabase
 * CONSOLIDATED: Direct implementation instead of delegation
 * @param calculationId - The calculation ID
 * @param includeOptions - Whether to include package options
 * @returns Promise resolving to packages organized by category
 */
export const getPackagesByCategory = async (
  calculationId: string,
  includeOptions: boolean = false
): Promise<CategoryWithPackages[]> => {
  try {
    console.log(
      `Fetching packages by category for calculation ${calculationId} from Supabase`
    );

    // First, get the calculation to determine currency, city, and venue
    const { data: calculation, error: calcError } = await supabase
      .from("calculation_history")
      .select(
        `
        currency_id,
        city_id,
        calculation_venues(venue_id)
      `
      )
      .eq("id", calculationId)
      .single();

    if (calcError) {
      console.error(`Error fetching calculation ${calculationId}:`, calcError);
      throw calcError;
    }

    const currencyId = calculation.currency_id;
    const cityId = calculation.city_id;

    // Get all categories
    const { data: categories, error: catError } = await supabase
      .from("categories")
      .select("*")
      .order("display_order", { ascending: true });

    if (catError) {
      console.error("Error fetching categories:", catError);
      throw catError;
    }

    // Get packages with prices for the specified currency (exclude deleted packages)
    const { data: packages, error: pkgError } = await supabase
      .from("packages")
      .select(
        `
        *,
        package_prices!inner(price, unit_base_cost)
      `
      )
      .eq("package_prices.currency_id", currencyId)
      .eq("is_deleted", false);

    if (pkgError) {
      console.error("Error fetching packages:", pkgError);
      throw pkgError;
    }

    // Filter packages by city if cityId is provided
    let filteredPackages = packages;
    if (cityId) {
      // Get package_cities for the specified city
      const { data: packageCities, error: pcError } = await supabase
        .from("package_cities")
        .select("package_id")
        .eq("city_id", cityId);

      if (pcError) {
        console.error("Error fetching package cities:", pcError);
        throw pcError;
      }

      // Create a set of package IDs available in the city
      const packageIdsInCity = new Set(
        packageCities.map((pc) => pc.package_id)
      );

      // Filter packages to only include those available in the city
      filteredPackages = packages.filter((pkg) => packageIdsInCity.has(pkg.id));
    }

    // Get all venues for the calculation
    const { data: calculationVenues, error: cvError } = await supabase
      .from("calculation_venues")
      .select("venue_id")
      .eq("calculation_id", calculationId);

    if (cvError) {
      console.error("Error fetching calculation venues:", cvError);
      throw cvError;
    }

    // Create a set of venue IDs for the calculation
    const calculationVenueIds = new Set(
      calculationVenues.map((cv) => cv.venue_id)
    );

    // Get all package-venue associations (only active ones)
    const { data: allPackageVenues, error: pvError } = await supabase
      .from("package_venues")
      .select("package_id, venue_id")
      .eq("is_deleted", false);

    if (pvError) {
      console.error("Error fetching package venues:", pvError);
      throw pvError;
    }

    // Create a map of package IDs to their associated venue IDs
    const packageToVenuesMap = new Map<string, Set<string>>();

    allPackageVenues.forEach((pv) => {
      if (!packageToVenuesMap.has(pv.package_id)) {
        packageToVenuesMap.set(pv.package_id, new Set());
      }
      packageToVenuesMap.get(pv.package_id)!.add(pv.venue_id);
    });

    console.log(`Calculation has ${calculationVenueIds.size} venues`);
    console.log(
      `Found ${packageToVenuesMap.size} packages with venue associations`
    );

    // Filter packages based on venue associations
    filteredPackages = filteredPackages.filter((pkg) => {
      // If the package has no venue associations, always include it
      if (!packageToVenuesMap.has(pkg.id)) {
        return true;
      }

      // If the package has venue associations, only include it if one of those venues
      // matches one of the calculation's venues
      const packageVenueIds = packageToVenuesMap.get(pkg.id)!;

      // Check if any of the package's venues match any of the calculation's venues
      for (const venueId of calculationVenueIds) {
        if (packageVenueIds.has(venueId)) {
          return true;
        }
      }

      // No matching venues found
      return false;
    });

    console.log(
      `After venue filtering: ${filteredPackages.length} packages remaining`
    );

    // Fetch package options if requested
    let packageOptions: Record<string, PackageOption[]> = {};
    if (includeOptions) {
      const packageIds = filteredPackages.map((pkg) => pkg.id);

      if (packageIds.length > 0) {
        // Use a simpler query to avoid type issues
        const { data: options, error: optError } = await supabase
          .from("package_options")
          .select("*")
          .in("applicable_package_id", packageIds);

        if (optError) {
          console.error("Error fetching package options:", optError);
          throw optError;
        }

        // Group options by package ID
        packageOptions = (options || []).reduce((acc, opt) => {
          const packageId = opt.applicable_package_id;
          if (!acc[packageId]) {
            acc[packageId] = [];
          }

          // Map to the expected PackageOption format
          acc[packageId].push({
            id: opt.id,
            option_name: opt.option_name,
            description: opt.description || "",
            price_adjustment: opt.price_adjustment || 0,
            is_default_for_package: opt.is_default_for_package || false,
            is_required: opt.is_required || false,
          });
          return acc;
        }, {} as Record<string, PackageOption[]>);
      }
    }

    // Organize packages by category
    const categoryMap = new Map<string, CategoryWithPackages>();

    // Initialize categories
    categories.forEach((cat) => {
      categoryMap.set(cat.id, {
        id: cat.id,
        name: cat.name,
        display_order: cat.display_order,
        packages: [],
      });
    });

    // Add packages to their respective categories
    filteredPackages.forEach((pkg) => {
      const categoryId = pkg.category_id;
      if (categoryMap.has(categoryId)) {
        const category = categoryMap.get(categoryId)!;

        // Create package object with correct field names to match PackageWithOptions type
        const packageObj: any = {
          id: pkg.id,
          name: pkg.name,
          description: pkg.description || "",
          category_id: categoryId,
          category_name: categoryMap.get(categoryId)?.name || "Unknown",
          quantity_basis: pkg.quantity_basis,
          price: pkg.package_prices[0]?.price?.toString() || "0",
          unit_base_cost:
            pkg.package_prices[0]?.unit_base_cost?.toString() || "0",
          currency_symbol: "Rp",
        };

        // Add options if available, otherwise default to empty array
        if (includeOptions) {
          packageObj.options = packageOptions[pkg.id] || [];
        } else {
          packageObj.options = []; // Always include options array to match type
        }

        category.packages.push(packageObj);
      }
    });

    // Convert map to array and filter out categories with no packages
    const result = Array.from(categoryMap.values())
      .filter((cat) => cat.packages.length > 0)
      .sort((a, b) => a.display_order - b.display_order);

    console.log(
      `Successfully fetched ${result.length} categories with packages from Supabase`
    );
    return result;
  } catch (error) {
    console.error(`Error fetching packages by category from Supabase:`, error);
    toast.error("Failed to load packages");
    return [];
  }
};

/**
 * Create a new calculation directly with Supabase
 * CONSOLIDATED: Direct implementation instead of delegation
 * @param calculationData - The calculation data to create
 * @returns The created calculation
 */
export const createCalculation = async (
  calculationData: CalculationFormData
): Promise<CalculationDetails> => {
  try {
    // Prepare the calculation data for creation

    // Format dates to YYYY-MM-DD format
    const formattedStartDate = calculationData.event_start_date.split("T")[0];
    const formattedEndDate = calculationData.event_end_date.split("T")[0];

    // Get the current user ID
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error(
        "User not authenticated. Please sign in to create a calculation."
      );
    }

    // Prepare the payload for Supabase
    const payload: any = {
      name: calculationData.name,
      currency_id: calculationData.currency_id,
      city_id: calculationData.city_id,
      event_start_date: formattedStartDate,
      event_end_date: formattedEndDate,
      attendees: calculationData.attendees,
      event_type_id: calculationData.event_type_id,
      notes: calculationData.notes || "",
      status: calculationData.status,
      created_by: user.id, // Add the current user ID as created_by
    };

    // Only add client_id and event_id if they are defined and not 'none'
    if (calculationData.client_id && calculationData.client_id !== "none") {
      payload.client_id = calculationData.client_id;
    }

    if (calculationData.event_id && calculationData.event_id !== "none") {
      payload.event_id = calculationData.event_id;
    }

    // Insert the calculation into the database
    const { data: calculation, error } = await supabase
      .from("calculation_history")
      .insert(payload)
      .select()
      .single();

    if (error) {
      console.error("Error creating calculation with Supabase:", error);
      throw error;
    }

    // If venue_ids are provided, insert them into calculation_venues
    if (calculationData.venue_ids && calculationData.venue_ids.length > 0) {
      const venuePayloads = calculationData.venue_ids.map((venueId) => ({
        calculation_id: calculation.id,
        venue_id: venueId,
      }));

      const { error: venueError } = await supabase
        .from("calculation_venues")
        .insert(venuePayloads);

      if (venueError) {
        console.error("Error adding venues to calculation:", venueError);
        // Continue anyway, we've already created the calculation
      }
    }

    // Fetch the complete calculation with all related data
    return getCalculationById(calculation.id);
  } catch (error) {
    console.error("Error creating calculation with Supabase:", error);
    toast.error("Failed to create calculation");
    throw error;
  }
};

// NOTE: Update and delete operations have been moved to calculationMutationService.ts
// for better organization and separation of concerns.
