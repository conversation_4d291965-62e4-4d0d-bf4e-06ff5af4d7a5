import {
  Controller,
  Get,
  Logger,
  UseG<PERSON>s,
  Query,
  Param,
  ParseUUIDPipe,
} from '@nestjs/common';
import { DivisionsService } from './divisions.service';
import { DivisionDto } from './dto/division.dto';
import {
  ApiTags,
  ApiOkResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Divisions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('divisions')
export class DivisionsController {
  private readonly logger = new Logger(DivisionsController.name);

  constructor(private readonly divisionsService: DivisionsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all divisions' })
  @ApiOkResponse({
    description: 'List of all divisions',
    type: [DivisionDto],
  })
  @ApiQuery({
    name: 'active',
    required: false,
    type: Boolean,
    description: 'Filter by active status (true/false)',
  })
  async getAllDivisions(
    @Query('active') active?: boolean,
  ): Promise<DivisionDto[]> {
    this.logger.log('Request to get all divisions');
    return await this.divisionsService.findAll(active);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a division by ID' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiOkResponse({
    description: 'Division found',
    type: DivisionDto,
  })
  async getDivisionById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<DivisionDto> {
    this.logger.log(`Request to get division with ID: ${id}`);
    return await this.divisionsService.findOneById(id);
  }
}
