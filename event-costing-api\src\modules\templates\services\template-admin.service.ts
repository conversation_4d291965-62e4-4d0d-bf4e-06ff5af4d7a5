import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { ListAdminTemplatesQueryDto } from '../dto/list-admin-templates.dto';
import {
  PaginatedAdminTemplatesResponse,
  TemplateDetailDto,
} from '../dto/template-summary.dto';
import { UpdateTemplateDto } from '../dto/update-template.dto';
import { TemplateVenueService } from './template-venue.service';
import { TemplateConstants } from '../constants/template.constants';

@Injectable()
export class TemplateAdminService {
  private readonly logger = new Logger(TemplateAdminService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly templateVenueService: TemplateVenueService,
  ) {}

  /**
   * Find all templates (admin endpoint)
   */
  async findAllAdmin(
    queryDto: ListAdminTemplatesQueryDto,
  ): Promise<PaginatedAdminTemplatesResponse> {
    this.logger.log(
      `Admin finding templates with query: ${JSON.stringify(queryDto)}`,
    );
    const supabase = this.supabaseService.getClient();

    let query = supabase
      .from(TemplateConstants.TABLE_NAME)
      .select(TemplateConstants.DETAIL_SELECT_FIELDS, { count: 'exact' });

    // Apply Admin Filters
    if (queryDto.name) {
      query = query.ilike('name', `%${queryDto.name}%`);
    }
    if (typeof queryDto.isPublic === 'boolean') {
      query = query.eq('is_public', queryDto.isPublic);
    }

    // We'll always return both active and inactive templates
    // The frontend will handle filtering based on the is_deleted flag
    // This allows the "Show Inactive Templates" checkbox to work properly
    if (queryDto.cityId) {
      query = query.eq('city_id', queryDto.cityId);
    }
    if (queryDto.categoryId) {
      query = query.eq('category_id', queryDto.categoryId);
    }

    // Apply Sorting
    const sortBy = queryDto.sortBy || 'created_at';
    const sortOrder = queryDto.sortOrder || 'desc';
    const validSortColumns = [
      'name',
      'created_at',
      'updated_at',
      'template_start_date',
      'is_public',
      'is_deleted',
    ];
    const dbSortColumn = validSortColumns.includes(sortBy)
      ? sortBy
      : 'created_at';
    query = query.order(dbSortColumn, {
      ascending: sortOrder === 'asc',
      nullsFirst: false,
    });

    // Apply Pagination
    const limit = queryDto.limit || 20;
    const offset = queryDto.offset || 0;
    query = query.range(offset, offset + limit - 1);

    // Return type matches TemplateDetailDto
    const { data, error, count } = await query;

    if (error) {
      this.logger.error(
        `Error admin fetching templates: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not retrieve templates.');
    }

    // Map raw data if necessary
    const templates: TemplateDetailDto[] = (data || []).map((raw: any) => ({
      ...raw,
      template_start_date: raw.template_start_date
        ? new Date(raw.template_start_date as unknown as string)
        : undefined,
      template_end_date: raw.template_end_date
        ? new Date(raw.template_end_date as unknown as string)
        : undefined,
      created_at: new Date(raw.created_at as unknown as string),
      updated_at: new Date(raw.updated_at as unknown as string),
      is_deleted: raw.is_deleted || false, // Ensure is_deleted is always included
    }));

    // Add venue IDs to templates
    await this.templateVenueService.addVenueIdsToTemplates(templates);

    return { data: templates, count: count || 0 };
  }

  /**
   * Find a single template by ID (admin endpoint)
   */
  async findOneAdmin(id: string): Promise<TemplateDetailDto> {
    this.logger.log(`Admin finding template details for ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(TemplateConstants.TABLE_NAME)
      .select(TemplateConstants.DETAIL_SELECT_FIELDS)
      .eq('id', id)
      // Admin can see deleted templates
      .single<TemplateDetailDto>();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(`Template with ID ${id} not found.`);
      }
      this.logger.error(
        `Error fetching admin template ${id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Could not retrieve template details.',
      );
    }

    if (!data) {
      throw new NotFoundException(`Template with ID ${id} not found.`);
    }

    // Map dates
    const template: TemplateDetailDto = {
      ...data,
      template_start_date: data.template_start_date
        ? new Date(data.template_start_date as unknown as string)
        : undefined,
      template_end_date: data.template_end_date
        ? new Date(data.template_end_date as unknown as string)
        : undefined,
      created_at: new Date(data.created_at as unknown as string),
      updated_at: new Date(data.updated_at as unknown as string),
      is_deleted: data.is_deleted || false, // Ensure is_deleted is always included
    };

    // Add venue IDs to template
    await this.templateVenueService.addVenueIdsToTemplate(template);

    return template;
  }

  /**
   * Update a template
   */
  async updateTemplate(
    id: string,
    updateDto: UpdateTemplateDto,
  ): Promise<TemplateDetailDto> {
    this.logger.log(`Updating template with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    // Prepare update data
    const updateData = {
      name: updateDto.name,
      description: updateDto.description,
      event_type_id: updateDto.event_type_id, // Updated to use event_type_id
      template_start_date: updateDto.template_start_date,
      template_end_date: updateDto.template_end_date,
      is_public: updateDto.is_public,
      updated_at: new Date().toISOString(), // Force update timestamp
    };

    // Update template
    const { data, error } = await supabase
      .from(TemplateConstants.TABLE_NAME)
      .update(updateData)
      .eq('id', id)
      .select(TemplateConstants.DETAIL_SELECT_FIELDS)
      .single<TemplateDetailDto>();

    if (error) {
      this.logger.error(
        `Error updating template: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not update template.');
    }

    if (!data) {
      throw new NotFoundException(`Template with ID ${id} not found.`);
    }

    // Map dates
    const template: TemplateDetailDto = {
      ...data,
      template_start_date: data.template_start_date
        ? new Date(data.template_start_date as unknown as string)
        : undefined,
      template_end_date: data.template_end_date
        ? new Date(data.template_end_date as unknown as string)
        : undefined,
      created_at: new Date(data.created_at as unknown as string),
      updated_at: new Date(data.updated_at as unknown as string),
      is_deleted: data.is_deleted || false, // Ensure is_deleted is always included
    };

    // Add venue IDs to template
    await this.templateVenueService.addVenueIdsToTemplate(template);

    return template;
  }

  /**
   * Update template status (active/inactive)
   */
  async updateTemplateStatus(
    id: string,
    isActive: boolean,
  ): Promise<TemplateDetailDto> {
    this.logger.log(
      `Updating template status for ID: ${id} to ${isActive ? 'active' : 'inactive'}`,
    );
    const supabase = this.supabaseService.getClient();

    // Update is_deleted flag (inverse of isActive)
    const { data, error } = await supabase
      .from(TemplateConstants.TABLE_NAME)
      .update({
        is_deleted: !isActive,
        updated_at: new Date().toISOString(), // Force update timestamp
      })
      .eq('id', id)
      .select(TemplateConstants.DETAIL_SELECT_FIELDS)
      .single<TemplateDetailDto>();

    if (error) {
      this.logger.error(
        `Error updating template status: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Could not update template status.',
      );
    }

    if (!data) {
      throw new NotFoundException(`Template with ID ${id} not found.`);
    }

    // Map dates
    const template: TemplateDetailDto = {
      ...data,
      template_start_date: data.template_start_date
        ? new Date(data.template_start_date as unknown as string)
        : undefined,
      template_end_date: data.template_end_date
        ? new Date(data.template_end_date as unknown as string)
        : undefined,
      created_at: new Date(data.created_at as unknown as string),
      updated_at: new Date(data.updated_at as unknown as string),
      is_deleted: data.is_deleted || false, // Ensure is_deleted is always included
    };

    // Add venue IDs to template
    await this.templateVenueService.addVenueIdsToTemplate(template);

    return template;
  }

  /**
   * Delete a template
   */
  async deleteTemplate(id: string): Promise<void> {
    this.logger.log(`Deleting template with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    // Hard delete
    const { error } = await supabase
      .from(TemplateConstants.TABLE_NAME)
      .delete()
      .eq('id', id);

    if (error) {
      this.logger.error(
        `Error deleting template: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not delete template.');
    }
  }
}
