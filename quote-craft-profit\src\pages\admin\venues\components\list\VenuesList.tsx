import React, { useState } from 'react';
import { toast } from 'sonner';
import {
  Edit,
  MoreHorizontal,
  Loader2,
  RefreshCw,
  Trash2,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { VenueDisplay } from '@/types/venues';
import { deleteVenue, restoreVenue } from '@/services/shared/entities/venues';

interface VenuesListProps {
  venues: VenueDisplay[];
  onEdit: (venueId: string) => void;
  onRefresh: () => void;
}

const VenuesList: React.FC<VenuesListProps> = ({ venues, onEdit, onRefresh }) => {
  const [processingVenues, setProcessingVenues] = useState<Record<string, boolean>>({});

  // Toggle venue status (active/inactive)
  const toggleVenueStatus = async (venue: VenueDisplay) => {
    // Set processing state for this venue
    setProcessingVenues((prev) => ({ ...prev, [venue.id]: true }));

    try {
      if (venue.is_deleted) {
        // Activate venue
        await restoreVenue(venue.id);
        toast.success(`${venue.name} activated successfully`);
      } else {
        // Deactivate venue
        await deleteVenue(venue.id);
        toast.success(`${venue.name} deactivated successfully`);
      }
      onRefresh();
    } catch (error) {
      console.error(`Error toggling status for venue ${venue.id}:`, error);
      toast.error(`Failed to ${venue.is_deleted ? 'activate' : 'deactivate'} venue`);
    } finally {
      setProcessingVenues((prev) => ({ ...prev, [venue.id]: false }));
    }
  };

  // If no venues, show empty state
  if (venues.length === 0) {
    return (
      <div className='text-center py-8 border rounded-lg bg-gray-50'>
        <p className='text-gray-500'>
          No venues found. Try adjusting your filters or add a new venue.
        </p>
      </div>
    );
  }

  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className='w-16'>#</TableHead>
            <TableHead>Venue Name</TableHead>
            <TableHead>City</TableHead>
            <TableHead>Address</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className='text-right'>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {venues.map((venue, index) => (
            <TableRow key={venue.id}>
              <TableCell className='text-muted-foreground'>{index + 1}</TableCell>
              <TableCell className='font-medium'>{venue.name}</TableCell>
              <TableCell>{venue.city_name || 'Not assigned'}</TableCell>
              <TableCell>{venue.address || 'N/A'}</TableCell>
              <TableCell>
                <div className='flex items-center gap-2'>
                  {processingVenues[venue.id] ? (
                    <Loader2 className='h-4 w-4 animate-spin text-muted-foreground' />
                  ) : (
                    <Switch
                      checked={!venue.is_deleted}
                      onCheckedChange={() => toggleVenueStatus(venue)}
                      aria-label={`${venue.is_deleted ? 'Activate' : 'Deactivate'} ${
                        venue.name
                      }`}
                    />
                  )}
                  <span
                    className={
                      venue.is_deleted ? 'text-muted-foreground' : 'text-green-600'
                    }
                  >
                    {venue.is_deleted ? 'Inactive' : 'Active'}
                  </span>
                </div>
              </TableCell>
              <TableCell className='text-right'>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => onEdit(venue.id)}
                  className='h-8 px-2'
                >
                  <Edit className='h-4 w-4 mr-1' />
                  Edit
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default VenuesList;
