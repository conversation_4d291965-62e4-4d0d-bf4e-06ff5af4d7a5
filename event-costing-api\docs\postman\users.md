## Users (`/users`)

### 1. Get My Profile

- **Method:** `GET`
- **URL:** `/users/me`
- **Headers:**
  - `Authorization`: `Bearer <YOUR_SUPABASE_JWT>`
- **Description:** Retrieves the profile information for the currently authenticated user.
- **Success Response (200 OK):**
  ```json
  // Example combined User + Profile structure
  {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "lastSignInAt": "timestamp",
    "createdAt": "timestamp",
    "username": "testuser",
    "fullName": "Test User Full Name",
    "phoneNumber": null,
    "address": null,
    "profilePictureUrl": null,
    "companyName": null,
    "preferences": {},
    "city": null,
    "role": "user" // or "admin", etc.
  }
  ```
- **Error Response (401 Unauthorized):** If token is missing, invalid, or expired.
  ```json
  {
    "statusCode": 401,
    "message": "Authorization token not found" // or "Invalid or expired token"
    // ... other standard error fields
  }
  ```
- **Error Response (404 Not Found):** If the user exists in Supabase Auth but has no corresponding profile record in `public.profiles`.
  ```json
  {
    "statusCode": 404,
    "message": "Profile not found for user.",
    "error": "Not Found"
    // ... other standard error fields
  }
  ```
