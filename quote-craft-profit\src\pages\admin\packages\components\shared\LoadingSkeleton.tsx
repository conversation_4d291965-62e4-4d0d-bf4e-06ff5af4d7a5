import React from "react";

interface LoadingSkeletonProps {
  className?: string;
  height?: string;
  width?: string;
  rounded?: boolean;
}

/**
 * Reusable loading skeleton component
 * Provides consistent loading states across the application
 */
export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  className = "",
  height = "h-4",
  width = "w-full",
  rounded = true,
}) => {
  return (
    <div
      className={`animate-pulse bg-slate-200 dark:bg-slate-700 ${height} ${width} ${
        rounded ? "rounded" : ""
      } ${className}`}
    />
  );
};

/**
 * Form field loading skeleton
 */
export const FormFieldSkeleton: React.FC = () => {
  return (
    <div className="space-y-2">
      <LoadingSkeleton height="h-4" width="w-24" />
      <LoadingSkeleton height="h-10" width="w-full" />
    </div>
  );
};

/**
 * Form section loading skeleton
 */
export const FormSectionSkeleton: React.FC<{
  title?: string;
  fieldCount?: number;
}> = ({ title, fieldCount = 3 }) => {
  return (
    <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700">
      <div className="flex items-center mb-4">
        <LoadingSkeleton height="h-6" width="w-6" className="mr-2" />
        <LoadingSkeleton height="h-5" width="w-32" />
      </div>
      <div className="space-y-4">
        {Array.from({ length: fieldCount }).map((_, index) => (
          <FormFieldSkeleton key={index} />
        ))}
      </div>
    </div>
  );
};

/**
 * Table loading skeleton
 */
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({
  rows = 5,
  columns = 4,
}) => {
  return (
    <div className="border dark:border-slate-700 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-muted dark:bg-slate-800 p-3 border-b dark:border-slate-700">
        <div
          className="grid gap-4"
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, index) => (
            <LoadingSkeleton key={index} height="h-4" width="w-20" />
          ))}
        </div>
      </div>

      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="p-3 border-b dark:border-slate-700 last:border-b-0"
        >
          <div
            className="grid gap-4"
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <LoadingSkeleton key={colIndex} height="h-4" />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
