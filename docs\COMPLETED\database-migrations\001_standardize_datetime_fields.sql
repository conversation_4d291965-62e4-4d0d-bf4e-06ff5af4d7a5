-- =====================================================
-- Database Migration: Standardize DateTime Fields
-- Migration ID: 001_standardize_datetime_fields
-- Description: Convert all date fields to timestamp with time zone
-- Author: System Migration
-- Date: 2025-01-XX
-- =====================================================

-- IMPORTANT: This migration converts date fields to timestamp with time zone
-- All existing date values will be converted to timestamps at midnight UTC

BEGIN;

-- =====================================================
-- STEP 1: Backup existing data (for rollback purposes)
-- =====================================================

-- Create backup table for templates
CREATE TABLE IF NOT EXISTS templates_backup_001 AS 
SELECT * FROM templates;

-- Add migration metadata
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_id VARCHAR(255) NOT NULL,
    table_name VARCHAR(255) NOT NULL,
    column_name VARCHAR(255) NOT NULL,
    old_data_type VARCHAR(255) NOT NULL,
    new_data_type VARCHAR(255) NOT NULL,
    records_affected INTEGER,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'PENDING'
);

-- =====================================================
-- STEP 2: Templates Table Migration
-- =====================================================

-- Log the migration start
INSERT INTO migration_log (migration_id, table_name, column_name, old_data_type, new_data_type, records_affected, status)
VALUES 
    ('001_standardize_datetime_fields', 'templates', 'template_start_date', 'date', 'timestamp with time zone', 
     (SELECT COUNT(*) FROM templates WHERE template_start_date IS NOT NULL), 'STARTED'),
    ('001_standardize_datetime_fields', 'templates', 'template_end_date', 'date', 'timestamp with time zone', 
     (SELECT COUNT(*) FROM templates WHERE template_end_date IS NOT NULL), 'STARTED');

-- Add new timestamp columns
ALTER TABLE templates 
ADD COLUMN template_start_datetime TIMESTAMP WITH TIME ZONE,
ADD COLUMN template_end_datetime TIMESTAMP WITH TIME ZONE;

-- Migrate data: Convert date to timestamp at midnight UTC
UPDATE templates 
SET 
    template_start_datetime = CASE 
        WHEN template_start_date IS NOT NULL 
        THEN template_start_date::timestamp AT TIME ZONE 'UTC'
        ELSE NULL 
    END,
    template_end_datetime = CASE 
        WHEN template_end_date IS NOT NULL 
        THEN template_end_date::timestamp AT TIME ZONE 'UTC'
        ELSE NULL 
    END;

-- Verify data migration
DO $$
DECLARE
    start_count INTEGER;
    end_count INTEGER;
    migrated_start_count INTEGER;
    migrated_end_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO start_count FROM templates WHERE template_start_date IS NOT NULL;
    SELECT COUNT(*) INTO end_count FROM templates WHERE template_end_date IS NOT NULL;
    SELECT COUNT(*) INTO migrated_start_count FROM templates WHERE template_start_datetime IS NOT NULL;
    SELECT COUNT(*) INTO migrated_end_count FROM templates WHERE template_end_datetime IS NOT NULL;
    
    IF start_count != migrated_start_count OR end_count != migrated_end_count THEN
        RAISE EXCEPTION 'Data migration verification failed. Original start: %, migrated start: %, original end: %, migrated end: %', 
            start_count, migrated_start_count, end_count, migrated_end_count;
    END IF;
    
    RAISE NOTICE 'Data migration verified successfully. Start dates: %, End dates: %', migrated_start_count, migrated_end_count;
END $$;

-- Drop old date columns (after verification)
ALTER TABLE templates 
DROP COLUMN template_start_date,
DROP COLUMN template_end_date;

-- Rename new columns to original names
ALTER TABLE templates 
RENAME COLUMN template_start_datetime TO template_start_date;

ALTER TABLE templates 
RENAME COLUMN template_end_datetime TO template_end_date;

-- Update migration log
UPDATE migration_log 
SET status = 'COMPLETED', executed_at = NOW()
WHERE migration_id = '001_standardize_datetime_fields' 
    AND table_name = 'templates';

-- =====================================================
-- STEP 3: Verify Final State
-- =====================================================

-- Verify all datetime fields are now timestamp with time zone
DO $$
DECLARE
    template_start_type TEXT;
    template_end_type TEXT;
BEGIN
    SELECT data_type INTO template_start_type 
    FROM information_schema.columns 
    WHERE table_name = 'templates' AND column_name = 'template_start_date';
    
    SELECT data_type INTO template_end_type 
    FROM information_schema.columns 
    WHERE table_name = 'templates' AND column_name = 'template_end_date';
    
    IF template_start_type != 'timestamp with time zone' OR template_end_type != 'timestamp with time zone' THEN
        RAISE EXCEPTION 'Migration verification failed. Start type: %, End type: %', template_start_type, template_end_type;
    END IF;
    
    RAISE NOTICE 'Schema migration verified successfully. All date fields are now timestamp with time zone.';
END $$;

-- =====================================================
-- STEP 4: Create Rollback Script Reference
-- =====================================================

-- Log rollback instructions
INSERT INTO migration_log (migration_id, table_name, column_name, old_data_type, new_data_type, records_affected, status)
VALUES ('001_standardize_datetime_fields', 'ROLLBACK_INFO', 'templates_backup_001', 'backup_table', 'available', 
        (SELECT COUNT(*) FROM templates_backup_001), 'AVAILABLE');

COMMIT;

-- =====================================================
-- Migration Complete
-- =====================================================
-- 
-- ROLLBACK INSTRUCTIONS (if needed):
-- 1. DROP TABLE templates;
-- 2. ALTER TABLE templates_backup_001 RENAME TO templates;
-- 3. DELETE FROM migration_log WHERE migration_id = '001_standardize_datetime_fields';
--
-- =====================================================
