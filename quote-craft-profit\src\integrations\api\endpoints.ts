/**
 * API endpoints for the application
 */
export const API_ENDPOINTS = {
  /**
   * Authentication endpoints
   */
  AUTH: {
    LOGIN: "/auth/login",
    REGISTER: "/auth/register",
    LOGOUT: "/auth/logout",
    PROFILE: "/auth/profile",
    REFRESH_TOKEN: "/auth/refresh-token",
    FORGOT_PASSWORD: "/auth/forgot-password",
    RESET_PASSWORD: "/auth/reset-password",
  },

  /**
   * User endpoints
   */
  USERS: {
    ME: "/users/me",
    UPDATE_PROFILE: "/users/me",
    CHANGE_PASSWORD: "/users/me/password",
  },

  /**
   * Profile endpoints
   */
  PROFILE: {
    UPLOAD_PICTURE: "/profile/picture",
    UPDATE: "/profile",
  },

  /**
   * Division endpoints
   */
  DIVISIONS: {
    GET_ALL: "/divisions",
    GET_BY_ID: (id: string) => `/admin/divisions/${id}`,
    CREATE: "/admin/divisions",
    UPDATE: (id: string) => `/admin/divisions/${id}`,
    DELETE: (id: string) => `/admin/divisions/${id}`,
  },

  /**
   * Currency endpoints
   */
  CURRENCIES: {
    GET_ALL: "/currencies",
    GET_BY_ID: (id: string) => `/currencies/${id}`,
  },

  /**
   * Category endpoints
   */
  CATEGORIES: {
    GET_ALL: "/categories",
    GET_BY_ID: (id: string) => `/categories/${id}`,
    CREATE: "/categories",
    UPDATE: (id: string) => `/categories/${id}`,
    DELETE: (id: string) => `/categories/${id}`,
    UPDATE_ORDER: "/categories/order",
  },

  /**
   * Package endpoints
   */
  PACKAGES: {
    GET_ALL: "/admin/packages",
    GET_BY_ID: (id: string) => `/admin/packages/${id}`,
    CREATE: "/admin/packages",
    UPDATE: (id: string) => `/admin/packages/${id}`,
    UPDATE_STATUS: (id: string) => `/admin/packages/${id}/status`,
    DELETE: (id: string) => `/admin/packages/${id}`,

    /**
     * Package cities endpoints
     */
    CITIES: {
      GET_ALL: (packageId: string) => `/admin/packages/${packageId}/cities`,
      ADD: (packageId: string) => `/admin/packages/${packageId}/cities`,
      REMOVE: (packageId: string, cityId: string) =>
        `/admin/packages/${packageId}/cities/${cityId}`,
    },

    /**
     * Package venues endpoints
     */
    VENUES: {
      GET_ALL: (packageId: string) => `/admin/packages/${packageId}/venues`,
      ADD: (packageId: string) => `/admin/packages/${packageId}/venues`,
      REMOVE: (packageId: string, venueId: string) =>
        `/admin/packages/${packageId}/venues/${venueId}`,
    },

    /**
     * Package prices endpoints
     */
    PRICES: {
      GET_ALL: (packageId: string) => `/admin/packages/${packageId}/prices`,
      ADD: (packageId: string) => `/admin/packages/${packageId}/prices`,
      UPDATE: (packageId: string, priceId: string) =>
        `/admin/packages/${packageId}/prices/${priceId}`,
      REMOVE: (packageId: string, priceId: string) =>
        `/admin/packages/${packageId}/prices/${priceId}`,
    },

    /**
     * Package variations endpoints (for browsing the catalog)
     */
    VARIATIONS: {
      GET_ALL: "/packages/variations",
      GET_OPTIONS: (packageId: string) =>
        `/packages/variations/${packageId}/options`,
    },

    /**
     * Consolidated package catalog endpoints
     * Replaces multiple separate API calls with unified endpoints
     */
    CATALOG: {
      GET_ALL: "/packages/catalog",
      GET_ADVANCED: "/packages/catalog/advanced",
      GET_SUMMARY: "/packages/catalog/summary",
    },

    /**
     * Package dependency endpoints (Admin)
     */
    DEPENDENCIES: {
      GET_ALL: (packageId: string) =>
        `/admin/packages/${packageId}/dependencies`,
      GET_BY_ID: (packageId: string, dependencyId: string) =>
        `/admin/packages/${packageId}/dependencies/${dependencyId}`,
      CREATE: (packageId: string) =>
        `/admin/packages/${packageId}/dependencies`,
      UPDATE: (packageId: string, dependencyId: string) =>
        `/admin/packages/${packageId}/dependencies/${dependencyId}`,
      DELETE: (packageId: string, dependencyId: string) =>
        `/admin/packages/${packageId}/dependencies/${dependencyId}`,
    },

    /**
     * Package option endpoints (Admin)
     */
    OPTIONS: {
      GET_ALL: (packageId: string) => `/admin/packages/${packageId}/options`,
      GET_BY_ID: (packageId: string, optionId: string) =>
        `/admin/packages/${packageId}/options/${optionId}`,
      CREATE: (packageId: string) => `/admin/packages/${packageId}/options`,
      UPDATE: (packageId: string, optionId: string) =>
        `/admin/packages/${packageId}/options/${optionId}`,
      DELETE: (packageId: string, optionId: string) =>
        `/admin/packages/${packageId}/options/${optionId}`,
    },

    /**
     * Batch package options endpoint
     */
    BATCH_OPTIONS: "/packages/batch-options",
  },

  /**
   * Calculation endpoints
   */
  CALCULATIONS: {
    GET_ALL: "/calculations",
    GET_BY_ID: (id: string) => `/calculations/${id}`,
    GET_SUMMARY: (id: string) => `/calculations/${id}/summary`,
    CREATE: "/calculations",
    UPDATE: (id: string) => `/calculations/${id}`,
    DELETE: (id: string) => `/calculations/${id}`,
    DUPLICATE: (id: string) => `/calculations/${id}/duplicate`,
    EXPORT: (id: string, format: string) =>
      `/calculations/${id}/export/${format}`,
    RECALCULATE: (id: string) => `/calculations/${id}/recalculate`,
    GET_PACKAGES_BY_CATEGORY: (id: string) =>
      `/calculations/${id}/available-packages`,

    /**
     * Consolidated endpoint for complete calculation data
     * Replaces the need for 4 separate API calls
     */
    GET_COMPLETE_DATA: (id: string) => `/calculations/${id}/complete-data`,

    /**
     * Template-to-calculation endpoint
     */
    FROM_TEMPLATE: (templateId: string) =>
      `/calculations/from-template/${templateId}`,

    /**
     * Line item endpoints (updated for new API structure)
     */
    LINE_ITEMS: {
      GET_ALL: (calculationId: string) =>
        `/calculations/${calculationId}/items`,
      GET_BY_ID: (calculationId: string, itemId: string) =>
        `/calculations/${calculationId}/items/${itemId}`,
      ADD_PACKAGE: (calculationId: string) =>
        `/calculations/${calculationId}/line-items/package`,
      ADD_CUSTOM: (calculationId: string) =>
        `/calculations/${calculationId}/line-items/custom`,
      UPDATE: (calculationId: string, itemId: string) =>
        `/calculations/${calculationId}/line-items/${itemId}`,
      DELETE: (calculationId: string, itemId: string) =>
        `/calculations/${calculationId}/line-items/${itemId}`,
    },
  },

  /**
   * Client endpoints
   */
  CLIENTS: {
    LIST: "/clients",
    GET_ALL: "/clients",
    GET_BY_ID: (id: string) => `/clients/${id}`,
    CREATE: "/clients",
    UPDATE: (id: string) => `/clients/${id}`,
    DELETE: (id: string) => `/clients/${id}`,
  },

  /**
   * Event endpoints
   */
  EVENTS: {
    LIST: "/events",
    GET_ALL: "/events",
    GET_BY_ID: (id: string) => `/events/${id}`,
    CREATE: "/events",
    UPDATE: (id: string) => `/events/${id}`,
    DELETE: (id: string) => `/events/${id}`,
  },

  /**
   * Venue endpoints
   */
  VENUES: {
    GET_ALL: "/venues",
    GET_BY_ID: (id: string) => `/venues/${id}`,
  },

  /**
   * Admin Venue endpoints
   */
  ADMIN_VENUES: {
    GET_ALL: "/admin/venues",
    GET_BY_ID: (id: string) => `/admin/venues/${id}`,
    CREATE: "/admin/venues",
    UPDATE: (id: string) => `/admin/venues/${id}`,
    DELETE: (id: string) => `/admin/venues/${id}`,
    RESTORE: (id: string) => `/admin/venues/${id}/restore`,
  },

  /**
   * Template endpoints
   */
  TEMPLATES: {
    // User/Public endpoints (no auth required)
    GET_PUBLIC: "/templates",
    GET_PUBLIC_DETAIL: (id: string) => `/templates/${id}`,
    GET_PUBLIC_DETAIL_ENHANCED: (id: string) => `/templates/${id}/enhanced`,

    // Admin endpoints (auth + admin role required)
    GET_ADMIN_DETAILS: (id: string) => `/admin/templates/${id}`,
    CREATE: "/admin/templates",
    CREATE_FROM_CALCULATION: "/admin/templates/from-calculation",
    UPDATE: (id: string) => `/admin/templates/${id}`,
    UPDATE_STATUS: (id: string) => `/admin/templates/${id}/status`,
    DELETE: (id: string) => `/admin/templates/${id}`,
    LIST: "/admin/templates",
    CALCULATE: (id: string) => `/admin/templates/${id}/calculate`,
    CALCULATE_SUMMARY: (id: string) =>
      `/admin/templates/${id}/calculate/summary`,

    // Legacy endpoints (for backward compatibility)
    GET_ALL: "/templates",
    GET_BY_ID: (id: string) => `/templates/${id}`,
    APPLY: (id: string, calculationId: string) =>
      `/templates/${id}/apply/${calculationId}`,

    /**
     * Consolidated template management endpoints
     * Replaces multiple separate API calls with unified endpoints
     */
    MANAGEMENT: {
      GET_ALL: "/templates/management",
      GET_DETAIL: (id: string) => `/templates/management/${id}/detail`,
      GET_SUMMARY: "/templates/management/summary",
    },
  },

  /**
   * City endpoints
   */
  CITIES: {
    GET_ALL: "/cities",
    GET_BY_ID: (id: string) => `/cities/${id}`,
    CREATE: "/cities",
    UPDATE: (id: string) => `/cities/${id}`,
    DELETE: (id: string) => `/cities/${id}`,
  },

  /**
   * Admin endpoints
   */
  ADMIN: {
    /**
     * Consolidated admin dashboard endpoints
     * Replaces multiple separate API calls with unified endpoints
     */
    DASHBOARD: {
      GET_ALL: "/admin/dashboard",
      GET_SUMMARY: "/admin/dashboard/summary",
      GET_HEALTH: "/admin/dashboard/health",
    },

    // Legacy endpoints
    USERS: "/admin/users",
    SETTINGS: "/admin/settings",
  },

  /**
   * Admin Users endpoints
   */
  ADMIN_USERS: {
    LIST: "/admin/users",
    ROLES: "/admin/users/roles",
    GET_BY_ID: (id: string) => `/admin/users/${id}`,
    CREATE: "/admin/users",
    UPDATE: (id: string) => `/admin/users/${id}`,
    UPDATE_ROLE: (id: string) => `/admin/users/${id}/role`,
    UPDATE_STATUS: (id: string) => `/admin/users/${id}/status`,
  },

  /**
   * Admin Event Types endpoints
   */
  ADMIN_EVENT_TYPES: {
    LIST: "/admin/event-types",
    GET_BY_ID: (id: string) => `/admin/event-types/${id}`,
    CREATE: "/admin/event-types",
    UPDATE: (id: string) => `/admin/event-types/${id}`,
    DELETE: (id: string) => `/admin/event-types/${id}`,
  },

  /**
   * Export endpoints
   */
  EXPORTS: {
    INITIATE: "/exports",
    GET_BY_CALCULATION: (calculationId: string) =>
      `/exports/calculation/${calculationId}`,
    GET_BY_ID: (exportId: string) => `/exports/${exportId}`,
    DOWNLOAD: (exportId: string) => `/exports/${exportId}/download`,

    /**
     * Consolidated export management endpoints
     * Replaces multiple separate API calls with unified endpoints
     */
    MANAGEMENT: {
      GET_ALL: "/exports/management",
      BATCH_INITIATE: "/exports/management/batch",
      GET_SUMMARY: "/exports/management/summary",
    },
  },
};
