import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileText,
  Search,
  Filter,
  Calendar,
  Users,
  MapPin,
  Clock,
} from "lucide-react";
import { Link } from "react-router-dom";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  getPublicTemplates,
  getTemplateEventTypes,
  UserTemplateFilters,
  TemplateListItem,
} from "@/services/templates";
import { CreateCalculationFromTemplateDialog } from "@/pages/templates/components";
import { useTimezoneAwareDates } from "@/hooks/useTimezoneAwareDates";

const TemplatesPage: React.FC = () => {
  const { formatForDisplay } = useTimezoneAwareDates();
  const [searchQuery, setSearchQuery] = useState("");
  const [eventTypeFilter, setEventTypeFilter] = useState("all");
  const [attendeesFilter, setAttendeesFilter] = useState("all");
  const [selectedTemplate, setSelectedTemplate] =
    useState<TemplateListItem | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Build filters for API call
  const filters: UserTemplateFilters = {
    search: searchQuery || undefined,
    eventType: eventTypeFilter !== "all" ? eventTypeFilter : undefined,
    limit: 50, // Reasonable limit for user templates
    sortBy: "created_at",
    sortOrder: "desc",
  };

  // Add attendees filter
  if (attendeesFilter !== "all") {
    switch (attendeesFilter) {
      case "1-50":
        filters.attendeesMin = 1;
        filters.attendeesMax = 50;
        break;
      case "51-100":
        filters.attendeesMin = 51;
        filters.attendeesMax = 100;
        break;
      case "101-500":
        filters.attendeesMin = 101;
        filters.attendeesMax = 500;
        break;
      case "500+":
        filters.attendeesMin = 500;
        break;
    }
  }

  // Fetch templates
  const {
    data: templatesResponse,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["publicTemplates", filters],
    queryFn: () => getPublicTemplates(filters),
    meta: {
      onError: (error: Error) => {
        toast.error(`Failed to load templates: ${error.message}`);
      },
    },
  });

  // Fetch event types for filter dropdown
  const { data: eventTypes = [] } = useQuery({
    queryKey: ["templateEventTypes"],
    queryFn: getTemplateEventTypes,
    meta: {
      onError: (error: Error) => {
        console.error("Failed to load event types:", error);
      },
    },
  });

  const templates = templatesResponse?.data || [];

  // Handle template selection for calculation creation
  const handleUseTemplate = (template: TemplateListItem) => {
    setSelectedTemplate(template);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedTemplate(null);
  };

  return (
    <MainLayout>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Templates</h1>
          <p className="text-gray-600">
            Browse and use templates provided by administrators
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="col-span-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search templates..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div>
              <Select
                value={eventTypeFilter}
                onValueChange={setEventTypeFilter}
              >
                <SelectTrigger>
                  <div className="flex items-center">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Event Type" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Event Types</SelectItem>
                  {eventTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select
                value={attendeesFilter}
                onValueChange={setAttendeesFilter}
              >
                <SelectTrigger>
                  <div className="flex items-center">
                    <Users className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Attendees" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Attendees</SelectItem>
                  <SelectItem value="1-50">1-50 people</SelectItem>
                  <SelectItem value="51-100">51-100 people</SelectItem>
                  <SelectItem value="101-500">101-500 people</SelectItem>
                  <SelectItem value="500+">500+ people</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-1/2 mt-1" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6 mt-2" />
                <div className="flex justify-between mt-4">
                  <Skeleton className="h-9 w-[100px]" />
                  <Skeleton className="h-9 w-[100px]" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : isError ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-red-500">
              Failed to load templates. Please try again later.
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      ) : templates?.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium mb-2">No templates found</h3>
            <p className="text-gray-500 mb-4">
              {searchQuery ||
              eventTypeFilter !== "all" ||
              attendeesFilter !== "all"
                ? "Try adjusting your search or filters"
                : "No public templates are available at the moment"}
            </p>
            {(searchQuery ||
              eventTypeFilter !== "all" ||
              attendeesFilter !== "all") && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery("");
                  setEventTypeFilter("all");
                  setAttendeesFilter("all");
                }}
              >
                Clear filters
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <Card
              key={template.id}
              className="hover:shadow-md transition-shadow hover-scale"
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  {template.event_type && (
                    <Badge
                      variant="outline"
                      className="bg-blue-50 text-blue-700 hover:bg-blue-100"
                    >
                      {template.event_type}
                    </Badge>
                  )}
                </div>
                <CardDescription>
                  Added {formatForDisplay(template.created_at, "MMM d, yyyy")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  {template.description || "No description provided"}
                </p>

                <div className="grid grid-cols-2 gap-2 mb-4">
                  <div className="flex items-center text-xs text-gray-500">
                    <Users className="h-3 w-3 mr-1" />
                    {template.attendees
                      ? `${template.attendees} guests`
                      : "Not specified"}
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="h-3 w-3 mr-1" />
                    {template.is_public ? "Public" : "Private"}
                  </div>
                </div>

                <div className="flex justify-between mt-4">
                  <Link to={`/templates/${template.id}`}>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </Link>
                  <Button size="sm" onClick={() => handleUseTemplate(template)}>
                    <FileText className="h-4 w-4 mr-1" />
                    Use Template
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Calculation Dialog */}
      <CreateCalculationFromTemplateDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        template={selectedTemplate as any} // Type conversion needed due to interface differences
      />
    </MainLayout>
  );
};

export default TemplatesPage;
