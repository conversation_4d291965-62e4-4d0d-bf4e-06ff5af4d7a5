/**
 * React Context for calculation detail page state management
 * Eliminates prop drilling by providing centralized state access
 *
 * This file only exports React components to satisfy Fast Refresh requirements.
 * Hooks are exported from ./calculationHooks.ts
 * Constants are exported from ./calculationConstants.ts
 */
import React, { useMemo, ReactNode } from "react";
import { CalculationContext } from "./context";
import {
  CalculationDetailState,
  CalculationDetailActions,
} from "../types/calculationState";

/**
 * Provider props interface
 */
interface CalculationProviderProps {
  children: ReactNode;
  calculationId: string;
  state: CalculationDetailState;
  actions: CalculationDetailActions;
}

/**
 * Provider component with deep memoization for performance
 * CRITICAL FIX: Prevents infinite re-renders by stabilizing object references
 */
export const CalculationProvider: React.FC<CalculationProviderProps> = ({
  children,
  calculationId,
  state,
  actions,
}) => {
  // CRITICAL FIX: Deep memoization to prevent object recreation
  // Only recreate context value when actual data changes, not object references
  const contextValue = useMemo(() => {
    // Create stable references for the context value
    const stableState = {
      // Core data (stable references)
      calculation: state.calculation,
      lineItems: state.lineItems,
      categories: state.categories,
      packagesByCategory: state.packagesByCategory,

      // UI state (primitive values - stable)
      expandedCategories: state.expandedCategories,
      packageForms: state.packageForms,
      isEditMode: state.isEditMode,
      isSaving: state.isSaving,
      isAddingCustomItem: state.isAddingCustomItem,
      isEditingLineItem: state.isEditingLineItem,
      currentEditingLineItem: state.currentEditingLineItem,
      isDeleting: state.isDeleting,

      // Edit state (primitive values - stable)
      editedName: state.editedName,
      editedEventType: state.editedEventType,
      editedNotes: state.editedNotes,
      editedAttendees: state.editedAttendees,
      dateRange: state.dateRange,

      // Loading state (primitive values - stable)
      isLoading: state.isLoading,
      isLoadingPackages: state.isLoadingPackages,
      isError: state.isError,
      isPackagesError: state.isPackagesError,

      // Financial data (stable references)
      taxes: state.taxes,
      discount: state.discount,
      financialCalculations: state.financialCalculations,

      // Utility functions (stable references)
      formatCurrency: state.formatCurrency,
      formatDate: state.formatDate,

      // Handler functions (stable references)
      setIsAddingCustomItem: state.setIsAddingCustomItem,
      setIsEditingLineItem: state.setIsEditingLineItem,
      setEditedName: state.setEditedName,
      setEditedEventType: state.setEditedEventType,
      setEditedNotes: state.setEditedNotes,
      setEditedAttendees: state.setEditedAttendees,
      setDateRange: state.setDateRange,
      toggleCategory: state.toggleCategory,
      handleQuantityChange: state.handleQuantityChange,
      handleItemQuantityBasisChange: state.handleItemQuantityBasisChange,
      handleOptionToggle: state.handleOptionToggle,
      cleanupPackageForm: state.cleanupPackageForm,
      cleanupMultiplePackageForms: state.cleanupMultiplePackageForms,
      resetAllPackageForms: state.resetAllPackageForms,
      getPackageFormData: state.getPackageFormData,
      handleAddToCalculation: state.handleAddToCalculation,
      handleAddCustomItem: state.handleAddCustomItem,
      handleEditLineItem: state.handleEditLineItem,
      handleUpdateLineItem: state.handleUpdateLineItem,
      handleRemoveLineItem: state.handleRemoveLineItem,
      handleToggleEditMode: state.handleToggleEditMode,
      handleSaveChanges: state.handleSaveChanges,
      addTax: state.addTax,
      updateDiscount: state.updateDiscount,
    };

    const stableActions = {
      handleStatusChange: actions.handleStatusChange,
      handleDelete: actions.handleDelete,
      handleNavigateBack: actions.handleNavigateBack,
      handleNavigateToList: actions.handleNavigateToList,
    };

    return {
      calculationId,
      state: stableState,
      actions: stableActions,
    };
  }, [
    calculationId,
    // Core data dependencies (only change when actual data changes)
    state.calculation?.id,
    state.calculation?.name,
    state.calculation?.status,
    state.lineItems?.length,
    state.categories?.length,
    state.packagesByCategory?.length,

    // UI state dependencies (primitive values)
    state.isEditMode,
    state.isSaving,
    state.isAddingCustomItem,
    state.isEditingLineItem,
    state.currentEditingLineItem?.id,
    state.isDeleting,

    // Edit state dependencies (primitive values)
    state.editedName,
    state.editedEventType,
    state.editedNotes,
    state.editedAttendees,
    state.dateRange?.from,
    state.dateRange?.to,

    // Loading state dependencies (primitive values)
    state.isLoading,
    state.isLoadingPackages,
    state.isError,
    state.isPackagesError,

    // Financial data dependencies (only change when actual data changes)
    state.taxes?.length,
    state.discount?.value,
    state.financialCalculations?.total,

    // Function dependencies (stable references)
    actions.handleStatusChange,
    actions.handleDelete,
    actions.handleNavigateBack,
    actions.handleNavigateToList,
  ]);

  return (
    <CalculationContext.Provider value={contextValue}>
      {children}
    </CalculationContext.Provider>
  );
};
