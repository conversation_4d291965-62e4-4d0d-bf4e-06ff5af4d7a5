# Quote Craft Profit - Developer Guidelines

## Project Overview

Quote Craft Profit is a React-based web application for event planning professionals to create detailed cost calculations and quotes for events. This document outlines the coding standards, architecture, and best practices.

## Technology Stack

- **Frontend**: React 18 + TypeScript 5 + Vite
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: React Query (server state) + React Context (global state)
- **Routing**: React Router
- **Backend**: NestJS API + Supabase (PostgreSQL, Auth, Storage)
- **Forms**: React Hook Form + Zod validation
- **HTTP Client**: Axios

## Architecture

### Feature-Based Structure

```
src/
├── components/         # Reusable UI components
│   ├── ui/             # shadcn/ui base components
│   └── layout/         # Layout components
├── contexts/           # React Context providers
├── hooks/              # Custom React hooks
├── integrations/       # Third-party integrations
│   ├── supabase/       # Supabase client
│   └── api/            # External API client
├── lib/                # Utility functions
├── pages/              # Feature-based pages
│   ├── admin/          # Admin features
│   ├── calculations/   # Calculation features
│   ├── clients/        # Client management
│   └── events/         # Event management
├── services/           # API services (feature-based)
│   ├── admin/          # Admin-specific services (categories, divisions, packages, etc.)
│   ├── calculations/   # Calculation services (direct Supabase integration)
│   └── shared/         # Cross-feature services
│       ├── entities/   # Entity services (cities, clients, currencies, events, venues)
│       └── users/      # User management services
└── types/              # TypeScript definitions
```

### Integration Points

#### Frontend to Backend Communication

1. **Direct Supabase Integration**:
   - Authentication using Supabase Auth
   - Calculation operations (line items, totals, etc.)
   - Real-time subscriptions for live updates

2. **External API Integration**:
   - Admin operations (packages, categories, divisions, templates)
   - Entity management (cities, clients, currencies, events, venues)
   - RESTful API communication using Axios
   - Authenticated requests with JWT tokens

## Development Workflow

### Backend-First Approach
1. **API Development**: Always implement backend endpoints before frontend
2. **Documentation**: Document API contracts before implementation
3. **Testing**: Test backend thoroughly before frontend integration

### Service Architecture
- **Shared Services**: Use `src/services/shared/` for cross-feature functionality
- **Feature Services**: Use `src/services/admin/`, `src/services/calculations/` for feature-specific logic
- **API Integration**: Use external API for complex operations, Supabase for auth/calculations

## Coding Standards

### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use shared types from `src/types/`
- Leverage type inference where possible

### Components
```tsx
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  onClick,
}) => {
  // Implementation
};
```

### State Management
- **Local State**: `useState` for component state
- **Global State**: React Context for app-wide state
- **Server State**: React Query for API data
- **Forms**: React Hook Form + Zod validation

### File Naming & Organization
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Feature-based**: Group by domain, not by type
- **Barrel Exports**: Use `index.ts` files for clean imports

### Styling
- **Tailwind CSS**: Use utility classes for styling
- **shadcn/ui**: Use for consistent UI components
- **Responsive**: Mobile-first design with Tailwind breakpoints

### Data Fetching

#### React Query Pattern
```tsx
const { data, isLoading, isError } = useQuery({
  queryKey: ['packages', filters],
  queryFn: () => getAllPackages(filters),
  meta: {
    onError: () => toast.error('Failed to load packages'),
  },
});
```

#### Service Layer
```tsx
// Shared service example
export const getAllPackages = async (
  filters: PackageFilters = {}
): Promise<PaginatedResult<Package>> => {
  try {
    return await getAllPackagesFromApi(filters);
  } catch (error) {
    console.error('Error in getAllPackages:', error);
    return {
      data: [],
      totalCount: 0,
      page: filters.page || 1,
      pageSize: filters.pageSize || 10,
      totalPages: 0,
    };
  }
};
```

#### API Integration
- **External API**: Use `getAuthenticatedApiClient()` for backend calls
- **Supabase**: Direct client for auth and calculations only
- **Error Handling**: Consistent error responses and user feedback

#### External API Pattern
```tsx
export const getTemplateById = async (id: string): Promise<Template> => {
  try {
    const authClient = await getAuthenticatedApiClient();
    const response = await authClient.get(API_ENDPOINTS.TEMPLATES.GET_BY_ID(id));
    return response.data;
  } catch (error) {
    console.error('Error fetching template:', error);
    throw error;
  }
};
```

## Key Patterns

### Authentication
- **AuthContext**: Manage authentication state
- **ProtectedRoute**: Route protection
- **Role-based Access**: Check user roles for admin features

### Error Handling
- **Forms**: Zod validation with clear error messages
- **API**: Toast notifications for user feedback
- **Boundaries**: Error boundaries for critical components

### UI Components
- **shadcn/ui**: Use for consistent design system
- **Dialogs**: Proper open/close state management
- **Forms**: Use FormField, FormItem, FormLabel pattern
- **Select**: Use "all" instead of empty strings for filters

## API Integration

### Service Organization
- **Feature-based**: Organize by domain (admin, calculations, shared)
- **Shared Services**: Cross-feature functionality in `src/services/shared/`
- **Barrel Exports**: Clean imports through `index.ts` files

### API Client Configuration
```tsx
// Use authenticated client for backend calls
const authClient = await getAuthenticatedApiClient();
const response = await authClient.get(API_ENDPOINTS.PACKAGES.LIST);

// Define endpoints in config
export const API_ENDPOINTS = {
  PACKAGES: {
    LIST: '/api/packages',
    GET_BY_ID: (id: string) => `/api/packages/${id}`,
  },
};
```

### Error Handling Strategy
- **Consistent Patterns**: Same error handling across all services
- **User Feedback**: Toast notifications for user-facing errors
- **Logging**: Detailed console logs for debugging
- **Graceful Degradation**: Fallback UI for error states

## Business Logic

### Calculations
- **Pricing Models**: Per event, per day, per attendee
- **Tax Handling**: Percentage and fixed amounts
- **Profit Margins**: Accurate calculation and display

### Event Workflow
- **Status Lifecycle**: Lead → Planning → Confirmed → Complete
- **Validation**: Proper status transition rules
- **Updates**: Sync calculations with event changes

### Package Management
- **Quantity Bases**: Different pricing models
- **Multi-currency**: Support various currencies
- **Options**: Handle add-ons and variations

### Template Management
- **Creation**: From existing calculations with validation
- **Usage**: Starting point for new calculations
- **Versioning**: Handle template updates and compatibility

## Testing & Performance

### Testing Strategy
- **Unit Tests**: Utility functions and hooks with React Testing Library
- **Integration Tests**: Form submissions and API interactions
- **Manual Testing**: Cross-browser and device testing

### Performance Optimization
- **React Query**: Proper caching and pagination
- **Components**: React.memo, useCallback, useMemo for optimization
- **Bundle**: Code splitting and selective imports

## Deployment

### Environment Setup
- Use environment variables for configuration
- Never commit sensitive data
- Separate staging/production environments

### Build Process
```bash
npm run build  # Vite production build
npm run preview  # Preview build locally
```

### Deployment Platforms
- **Frontend**: Vercel (recommended)
- **Backend**: Railway (recommended)
- **Database**: Supabase (managed PostgreSQL)

## Quick Reference

### Import Patterns
```tsx
// Shared services
import { getAllClients } from '@/services/shared/entities/clients';
import { getAllUsers } from '@/services/shared/users';

// Feature services
import { getAllPackages } from '@/services/admin/packages';
import { saveCalculation } from '@/services/calculations';

// Types
import { PaginatedResult } from '@/types/pagination';
import { Client, Event } from '@/types';
```

### Service Architecture

Our service layer follows a **feature-based architecture with shared services** pattern:

#### **Shared Services** (`src/services/shared/`)
Cross-feature services used by multiple parts of the application:

- **`entities/`** - Core business entities (cities, clients, currencies, events, venues)
  - Used across admin, calculations, and user-facing features
  - Handles basic CRUD operations and data fetching
  - Example: `getAllCities()` used by admin pages, venue forms, and calculations

- **`users/`** - User profile and authentication services
  - User profile management, preferences, session handling
  - Used by both admin and regular user features

#### **Feature-Based Services** (`src/services/admin/`, `src/services/calculations/`)
Feature-specific services with specialized business logic:

- **`admin/`** - Admin-only management operations
  - Categories, divisions, packages, settings, templates, users
  - Complex CRUD with admin-specific validation and permissions
  - Example: `createPackage()` with options, dependencies, and admin workflows

- **`calculations/`** - Calculation-specific logic
  - Event costing calculations, pricing logic, quote generation
  - Direct Supabase integration for performance-critical operations
  - Specialized for calculation workflows and data structures

#### **Service Selection Guidelines**

**Use Shared Services When:**
- Entity is used across multiple features (cities, clients, venues)
- Basic CRUD operations without feature-specific logic
- Data needed by both admin and user-facing components

**Use Feature Services When:**
- Complex business logic specific to one feature area
- Admin-only operations with special permissions
- Feature-specific workflows and validation rules
- Performance-critical operations (like calculations)

### Type Architecture

Our type system follows a **hybrid approach** with both global and feature-specific types:

#### **Global Types** (`src/types/`)
Core entity types used across multiple features:

```typescript
// Core business entities
import { City, Division, Category, Package } from '@/types/types';

// Specialized entity types
import { Venue, VenueDisplay, SaveVenueData } from '@/types/venues';
import { Event, EventStatus } from '@/types/events';

// Utility types
import { PaginatedResult } from '@/types/pagination';
```

**Use Global Types For:**
- Core business entities (City, Division, Category, Package, etc.)
- Types used across multiple features
- Base interfaces that other types extend
- Utility and common types (pagination, API responses)

#### **Feature-Specific Types** (`src/pages/admin/*/types/`, `src/services/*/types/`)
Types specific to particular features or workflows:

```typescript
// Admin-specific extensions
import { PackageWithOptions, PackageDependency } from '@/pages/admin/packages/types';
import { TemplateFormData, TemplateResponse } from '@/pages/admin/templates/types';

// Calculation-specific types
import { CalculationInput, PricingBreakdown } from '@/services/calculations/types';
```

**Use Feature Types For:**
- Extensions of global types with feature-specific fields
- Form data interfaces and validation schemas
- API request/response types specific to a feature
- Complex nested types used only within one feature area

#### **Type Organization Examples**

```typescript
// ✅ Good: Global base type
// src/types/types.ts
export interface Package {
  id: string;
  name: string;
  category_id: string;
  // ... base fields
}

// ✅ Good: Feature-specific extension
// src/pages/admin/packages/types/package.ts
import { Package as GlobalPackage } from '@/types/types';

export interface PackageWithOptions extends GlobalPackage {
  options: PackageOption[];
  dependencies: PackageDependency[];
  // ... admin-specific fields
}

// ✅ Good: Feature-specific form type
export interface PackageFormData {
  name: string;
  description: string;
  category_id: string;
  // ... form-specific structure
}
```

#### **Type Import Patterns**

```typescript
// ✅ Preferred: Use global types for core entities
import { City, Package, Category } from '@/types/types';

// ✅ Good: Use feature types for extensions
import { PackageWithOptions } from '@/pages/admin/packages/types';

// ✅ Good: Use specific type files for specialized types
import { VenueFormMode, SaveVenueData } from '@/types/venues';
```

### Package Management
- **Always use package managers** for dependency management
- **Never edit** package.json, requirements.txt manually
- **Use correct commands**: `npm install`, `yarn add`, etc.

### Recent Improvements

For detailed information about recent architectural improvements, see:
- **[Type Consolidation Improvement Guide](./TYPE_CONSOLIDATION_IMPROVEMENT.md)** - Comprehensive documentation of type consolidation, service layer improvements, and future roadmap

Key improvements include:
- Eliminated duplicate type definitions between admin pages and global types
- Standardized service layer usage across all admin components
- Removed mock data fallbacks for better error handling
- Enhanced API endpoint coverage and consistency

### Key Resources
- [React Query](https://tanstack.com/query/latest) - Server state management
- [shadcn/ui](https://ui.shadcn.com/) - UI component library
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [Supabase](https://supabase.io/docs) - Backend platform
- [NestJS](https://nestjs.com/) - Backend framework

---

*Follow these guidelines to maintain consistent, scalable code architecture.*
