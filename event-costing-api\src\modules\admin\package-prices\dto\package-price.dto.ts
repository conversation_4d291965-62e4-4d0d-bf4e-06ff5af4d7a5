import { ApiProperty } from '@nestjs/swagger';

// DTO for returning package price details
export class PackagePriceDto {
  @ApiProperty({ description: 'Price record UUID', format: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Associated package UUID', format: 'uuid' })
  package_id: string;

  @ApiProperty({ description: 'Associated currency UUID', format: 'uuid' })
  currency_id: string;

  @ApiProperty({ description: 'Base unit price', type: Number })
  price: number;

  @ApiProperty({ description: 'Base unit cost', type: Number })
  unit_base_cost: number;

  @ApiProperty({ description: 'Price description', nullable: true })
  description: string | null;

  @ApiProperty({ description: 'Creation timestamp' })
  created_at: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updated_at: string;
}
