import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { MultiSelectSimple } from '@/components/ui/multi-select-simple';
import { PackageFormValues } from '../../../types/package';
import { Category } from '@/types/types';
import { FormSection } from '../../shared';

interface Division {
  id: string;
  name: string;
}

interface City {
  id: string;
  name: string;
}

interface ClassificationSectionProps {
  form: UseFormReturn<PackageFormValues>;
  categories: Category[];
  divisions: Division[];
  cities: City[];
  isLoading?: boolean;
  hideCitySelection?: boolean;
}

export const ClassificationSection: React.FC<ClassificationSectionProps> = ({
  form,
  categories,
  divisions,
  cities,
  isLoading = false,
  hideCitySelection = false,
}) => {
  return (
    <FormSection title='Classification' stepNumber={2}>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <FormField
            control={form.control}
            name='categoryId'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='font-medium'>Category *</FormLabel>
                <Select
                  key={`category-${field.value}-${categories.length}`}
                  onValueChange={field.onChange}
                  value={field.value || ''}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder='Select a category' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {isLoading ? (
                      <SelectItem value='loading' disabled>
                        Loading categories...
                      </SelectItem>
                    ) : (
                      categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='divisionId'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='font-medium'>Division *</FormLabel>
                <Select
                  key={`division-${field.value}-${divisions.length}`}
                  onValueChange={field.onChange}
                  value={field.value || ''}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder='Select a division' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {isLoading ? (
                      <SelectItem value='loading' disabled>
                        Loading divisions...
                      </SelectItem>
                    ) : (
                      divisions.map((division) => (
                        <SelectItem key={division.id} value={division.id}>
                          {division.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {!hideCitySelection && (
          <FormField
            control={form.control}
            name='cityIds'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='font-medium'>Cities *</FormLabel>
                <FormControl>
                  <MultiSelectSimple
                    key={`cities-${field.value?.join(',')}-${cities.length}`}
                    options={cities.map((city) => ({
                      value: city.id,
                      label: city.name,
                    }))}
                    selected={Array.isArray(field.value) ? field.value : []}
                    onChange={(values) => {
                      field.onChange(values);
                    }}
                    placeholder='Select cities'
                    disabled={isLoading}
                  />
                </FormControl>
                <FormDescription>
                  Select one or more cities where this package is available
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
    </FormSection>
  );
};
