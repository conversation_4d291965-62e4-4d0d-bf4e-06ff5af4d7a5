import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { CurrencyInput } from "@/components/ui/currency-input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCustomItems } from "@/hooks/useCustomItems";
import { QuantityBasisEnum, QuantityBasisLabels } from "@/types/types";
import { customItemSchema, type CustomItemFormValues } from "../../../schemas";
import { useCalculationId, useCalculationCoreData } from "../../../contexts";

interface AddCustomItemDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddCustomItemDialog: React.FC<AddCustomItemDialogProps> = ({
  isOpen,
  onClose,
}) => {
  // Get data from context
  const calculationId = useCalculationId();
  const { categories } = useCalculationCoreData();

  // Get custom items hook
  const { addCustomItem, isAddingCustomItem } = useCustomItems(calculationId);

  // Initialize form
  const form = useForm<CustomItemFormValues>({
    resolver: zodResolver(customItemSchema),
    defaultValues: {
      name: "",
      description: "",
      quantity: 1,
      item_quantity_basis: 1,
      quantity_basis: QuantityBasisEnum.PER_DAY,
      unit_price: 0,
      category_id: "",
    },
  });

  // Handle form submission
  const onSubmit = async (values: CustomItemFormValues) => {
    try {
      // Create custom item input
      const customItemInput = {
        itemName: values.name,
        description: values.description,
        quantity: values.quantity,
        unitPrice: values.unit_price, // Already a number from CurrencyInput
        itemQuantityBasis: values.item_quantity_basis,
        quantityBasis: values.quantity_basis,
        categoryId: values.category_id,
      };

      console.log("Submitting custom item:", customItemInput);

      // Add custom item using the new service
      await new Promise<void>((resolve, reject) => {
        addCustomItem(customItemInput, {
          onSuccess: () => {
            console.log("Custom item added successfully, closing dialog");
            resolve();
          },
          onError: (error) => {
            console.error("Failed to add custom item:", error);
            reject(error);
          },
        });
      });

      // Reset form and close dialog only after successful addition
      form.reset();
      onClose();
    } catch (error) {
      console.error("Error in form submission:", error);
      // Don't close dialog on error, let user retry
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-[500px]"
        aria-describedby="custom-item-description"
      >
        <DialogHeader>
          <DialogTitle>Add Custom Item</DialogTitle>
          <DialogDescription id="custom-item-description">
            Add a custom item to your calculation
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Item Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter item name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter item description"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        placeholder="Enter quantity"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="item_quantity_basis"
                render={({ field }) => {
                  const quantityBasis = form.watch("quantity_basis");

                  // Determine the label based on quantity basis
                  let label = "Units";
                  switch (quantityBasis) {
                    case QuantityBasisEnum.PER_DAY:
                    case QuantityBasisEnum.PER_ITEM_PER_DAY:
                    case QuantityBasisEnum.PER_ATTENDEE_PER_DAY:
                      label = "Days";
                      break;
                    case QuantityBasisEnum.PER_EVENT:
                      label = "Events";
                      break;
                    case QuantityBasisEnum.PER_ATTENDEE:
                      label = "Attendees";
                      break;
                    case QuantityBasisEnum.PER_ITEM:
                      label = "Items";
                      break;
                    default:
                      label = "Units";
                  }

                  return (
                    <FormItem>
                      <FormLabel>{label} *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          placeholder={`Enter number of ${label.toLowerCase()}`}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="unit_price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit Price *</FormLabel>
                    <FormControl>
                      <CurrencyInput
                        value={field.value || 0}
                        onChange={(numericValue) =>
                          field.onChange(numericValue)
                        }
                        placeholder="Enter unit price"
                        showSymbol={true}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quantity_basis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity Basis</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a basis" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(QuantityBasisLabels).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.isArray(categories) && categories.length > 0 ? (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="__no_categories__" disabled>
                            No categories available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div></div> {/* Empty div for grid alignment */}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isAddingCustomItem}>
                {isAddingCustomItem ? "Adding..." : "Add Item"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddCustomItemDialog;
