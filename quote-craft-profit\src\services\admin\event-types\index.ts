import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";

export interface EventType {
  id: string;
  name: string;
  code: string;
  description?: string;
  icon?: string;
  color: string;
  display_order: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreateEventTypeRequest {
  name: string;
  code: string;
  description?: string;
  icon?: string;
  color?: string;
  display_order?: number;
}

export interface UpdateEventTypeRequest {
  name?: string;
  code?: string;
  description?: string;
  icon?: string;
  color?: string;
  display_order?: number;
  is_active?: boolean;
}

/**
 * Get all event types for admin (includes inactive)
 */
export const getAllEventTypesAdmin = async (): Promise<EventType[]> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    const response = await apiClient.get(API_ENDPOINTS.ADMIN_EVENT_TYPES.LIST);
    return response.data.data || [];
  } catch (error) {
    console.error("Error fetching admin event types:", error);
    throw error;
  }
};

/**
 * Get event type by ID
 */
export const getEventTypeById = async (id: string): Promise<EventType> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    const response = await apiClient.get(
      API_ENDPOINTS.ADMIN_EVENT_TYPES.GET_BY_ID(id)
    );
    return response.data.data;
  } catch (error) {
    console.error("Error fetching event type:", error);
    throw error;
  }
};

/**
 * Create new event type
 */
export const createEventType = async (
  data: CreateEventTypeRequest
): Promise<EventType> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    const response = await apiClient.post(
      API_ENDPOINTS.ADMIN_EVENT_TYPES.CREATE,
      data
    );
    return response.data.data;
  } catch (error) {
    console.error("Error creating event type:", error);
    throw error;
  }
};

/**
 * Update event type
 */
export const updateEventType = async (
  id: string,
  data: UpdateEventTypeRequest
): Promise<EventType> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    const response = await apiClient.patch(
      API_ENDPOINTS.ADMIN_EVENT_TYPES.UPDATE(id),
      data
    );
    return response.data.data;
  } catch (error) {
    console.error("Error updating event type:", error);
    throw error;
  }
};

/**
 * Delete event type (soft delete)
 */
export const deleteEventType = async (id: string): Promise<void> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    await apiClient.delete(API_ENDPOINTS.ADMIN_EVENT_TYPES.DELETE(id));
  } catch (error) {
    console.error("Error deleting event type:", error);
    throw error;
  }
};
