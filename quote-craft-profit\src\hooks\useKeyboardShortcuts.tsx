import { useEffect, useCallback, useMemo } from "react";
import { useLocation } from "react-router-dom";

/**
 * Interface for keyboard shortcut configuration
 */
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  description: string;
  action: () => void;
  global?: boolean;
}

/**
 * Custom hook for registering and handling keyboard shortcuts
 * @param shortcuts Array of keyboard shortcut configurations
 * @param enabled Whether shortcuts are enabled (default: true)
 */
export const useKeyboardShortcuts = (
  shortcuts: KeyboardShortcut[],
  enabled = true
) => {
  const location = useLocation();

  // Global shortcuts disabled - empty array to prevent any keyboard shortcuts
  const globalShortcuts: KeyboardShortcut[] = useMemo(() => [], []);

  // Handle keyboard events
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in input fields
      if (
        ["INPUT", "TEXTAREA", "SELECT"].includes(
          (event.target as HTMLElement).tagName
        ) ||
        (event.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      // Check if the shortcut matches any registered shortcuts
      const allShortcuts = [...globalShortcuts, ...shortcuts];

      for (const shortcut of allShortcuts) {
        if (
          event.key.toLowerCase() === shortcut.key.toLowerCase() &&
          !!event.ctrlKey === !!shortcut.ctrlKey &&
          !!event.altKey === !!shortcut.altKey &&
          !!event.shiftKey === !!shortcut.shiftKey &&
          (shortcut.global || enabled)
        ) {
          event.preventDefault();
          shortcut.action();
          return;
        }
      }
    },
    [shortcuts, enabled, globalShortcuts]
  );

  // Register and unregister keyboard event listener
  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown, location.pathname]);

  // Return the list of all shortcuts for documentation
  return {
    shortcuts: [...globalShortcuts, ...shortcuts],
  };
};
