import { z } from 'zod';
import { formFieldPatterns } from '@/schemas/common';

/**
 * Profile-related validation schemas
 * Centralized schemas for all profile forms and components
 */

// Profile form schema (extracted from PersonalInfoSection.tsx)
export const profileFormSchema = z.object({
  fullName: formFieldPatterns.name('Full name'),
  username: z
    .string()
    .min(2, {
      message: 'Username must be at least 2 characters.',
    })
    .optional(),
  phoneNumber: formFieldPatterns.phone(),
  address: formFieldPatterns.address(),
  city: z.string().optional(),
  companyName: z.string().optional(),
});

// Password change schema (extracted from PasswordChangeSection.tsx)
export const passwordFormSchema = z
  .object({
    currentPassword: z.string().min(6, {
      message: 'Current password is required.',
    }),
    newPassword: z.string().min(6, {
      message: 'New password must be at least 6 characters.',
    }),
    confirmPassword: z.string().min(6, {
      message: 'Please confirm your new password.',
    }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// Type exports for TypeScript inference
export type ProfileFormValues = z.infer<typeof profileFormSchema>;
export type PasswordFormValues = z.infer<typeof passwordFormSchema>;
