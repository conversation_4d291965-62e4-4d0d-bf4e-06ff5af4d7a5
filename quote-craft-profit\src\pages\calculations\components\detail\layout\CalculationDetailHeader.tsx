/**
 * Header component for calculation detail page
 * Shows the calculation name, status, and navigation
 */
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

interface CalculationDetailHeaderProps {
  name: string;
  status: string;
  onNavigateBack: () => void;
}

const CalculationDetailHeader: React.FC<CalculationDetailHeaderProps> = ({
  name,
  status,
  onNavigateBack,
}) => {
  return (
    <div className="mb-6 flex items-center gap-2">
      <Button variant="ghost" size="sm" onClick={onNavigateBack}>
        <ChevronLeft size={16} />
        <span>Back</span>
      </Button>
      <div className="flex items-center gap-4">
        <h1 className="text-2xl font-bold dark:text-white">{name}</h1>
        <span
          className={`px-2 py-1 rounded text-sm font-medium ${
            status === "completed"
              ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300"
              : status === "canceled"
              ? "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300"
              : "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300"
          }`}
        >
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </div>
    </div>
  );
};

export default CalculationDetailHeader;
