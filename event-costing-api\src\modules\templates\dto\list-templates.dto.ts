import {
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  IsUUI<PERSON>,
  IsISO8601,
} from 'class-validator';
import { Type } from 'class-transformer';

// Default values for pagination
const DEFAULT_LIMIT = 20;
const MAX_LIMIT = 100;
const DEFAULT_OFFSET = 0;

export class ListTemplatesDto {
  @IsOptional()
  @IsString()
  @Min(1)
  search?: string;

  @IsOptional()
  @IsString()
  eventType?: string;

  @IsOptional()
  @IsUUID()
  cityId?: string;

  @IsOptional()
  @IsUUID()
  categoryId?: string; // Matches DB function parameter p_category_id

  @IsOptional()
  @IsISO8601()
  dateStart?: string; // Matches DB function parameter p_date_start (now ISO datetime)

  @IsOptional()
  @IsISO8601()
  dateEnd?: string; // Matches DB function parameter p_date_end (now ISO datetime)

  @IsOptional()
  @IsString()
  sortBy?: string = 'created_at'; // Matches DB function parameter p_sort_by

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc'; // Matches DB function parameter p_sort_order

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(MAX_LIMIT)
  limit?: number = DEFAULT_LIMIT; // Matches DB function parameter p_limit

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  offset?: number = DEFAULT_OFFSET; // Matches DB function parameter p_offset
}
