import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
// import { ExportStatus } from '../enums/export-status.enum'; // Not used here
// import { User } from '@supabase/supabase-js'; // Not used here

@Injectable()
export class ExportStorageService {
  private readonly logger = new Logger(ExportStorageService.name);
  private readonly BUCKET_NAME = 'calculation-exports'; // Centralize bucket name

  constructor(private readonly supabaseService: SupabaseService) {}

  async uploadExportFile(
    userId: string,
    fileName: string,
    fileBuffer: Buffer,
    mimeType: string,
  ): Promise<string> {
    const storagePath = `exports/${userId}/${fileName}`;
    this.logger.log(`Attempting to upload file to ${storagePath}`);
    const supabase = this.supabaseService.getClient();

    const { error: uploadError } = await supabase.storage
      .from(this.BUCKET_NAME)
      .upload(storagePath, fileBuffer, {
        upsert: true,
        contentType: mimeType,
      });

    if (uploadError) {
      this.logger.error(
        `Failed to upload file to Supabase (${storagePath}): ${uploadError.message}`,
        uploadError.stack,
      );
      throw new InternalServerErrorException('Failed to upload exported file.');
    }

    this.logger.log(`File uploaded successfully to ${storagePath}`);
    return storagePath;
  }

  async getSignedUrl(
    storagePath: string | null | undefined,
  ): Promise<string | null> {
    if (!storagePath) {
      return null;
    }

    this.logger.debug(`Generating signed URL for path: ${storagePath}`);
    const supabase = this.supabaseService.getClient();
    try {
      const { data: urlData, error: urlError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(storagePath, 60 * 60); // 1-hour expiry

      if (urlError) {
        this.logger.error(
          `Failed to create signed URL for ${storagePath}: ${urlError.message}`,
        );
        return null; // Don't fail, just return null
      }
      return urlData?.signedUrl ?? null;
    } catch (error) {
      this.logger.error(
        `Unexpected error generating signed URL for ${storagePath}: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return null;
    }
  }
}
