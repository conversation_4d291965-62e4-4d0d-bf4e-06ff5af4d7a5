import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PostgrestError } from '@supabase/supabase-js';
import { CreatePackageDto } from '../dto/create-package.dto';
import { UpdatePackageDto } from '../dto/update-package.dto';
import { PackageDto } from '../dto/package.dto';

@Injectable()
export class PackageCrudService {
  private readonly logger = new Logger(PackageCrudService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Create a new package with basic data
   * @param createPackageDto - Package creation data
   * @returns Created package ID
   */
  async createPackage(createPackageDto: CreatePackageDto): Promise<string> {
    this.logger.log(
      `Creating package: ${JSON.stringify(createPackageDto)}`,
    );
    const supabase = this.supabaseService.getClient();

    const insertData = {
      name: createPackageDto.name,
      description: createPackageDto.description,
      category_id: createPackageDto.category_id,
      division_id: createPackageDto.division_id,
      variation_group_code: createPackageDto.variation_group_code,
      quantity_basis: createPackageDto.quantity_basis,
      is_deleted: createPackageDto.is_deleted || false,
    };

    const { data: packageData, error: packageError } = await supabase
      .from('packages')
      .insert([insertData])
      .select('id')
      .single();

    if (packageError) {
      this.logger.error(
        `Error creating package: ${packageError.message}`,
        packageError.stack,
      );
      this.handleDatabaseError(packageError, createPackageDto);
    }

    if (!packageData) {
      this.logger.error(
        'Package creation did not return data unexpectedly after a successful insert.',
      );
      throw new InternalServerErrorException(
        'Failed to create package: No data returned.',
      );
    }

    this.logger.log(`Package created successfully with ID: ${packageData.id}`);
    return packageData.id;
  }

  /**
   * Update package basic data
   * @param id - Package ID
   * @param updatePackageDto - Update data
   * @returns Updated package data
   */
  async updatePackage(
    id: string,
    updatePackageDto: UpdatePackageDto,
  ): Promise<PackageDto> {
    this.logger.log(
      `Updating package ID ${id} with data: ${JSON.stringify(updatePackageDto)}`,
    );
    const supabase = this.supabaseService.getClient();

    const updateData = {
      name: updatePackageDto.name,
      description: updatePackageDto.description,
      category_id: updatePackageDto.category_id,
      division_id: updatePackageDto.division_id,
      variation_group_code: updatePackageDto.variation_group_code,
      quantity_basis: updatePackageDto.quantity_basis,
      is_deleted: updatePackageDto.is_deleted,
    };

    // Remove undefined values
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    const { data: packageData, error: packageError } = await supabase
      .from('packages')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single<PackageDto>();

    if (packageError) {
      this.logger.error(
        `Error updating package ${id}: ${packageError.message}`,
        packageError.stack,
      );
      this.handleUpdateError(packageError, id);
    }

    if (!packageData) {
      this.logger.warn(`Package with ID ${id} not found for update.`);
      throw new NotFoundException(`Package with ID ${id} not found.`);
    }

    this.logger.log(`Package ${id} updated successfully`);
    return packageData;
  }

  /**
   * Soft delete a package
   * @param id - Package ID
   */
  async deletePackage(id: string): Promise<void> {
    this.logger.log(`Soft deleting package with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { error, count } = await supabase
      .from('packages')
      .update({ is_deleted: true })
      .match({ id: id, is_deleted: false });

    if (error) {
      this.logger.error(
        `Error soft deleting package with ID ${id}: ${error.message}`,
        error.stack,
      );
      if (error instanceof PostgrestError && error.code === '23503') {
        throw new ConflictException(
          `Cannot delete package ${id} as it is referenced elsewhere. Details: ${error.details}`,
        );
      }
      throw new InternalServerErrorException('Failed to remove package.');
    }

    if (count === 0) {
      this.logger.warn(
        `Package with ID ${id} not found or already deleted for soft deletion.`,
      );
      throw new NotFoundException(
        `Package with ID ${id} not found or already deleted.`,
      );
    }

    this.logger.log(`Package with ID ${id} soft deleted successfully.`);
  }

  /**
   * Update package status (active/inactive)
   * @param id - Package ID
   * @param isActive - Whether the package should be active
   * @returns Updated package data
   */
  async updatePackageStatus(id: string, isActive: boolean): Promise<PackageDto> {
    this.logger.log(
      `Updating status for package ID ${id} to ${isActive ? 'active' : 'inactive'}`,
    );
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('packages')
      .update({
        is_deleted: !isActive,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select('*')
      .single<PackageDto>();

    if (error) {
      this.logger.error(
        `Error updating status for package ${id}: ${error.message}`,
        error.stack,
      );
      if (error instanceof PostgrestError && error.code === 'PGRST116') {
        throw new NotFoundException(`Package with ID ${id} not found.`);
      }
      throw new InternalServerErrorException(
        'Failed to update package status.',
      );
    }

    if (!data) {
      this.logger.warn(`Package with ID ${id} not found for status update.`);
      throw new NotFoundException(`Package with ID ${id} not found.`);
    }

    this.logger.log(`Package status updated successfully for ID: ${id}`);
    return data;
  }

  /**
   * Handle database errors during package creation
   */
  private handleDatabaseError(error: PostgrestError, createPackageDto: CreatePackageDto): never {
    if (error.code === '23505') {
      throw new ConflictException(
        `Package creation failed due to a unique constraint violation. Check name or other unique fields. Details: ${error.details}`,
      );
    }
    if (error.code === '23503') {
      if (error.details?.includes('category_id')) {
        throw new BadRequestException(
          `Invalid category ID provided: ${createPackageDto.category_id}`,
        );
      }
      if (error.details?.includes('division_id')) {
        throw new BadRequestException(
          `Invalid division ID provided: ${createPackageDto.division_id}`,
        );
      }
      throw new BadRequestException(
        `Package creation failed due to an invalid foreign key. Details: ${error.details}`,
      );
    }
    throw new InternalServerErrorException(
      `Failed to create package: ${error?.message || 'Unknown database error'}`,
    );
  }

  /**
   * Handle database errors during package update
   */
  private handleUpdateError(error: PostgrestError, id: string): never {
    if (error.code === 'PGRST116') {
      throw new NotFoundException(`Package with ID ${id} not found for update.`);
    }
    if (error.code === '23505') {
      throw new ConflictException(
        `Update failed due to unique constraint violation. Details: ${error.details}`,
      );
    }
    if (error.code === '23503') {
      throw new BadRequestException(
        `Update failed due to invalid foreign key. Details: ${error.details}`,
      );
    }
    throw new InternalServerErrorException('Failed to update package.');
  }
}
