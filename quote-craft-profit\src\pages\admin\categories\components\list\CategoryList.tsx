import React, { useState } from "react";
import { Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { Category } from "@/types/types";

interface CategoryListProps {
  categories: Category[];
  isLoading: boolean;
  isError: boolean;
  onEdit: (id: string) => void;
}

const CategoryList: React.FC<CategoryListProps> = ({
  categories,
  isLoading,
  isError,
  onEdit,
}) => {
  const [deletingCategoryId, setDeletingCategoryId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const categoryToDelete = categories.find(c => c.id === deletingCategoryId);

  const handleDeleteClick = (id: string) => {
    setDeletingCategoryId(id);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingCategoryId) return;

    setIsDeleting(true);

    try {
      // Check if any packages use this category before deleting
      const { data: packagesUsingCategory, error: checkError } = await supabase
        .from("packages")
        .select("id")
        .eq("category_id", deletingCategoryId)
        .limit(1);

      if (checkError) {
        throw checkError;
      }

      if (packagesUsingCategory && packagesUsingCategory.length > 0) {
        toast.error("Cannot delete this category because it is being used by one or more packages");
        return;
      }

      // Proceed with deletion if no packages are using this category
      const { error } = await supabase
        .from("categories")
        .delete()
        .eq("id", deletingCategoryId);

      if (error) {
        throw error;
      }

      toast.success("Category deleted successfully");
      // Reload the page to refresh the data
      window.location.reload();
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error("Failed to delete category");
    } finally {
      setIsDeleting(false);
      setDeletingCategoryId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeletingCategoryId(null);
  };

  if (isLoading) {
    return <div className="text-center py-8">Loading categories...</div>;
  }

  if (isError) {
    return <div className="text-center py-8 text-red-500">Error loading categories. Please try again.</div>;
  }

  if (categories.length === 0) {
    return (
      <div className="text-center py-16 border rounded-lg">
        <p className="text-muted-foreground mb-4">No categories have been created yet.</p>
        <p className="text-sm text-muted-foreground">Click the "Add New Category" button to create one.</p>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-16">#</TableHead>
              <TableHead className="w-[35%]">Name</TableHead>
              <TableHead className="w-[20%]">Code</TableHead>
              <TableHead className="w-[30%]">Description</TableHead>
              <TableHead className="w-[10%] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categories.map((category, index) => (
              <TableRow key={category.id}>
                <TableCell className="text-muted-foreground">{index + 1}</TableCell>
                <TableCell>{category.name}</TableCell>
                <TableCell>{category.code}</TableCell>
                <TableCell className="truncate max-w-[200px]">
                  {category.description || "-"}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(category.id)}
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteClick(category.id)}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingCategoryId} onOpenChange={() => !isDeleting && setDeletingCategoryId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Category</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the category "{categoryToDelete?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-red-500 hover:bg-red-600"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default CategoryList;
