import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsUUID,
  IsNumber,
  IsBoolean,
  MaxLength,
} from 'class-validator';

export class CreatePackageOptionDto {
  @ApiProperty({
    description: 'Unique code for the option within the package.',
    example: 'OPT_VEGAN',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  option_code: string;

  @ApiProperty({
    description: 'Display name for the option.',
    example: 'Vegan Meal Option',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  option_name: string;

  @ApiProperty({
    description: 'Currency ID for the price/cost adjustments.',
    format: 'uuid',
    example: '685860b9-257f-41eb-b223-b3e1fad8f3b9',
  })
  @IsNotEmpty()
  @IsUUID()
  currency_id: string;

  @ApiProperty({
    description: 'Price adjustment when this option is selected.',
    type: Number,
    default: 0,
  })
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  price_adjustment: number;

  @ApiProperty({
    description: 'Cost adjustment when this option is selected.',
    type: Number,
    default: 0,
  })
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  cost_adjustment: number;

  @ApiProperty({ description: 'Optional description.', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Optional group name for mutual exclusivity.',
    required: false,
    example: 'MEAL_CHOICE',
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  option_group?: string;

  @ApiProperty({
    description: 'Is this option selected by default?',
    type: Boolean,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  is_default_for_package?: boolean;

  @ApiProperty({
    description: 'Is this option mandatory?',
    type: Boolean,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  is_required?: boolean;
}
