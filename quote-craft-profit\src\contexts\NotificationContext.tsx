import React, { createContext, useContext, useState, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";

export type NotificationType = "info" | "success" | "warning" | "error";

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  read: boolean;
  date: Date;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (
    notification: Omit<Notification, "id" | "date" | "read">
  ) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

// Sample notifications for demo purposes
const sampleNotifications: Omit<Notification, "id" | "date" | "read">[] = [
  {
    title: "New Template Available",
    message: "Corporate Event template has been added to your account.",
    type: "info",
    action: {
      label: "View Template",
      onClick: () => (window.location.href = "/templates"),
    },
  },
  {
    title: "Calculation Updated",
    message:
      'Your "Tech Conference 2025" calculation has been updated with new pricing.',
    type: "success",
    action: {
      label: "View Calculation",
      onClick: () => (window.location.href = "/calculations"),
    },
  },
  {
    title: "Package Price Change",
    message:
      "Some package prices have been updated. Review your active calculations.",
    type: "warning",
  },
];

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);

  useEffect(() => {
    // Load notifications from localStorage
    const savedNotifications = localStorage.getItem("notifications");
    if (savedNotifications) {
      const parsedNotifications = JSON.parse(savedNotifications).map(
        (notification: any) => ({
          ...notification,
          date: new Date(notification.date),
        })
      );
      setNotifications(parsedNotifications);
      setUnreadCount(
        parsedNotifications.filter((n: Notification) => !n.read).length
      );
    } else {
      // Add sample notifications for demo purposes
      const initialNotifications = sampleNotifications.map((notification) => ({
        ...notification,
        id: uuidv4(),
        date: new Date(Date.now() - Math.floor(Math.random() * 86400000)), // Random time in the last 24 hours
        read: false,
      }));
      setNotifications(initialNotifications);
      setUnreadCount(initialNotifications.length);
      localStorage.setItem(
        "notifications",
        JSON.stringify(initialNotifications)
      );
    }
  }, []);

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    if (notifications.length > 0) {
      localStorage.setItem("notifications", JSON.stringify(notifications));
      setUnreadCount(notifications.filter((n) => !n.read).length);
    }
  }, [notifications]);

  const addNotification = (
    notification: Omit<Notification, "id" | "date" | "read">
  ) => {
    const newNotification: Notification = {
      ...notification,
      id: uuidv4(),
      date: new Date(),
      read: false,
    };
    setNotifications((prev) => [newNotification, ...prev]);
  };

  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, read: true }))
    );
  };

  const removeNotification = (id: string) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification.id !== id)
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
    localStorage.removeItem("notifications");
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        removeNotification,
        clearAllNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
};
