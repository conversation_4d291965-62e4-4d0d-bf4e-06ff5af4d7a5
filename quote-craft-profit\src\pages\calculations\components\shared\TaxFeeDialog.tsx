
import React, { useState } from "react";
import { TaxFee } from "@/types/types";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface TaxFeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (taxFee: TaxFee) => void;
  initialTaxFee?: TaxFee;
  isEditing?: boolean;
}

const TaxFeeDialog: React.FC<TaxFeeDialogProps> = ({
  open,
  onOpenChange,
  onSave,
  initialTaxFee,
  isEditing = false,
}) => {
  const [name, setName] = useState(initialTaxFee?.name || "");
  const [type, setType] = useState<"percentage" | "fixed">(
    initialTaxFee?.type || "percentage"
  );
  const [value, setValue] = useState(
    initialTaxFee?.value ? initialTaxFee.value.toString() : ""
  );

  const handleSave = () => {
    if (!name || !value) return;

    const newTaxFee: TaxFee = {
      id: initialTaxFee?.id || `tax_${Date.now()}`,
      name,
      type,
      value: parseFloat(value) || 0,
    };

    onSave(newTaxFee);
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isEditing ? "Edit Tax/Fee" : "Add New Tax/Fee"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            Enter the details of the tax or fee for this calculation.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g. PPN, Service Charge, Admin Fee"
            />
          </div>
          <div className="grid gap-2">
            <Label>Type</Label>
            <RadioGroup
              value={type}
              onValueChange={(value) => setType(value as "percentage" | "fixed")}
              className="flex gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="percentage" id="percentage" />
                <Label htmlFor="percentage">Percentage (%)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="fixed" id="fixed" />
                <Label htmlFor="fixed">Fixed Amount (Rp)</Label>
              </div>
            </RadioGroup>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="value">Value</Label>
            <Input
              id="value"
              type="number"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder={type === "percentage" ? "e.g. 11 (for 11%)" : "e.g. 50000"}
              step="0.01"
            />
          </div>
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleSave}>Save</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default TaxFeeDialog;
