import React, { createContext, useContext, useState, useEffect } from 'react';

interface OnboardingContextType {
  isFirstTimeUser: boolean;
  setIsFirstTimeUser: (value: boolean) => void;
  completedSteps: string[];
  markStepAsCompleted: (step: string) => void;
  resetOnboarding: () => void;
  dismissWhatIsNew: () => void;
  showWhatIsNew: boolean;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const ONBOARDING_STEPS = {
  DASHBOARD_INTRO: 'dashboard_intro',
  QUICK_STATS: 'quick_stats',
  RECENT_CALCULATIONS: 'recent_calculations',
  PACKAGES: 'packages',
  TEMPLATES: 'templates',
};

export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isFirstTimeUser, setIsFirstTimeUser] = useState<boolean>(false);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const [showWhatIsNew, setShowWhatIsNew] = useState<boolean>(true);

  useEffect(() => {
    // Check if this is the first time the user is visiting
    const hasVisitedBefore = localStorage.getItem('hasVisitedBefore');
    if (!hasVisitedBefore) {
      setIsFirstTimeUser(true);
      localStorage.setItem('hasVisitedBefore', 'true');
    }

    // Load completed steps from localStorage
    const savedCompletedSteps = localStorage.getItem('completedOnboardingSteps');
    if (savedCompletedSteps) {
      setCompletedSteps(JSON.parse(savedCompletedSteps));
    }

    // Check if user has dismissed the What's New section
    const whatIsNewDismissed = localStorage.getItem('whatIsNewDismissed');
    if (whatIsNewDismissed) {
      setShowWhatIsNew(false);
    }
  }, []);

  const markStepAsCompleted = (step: string) => {
    setCompletedSteps((prev) => {
      const newCompletedSteps = [...prev, step];
      localStorage.setItem('completedOnboardingSteps', JSON.stringify(newCompletedSteps));
      return newCompletedSteps;
    });
  };

  const resetOnboarding = () => {
    setIsFirstTimeUser(true);
    setCompletedSteps([]);
    localStorage.removeItem('completedOnboardingSteps');
    localStorage.removeItem('hasVisitedBefore');
  };

  const dismissWhatIsNew = () => {
    setShowWhatIsNew(false);
    localStorage.setItem('whatIsNewDismissed', 'true');
  };

  return (
    <OnboardingContext.Provider
      value={{
        isFirstTimeUser,
        setIsFirstTimeUser,
        completedSteps,
        markStepAsCompleted,
        resetOnboarding,
        dismissWhatIsNew,
        showWhatIsNew,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = (): OnboardingContextType => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};
