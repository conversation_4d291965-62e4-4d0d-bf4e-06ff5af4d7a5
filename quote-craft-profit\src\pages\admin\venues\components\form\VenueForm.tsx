import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Loader2 } from "lucide-react";
import { City } from "@/types/types";
import { SaveVenueData, Venue, venueSchema } from "@/types/venues";
import { getVenueById, saveVenue } from "@/services/shared/entities/venues";
import { getAllCities } from "@/services/shared/entities/cities";

interface VenueFormProps {
  open: boolean;
  onOpenChange: (open: boolean, refresh?: boolean) => void;
  mode: "create" | "edit";
  venueId?: string;
}

const VenueForm: React.FC<VenueFormProps> = ({
  open,
  onOpenChange,
  mode,
  venueId,
}) => {
  const isEditMode = mode === "edit";
  const title = isEditMode ? "Edit Venue" : "Add New Venue";
  const buttonText = isEditMode ? "Update Venue" : "Create Venue";

  // Get the query client for cache invalidation
  const queryClient = useQueryClient();

  // Fetch cities for dropdown using the service layer
  const { data: cities = [] } = useQuery({
    queryKey: ["cities-for-venue-form"],
    queryFn: getAllCities,
    meta: {
      onError: () => {
        toast.error("Failed to load cities");
      },
    },
  });

  // Fetch venue data if in edit mode
  const { data: venueData, isLoading: isLoadingVenue } = useQuery({
    queryKey: ["venue", venueId],
    queryFn: () => getVenueById(venueId!),
    enabled: isEditMode && !!venueId && open,
    meta: {
      onError: () => {
        toast.error("Failed to load venue details");
      },
    },
  });

  // Initialize form
  const form = useForm({
    resolver: zodResolver(venueSchema),
    defaultValues: {
      name: "",
      description: "",
      address: "",
      city_id: "",
    },
  });

  const { formState, reset } = form;
  const { isSubmitting } = formState;

  // Update form values when venue data is loaded
  useEffect(() => {
    if (isEditMode && venueData) {
      reset({
        name: venueData.name,
        description: venueData.description || "",
        address: venueData.address || "",
        city_id: venueData.city_id || "",
      });
    } else if (!isEditMode) {
      reset({
        name: "",
        description: "",
        address: "",
        city_id: "",
      });
    }
  }, [isEditMode, venueData, reset, open]);

  // Handle form submission
  const onSubmit = async (values: any) => {
    try {
      const venueData: SaveVenueData = {
        name: values.name,
        description: values.description || null,
        address: values.address || null,
        city_id: values.city_id,
      };

      if (isEditMode && venueId && venueData) {
        venueData.id = venueId;
        // Preserve the current active status when editing (already set in venueData)
        // No need to reassign venueData.is_active = venueData.is_active;
      } else {
        // Set default active status for new venues
        venueData.is_active = true;
      }

      // Save the venue data
      const savedVenue = await saveVenue(venueData);

      // Invalidate all venue-related queries to ensure fresh data
      await queryClient.invalidateQueries({ queryKey: ["venues"] });

      // If editing, also invalidate the specific venue query
      if (isEditMode && venueId) {
        await queryClient.invalidateQueries({ queryKey: ["venue", venueId] });
      }

      toast.success(
        isEditMode ? "Venue updated successfully" : "Venue created successfully"
      );

      // Close the form and trigger a refresh in the parent component
      onOpenChange(false, true);
    } catch (error) {
      console.error("Error saving venue:", error);
      toast.error(
        isEditMode
          ? "Failed to update venue. Please try again."
          : "Failed to create venue. Please try again."
      );
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        // Only pass false for the refresh parameter when closing the dialog without submitting
        onOpenChange(open, false);
      }}
    >
      <DialogContent
        className="sm:max-w-[500px]"
        aria-describedby="venue-form-description"
      >
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription id="venue-form-description">
            {isEditMode
              ? "Update the venue details below."
              : "Fill in the details to create a new venue."}
          </DialogDescription>
        </DialogHeader>

        {isEditMode && isLoadingVenue ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Venue Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter venue name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="city_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a city" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {cities.map((city) => (
                          <SelectItem key={city.id} value={city.id}>
                            {city.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter venue address"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter venue description"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false, false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    buttonText
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default VenueForm;
