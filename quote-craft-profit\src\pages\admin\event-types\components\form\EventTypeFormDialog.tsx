import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import {
  getEventTypeById,
  createEventType,
  updateEventType,
} from "@/services/admin/event-types";
import {
  eventTypeFormSchema,
  type EventTypeFormValues,
  defaultEventTypeFormValues,
} from "../../schemas";

interface EventTypeFormDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  eventTypeId: string | null;
}

// Predefined color options
const colorOptions = [
  { value: "blue", label: "Blue" },
  { value: "green", label: "Green" },
  { value: "red", label: "Red" },
  { value: "yellow", label: "Yellow" },
  { value: "purple", label: "Purple" },
  { value: "pink", label: "Pink" },
  { value: "indigo", label: "Indigo" },
  { value: "gray", label: "Gray" },
  { value: "orange", label: "Orange" },
  { value: "teal", label: "Teal" },
];

const EventTypeFormDialog: React.FC<EventTypeFormDialogProps> = ({
  isOpen,
  onClose,
  eventTypeId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isActiveToggle, setIsActiveToggle] = useState(true);
  const isEditMode = !!eventTypeId;

  const form = useForm<EventTypeFormValues>({
    resolver: zodResolver(eventTypeFormSchema),
    defaultValues: defaultEventTypeFormValues,
  });

  // Load event type data for editing
  useEffect(() => {
    if (isOpen && eventTypeId) {
      setIsLoading(true);
      getEventTypeById(eventTypeId)
        .then((eventType) => {
          form.reset({
            name: eventType.name,
            code: eventType.code,
            description: eventType.description || "",
            icon: eventType.icon || "",
            color: eventType.color,
            display_order: eventType.display_order,
          });
          setIsActiveToggle(eventType.is_active);
        })
        .catch((error) => {
          console.error("Error loading event type:", error);
          toast.error("Failed to load event type details");
          onClose();
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else if (isOpen && !eventTypeId) {
      // Reset form for new event type
      form.reset(defaultEventTypeFormValues);
      setIsActiveToggle(true);
    }
  }, [isOpen, eventTypeId, form, onClose]);

  const onSubmit = async (values: EventTypeFormValues) => {
    setIsSubmitting(true);
    try {
      if (isEditMode && eventTypeId) {
        await updateEventType(eventTypeId, {
          ...values,
          is_active: isActiveToggle,
        });
        toast.success("Event type updated successfully");
      } else {
        await createEventType(values);
        toast.success("Event type created successfully");
      }
      onClose(true);
    } catch (error: any) {
      console.error("Error saving event type:", error);
      
      // Handle specific error messages
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error(
          isEditMode ? "Failed to update event type" : "Failed to create event type"
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit Event Type" : "Add New Event Type"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Update the event type information below."
              : "Create a new event type for your events."}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Loading event type details...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Event Type Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Corporate Meeting" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Code *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., CORP_MEETING"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                        />
                      </FormControl>
                      <FormDescription>
                        Unique identifier (uppercase letters, numbers, underscores only)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe this event type..."
                        className="resize-none"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="icon"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Icon</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., 🏢" {...field} />
                      </FormControl>
                      <FormDescription>
                        Emoji or icon character
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="color"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Color *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select color" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {colorOptions.map((color) => (
                            <SelectItem key={color.value} value={color.value}>
                              <div className="flex items-center space-x-2">
                                <div
                                  className="w-4 h-4 rounded-full border"
                                  style={{ backgroundColor: color.value }}
                                />
                                <span>{color.label}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="display_order"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Order</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>
                        Lower numbers appear first
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {isEditMode && (
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={isActiveToggle}
                    onCheckedChange={setIsActiveToggle}
                  />
                  <label htmlFor="is_active" className="text-sm font-medium">
                    Active
                  </label>
                  <span className="text-sm text-muted-foreground">
                    (Inactive event types won't appear in selections)
                  </span>
                </div>
              )}

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      {isEditMode ? "Updating..." : "Creating..."}
                    </>
                  ) : (
                    <>{isEditMode ? "Update Event Type" : "Create Event Type"}</>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EventTypeFormDialog;
