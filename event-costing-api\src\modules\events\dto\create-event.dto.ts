import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  IsISO8601,
  IsE<PERSON>,
  MaxLength,
} from 'class-validator';
import { EventStatus } from './event-status.enum'; // Assuming enum is defined here

export class CreateEventDto {
  @ApiProperty({
    description: 'Name of the event',
    example: 'Gathering of MKC',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  event_name: string;

  @ApiPropertyOptional({ description: 'Associated client ID', format: 'uuid' })
  @IsOptional()
  @IsUUID()
  client_id?: string;

  @ApiPropertyOptional({
    description: 'Event start date and time (ISO 8601)',
    example: '2025-01-01T00:00:00.000Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsISO8601()
  event_start_datetime?: string;

  @ApiPropertyOptional({
    description: 'Event end date and time (ISO 8601)',
    example: '2025-01-01T00:00:00.000Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsISO8601()
  event_end_datetime?: string;

  @ApiPropertyOptional({
    description: 'Status of the event',
    enum: EventStatus,
    default: EventStatus.PLANNING,
  })
  @IsOptional()
  @IsEnum(EventStatus)
  status?: EventStatus = EventStatus.PLANNING;

  @ApiPropertyOptional({ description: 'Details about the event venue' })
  @IsOptional()
  @IsString()
  venue_details?: string;

  @ApiPropertyOptional({
    description: 'Internal primary contact user ID',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  primary_contact_id?: string;

  @ApiPropertyOptional({ description: 'General notes about the event' })
  @IsOptional()
  @IsString()
  notes?: string;
}

