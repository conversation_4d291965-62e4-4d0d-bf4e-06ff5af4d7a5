import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import {
  BatchUpdatePackagesDto,
  PackageUpdateItem,
} from '../dto/batch-update-packages.dto';

@Injectable()
export class PackageBatchService {
  private readonly logger = new Logger(PackageBatchService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Batch update multiple packages at once
   * @param batchUpdateDto - The batch update DTO
   * @returns A summary of the update operation
   */
  async batchUpdatePackages(
    batchUpdateDto: BatchUpdatePackagesDto,
  ): Promise<{ updatedCount: number; errors: any[] }> {
    this.logger.log(
      `Attempting to batch update ${batchUpdateDto.packages.length} packages`,
    );

    const supabase = this.supabaseService.getClient();
    const errors: any[] = [];
    let updatedCount = 0;

    // Process updates in batches of 10 for better performance
    const batchSize = 10;
    const batches: PackageUpdateItem[][] = [];

    for (let i = 0; i < batchUpdateDto.packages.length; i += batchSize) {
      batches.push(batchUpdateDto.packages.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const updatePromises = batch.map(
        async (packageUpdate: PackageUpdateItem) => {
          try {
            // Prepare update data
            const updateData: any = {};

            if (packageUpdate.categoryId) {
              updateData.category_id = packageUpdate.categoryId;
            }

            if (packageUpdate.divisionId) {
              updateData.division_id = packageUpdate.divisionId;
            }

            // Skip if no updates to apply
            if (Object.keys(updateData).length === 0) {
              return {
                success: true,
                id: packageUpdate.id,
                message: 'No changes to apply',
              };
            }

            // Perform the update
            const { data, error } = await supabase
              .from('packages')
              .update(updateData)
              .eq('id', packageUpdate.id)
              .select('id');

            if (error) {
              this.logger.error(
                `Error updating package ${packageUpdate.id}: ${error.message}`,
                error.stack,
              );
              return {
                success: false,
                id: packageUpdate.id,
                error: error.message,
              };
            }

            updatedCount++;
            return { success: true, id: packageUpdate.id };
          } catch (error: unknown) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(
              `Unexpected error updating package ${packageUpdate.id}: ${errorMessage}`,
              errorStack,
            );
            return {
              success: false,
              id: packageUpdate.id,
              error: errorMessage,
            };
          }
        },
      );

      // Wait for all updates in this batch to complete
      const results = await Promise.all(updatePromises);

      // Collect errors
      results
        .filter(result => !result.success)
        .forEach(result => errors.push(result));
    }

    this.logger.log(
      `Batch update completed. Updated ${updatedCount} packages with ${errors.length} errors.`,
    );

    return {
      updatedCount,
      errors,
    };
  }

  /**
   * Batch delete multiple packages
   * @param packageIds - Array of package IDs to delete
   * @returns Summary of the delete operation
   */
  async batchDeletePackages(
    packageIds: string[],
  ): Promise<{ deletedCount: number; errors: any[] }> {
    this.logger.log(
      `Attempting to batch delete ${packageIds.length} packages`,
    );

    const supabase = this.supabaseService.getClient();
    const errors: any[] = [];
    let deletedCount = 0;

    // Process deletes in batches of 10 for better performance
    const batchSize = 10;
    const batches: string[][] = [];

    for (let i = 0; i < packageIds.length; i += batchSize) {
      batches.push(packageIds.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const deletePromises = batch.map(async (packageId: string) => {
        try {
          // Perform soft delete
          const { error, count } = await supabase
            .from('packages')
            .update({ is_deleted: true })
            .match({ id: packageId, is_deleted: false });

          if (error) {
            this.logger.error(
              `Error deleting package ${packageId}: ${error.message}`,
              error.stack,
            );
            return {
              success: false,
              id: packageId,
              error: error.message,
            };
          }

          if (count === 0) {
            return {
              success: false,
              id: packageId,
              error: 'Package not found or already deleted',
            };
          }

          deletedCount++;
          return { success: true, id: packageId };
        } catch (error: unknown) {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          const errorStack = error instanceof Error ? error.stack : undefined;
          this.logger.error(
            `Unexpected error deleting package ${packageId}: ${errorMessage}`,
            errorStack,
          );
          return {
            success: false,
            id: packageId,
            error: errorMessage,
          };
        }
      });

      // Wait for all deletes in this batch to complete
      const results = await Promise.all(deletePromises);

      // Collect errors
      results
        .filter(result => !result.success)
        .forEach(result => errors.push(result));
    }

    this.logger.log(
      `Batch delete completed. Deleted ${deletedCount} packages with ${errors.length} errors.`,
    );

    return {
      deletedCount,
      errors,
    };
  }

  /**
   * Batch update package status
   * @param packageIds - Array of package IDs
   * @param isActive - Whether packages should be active
   * @returns Summary of the status update operation
   */
  async batchUpdatePackageStatus(
    packageIds: string[],
    isActive: boolean,
  ): Promise<{ updatedCount: number; errors: any[] }> {
    this.logger.log(
      `Attempting to batch update status for ${packageIds.length} packages to ${isActive ? 'active' : 'inactive'}`,
    );

    const supabase = this.supabaseService.getClient();
    const errors: any[] = [];
    let updatedCount = 0;

    // Process updates in batches of 10 for better performance
    const batchSize = 10;
    const batches: string[][] = [];

    for (let i = 0; i < packageIds.length; i += batchSize) {
      batches.push(packageIds.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const updatePromises = batch.map(async (packageId: string) => {
        try {
          // Update package status
          const { data, error } = await supabase
            .from('packages')
            .update({
              is_deleted: !isActive,
              updated_at: new Date().toISOString(),
            })
            .eq('id', packageId)
            .select('id');

          if (error) {
            this.logger.error(
              `Error updating status for package ${packageId}: ${error.message}`,
              error.stack,
            );
            return {
              success: false,
              id: packageId,
              error: error.message,
            };
          }

          if (!data || data.length === 0) {
            return {
              success: false,
              id: packageId,
              error: 'Package not found',
            };
          }

          updatedCount++;
          return { success: true, id: packageId };
        } catch (error: unknown) {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          const errorStack = error instanceof Error ? error.stack : undefined;
          this.logger.error(
            `Unexpected error updating status for package ${packageId}: ${errorMessage}`,
            errorStack,
          );
          return {
            success: false,
            id: packageId,
            error: errorMessage,
          };
        }
      });

      // Wait for all updates in this batch to complete
      const results = await Promise.all(updatePromises);

      // Collect errors
      results
        .filter(result => !result.success)
        .forEach(result => errors.push(result));
    }

    this.logger.log(
      `Batch status update completed. Updated ${updatedCount} packages with ${errors.length} errors.`,
    );

    return {
      updatedCount,
      errors,
    };
  }
}
