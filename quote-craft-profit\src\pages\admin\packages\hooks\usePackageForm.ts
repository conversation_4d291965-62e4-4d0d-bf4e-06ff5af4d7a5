import { useEffect, useCallback, useMemo } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { getAllCategories } from "@/services/admin/categories";
import { getAllDivisions } from "@/services/admin/divisions";
import { getAllCities } from "@/services/shared/entities/cities";
import { getAllCurrencies } from "@/services/shared/entities/currencies";
import { PackageFormValues } from "../types/package";
import {
  packageFormSchema,
  defaultPackageFormValues,
} from "../schemas/packageValidation";
import { Package } from "../types/package";

interface UsePackageFormOptions {
  packageData?: Package | null;
  enabled?: boolean;
}

interface UsePackageFormReturn {
  form: UseFormReturn<PackageFormValues>;
  categories: any[];
  divisions: any[];
  cities: any[];
  currencies: any[];
  isLoading: boolean;
  isLoadingCategories: boolean;
  isLoadingDivisions: boolean;
  isLoadingCities: boolean;
  isLoadingCurrencies: boolean;
}

/**
 * Custom hook for managing package form state and data fetching
 * Centralizes form logic to reduce complexity in components
 */
export const usePackageForm = (
  options: UsePackageFormOptions = {}
): UsePackageFormReturn => {
  const { packageData, enabled = true } = options;

  // Initialize form with validation
  const form = useForm<PackageFormValues>({
    resolver: zodResolver(packageFormSchema),
    defaultValues: defaultPackageFormValues,
    mode: "onChange",
  });

  // Fetch categories with caching
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ["categories"],
    queryFn: getAllCategories,
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    meta: {
      onError: () => {
        toast.error("Failed to load categories");
      },
    },
  });

  // Fetch divisions with caching
  const { data: divisions = [], isLoading: isLoadingDivisions } = useQuery({
    queryKey: ["divisions"],
    queryFn: () => getAllDivisions(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    meta: {
      onError: () => {
        toast.error("Failed to load divisions");
      },
    },
  });

  // Fetch cities with caching
  const { data: cities = [], isLoading: isLoadingCities } = useQuery({
    queryKey: ["cities"],
    queryFn: getAllCities,
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    meta: {
      onError: () => {
        toast.error("Failed to load cities");
      },
    },
  });

  // Fetch currencies with caching
  const { data: currencies = [], isLoading: isLoadingCurrencies } = useQuery({
    queryKey: ["currencies"],
    queryFn: getAllCurrencies,
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes (currencies change rarely)
    gcTime: 15 * 60 * 1000, // 15 minutes
    meta: {
      onError: () => {
        toast.error("Failed to load currencies");
      },
    },
  });

  // Update form when packageData changes (for edit mode)
  useEffect(() => {
    if (
      packageData &&
      currencies.length > 0 &&
      categories.length > 0 &&
      divisions.length > 0 &&
      cities.length > 0
    ) {
      const formValues: PackageFormValues = {
        name: packageData.name,
        description: packageData.description || "",
        categoryId: packageData.categoryId || "",
        divisionId: packageData.divisionId || "",
        cityIds: packageData.cityIds || [],
        venueIds: packageData.venueIds || [],
        enableVenues: packageData.venueIds && packageData.venueIds.length > 0,
        quantityBasis: packageData.quantityBasis,
        isActive: !packageData.isDeleted,
        price: packageData.price || "",
        unitBaseCost: packageData.unitBaseCost || "",
        currencyId:
          packageData.currencyId ||
          (currencies.length > 0 ? currencies[0].id : ""),
      };

      // Only reset form if values have actually changed to prevent unnecessary re-renders
      const currentValues = form.getValues();
      const hasChanges = Object.keys(formValues).some((key) => {
        const currentValue = currentValues[key as keyof PackageFormValues];
        const newValue = formValues[key as keyof PackageFormValues];

        // Handle array comparison
        if (Array.isArray(currentValue) && Array.isArray(newValue)) {
          return (
            JSON.stringify(currentValue.sort()) !==
            JSON.stringify(newValue.sort())
          );
        }

        return currentValue !== newValue;
      });

      if (hasChanges) {
        form.reset(formValues);
      }
    }
  }, [packageData, currencies, categories, divisions, cities]);

  // Set default currency when currencies are loaded (for create mode)
  useEffect(() => {
    if (!packageData && currencies.length > 0) {
      const defaultCurrencyId = currencies[0].id;
      form.setValue("currencyId", defaultCurrencyId);
    }
  }, [currencies, packageData]);

  const isLoading =
    isLoadingCategories ||
    isLoadingDivisions ||
    isLoadingCities ||
    isLoadingCurrencies;

  return {
    form,
    categories,
    divisions,
    cities,
    currencies,
    isLoading,
    isLoadingCategories,
    isLoadingDivisions,
    isLoadingCities,
    isLoadingCurrencies,
  };
};
