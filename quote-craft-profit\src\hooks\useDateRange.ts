import { useState, useMemo, useCallback } from "react";
import { DateRange } from "react-day-picker";
import { useTimezoneAwareDates } from "./useTimezoneAwareDates";

/**
 * Custom hook for managing date range state with validation and utilities
 *
 * @param initialRange - Optional initial date range
 * @returns Object with date range state and utility functions
 */
export const useDateRange = (initialRange?: DateRange) => {
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    initialRange
  );

  const {
    validateDateRange,
    getDateRangeErrorMessage,
    isDateRangeComplete,
    formatDateRangeForDisplay,
  } = useTimezoneAwareDates();

  // Computed properties
  const isComplete = useMemo(
    () => isDateRangeComplete(dateRange),
    [dateRange, isDateRangeComplete]
  );

  const isValid = useMemo(
    () => validateDateRange(dateRange),
    [dateRange, validateDateRange]
  );

  const errorMessage = useMemo(
    () => getDateRangeErrorMessage(dateRange),
    [dateRange, getDateRangeErrorMessage]
  );

  // Utility functions
  const formatRange = useCallback(
    () => formatDateRangeForDisplay(dateRange),
    [dateRange, formatDateRangeForDisplay]
  );

  const clear = useCallback(() => {
    setDateRange(undefined);
  }, []);

  const setFromDate = useCallback((date: Date | undefined) => {
    setDateRange((prev) => ({
      from: date,
      to: prev?.to,
    }));
  }, []);

  const setToDate = useCallback((date: Date | undefined) => {
    setDateRange((prev) => ({
      from: prev?.from,
      to: date,
    }));
  }, []);

  const setRange = useCallback(
    (from: Date | undefined, to: Date | undefined) => {
      setDateRange({ from, to });
    },
    []
  );

  // Validation helpers
  const canSubmit = useMemo(() => isComplete && isValid, [isComplete, isValid]);

  return {
    // State
    dateRange,
    setDateRange,

    // Computed properties
    isComplete,
    isValid,
    errorMessage,
    canSubmit,

    // Utility functions
    formatRange,
    clear,
    setFromDate,
    setToDate,
    setRange,
  };
};

export default useDateRange;
