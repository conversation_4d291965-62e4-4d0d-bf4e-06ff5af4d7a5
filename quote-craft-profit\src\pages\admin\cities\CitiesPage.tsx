import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import AdminLayout from "@/components/layout/AdminLayout";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage
} from "@/components/ui/breadcrumb";
import { getAllCities } from "@/services/shared/entities/cities";
import { CityList } from "./components/list";
import { CityFormDialog } from "./components/form";
import { City } from "@/types/types";

const CitiesPage: React.FC = () => {
  const [isCityFormOpen, setIsCityFormOpen] = useState(false);
  const [editingCityId, setEditingCityId] = useState<string | null>(null);

  // Fetch cities using the improved shared service
  const {
    data: cities,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ["cities"],
    queryFn: getAllCities,
    meta: {
      onError: () => {
        toast.error("Failed to load cities");
      },
    },
  });

  const handleOpenNewCityForm = () => {
    setEditingCityId(null);
    setIsCityFormOpen(true);
  };

  const handleEditCity = (cityId: string) => {
    setEditingCityId(cityId);
    setIsCityFormOpen(true);
  };

  const handleCityFormClose = (shouldRefresh: boolean = false) => {
    setIsCityFormOpen(false);
    setEditingCityId(null);

    if (shouldRefresh) {
      refetch();
    }
  };

  return (
    <AdminLayout title="Manage Cities">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/catalogue">Catalogue</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Cities</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="mb-6 text-muted-foreground">
        Create and manage cities where your services are available.
      </div>

      {/* Action Bar Block */}
      <div className="mb-6">
        <Button onClick={handleOpenNewCityForm}>
          <Plus className="w-4 h-4 mr-2" /> Add New City
        </Button>
      </div>

      {/* City List Block */}
      <CityList
        cities={cities || []}
        isLoading={isLoading}
        isError={isError}
        onEdit={handleEditCity}
        onRefresh={refetch}
      />

      {/* Add/Edit City Dialog */}
      <CityFormDialog
        isOpen={isCityFormOpen}
        onClose={handleCityFormClose}
        cityId={editingCityId}
      />
    </AdminLayout>
  );
};

export default CitiesPage;
