// API calculation response types
export interface ApiCalculation {
  id: string;
  name: string;
  status: "draft" | "completed" | "canceled";
  event_start_date: string;
  event_end_date: string;
  total: number;
  total_cost: number;
  estimated_profit: number;
  currency_id: string;
  created_at: string;
  updated_at: string;
  // Additional properties to match Calculation interface
  eventName: string;
  eventType: string;
  attendees: number;
  createdAt: string;
  updatedAt: string;
  venues?: {
    id: string;
    name: string;
  }[];
}

export interface ApiCalculationResponse {
  data: ApiCalculation[];
  count: number;
}

// Note: CalculationFormData interface moved to @/types/calculations.ts for consistency

// Data for updating an existing calculation
export interface CalculationUpdateData {
  name?: string;
  event_start_date?: string; // ISO 8601 datetime format
  event_end_date?: string; // ISO 8601 datetime format
  event_type_id?: string; // Updated to use event_type_id
  notes?: string;
  status?: "draft" | "completed" | "canceled";
  attendees?: number;
  venue_ids?: string[]; // Added venue_ids for updating venues
}

// Detailed calculation data structure
export interface CalculationDetails {
  id: string;
  name: string;
  currency: {
    id: string;
    code: string;
  };
  city: {
    id: string;
    name: string;
  };
  venues: {
    id: string;
    name: string;
  }[];
  event_start_date: string; // ISO 8601 datetime format
  event_end_date: string; // ISO 8601 datetime format
  attendees: number;
  event_type_id: string | null; // Updated to use event_type_id (UUID)
  notes?: string;
  status: "draft" | "completed" | "canceled";
  client?: {
    id: string;
    client_name: string;
  };
  event?: {
    id: string;
    event_name: string;
  };
  // Additional properties to match usage in components
  eventName: string;
  total: number;
  lineItems?: Array<{
    id: string;
    name: string;
    isCustom?: boolean;
  }>;
}
