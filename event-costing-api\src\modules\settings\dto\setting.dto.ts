import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SettingDto {
  @ApiProperty({
    description: 'The unique key for the setting',
    example: 'site_name',
  })
  key: string;

  @ApiProperty({
    description: 'The value of the setting (can be any JSON type)',
    example: { name: 'My Event Co', version: 1.2 },
  })
  value: any; // Using 'any' as value can be JSON primitive or object

  @ApiPropertyOptional({
    description: 'Optional description for the setting',
  })
  description?: string | null;

  @ApiProperty({ description: 'Timestamp when the setting was created' })
  created_at: Date;

  @ApiProperty({ description: 'Timestamp when the setting was last updated' })
  updated_at: Date;
}
