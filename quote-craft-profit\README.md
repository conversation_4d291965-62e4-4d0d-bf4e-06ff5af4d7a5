# Quote Craft Profit

A comprehensive event planning and cost calculation application for event professionals.

## Overview

Quote Craft Profit is a React-based web application designed for event planning professionals to create detailed cost calculations and quotes for events. The application provides a complete solution for managing packages, calculating costs, generating quotes, and handling client relationships.

### Key Features

- **Package Management**: Create and manage service packages with various pricing models
- **Cost Calculations**: Advanced calculation engine with multiple quantity bases
- **Quote Generation**: Professional quotes for clients
- **Template System**: Reusable event configurations
- **Multi-City Support**: City-specific pricing and availability
- **User Management**: Role-based access control (Admin/User)
- **Client Management**: Comprehensive client relationship management

## Technology Stack

- **Frontend**: React 18 + TypeScript 5 + Vite
- **UI Framework**: shadcn/ui components + Tailwind CSS
- **State Management**: React Query (server state) + React Context (global state)
- **Forms**: React Hook Form + Zod validation
- **Backend**: NestJS API + Supabase (PostgreSQL, Auth, Storage)
- **HTTP Client**: Axios
- **Routing**: React Router
- **Data Visualization**: Recharts
- **Date Utilities**: Date-fns
- **Icons**: Lucide React
- **Notifications**: Sonner (toast notifications)
- **Image Editing**: React Image Crop
- **Carousels**: Embla Carousel
- **Theme Management**: Next Themes

## Architecture

### Feature-Based Structure

```
src/
├── components/         # Reusable UI components
│   ├── ui/             # shadcn/ui base components
│   └── layout/         # Layout components
├── contexts/           # React Context providers
├── hooks/              # Custom React hooks
├── integrations/       # Third-party integrations
│   ├── supabase/       # Supabase client
│   └── api/            # External API client
├── lib/                # Utility functions
├── pages/              # Feature-based pages
│   ├── admin/          # Admin features
│   ├── calculations/   # Calculation features
│   ├── clients/        # Client management
│   └── events/         # Event management
├── services/           # API services (feature-based)
│   ├── admin/          # Admin-specific services (categories, divisions, packages, etc.)
│   ├── calculations/   # Calculation services (direct Supabase integration)
│   └── shared/         # Cross-feature services
│       ├── entities/   # Entity services (cities, clients, currencies, events, venues)
│       └── users/      # User management services
└── types/              # TypeScript definitions
```

### Integration Points

The application uses a dual backend approach:

1. **Direct Supabase Integration**:
   - Authentication using Supabase Auth
   - Calculation operations (line items, totals, etc.)
   - Real-time subscriptions for live updates

2. **External NestJS API**:
   - Admin operations (packages, categories, divisions, templates)
   - Entity management (cities, clients, currencies, events, venues)
   - Complex business logic operations
   - RESTful API with JWT authentication

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm, yarn, or pnpm
- Supabase account
- Access to the NestJS backend API

### Installation

```bash
# Clone the repository
git clone <YOUR_GIT_URL>
cd quote-craft-profit

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Start development server
npm run dev
```

### Environment Variables

Create a `.env` file with the following variables:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# External API Configuration
VITE_EVENT_COSTING_API_URL=https://your-backend-api.railway.app
```

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

## Database Schema

### Core Tables

- **packages**: Service packages with pricing models
- **package_cities**: Many-to-many relationship between packages and cities
- **package_prices**: Pricing information for packages
- **package_options**: Add-on options for packages
- **categories**: Package categorization
- **divisions**: Organizational divisions
- **cities**: Supported service cities
- **clients**: Client information and contacts
- **events**: Event details and specifications
- **calculation_history**: Saved calculations
- **calculation_line_items**: Individual calculation items
- **currencies**: Multi-currency support

## Key Components

### Package Management
- **Multi-city Support**: Packages can be available in multiple cities
- **Flexible Pricing**: Various quantity bases (per event, per day, per attendee)
- **Options System**: Add-on options with price adjustments
- **Category Organization**: Hierarchical categorization system

### Calculation Engine
- **Dynamic Calculations**: Real-time cost calculations
- **Tax & Discount Support**: Configurable tax rates and discount systems
- **Profit Margin Tracking**: Built-in profit margin calculations
- **Line Item Management**: Detailed breakdown of all costs

### Template System
- **Reusable Configurations**: Save calculations as templates
- **Template Sharing**: Admin-managed public templates
- **Version Control**: Template versioning and updates

## Development

### Code Organization

#### **Service Architecture**
Our service layer follows a **feature-based architecture with shared services** pattern:

- **Shared Services** (`src/services/shared/`): Cross-feature functionality
  - `entities/` - Core business entities (cities, clients, currencies, events, venues)
  - `users/` - User profile and authentication services
  - Used across admin, calculations, and user-facing features

- **Feature Services** (`src/services/admin/`, `src/services/calculations/`):
  - `admin/` - Admin-only management operations with complex business logic
  - `calculations/` - Calculation-specific logic with direct Supabase integration
  - Feature-specific workflows and validation rules

#### **Type System**
**Hybrid approach** with both global and feature-specific types:

- **Global Types** (`src/types/`): Core entities used across features
  - Base interfaces: `City`, `Division`, `Category`, `Package`
  - Specialized types: `Venue`, `Event`, utility types
  - Single source of truth for core business entities

- **Feature Types** (`src/pages/admin/*/types/`): Feature-specific extensions
  - Admin-specific extensions of global types
  - Form data interfaces and validation schemas
  - Complex nested types used within specific features

#### **Architecture Benefits**
- **Type Safety**: Comprehensive TypeScript coverage with hybrid type organization
- **Component Library**: Consistent UI with shadcn/ui
- **Maintainability**: Clear separation between shared and feature-specific code
- **Scalability**: Easy to extend with new features following established patterns

### Best Practices

#### **Service Layer Usage**
- **Shared Services**: Use for entities needed across features (cities, clients, venues)
- **Feature Services**: Use for complex business logic specific to one area
- **Consistent Patterns**: All services follow the same structure and error handling

#### **Type Management**
- **Global Types**: Use for core entities and cross-feature interfaces
- **Feature Types**: Use for extensions, form data, and feature-specific structures
- **Import Consistency**: Prefer global types, extend with feature types when needed

#### **Development Guidelines**
- Use React Query for all data fetching
- Implement proper error handling with user feedback
- Follow TypeScript strict mode guidelines
- Maintain mobile-responsive design
- Use authenticated API clients for all external calls

### Testing
- Unit tests for utility functions and hooks
- Integration tests for API interactions
- Manual testing across browsers and devices

## Deployment

### Frontend (Vercel - Recommended)

```bash
# Build the application
npm run build

# Deploy to Vercel
npx vercel --prod
```

**Environment Variables for Vercel:**
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`
- `VITE_EVENT_COSTING_API_URL`

### Backend (Railway - Recommended)
- NestJS API deployed on Railway
- PostgreSQL database via Supabase
- Automatic deployments from Git

### Alternative Platforms
- **Netlify**: Frontend deployment alternative
- **Heroku**: Backend deployment alternative
- **AWS/GCP**: Enterprise deployment options

## Documentation

- **[Developer Guidelines](docs/DEVELOPER_GUIDELINES.md)**: Comprehensive development guide with service architecture and type system details
- **[Type Consolidation Improvements](docs/TYPE_CONSOLIDATION_IMPROVEMENT.md)**: Recent architectural improvements and future roadmap
- **[Authentication](docs/AUTHENTICATION.md)**: Authentication system details
- **API Documentation**: Available in the NestJS backend repository

### Architecture Documentation

Our codebase follows a **feature-based architecture with shared services** and **hybrid type system**:

- **Service Layer**: Shared services for cross-feature entities, feature services for specialized logic
- **Type System**: Global types for core entities, feature types for extensions and forms
- **Benefits**: Maintainable, scalable, and type-safe architecture with clear separation of concerns

For detailed implementation guides and examples, see the Developer Guidelines above.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is proprietary software. All rights reserved.

---

For technical support or questions, please refer to the documentation or contact the development team.
