/**
 * Consolidated Package Catalog Service
 * 
 * This service implements the consolidated endpoint pattern for package catalog operations.
 * It replaces the need for multiple separate API calls with a single consolidated endpoint.
 */

import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';
import { showError } from '@/lib/notifications';
import type { PackageFilters } from '@/pages/admin/packages/types/filters';

/**
 * Types for the consolidated package catalog response
 */
export interface PackageCatalogData {
  packages: {
    data: Array<{
      id: string;
      name: string;
      description: string;
      category_id: string;
      division_id: string;
      quantity_basis: string;
      price: number;
      unit_base_cost: number;
      is_deleted: boolean;
      created_at: string;
      updated_at: string;
      // Enhanced fields for catalog
      options?: Array<{
        id: string;
        option_name: string;
        description: string;
        price_adjustment: number;
        cost_adjustment: number;
      }>;
      dependencies?: Array<{
        id: string;
        dependent_package_id: string;
        dependency_type: string;
      }>;
      availability?: {
        cities: string[];
        venues: string[];
      };
    }>;
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  categories: Array<{
    id: string;
    name: string;
    display_order: number;
    is_deleted: boolean;
    created_at: string;
    updated_at: string;
  }>;
  cities: Array<{
    id: string;
    name: string;
    code?: string;
  }>;
  divisions: Array<{
    id: string;
    name: string;
    code: string;
  }>;
  currencies: Array<{
    id: string;
    code: string;
    symbol: string;
    name: string;
  }>;
  filters: {
    applied: PackageFilters;
    available: {
      categories: Array<{ id: string; name: string }>;
      cities: Array<{ id: string; name: string }>;
      divisions: Array<{ id: string; name: string }>;
    };
  };
  metadata: {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
    totalPackages: number;
    appliedFilters: number;
  };
}

/**
 * Package catalog summary data
 */
export interface PackageCatalogSummary {
  totalPackages: number;
  totalCategories: number;
  totalDivisions: number;
  averagePrice: number;
  priceRange: {
    min: number;
    max: number;
  };
  packagesByCategory: Array<{
    categoryId: string;
    categoryName: string;
    count: number;
  }>;
  packagesByDivision: Array<{
    divisionId: string;
    divisionName: string;
    count: number;
  }>;
  metadata: {
    loadTime: number;
    timestamp: string;
  };
}

/**
 * Fetch complete package catalog data in a single API call
 * This replaces the need for multiple separate API calls:
 * 1. GET /admin/packages (packages)
 * 2. GET /admin/categories (categories)
 * 3. GET /admin/cities (cities)
 * 4. GET /admin/divisions (divisions)
 * 5. GET /currencies (currencies)
 * 
 * @param filters - Package filters
 * @returns Complete package catalog data with metadata
 */
export const getPackageCatalogData = async (
  filters: PackageFilters = {},
): Promise<PackageCatalogData> => {
  try {
    console.log(`[API] Fetching package catalog data with filters:`, filters);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Build query parameters
    const queryParams = new URLSearchParams();
    
    // Map frontend filters to backend parameters
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.categoryId && filters.categoryId !== 'all') {
      queryParams.append('categoryId', filters.categoryId);
    }
    if (filters.divisionId && filters.divisionId !== 'all') {
      queryParams.append('divisionId', filters.divisionId);
    }
    if (filters.cityId && filters.cityId !== 'all') {
      queryParams.append('cityId', filters.cityId);
    }
    if (filters.venueIds && filters.venueIds.length > 0) {
      queryParams.append('venueIds', filters.venueIds.join(','));
    }
    if (filters.showDeleted !== undefined) {
      queryParams.append('showDeleted', filters.showDeleted.toString());
    }
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.pageSize) queryParams.append('pageSize', filters.pageSize.toString());
    if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
    if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder);

    // Make single API request to get all data
    const url = `${API_ENDPOINTS.PACKAGES.CATALOG.GET_ALL}${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    
    const response = await authClient.get(url);
    const data = response.data as PackageCatalogData;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched package catalog data in ${loadTime}ms:`, {
      packages: data.packages.totalCount,
      categories: data.categories.length,
      cities: data.cities.length,
      divisions: data.divisions.length,
      currencies: data.currencies.length,
      errors: data.metadata.errors?.length || 0,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching package catalog data:`, error);
    showError('Failed to load package catalog', {
      description: 'There was an error loading the package catalog. Please try again.',
    });
    throw error;
  }
};

/**
 * Fetch advanced package catalog data with enhanced information
 * Includes package options, dependencies, and availability information
 * 
 * @param filters - Package filters with enhancement options
 * @returns Enhanced package catalog data
 */
export const getAdvancedPackageCatalogData = async (
  filters: PackageFilters & {
    includeOptions?: boolean;
    includeDependencies?: boolean;
    includeAvailability?: boolean;
  } = {},
): Promise<PackageCatalogData> => {
  try {
    console.log(`[API] Fetching advanced package catalog data with filters:`, filters);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Build query parameters
    const queryParams = new URLSearchParams();
    
    // Map all filters including enhancement options
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          queryParams.append(key, value.join(','));
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });

    // Make API request to advanced endpoint
    const url = `${API_ENDPOINTS.PACKAGES.CATALOG.GET_ADVANCED}${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    
    const response = await authClient.get(url);
    const data = response.data as PackageCatalogData;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched advanced package catalog data in ${loadTime}ms:`, {
      packages: data.packages.totalCount,
      withOptions: data.packages.data.filter(p => p.options?.length).length,
      withDependencies: data.packages.data.filter(p => p.dependencies?.length).length,
      withAvailability: data.packages.data.filter(p => p.availability).length,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching advanced package catalog data:`, error);
    showError('Failed to load advanced package catalog', {
      description: 'There was an error loading the advanced package catalog. Please try again.',
    });
    throw error;
  }
};

/**
 * Fetch package catalog summary statistics
 * Provides overview metrics for the package catalog
 * 
 * @returns Package catalog summary data
 */
export const getPackageCatalogSummary = async (): Promise<PackageCatalogSummary> => {
  try {
    console.log(`[API] Fetching package catalog summary`);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to summary endpoint
    const response = await authClient.get(API_ENDPOINTS.PACKAGES.CATALOG.GET_SUMMARY);
    const data = response.data as PackageCatalogSummary;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched package catalog summary in ${loadTime}ms:`, {
      totalPackages: data.totalPackages,
      totalCategories: data.totalCategories,
      totalDivisions: data.totalDivisions,
      averagePrice: data.averagePrice,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching package catalog summary:`, error);
    showError('Failed to load package catalog summary', {
      description: 'There was an error loading the package catalog summary. Please try again.',
    });
    throw error;
  }
};

/**
 * Check if the consolidated package catalog endpoint is available
 * This can be used for feature detection and fallback logic
 * 
 * @returns Promise<boolean> - Whether the consolidated endpoint is available
 */
export const isPackageCatalogEndpointAvailable = async (): Promise<boolean> => {
  try {
    const authClient = await getAuthenticatedApiClient();
    
    try {
      await authClient.get(`${API_ENDPOINTS.PACKAGES.CATALOG.GET_ALL}?page=1&pageSize=1`);
      return true;
    } catch (error: any) {
      // If we get a 404, the endpoint doesn't exist
      // If we get other errors, the endpoint exists but there might be other issues
      if (error.response?.status === 404) {
        return false;
      }
      return true;
    }
  } catch (error) {
    console.warn('[API] Could not check package catalog endpoint availability:', error);
    return false;
  }
};
