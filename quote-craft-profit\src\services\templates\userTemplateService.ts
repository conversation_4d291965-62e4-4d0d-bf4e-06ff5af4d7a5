import { apiClient, API_ENDPOINTS } from '@/integrations/api';

export interface UserTemplateFilters {
  search?: string;
  eventType?: string;
  cityId?: string;
  attendeesMin?: number;
  attendeesMax?: number;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'created_at' | 'attendees' | 'event_type';
  sortOrder?: 'asc' | 'desc';
}

export interface TemplateListItem {
  id: string;
  name: string;
  description: string | null;
  event_type: string | null;
  attendees: number | null;
  created_at: string;
  is_public: boolean;
  created_by: string;
  venue_ids: string[] | null;
}

export interface PaginatedTemplatesResponse {
  data: TemplateListItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TemplateDetailDto {
  id: string;
  name: string;
  description: string | null;
  event_type: string | null;
  attendees: number | null;
  template_start_date: string;
  template_end_date: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  venue_ids: string[] | null;
  package_selections: Array<{
    package_id: string;
    option_ids: string[];
  }>;
}

export interface EnhancedTemplateDetailDto {
  id: string;
  name: string;
  description: string | null;
  event_type: string | null;
  attendees: number | null;
  template_start_date: string;
  template_end_date: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  venue_ids: string[] | null;
  package_selections: Array<{
    package_id: string;
    package_name: string;
    option_ids: string[];
    option_names: string[];
    item_quantity: number | null;
    duration_days: number | null;
  }>;
}

/**
 * Get public templates (no authentication required)
 * @param filters - Filtering and pagination options
 * @returns Paginated list of public templates
 */
export const getPublicTemplates = async (
  filters: UserTemplateFilters = {}
): Promise<PaginatedTemplatesResponse> => {
  try {
    const params = new URLSearchParams();

    // Add filters to query parameters
    if (filters.search) {
      params.append('search', filters.search);
    }
    if (filters.eventType) {
      params.append('eventType', filters.eventType);
    }
    if (filters.cityId) {
      params.append('cityId', filters.cityId);
    }
    if (filters.attendeesMin !== undefined) {
      params.append('attendeesMin', filters.attendeesMin.toString());
    }
    if (filters.attendeesMax !== undefined) {
      params.append('attendeesMax', filters.attendeesMax.toString());
    }
    if (filters.limit !== undefined) {
      params.append('limit', filters.limit.toString());
    }
    if (filters.offset !== undefined) {
      params.append('offset', filters.offset.toString());
    }
    if (filters.sortBy) {
      params.append('sortBy', filters.sortBy);
    }
    if (filters.sortOrder) {
      params.append('sortOrder', filters.sortOrder);
    }

    const url = `${API_ENDPOINTS.TEMPLATES.GET_PUBLIC}${params.toString() ? `?${params.toString()}` : ''}`;

    const response = await apiClient.get(url);

    // Handle different response formats
    if (Array.isArray(response.data)) {
      // If backend returns array directly, create pagination wrapper
      return {
        data: response.data,
        total: response.data.length,
        page: 1,
        limit: response.data.length,
        totalPages: 1,
      };
    } else if (response.data && typeof response.data === 'object') {
      // If backend returns paginated response
      return {
        data: response.data.data || response.data.templates || [],
        total: response.data.total || 0,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        totalPages: response.data.totalPages || 1,
      };
    } else {
      throw new Error('Unexpected response format');
    }
  } catch (error) {
    console.error('Error fetching public templates:', error);
    throw error;
  }
};

/**
 * Get template details by ID (public endpoint)
 * @param id - Template ID
 * @returns Template details
 */
export const getTemplateById = async (id: string): Promise<TemplateDetailDto> => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.TEMPLATES.GET_PUBLIC_DETAIL(id));
    return response.data;
  } catch (error) {
    console.error('Error fetching template details:', error);
    throw error;
  }
};

/**
 * Get enhanced template details by ID with package and option names (public endpoint)
 * @param id - Template ID
 * @returns Enhanced template details with package and option names
 */
export const getEnhancedTemplateById = async (id: string): Promise<EnhancedTemplateDetailDto> => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.TEMPLATES.GET_PUBLIC_DETAIL_ENHANCED(id));
    return response.data;
  } catch (error) {
    console.error('Error fetching enhanced template details:', error);
    throw error;
  }
};

/**
 * Get unique event types from templates (for filtering)
 * @returns Array of unique event types
 */
export const getTemplateEventTypes = async (): Promise<string[]> => {
  try {
    // Fetch a sample of templates to get event types
    const response = await getPublicTemplates({ limit: 100 });

    const eventTypes = response.data
      .map(template => template.event_type)
      .filter((type): type is string => type !== null && type !== undefined)
      .filter((type, index, array) => array.indexOf(type) === index) // Remove duplicates
      .sort();

    return eventTypes;
  } catch (error) {
    console.error('Error fetching template event types:', error);
    return [];
  }
};

/**
 * Get template statistics for dashboard/overview
 * @returns Template statistics
 */
export const getTemplateStats = async () => {
  try {
    const response = await getPublicTemplates({ limit: 1000 }); // Get all templates for stats

    const templates = response.data;
    const totalTemplates = templates.length;

    // Calculate statistics
    const eventTypeStats = templates.reduce((acc, template) => {
      const eventType = template.event_type || 'Unknown';
      acc[eventType] = (acc[eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averageAttendees = templates
      .filter(t => t.attendees !== null)
      .reduce((sum, t) => sum + (t.attendees || 0), 0) / templates.filter(t => t.attendees !== null).length;

    return {
      totalTemplates,
      eventTypeStats,
      averageAttendees: Math.round(averageAttendees || 0),
      recentTemplates: templates
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5),
    };
  } catch (error) {
    console.error('Error fetching template stats:', error);
    return {
      totalTemplates: 0,
      eventTypeStats: {},
      averageAttendees: 0,
      recentTemplates: [],
    };
  }
};

/**
 * Search templates with advanced filtering
 * @param query - Search query
 * @param filters - Additional filters
 * @returns Filtered templates
 */
export const searchTemplates = async (
  query: string,
  filters: Omit<UserTemplateFilters, 'search'> = {}
): Promise<PaginatedTemplatesResponse> => {
  return getPublicTemplates({
    ...filters,
    search: query,
  });
};

/**
 * Get templates by event type
 * @param eventType - Event type to filter by
 * @param additionalFilters - Additional filters
 * @returns Templates of the specified event type
 */
export const getTemplatesByEventType = async (
  eventType: string,
  additionalFilters: Omit<UserTemplateFilters, 'eventType'> = {}
): Promise<PaginatedTemplatesResponse> => {
  return getPublicTemplates({
    ...additionalFilters,
    eventType,
  });
};

/**
 * Get templates by attendee range
 * @param minAttendees - Minimum number of attendees
 * @param maxAttendees - Maximum number of attendees
 * @param additionalFilters - Additional filters
 * @returns Templates within the attendee range
 */
export const getTemplatesByAttendeeRange = async (
  minAttendees: number,
  maxAttendees: number,
  additionalFilters: Omit<UserTemplateFilters, 'attendeesMin' | 'attendeesMax'> = {}
): Promise<PaginatedTemplatesResponse> => {
  return getPublicTemplates({
    ...additionalFilters,
    attendeesMin: minAttendees,
    attendeesMax: maxAttendees,
  });
};
