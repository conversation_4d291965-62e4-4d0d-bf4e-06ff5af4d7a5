/**
 * TypeScript interfaces for calculation state management
 * Provides proper typing for calculation detail page state and actions
 */

import { DateRange } from "react-day-picker";
import {
  LineItem,
  CategoryWithPackages,
  PackageFormState,
} from "@/types/calculation";
import { Tax, Discount } from "../utils/taxDiscountUtils";
import { FinancialCalculationResults } from "../hooks/financial/useFinancialCalculations";
import { CategoryWithOrder } from "../utils/sortingUtils";

/**
 * Interface for calculation detail page state
 * Consolidates all state from various hooks into a single interface
 */
export interface CalculationDetailState {
  // Core calculation data
  calculation: any; // TODO: Replace with proper Calculation interface
  packagesByCategory: CategoryWithPackages[];
  lineItems: LineItem[];
  categories: CategoryWithOrder[];

  // UI state
  expandedCategories: string[];
  packageForms: PackageFormState;
  isEditMode: boolean;
  isSaving: boolean;
  isAddingCustomItem: boolean;
  isEditingLineItem: boolean;
  currentEditingLineItem: LineItem | null;
  isDeleting: boolean;

  // Edit state
  editedName: string;
  editedEventType: string;
  editedNotes: string;
  editedAttendees: number;
  dateRange: DateRange | undefined;

  // Loading and error states
  isLoading: boolean;
  isLoadingPackages: boolean;
  isError: boolean;
  isPackagesError: boolean;

  // Financial data
  taxes: Tax[];
  discount: Discount;
  financialCalculations: FinancialCalculationResults;

  // State setters
  setIsAddingCustomItem: (value: boolean) => void;
  setIsEditingLineItem: (value: boolean) => void;
  setEditedName: (value: string) => void;
  setEditedEventType: (value: string) => void;
  setEditedNotes: (value: string) => void;
  setEditedAttendees: (value: number) => void;
  setDateRange: (value: DateRange | undefined) => void;

  // Category functions
  toggleCategory: (categoryId: string) => void;

  // Package form functions
  handleQuantityChange: (packageId: string, value: number) => void;
  handleItemQuantityBasisChange: (packageId: string, value: number) => void;
  handleOptionToggle: (
    packageId: string,
    optionId: string,
    isSelected: boolean
  ) => void;
  cleanupPackageForm: (packageId: string) => void;
  cleanupMultiplePackageForms: (packageIds: string[]) => void;
  resetAllPackageForms: () => void;
  getPackageFormData: (packageId: string) => any;

  // Edit mode functions
  handleToggleEditMode: () => void;
  handleSaveChanges: () => Promise<void>;

  // Line item functions
  handleAddToCalculation: (packageId: string) => void;
  handleAddCustomItem: (lineItem: any) => void; // TODO: Replace with proper LineItemInput interface
  handleEditLineItem: (lineItem: LineItem) => void;
  handleUpdateLineItem: (lineItemId: string, updates: any) => void; // TODO: Replace with proper updates interface
  handleRemoveLineItem: (lineItemId: string) => void;

  // Utility functions
  formatCurrency: (amount: number) => string;
  formatDate: (dateString: string) => string;

  // Tax and discount functions
  addTax: (tax: Tax) => Tax[];
  updateDiscount: (discount: Discount) => void;
  saveTaxesAndDiscount: (
    newStatus?: "draft" | "completed" | "canceled"
  ) => Promise<boolean>;
}

/**
 * Interface for calculation detail page actions
 * Consolidates all action handlers into a single interface
 */
export interface CalculationDetailActions {
  // Navigation actions
  handleNavigateBack: () => void;
  handleNavigateToList: () => void;

  // Status and lifecycle actions
  handleStatusChange: (
    newStatus: "draft" | "completed" | "canceled"
  ) => Promise<void>;
  handleDelete: () => Promise<void>;
}

/**
 * Combined interface for calculation detail content props
 * Used by CalculationDetailContent component
 */
export interface CalculationDetailContentProps {
  calculationId: string;
  state: CalculationDetailState;
  actions: CalculationDetailActions;
}

/**
 * Interface for line item input data
 * TODO: Move this to a more appropriate location or consolidate with existing interfaces
 */
export interface LineItemInput {
  package_id?: string;
  name: string;
  description: string;
  quantity: number;
  item_quantity_basis: number;
  unit_price: number;
  total_price: number;
  category_id: string;
  is_custom: boolean;
  quantity_basis?: string;
  selectedOptions?: string[];
}

/**
 * Interface for line item update data
 */
export interface LineItemUpdateData {
  name?: string;
  description?: string;
  quantity?: number;
  item_quantity_basis?: number;
  unit_price?: number;
  category_id?: string;
}

/**
 * Type guards for state validation
 */
export const isValidCalculationState = (
  state: any
): state is CalculationDetailState => {
  return (
    state &&
    typeof state === "object" &&
    Array.isArray(state.packagesByCategory) &&
    Array.isArray(state.lineItems) &&
    typeof state.isLoading === "boolean" &&
    typeof state.isError === "boolean"
  );
};

export const isValidCalculationActions = (
  actions: any
): actions is CalculationDetailActions => {
  return (
    actions &&
    typeof actions === "object" &&
    typeof actions.handleNavigateBack === "function" &&
    typeof actions.handleStatusChange === "function" &&
    typeof actions.handleDelete === "function"
  );
};
