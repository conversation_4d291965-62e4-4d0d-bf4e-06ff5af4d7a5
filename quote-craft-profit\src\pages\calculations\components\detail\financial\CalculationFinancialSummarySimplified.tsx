import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CurrencyInput } from "@/components/ui/currency-input";
import { Plus, Trash2, Save, CheckCircle, X } from "lucide-react";
import { toast } from "sonner";
import { Tax, Discount } from "../../../utils/calculationUtils";
import { useFinancialSummaryCalculations } from "../../../hooks/financial/useFinancialCalculations";

interface CalculationFinancialSummaryProps {
  total: number;
  formatCurrency: (amount: number) => string;
  calculationId?: string;
  status?: "draft" | "completed" | "canceled";
  initialTaxes?: Tax[]; // Initial taxes from the database
  initialDiscount?: Discount; // Initial discount from the database
  onStatusChange?: (
    status: "draft" | "completed" | "canceled"
  ) => Promise<void>;
  onDelete?: () => Promise<void>;
  onNavigateToList?: () => void; // New prop for navigation
  onTaxesChange?: (newTax: Tax) => void; // Callback when a tax is added
  onDiscountChange?: (discount: Discount) => void; // Callback when discount changes
}

const CalculationFinancialSummary: React.FC<
  CalculationFinancialSummaryProps
> = ({
  total,
  formatCurrency,
  calculationId,
  status = "draft",
  initialTaxes = [],
  initialDiscount,
  onStatusChange,
  onDelete,
  onNavigateToList,
  onTaxesChange,
  onDiscountChange,
}) => {
  // Local UI state
  const [isAddingTax, setIsAddingTax] = useState(false);
  const [newTaxName, setNewTaxName] = useState("");
  const [newTaxPercentage, setNewTaxPercentage] = useState("");
  const [isAddingDiscount, setIsAddingDiscount] = useState(false);
  const [discountAmount, setDiscountAmount] = useState("");
  const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Ensure total is a valid number
  const validTotal = typeof total === "number" && !isNaN(total) ? total : 0;

  // Use memoized financial calculations hook for better performance
  const { financialTotals, finalTotal } = useFinancialSummaryCalculations(
    validTotal,
    initialTaxes,
    initialDiscount || { name: "No Discount", type: "fixed", value: 0 }
  );

  // Handle adding a new tax
  const handleAddTax = () => {
    if (!newTaxName.trim()) {
      toast.error("Please enter a tax name");
      return;
    }

    const rate = parseFloat(newTaxPercentage);
    if (isNaN(rate) || rate <= 0) {
      toast.error("Please enter a valid tax percentage");
      return;
    }

    // Create a new tax with the simplified structure
    const newTax: Tax = {
      id: Date.now().toString(), // Generate a unique ID
      name: newTaxName.trim(),
      rate: rate,
    };

    // Call the callback if provided
    if (onTaxesChange) {
      onTaxesChange(newTax);
    }

    // Reset form
    setNewTaxName("");
    setNewTaxPercentage("");
    setIsAddingTax(false);
  };

  // Handle adding a discount
  const handleAddDiscount = () => {
    const value = parseFloat(discountAmount);
    if (isNaN(value) || value <= 0) {
      toast.error("Please enter a valid discount amount");
      return;
    }

    // Create a new discount with the simplified structure
    const newDiscount: Discount = {
      name: "Discount",
      type: "fixed",
      value: value,
    };

    // Call the callback if provided
    if (onDiscountChange) {
      onDiscountChange(newDiscount);
    }

    // Reset form
    setDiscountAmount("");
    setIsAddingDiscount(false);
  };

  // Handle removing the discount
  const handleRemoveDiscount = () => {
    // Create an empty discount with the simplified structure
    const emptyDiscount: Discount = {
      name: "Discount",
      type: "fixed",
      value: 0,
    };

    // Call the callback if provided
    if (onDiscountChange) {
      onDiscountChange(emptyDiscount);
    }
  };

  // Handle status change
  const handleStatusChange = async (
    newStatus: "draft" | "completed" | "canceled"
  ) => {
    if (!onStatusChange) return;

    try {
      setIsProcessing(true);

      // Call the status change handler
      await onStatusChange(newStatus);

      // Show success message
      toast.success(
        newStatus === "completed"
          ? "Calculation completed"
          : "Calculation saved as draft"
      );

      // Navigate to the calculation list page if saving as draft
      if (onNavigateToList && newStatus === "draft") {
        // Use a short delay to allow the toast to be seen
        setTimeout(() => {
          onNavigateToList();
        }, 500);
      }
    } catch (error) {
      toast.error(
        `Failed to ${
          newStatus === "completed" ? "complete" : "save"
        } calculation`
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!onDelete) return;

    try {
      setIsProcessing(true);
      await onDelete();
      toast.success("Calculation deleted");
    } catch (error) {
      console.error("Error deleting calculation:", error);
      toast.error("Failed to delete calculation");
    } finally {
      setIsProcessing(false);
      setIsConfirmingDelete(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg border">
      <h2 className="text-xl font-bold mb-4">Financial Summary</h2>
      <div className="space-y-4">
        {/* Subtotal */}
        <div className="flex justify-between items-center pb-2 border-b">
          <span className="text-gray-600">Sub Total</span>
          <span className="font-medium">{formatCurrency(validTotal)}</span>
        </div>

        {/* Taxes */}
        {initialTaxes.length > 0 && (
          <div className="space-y-2 pb-2 border-b">
            {financialTotals.calculatedTaxes.map(({ tax, amount }) => (
              <div key={tax.id} className="flex justify-between items-center">
                <div className="flex items-center">
                  <span className="text-gray-600">
                    {tax.name} ({tax.rate}%)
                  </span>
                </div>
                <span>{formatCurrency(amount)}</span>
              </div>
            ))}
          </div>
        )}

        {/* Add Tax Button/Form */}
        {isAddingTax ? (
          <div className="flex flex-col space-y-2 pb-2 border-b">
            <div className="flex space-x-2">
              <Input
                placeholder="Tax name"
                value={newTaxName}
                onChange={(e) => setNewTaxName(e.target.value)}
                className="flex-1"
              />
              <Input
                placeholder="Percentage"
                type="number"
                min="0"
                step="0.01"
                value={newTaxPercentage}
                onChange={(e) => setNewTaxPercentage(e.target.value)}
                className="w-24"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsAddingTax(false)}
              >
                <X size={16} className="mr-1" /> Cancel
              </Button>
              <Button variant="default" size="sm" onClick={handleAddTax}>
                <Plus size={16} className="mr-1" /> Add
              </Button>
            </div>
          </div>
        ) : (
          <div className="pb-2 border-b">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddingTax(true)}
              className="w-full"
            >
              <Plus size={16} className="mr-1" /> Add Tax
            </Button>
          </div>
        )}

        {/* Discount */}
        {initialDiscount?.value > 0 && (
          <div className="flex justify-between items-center pb-2 border-b">
            <div className="flex items-center">
              <span className="text-gray-600">Discount</span>
              <button
                onClick={handleRemoveDiscount}
                className="ml-2 text-red-500 hover:text-red-700"
              >
                <Trash2 size={16} />
              </button>
            </div>
            <span className="text-red-500">
              -{formatCurrency(financialTotals.discountAmount)}
            </span>
          </div>
        )}

        {/* Add Discount Button/Form */}
        {(!initialDiscount || initialDiscount.value === 0) &&
        isAddingDiscount ? (
          <div className="flex flex-col space-y-2 pb-2 border-b">
            <div className="flex space-x-2">
              <CurrencyInput
                placeholder="Discount amount"
                value={discountAmount}
                onChange={(numericValue) =>
                  setDiscountAmount(numericValue.toString())
                }
                className="flex-1"
                showSymbol={false}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsAddingDiscount(false)}
              >
                <X size={16} className="mr-1" /> Cancel
              </Button>
              <Button variant="default" size="sm" onClick={handleAddDiscount}>
                <Plus size={16} className="mr-1" /> Add
              </Button>
            </div>
          </div>
        ) : (
          (!initialDiscount || initialDiscount.value === 0) && (
            <div className="pb-2 border-b">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsAddingDiscount(true)}
                className="w-full"
              >
                <Plus size={16} className="mr-1" /> Add Discount
              </Button>
            </div>
          )
        )}

        {/* Total */}
        <div className="flex justify-between items-center pb-2 border-b">
          <span className="text-gray-800 font-semibold">Total</span>
          <span className="font-bold text-lg">
            {formatCurrency(finalTotal)}
          </span>
        </div>

        {/* Action Buttons */}
        {calculationId && (
          <div className="flex justify-between pt-4">
            {isConfirmingDelete ? (
              <div className="flex space-x-2">
                <span className="text-sm text-gray-600 mr-2 self-center">
                  Confirm delete?
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsConfirmingDelete(false)}
                  disabled={isProcessing}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isProcessing}
                >
                  {isProcessing ? "Deleting..." : "Delete"}
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsConfirmingDelete(true)}
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                disabled={isProcessing}
              >
                <Trash2 size={16} className="mr-1" /> Delete
              </Button>
            )}

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStatusChange("draft")}
                disabled={isProcessing}
              >
                <Save size={16} className="mr-1" /> Save Draft
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={() => handleStatusChange("completed")}
                disabled={isProcessing || status === "completed"}
              >
                <CheckCircle size={16} className="mr-1" /> Complete
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CalculationFinancialSummary;
