import React, { useState } from "react";
import { Trash2, Edit, Check, X } from "lucide-react";
import { CurrencyInput } from "@/components/ui/currency-input";
import { Button } from "@/components/ui/button";

interface DiscountDisplayProps {
  discountAmount: number;
  discountValue: number; // The actual discount value (not calculated amount)
  formatCurrency: (amount: number) => string;
  onRemove: () => void;
  onEdit?: (value: number) => void; // New prop for editing
}

/**
 * Discount display with edit and remove functionality
 * Shows discount amount and provides edit/remove functionality
 */
export const DiscountDisplay: React.FC<DiscountDisplayProps> = ({
  discountAmount,
  discountValue,
  formatCurrency,
  onRemove,
  onEdit,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(discountValue.toString());

  const handleSave = () => {
    const value = parseFloat(editValue);
    if (isNaN(value) || value < 0) {
      // Reset to original value if invalid
      setEditValue(discountValue.toString());
      setIsEditing(false);
      return;
    }

    if (onEdit) {
      onEdit(value);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(discountValue.toString());
    setIsEditing(false);
  };

  if (isEditing) {
    return (
      <div className="flex justify-between items-center gap-2 pb-2 border-b dark:border-gray-700">
        <div className="flex items-center gap-2 flex-1">
          <span className="text-gray-600 dark:text-gray-300">Discount</span>
          <div className="flex items-center gap-1">
            <CurrencyInput
              value={editValue}
              onChange={(numericValue) => setEditValue(numericValue.toString())}
              placeholder="Discount amount"
              className="h-8 w-32 text-sm"
              showSymbol={false}
            />
          </div>
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleSave}
              className="h-8 w-8 p-0 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-300"
            >
              <Check size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCancel}
              className="h-8 w-8 p-0 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-300"
            >
              <X size={14} />
            </Button>
          </div>
        </div>
        <span className="text-red-500 dark:text-red-400 text-sm">
          -{formatCurrency(discountAmount)}
        </span>
      </div>
    );
  }

  return (
    <div className="flex justify-between items-center pb-2 border-b dark:border-gray-700">
      <div className="flex items-center gap-2">
        <span className="text-gray-600 dark:text-gray-300">
          Discount (Fixed Price)
        </span>
        <div className="flex items-center gap-1">
          {onEdit && (
            <button
              onClick={() => setIsEditing(true)}
              className="text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1"
              title="Edit discount"
            >
              <Edit size={14} />
            </button>
          )}
          <button
            onClick={onRemove}
            className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1"
            title="Remove discount"
          >
            <Trash2 size={14} />
          </button>
        </div>
      </div>
      <span className="text-red-500 dark:text-red-400">
        -{formatCurrency(discountAmount)}
      </span>
    </div>
  );
};
