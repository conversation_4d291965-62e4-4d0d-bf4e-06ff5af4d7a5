import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Building2,
  MapPin,
  Loader2,
  ChevronRight,
  Users,
  Star,
  Crown,
  Home,
  Trees,
  AlertTriangle,
  CheckCircle,
  Filter,
} from "lucide-react";
import { getVenuesWithEnhancedFilters } from "@/services/shared/entities/venues";
import { WizardState } from "./WizardContainer";
import { toast } from "sonner";
import {
  VenueClassification,
  getVenueClassificationInfo,
  formatVenueCapacity,
  VENUE_CLASSIFICATIONS,
} from "@/types/venues";

interface VenueSelectorProps {
  wizardState: WizardState;
  updateWizardState: (updates: Partial<WizardState>) => void;
  onNext: () => void;
}

export const VenueSelector: React.FC<VenueSelectorProps> = ({
  wizardState,
  updateWizardState,
  onNext,
}) => {
  // State for venue filtering
  const [classificationFilter, setClassificationFilter] = useState<
    VenueClassification | "all"
  >("all");
  const [showFilters, setShowFilters] = useState(false);

  // Calculate capacity filter based on attendee count
  const minCapacityFilter = wizardState.attendeeCount
    ? Math.ceil(wizardState.attendeeCount * 0.8)
    : undefined;

  // Fetch venues with enhanced filtering
  const {
    data: venues,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [
      "venues-enhanced",
      wizardState.cityId,
      classificationFilter,
      minCapacityFilter,
    ],
    queryFn: () =>
      getVenuesWithEnhancedFilters({
        cityId: wizardState.cityId,
        active: true,
        classification:
          classificationFilter === "all" ? undefined : classificationFilter,
        minCapacity: minCapacityFilter,
      }),
    enabled: !!wizardState.cityId,
    meta: {
      onError: (error: Error) => {
        toast.error(`Failed to load venues: ${error.message}`);
      },
    },
  });

  const handleVenueSelect = (venueId: string) => {
    updateWizardState({ venueId });
    // Auto-advance to next step after selection
    setTimeout(() => {
      onNext();
    }, 300);
  };

  const handleSkip = () => {
    updateWizardState({ venueId: "" });
    onNext();
  };

  // Helper function to get venue classification icon
  const getClassificationIcon = (
    classification?: VenueClassification | null
  ) => {
    if (!classification) return Building2;

    switch (classification) {
      case "outdoor":
        return Trees;
      case "hotel":
        return Building2;
      case "indoor":
        return Home;
      case "premium":
        return Star;
      case "luxury":
        return Crown;
      default:
        return Building2;
    }
  };

  // Helper function to get capacity status
  const getCapacityStatus = (
    venueCapacity?: number | null,
    attendeeCount?: number
  ) => {
    if (!venueCapacity || !attendeeCount) return null;

    const percentage = (attendeeCount / venueCapacity) * 100;

    if (percentage > 100) {
      return { type: "over", icon: AlertTriangle, color: "text-red-500" };
    } else if (percentage > 80) {
      return { type: "near", icon: AlertTriangle, color: "text-yellow-500" };
    } else {
      return { type: "good", icon: CheckCircle, color: "text-green-500" };
    }
  };

  if (!wizardState.cityId) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Choose a venue (Optional)
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Please select a city first to see available venues
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Choose a venue (Optional)
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Select a specific venue or skip to see all templates
          </p>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-600 dark:text-gray-300">
              Loading venues...
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Choose a venue (Optional)
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Select a specific venue or skip to see all templates
          </p>
        </div>

        <div className="text-center py-12">
          <div className="text-red-600 dark:text-red-400 mb-4">
            <Building2 className="h-12 w-12 mx-auto mb-2" />
            <p className="text-lg font-medium">Unable to load venues</p>
            <p className="text-sm">You can skip this step to continue</p>
          </div>
          <Button onClick={handleSkip} variant="outline">
            Skip venue selection
          </Button>
        </div>
      </div>
    );
  }

  const selectedVenue = venues?.find(
    (venue) => venue.id === wizardState.venueId
  );

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          Choose a venue (Optional)
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Select a specific venue or skip to see all templates
        </p>
        {wizardState.attendeeCount && (
          <p className="text-sm text-blue-600 dark:text-blue-400 mt-2">
            Showing venues suitable for {wizardState.attendeeCount} attendees
          </p>
        )}
      </div>

      {/* Filtering Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
          </Button>
          {classificationFilter !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {VENUE_CLASSIFICATIONS[classificationFilter].label}
              <button
                onClick={() => setClassificationFilter("all")}
                className="ml-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full p-0.5"
              >
                ×
              </button>
            </Badge>
          )}
        </div>

        {showFilters && (
          <div className="flex flex-wrap gap-2">
            <Select
              value={classificationFilter}
              onValueChange={(value) =>
                setClassificationFilter(value as VenueClassification | "all")
              }
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                {Object.entries(VENUE_CLASSIFICATIONS).map(([key, info]) => (
                  <SelectItem key={key} value={key}>
                    {info.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {!venues || venues.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          {classificationFilter !== "all" || minCapacityFilter ? (
            <>
              <p className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
                No venues match your criteria
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                {classificationFilter !== "all" &&
                  `No ${VENUE_CLASSIFICATIONS[
                    classificationFilter
                  ].label.toLowerCase()} venues available. `}
                {minCapacityFilter &&
                  `No venues with capacity for ${wizardState.attendeeCount} attendees. `}
                Try adjusting your filters or continue without selecting a
                venue.
              </p>
              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                <Button
                  onClick={() => {
                    setClassificationFilter("all");
                  }}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  Clear filters
                </Button>
                <Button
                  onClick={handleSkip}
                  className="flex items-center gap-2"
                >
                  Continue without venue
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </>
          ) : (
            <>
              <p className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
                No venues available in this city
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                You can continue without selecting a venue
              </p>
              <Button onClick={handleSkip} className="flex items-center gap-2">
                Continue without venue
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {venues.map((venue) => {
              const isSelected = wizardState.venueId === venue.id;
              const ClassificationIcon = getClassificationIcon(
                venue.classification
              );
              const classificationInfo = getVenueClassificationInfo(
                venue.classification
              );
              const capacityStatus = getCapacityStatus(
                venue.capacity,
                wizardState.attendeeCount
              );

              return (
                <Card
                  key={venue.id}
                  className={`cursor-pointer transition-all duration-200 hover:scale-105 ${
                    isSelected
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-200 dark:ring-blue-800"
                      : "border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/10"
                  }`}
                  onClick={() => handleVenueSelect(venue.id)}
                >
                  <CardContent className="p-6">
                    {/* Venue Image */}
                    {venue.image_url && (
                      <div className="mb-4 rounded-lg overflow-hidden">
                        <img
                          src={venue.image_url}
                          alt={venue.name}
                          className="w-full h-32 object-cover"
                          onError={(e) => {
                            // Hide image if it fails to load
                            e.currentTarget.style.display = "none";
                          }}
                        />
                      </div>
                    )}

                    <div className="flex items-start space-x-4">
                      <div
                        className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 ${
                          isSelected
                            ? "bg-blue-500 text-white"
                            : "bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                        }`}
                      >
                        <ClassificationIcon className="h-6 w-6" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3
                            className={`font-semibold text-lg ${
                              isSelected
                                ? "text-blue-700 dark:text-blue-300"
                                : "text-gray-800 dark:text-white"
                            }`}
                          >
                            {venue.name}
                          </h3>
                          {classificationInfo && (
                            <Badge
                              variant="outline"
                              className={`text-xs ${
                                classificationInfo.color === "green"
                                  ? "border-green-500 text-green-700 dark:text-green-400"
                                  : classificationInfo.color === "blue"
                                  ? "border-blue-500 text-blue-700 dark:text-blue-400"
                                  : classificationInfo.color === "purple"
                                  ? "border-purple-500 text-purple-700 dark:text-purple-400"
                                  : classificationInfo.color === "yellow"
                                  ? "border-yellow-500 text-yellow-700 dark:text-yellow-400"
                                  : "border-gray-500 text-gray-700 dark:text-gray-400"
                              }`}
                            >
                              {classificationInfo.label}
                            </Badge>
                          )}
                        </div>

                        {venue.address && (
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-2">
                            <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                            <span className="truncate">{venue.address}</span>
                          </div>
                        )}

                        {venue.capacity && (
                          <div className="flex items-center text-sm mb-2">
                            <Users className="h-4 w-4 mr-1 flex-shrink-0 text-gray-400" />
                            <span className="text-gray-600 dark:text-gray-300">
                              {formatVenueCapacity(
                                venue.capacity,
                                wizardState.attendeeCount
                              )}
                            </span>
                            {capacityStatus && (
                              <capacityStatus.icon
                                className={`h-4 w-4 ml-2 ${capacityStatus.color}`}
                              />
                            )}
                          </div>
                        )}

                        {venue.city_name && (
                          <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                            {venue.city_name}
                          </div>
                        )}

                        {/* Venue Features */}
                        {venue.features && venue.features.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-2">
                            {venue.features
                              .slice(0, 3)
                              .map((feature, index) => (
                                <Badge
                                  key={index}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {feature}
                                </Badge>
                              ))}
                            {venue.features.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{venue.features.length - 3} more
                              </Badge>
                            )}
                          </div>
                        )}

                        {isSelected && (
                          <div className="mt-2">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                              Selected
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="text-center">
            <Button
              onClick={handleSkip}
              variant="outline"
              className="flex items-center gap-2"
            >
              Skip venue selection
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </>
      )}

      {selectedVenue && (
        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Selected: <span className="font-medium">{selectedVenue.name}</span>
          </p>
        </div>
      )}
    </div>
  );
};
