// src/modules/calculation-items/calculation-items.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { CalculationItemsService } from './calculation-items.service';
import { CalculationItemsController } from './calculation-items.controller';
import { CustomItemsController } from './controllers/custom-items.controller';
import { CustomItemsService } from './services/custom-items.service';
import { CalculationsModule } from '../calculations/calculations.module';
import { AuthModule } from '../auth/auth.module';
// Import SupabaseModule if needed and not global
// import { SupabaseModule } from '../../core/supabase/supabase.module';

@Module({
  imports: [
    AuthModule,
    forwardRef(() => CalculationsModule),
    // SupabaseModule, // Uncomment if SupabaseService is provided locally
  ],
  controllers: [CalculationItemsController, CustomItemsController],
  providers: [CalculationItemsService, CustomItemsService],
  exports: [CalculationItemsService, CustomItemsService], // Export services for injection
})
export class CalculationItemsModule {}
