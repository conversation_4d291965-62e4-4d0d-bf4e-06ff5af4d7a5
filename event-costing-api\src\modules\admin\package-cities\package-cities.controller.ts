import {
  Controller,
  Logger,
  UseGuards,
  Post,
  Delete,
  Get,
  Param,
  Body,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from 'src/modules/auth/guards/admin-role.guard';
import { PackageCitiesService } from './package-cities.service';
import { PackageCityDto } from './dto/package-city.dto';
import { AddPackageCityDto } from './dto/add-package-city.dto';
import { ParseUUIDPipe } from '@nestjs/common';

@ApiTags('Admin - Packages - Cities')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AdminRoleGuard)
@Controller('admin/packages/:packageId/cities')
export class PackageCitiesController {
  private readonly logger = new Logger(PackageCitiesController.name);

  constructor(private readonly packageCitiesService: PackageCitiesService) {}

  // --- Add City to Package ---
  @Post()
  @ApiOperation({
    summary: 'Add a city association to a package',
  })
  @ApiResponse({
    status: 201,
    description: 'City added successfully.',
    type: String, // Returning the new association ID
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (validation errors, city already added)',
  })
  @ApiResponse({ status: 404, description: 'Package or City not found' })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'Package UUID',
  })
  async addCity(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Body() addDto: AddPackageCityDto,
  ): Promise<{ id: string }> {
    // Return the ID of the new association
    this.logger.log(`Adding city ${addDto.city_id} to package ${packageId}`);
    // Removed explicit cast, calling directly
    const newAssociation = await this.packageCitiesService.addCityToPackage(
      packageId,
      addDto.city_id,
    );
    return { id: newAssociation.id }; // Return the ID
  }

  // --- List Cities for Package ---
  @Get()
  @ApiOperation({ summary: 'List cities associated with a package' })
  @ApiResponse({
    status: 200,
    description: 'List of associated cities.',
    type: [PackageCityDto],
  })
  @ApiResponse({ status: 404, description: 'Package not found' })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'Package UUID',
  })
  async listCities(
    @Param('packageId', ParseUUIDPipe) packageId: string,
  ): Promise<PackageCityDto[]> {
    this.logger.log(`Listing cities for package ${packageId}`);
    // Removed explicit cast, calling directly
    return this.packageCitiesService.listCitiesForPackage(packageId);
  }

  // --- Remove City from Package ---
  @Delete(':cityId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remove a city association from a package' })
  @ApiResponse({ status: 204, description: 'City removed successfully.' })
  @ApiResponse({
    status: 404,
    description: 'Package, City, or Association not found',
  })
  @ApiParam({
    name: 'packageId',
    type: 'string',
    format: 'uuid',
    description: 'Package UUID',
  })
  @ApiParam({
    name: 'cityId',
    type: 'string',
    format: 'uuid',
    description: 'City UUID',
  })
  async removeCity(
    @Param('packageId', ParseUUIDPipe) packageId: string,
    @Param('cityId', ParseUUIDPipe) cityId: string,
  ): Promise<void> {
    this.logger.log(`Removing city ${cityId} from package ${packageId}`);
    // Removed explicit cast, calling directly
    await this.packageCitiesService.removeCityFromPackage(packageId, cityId);
  }
}
