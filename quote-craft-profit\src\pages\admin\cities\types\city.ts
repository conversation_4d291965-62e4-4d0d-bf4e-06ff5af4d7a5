/**
 * City types for the admin cities feature
 */

import { City as GlobalCity } from '@/types/types';

// Re-export the global City interface
export interface City extends GlobalCity {}

/**
 * Form data for creating or updating a city
 */
export interface CityFormData {
  name: string;
}

/**
 * API response for city operations
 */
export interface CityApiResponse {
  data: City | City[];
  error?: string;
  message?: string;
}
