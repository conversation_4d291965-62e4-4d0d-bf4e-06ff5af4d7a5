import React from "react";
import AdminLayout from "@/components/layout/AdminLayout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Users, Package, FileText, Settings } from "lucide-react";
import {
  ActivityItem,
  ActivityItemProps,
  MetricsCard,
  NavigationCard,
} from "./components";

const AdminDashboardPage: React.FC = () => {
  // Mock data for metrics
  const metrics = [
    {
      title: "Total Users",
      value: "128",
      icon: <Users size={20} />,
      description: "+12 this month",
    },
    {
      title: "Service Packages",
      value: "45",
      icon: <Package size={20} />,
      description: "Across 6 categories",
    },
    {
      title: "Active Templates",
      value: "24",
      icon: <FileText size={20} />,
      description: "8 public templates",
    },
    {
      title: "Calculations",
      value: "356",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M2 9a3 3 0 0 1 0-6h9a3 3 0 0 1 0 6H2Z"></path>
          <path d="M2 15a3 3 0 0 1 0-6h9a3 3 0 0 1 0 6H2Z"></path>
          <path d="M2 21a3 3 0 0 1 0-6h9a3 3 0 0 1 0 6H2Z"></path>
          <path d="M17 6V3h5v3"></path>
          <path d="M17 12v-3h5v3"></path>
          <path d="M17 18v-3h5v3"></path>
        </svg>
      ),
      description: "42 this week",
    },
  ];

  // Mock data for recent activity
  const recentActivity: ActivityItemProps[] = [
    {
      type: "user",
      message: "New user registered: Sarah Johnson",
      timestamp: "2 hours ago",
      user: { name: "System", email: "<EMAIL>" },
    },
    {
      type: "package",
      message: "Package 'Premium Catering' was updated",
      timestamp: "4 hours ago",
      user: { name: "Admin", email: "<EMAIL>" },
    },
    {
      type: "template",
      message: "New template 'Corporate Conference' created",
      timestamp: "Yesterday",
      user: { name: "Admin", email: "<EMAIL>" },
    },
    {
      type: "calculation",
      message: "10 new calculations performed",
      timestamp: "2 days ago",
    },
    {
      type: "user",
      message: "User role updated: Mark Anderson (admin)",
      timestamp: "3 days ago",
      user: { name: "Admin", email: "<EMAIL>" },
    },
  ];

  // Navigation sections
  const navigationSections = [
    {
      title: "User Management",
      description: "Manage users, permissions, and access control",
      icon: <Users size={24} />,
      to: "/admin/users",
    },
    {
      title: "Catalogue Management",
      description: "Manage service packages, categories, and pricing",
      icon: <Package size={24} />,
      to: "/admin/catalogue",
    },
    {
      title: "Template Management",
      description: "Manage event templates and default configurations",
      icon: <FileText size={24} />,
      to: "/admin/templates",
    },
    {
      title: "Application Settings",
      description: "Configure application defaults and system settings",
      icon: <Settings size={24} />,
      to: "/admin/settings",
    },
  ];

  return (
    <AdminLayout title="Admin Dashboard">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric, index) => (
          <MetricsCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            description={metric.description}
          />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              {recentActivity.map((activity, index) => (
                <div key={index}>
                  <ActivityItem
                    type={activity.type}
                    message={activity.message}
                    timestamp={activity.timestamp}
                    user={activity.user}
                  />
                  {index < recentActivity.length - 1 && <Separator />}
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
        <div>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Storage Usage
                </p>
                <p className="text-lg font-bold">28.4 GB / 100 GB</p>
                <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full mt-1">
                  <div
                    className="h-2 bg-eventcost-primary rounded-full"
                    style={{ width: "28.4%" }}
                  ></div>
                </div>
              </div>
              <Separator />
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  API Calls
                </p>
                <p className="text-lg font-bold">12,543 / 50,000</p>
                <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full mt-1">
                  <div
                    className="h-2 bg-eventcost-primary rounded-full"
                    style={{ width: "25%" }}
                  ></div>
                </div>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Last backup:
                </span>
                <span className="text-sm font-medium">Today, 04:30 AM</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  System status:
                </span>
                <span className="text-sm font-medium flex items-center">
                  <span className="h-2 w-2 bg-green-500 rounded-full mr-2"></span>
                  Operational
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <h2 className="text-2xl font-semibold mb-4 dark:text-white">
        Administration
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {navigationSections.map((section, index) => (
          <NavigationCard
            key={index}
            title={section.title}
            description={section.description}
            icon={section.icon}
            to={section.to}
          />
        ))}
      </div>
    </AdminLayout>
  );
};

export default AdminDashboardPage;
