import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { TemplateDetailDto, EnhancedTemplateDetailDto } from '../dto/template-summary.dto';
import { TemplateVenueService } from './template-venue.service';
import { TemplateConstants } from '../constants/template.constants';

@Injectable()
export class TemplateDetailService {
  private readonly logger = new Logger(TemplateDetailService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly templateVenueService: TemplateVenueService,
  ) {}

  /**
   * Find a single public template by ID
   */
  async findOnePublic(id: string): Promise<TemplateDetailDto> {
    this.logger.log(`Finding public template with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from(TemplateConstants.TABLE_NAME)
      .select(TemplateConstants.DETAIL_SELECT_FIELDS)
      .eq('id', id)
      .eq('is_public', true)
      .eq('is_deleted', false)
      .single<TemplateDetailDto>();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(`Public template with ID ${id} not found.`);
      }
      this.logger.error(
        `Error fetching public template ${id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Could not retrieve public template.',
      );
    }

    if (!data) {
      // Should be caught by PGRST116
      throw new NotFoundException(`Public template with ID ${id} not found.`);
    }

    // Map dates
    const template: TemplateDetailDto = {
      ...data,
      template_start_date: data.template_start_date
        ? new Date(data.template_start_date as unknown as string)
        : undefined,
      template_end_date: data.template_end_date
        ? new Date(data.template_end_date as unknown as string)
        : undefined,
      created_at: new Date(data.created_at as unknown as string),
      updated_at: new Date(data.updated_at as unknown as string),
      is_deleted: data.is_deleted || false, // Ensure is_deleted is always included
    };

    // Add venue IDs to template
    await this.templateVenueService.addVenueIdsToTemplate(template);

    return template;
  }

  /**
   * Find a single public template by ID with enhanced package and option names
   */
  async findOnePublicEnhanced(id: string): Promise<EnhancedTemplateDetailDto> {
    this.logger.log(`Finding enhanced public template with ID: ${id}`);

    // First get the basic template details
    const template = await this.findOnePublic(id);

    // Enhance with package and option names
    const enhancedTemplate = await this.enhanceTemplateWithNames(template);

    return enhancedTemplate;
  }

  /**
   * Enhance template with package and option names
   */
  private async enhanceTemplateWithNames(template: TemplateDetailDto): Promise<EnhancedTemplateDetailDto> {
    const supabase = this.supabaseService.getClient();

    if (!template.package_selections || template.package_selections.length === 0) {
      return {
        ...template,
        package_selections: [],
      };
    }

    // Extract all unique package IDs and option IDs
    const packageIds = [...new Set(template.package_selections.map(sel => sel.package_id))];
    const allOptionIds = [...new Set(template.package_selections.flatMap(sel => sel.option_ids))];

    // Fetch package names
    const { data: packages, error: packagesError } = await supabase
      .from('packages')
      .select('id, name')
      .in('id', packageIds);

    if (packagesError) {
      this.logger.error(`Error fetching package names: ${packagesError.message}`);
      throw new InternalServerErrorException('Could not retrieve package names.');
    }

    // Fetch option names (if there are any options)
    let options: { id: string; option_name: string }[] = [];
    if (allOptionIds.length > 0) {
      const { data: optionsData, error: optionsError } = await supabase
        .from('package_options')
        .select('id, option_name')
        .in('id', allOptionIds);

      if (optionsError) {
        this.logger.error(`Error fetching option names: ${optionsError.message}`);
        throw new InternalServerErrorException('Could not retrieve option names.');
      }

      options = optionsData || [];
    }

    // Create lookup maps
    const packageNameMap = new Map(packages?.map(pkg => [pkg.id, pkg.name]) || []);
    const optionNameMap = new Map(options.map(opt => [opt.id, opt.option_name]));

    // Enhance package selections with names
    const enhancedPackageSelections = template.package_selections.map(selection => ({
      ...selection,
      package_name: packageNameMap.get(selection.package_id) || 'Unknown Package',
      option_names: selection.option_ids.map(optionId =>
        optionNameMap.get(optionId) || 'Unknown Option'
      ),
    }));

    return {
      ...template,
      package_selections: enhancedPackageSelections,
    };
  }
}
