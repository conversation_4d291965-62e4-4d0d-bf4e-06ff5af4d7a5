// Import the global QuantityBasisEnum
import { QuantityBasisEnum } from "./types";

// Re-export QuantityBasisEnum for convenience
export { QuantityBasisEnum };

export interface PackageOption {
  id: string;
  option_name: string;
  description: string;
  price_adjustment: number;
  is_default_for_package: boolean;
  is_required: boolean;
}

export interface PackageWithOptions {
  id: string;
  name: string;
  description: string;
  category_id: string;
  category_name: string;
  division_id?: string;
  division_name?: string;
  quantity_basis?: QuantityBasisEnum; // Updated to use enum instead of string
  price: string;
  unit_base_cost: string;
  currency_symbol: string;
  options: PackageOption[];
}

export interface PackageFormState {
  [packageId: string]: {
    quantity: number;
    item_quantity_basis: number;
    selectedOptions: string[]; // Array of selected option IDs
  };
}

export interface CategoryWithPackages {
  id: string;
  name: string;
  display_order?: number; // Add display_order property for sorting
  packages: PackageWithOptions[];
}

/**
 * Interface for line items in a calculation
 */
export interface LineItem {
  id: string;
  calculation_id: string;
  package_id?: string | null; // Optional, for package-based items
  name: string;
  description?: string;
  quantity: number;
  /**
   * Number of days/units this line item applies for.
   */
  item_quantity_basis: number;
  /**
   * Price per unit.
   * Note: In the database, this is stored as unit_base_price for standard line items
   * and unit_price for custom items.
   */
  unit_price: number;
  /**
   * Total price for this line item.
   */
  total_price: number;
  category_id: string;
  is_custom?: boolean;
  is_package?: boolean;
  /**
   * Determines how the price is calculated based on quantity and duration
   */
  quantity_basis: QuantityBasisEnum;
  /**
   * Alternative naming for quantity_basis
   */
  quantityBasis?: QuantityBasisEnum;
  /**
   * Total price adjustment from all selected package options
   * Used for displaying price breakdown in the UI
   */
  options_total_adjustment?: number;
  options?: any[]; // Array of options
  selectedOptions?: string[]; // Alternative naming
  category?: {
    id: string;
    name: string;
  };
  created_at: string;
  createdAt?: Date; // Alternative naming
  updated_at: string;
  updatedAt?: Date; // Alternative naming
  _isOptimistic?: boolean; // Flag for optimistic updates
}

/**
 * Input data for creating or updating a line item
 */
export interface LineItemInput {
  package_id?: string | null;
  name?: string;
  description?: string;
  quantity?: number;
  /**
   * Number of days/units this line item applies for.
   */
  item_quantity_basis?: number;
  /**
   * Price per unit.
   * Note: In the database, this is stored as unit_base_price for standard line items
   * and unit_price for custom items.
   */
  unit_price?: number;
  /**
   * Total price for this line item.
   */
  total_price?: number;
  category_id?: string;
  is_custom?: boolean;
  /**
   * Determines how the price is calculated based on quantity and duration
   */
  quantity_basis?: QuantityBasisEnum;
  /**
   * Alternative naming for quantity_basis
   */
  quantityBasis?: QuantityBasisEnum;
  selectedOptions?: string[];
}

/**
 * Interface for line item options
 * Maps to calculation_line_item_options table in Supabase
 */
export interface LineItemOption {
  id: string;
  line_item_id: string;
  package_option_id: string;
  name: string;
  description?: string;
  price_adjustment: number;
  cost_adjustment?: number;
}
