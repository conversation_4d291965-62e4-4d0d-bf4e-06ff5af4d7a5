import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import MainLayout from '@/components/layout/MainLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { ChevronLeft, Save, Trash2 } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { toast } from 'sonner';
import { getEventById, deleteEvent, updateEvent } from '@/services/shared/entities/events';
import { getAllClients } from '@/services/shared/entities/clients';
import { Event, EventFormData, transformEventFormDataToApiRequest, transformEventToFormData } from '@/types/events';
import { EventFormDialog } from './components';

// TODO: Implement real API call to get calculations for this event
// This should fetch calculations from the calculations table filtered by event_id

// Status options for the dropdown
const statusOptions = [
  { value: 'lead', label: 'Lead' },
  { value: 'planning', label: 'Planning' },
  { value: 'confirmed', label: 'Confirmed' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'post_event', label: 'Post Event' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'on_hold', label: 'On Hold' },
];

// Convert API Event to EventFormData for proper date range handling
const convertToEventFormData = (event: Event): EventFormData => {
  return transformEventToFormData(event);
};

const EventDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Fetch event data from API
  const {
    data: apiEvent,
    isLoading,
  } = useQuery({
    queryKey: ['event', id],
    queryFn: () => getEventById(id || ''),
    enabled: !!id,
    meta: {
      onError: (error: Error) => {
        console.error('Failed to fetch event:', error);
        toast.error('Failed to load event details. Please try again.');
      },
    },
  });

  // Fetch clients for the dropdown
  const { data: clients = [] } = useQuery({
    queryKey: ['clients'],
    queryFn: () => getAllClients({ pageSize: 100 }), // Get first 100 clients
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const [eventFormData, setEventFormData] = useState<EventFormData | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Update local state when data is loaded
  React.useEffect(() => {
    if (apiEvent) {
      const formData = convertToEventFormData(apiEvent);
      setEventFormData(formData);
      setHasChanges(false);
    }
  }, [apiEvent]);

  const handleChange = (field: keyof EventFormData, value: any) => {
    if (eventFormData) {
      setEventFormData({ ...eventFormData, [field]: value });
      setHasChanges(true);
    }
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      handleChange('dateRange', range);
    } else if (range?.from) {
      // Partial selection - update the range but don't mark as complete
      handleChange('dateRange', range);
    }
  };

  // Save event mutation
  const saveMutation = useMutation({
    mutationFn: (data: EventFormData) => {
      const apiRequest = transformEventFormDataToApiRequest(data);
      return updateEvent(id || '', apiRequest);
    },
    onSuccess: () => {
      toast.success('Event updated successfully');
      setHasChanges(false);
      queryClient.invalidateQueries({ queryKey: ['event', id] });
    },
    onError: (error) => {
      console.error('Failed to update event:', error);
      toast.error('Failed to update event. Please try again.');
    },
  });

  // Delete event mutation
  const deleteMutation = useMutation({
    mutationFn: () => deleteEvent(id || ''),
    onSuccess: () => {
      toast.success('Event deleted successfully');
      navigate('/events');
    },
    onError: (error) => {
      console.error('Failed to delete event:', error);
      toast.error('Failed to delete event. Please try again.');
    },
  });

  const handleSave = () => {
    if (!eventFormData) return;

    // Validate date range
    if (!eventFormData.dateRange?.from || !eventFormData.dateRange?.to) {
      toast.error('Please select both start and end dates');
      return;
    }

    saveMutation.mutate(eventFormData);
  };




  const handleDelete = () => {
    if (
      confirm('Are you sure you want to delete this event? This action cannot be undone.')
    ) {
      deleteMutation.mutate();
    }
  };

  const handleOpenEditDialog = () => {
    setIsEditDialogOpen(true);
  };

  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false);
    // Refresh the event data
    queryClient.invalidateQueries({ queryKey: ['event', id] });
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className='flex items-center justify-center h-64'>
          <div className='text-lg text-gray-500'>Loading event details...</div>
        </div>
      </MainLayout>
    );
  }

  if (!eventFormData) {
    return (
      <MainLayout>
        <div className='flex flex-col items-center justify-center h-64'>
          <div className='text-lg text-gray-500 mb-4'>Event not found</div>
          <Button asChild>
            <Link to='/events'>Back to Events</Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className='space-y-6'>
        {/* Breadcrumbs and Title */}
        <div className='space-y-2'>
          <div className='flex items-center text-sm text-gray-500'>
            <Link to='/events' className='hover:text-gray-900 flex items-center'>
              <ChevronLeft className='h-4 w-4 mr-1' />
              Events
            </Link>
            <span className='mx-2'>/</span>
            <span>{eventFormData.name}</span>
          </div>
          <h1 className='text-3xl font-bold tracking-tight'>{eventFormData.name}</h1>
        </div>

        {/* Action Buttons */}
        <div className='flex justify-between items-center'>
          <div className='flex gap-2'>
            <Button
              variant='default'
              onClick={handleSave}
              disabled={!hasChanges || saveMutation.isPending}
            >
              <Save className='mr-2 h-4 w-4' />
              {saveMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
            <Button variant='outline' onClick={handleOpenEditDialog}>
              Edit Event
            </Button>
            <Button variant='outline' asChild>
              <Link to={`/calculations/new?eventId=${apiEvent?.id}`}>
                Create Calculation
              </Link>
            </Button>
          </div>
          <Button variant='destructive' onClick={handleDelete}>
            <Trash2 className='mr-2 h-4 w-4' />
            Delete Event
          </Button>
        </div>

        {/* Edit Event Dialog */}
        {isEditDialogOpen && apiEvent && (
          <EventFormDialog
            isOpen={isEditDialogOpen}
            onClose={handleCloseEditDialog}
            event={apiEvent}
            isEditing={true}
          />
        )}

        {/* Event Details Form */}
        <Card>
          <CardContent className='p-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium'>Event Name</label>
                  <Input
                    value={eventFormData.name}
                    onChange={(e) => handleChange('name', e.target.value)}
                    placeholder='Enter event name'
                  />
                </div>

                <div className='space-y-2'>
                  <label className='text-sm font-medium'>Client</label>
                  <Select
                    value={eventFormData.clientId}
                    onValueChange={(value) => handleChange('clientId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select client' />
                    </SelectTrigger>
                    <SelectContent>
                      {(clients as any)?.data
                        ?.filter((client: any) => client.id && client.id.trim() !== '') // Filter out clients with empty IDs
                        .map((client: any) => (
                          <SelectItem key={client.id} value={client.id}>
                            {client.name}
                            {client.company && client.company !== client.name && (
                              <span className="text-gray-500 ml-1">({client.company})</span>
                            )}
                          </SelectItem>
                        )) || []}
                      {(!(clients as any)?.data || (clients as any)?.data.length === 0) && (
                        <SelectItem value="no-clients" disabled>
                          No clients available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className='space-y-2'>
                  <label className='text-sm font-medium'>Event Date Range</label>
                  <DateRangePicker
                    value={eventFormData.dateRange}
                    onChange={handleDateRangeChange}
                    placeholder='Select start and end dates'
                    disablePastDates={true}
                    numberOfMonths={2}
                  />
                </div>
              </div>

              <div className='space-y-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium'>Status</label>
                  <Select
                    value={eventFormData.status}
                    onValueChange={(value) => handleChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select status' />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className='space-y-2'>
                  <label className='text-sm font-medium'>Venue</label>
                  <Input
                    value={eventFormData.location}
                    onChange={(e) => handleChange('location', e.target.value)}
                    placeholder='Enter venue details'
                  />
                </div>

                <div className='space-y-2'>
                  <label className='text-sm font-medium'>Primary Contact</label>
                  <Select
                    value={eventFormData.primaryContactId || 'none'}
                    onValueChange={(value) => handleChange('primaryContactId', value === 'none' ? undefined : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select contact' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {(clients as any)?.data
                        ?.filter((client: any) => client.email && client.id && client.id.trim() !== '') // Filter out clients with empty IDs
                        .map((client: any) => (
                          <SelectItem key={client.id} value={client.id}>
                            {client.name}
                            {client.email && (
                              <span className="text-gray-500 ml-1">({client.email})</span>
                            )}
                          </SelectItem>
                        )) || []}
                      {(!(clients as any)?.data || (clients as any)?.data.length === 0) && (
                        <SelectItem value="no-contacts" disabled>
                          No contacts available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* Removed attendees field - this information is tracked in calculation history */}
              </div>
            </div>

            <div className='mt-6 space-y-2'>
              <label className='text-sm font-medium'>Notes</label>
              <Textarea
                placeholder='Add notes about this event'
                className='min-h-[120px]'
                value={eventFormData.notes || ''}
                onChange={(e) => handleChange('notes', e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Associated Calculations */}
        <div className='space-y-4'>
          <h2 className='text-xl font-bold'>Linked Calculations</h2>
          <Card>
            <CardContent className='p-0'>
              {/* TODO: Implement real calculations fetching */}
              <div className='p-6 text-center'>
                <p className='text-gray-500'>No calculations linked to this event</p>
                <Button className='mt-4' asChild>
                  <Link to={`/calculations/new?eventId=${apiEvent?.id}`}>
                    Create new calculation
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default EventDetailsPage;
