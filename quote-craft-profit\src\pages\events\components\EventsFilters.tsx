import React from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";

interface EventsFiltersProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  filterStatus: string | null;
  setFilterStatus: (value: string | null) => void;
  filterClient: string | null;
  setFilterClient: (value: string | null) => void;
  filterContact: string | null;
  setFilterContact: (value: string | null) => void;
  filterDateRange: DateRange | undefined;
  setFilterDateRange: (value: DateRange | undefined) => void;
  statusOptions: string[];
  clientOptions: string[];
  contactOptions: string[];
}

const EventsFilters: React.FC<EventsFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  filterStatus,
  setFilterStatus,
  filterClient,
  setFilterClient,
  filterContact,
  setFilterContact,
  filterDateRange,
  setFilterDateRange,
  statusOptions,
  clientOptions,
  contactOptions,
}) => {
  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500" />
        <Input
          placeholder="Search events by name or client..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Status
          </label>
          <Select
            value={filterStatus || "all-statuses"}
            onValueChange={(value) =>
              setFilterStatus(value === "all-statuses" ? null : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-statuses">All statuses</SelectItem>
              {statusOptions
                .filter((status) => status && status.trim() !== "") // Filter out empty strings
                .map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Client
          </label>
          <Select
            value={filterClient || "all-clients"}
            onValueChange={(value) =>
              setFilterClient(value === "all-clients" ? null : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All clients" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-clients">All clients</SelectItem>
              {clientOptions
                .filter((client) => client && client.trim() !== "") // Filter out empty strings
                .map((client) => (
                  <SelectItem key={client} value={client}>
                    {client}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Primary Contact
          </label>
          <Select
            value={filterContact || "all-contacts"}
            onValueChange={(value) =>
              setFilterContact(value === "all-contacts" ? null : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All contacts" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-contacts">All contacts</SelectItem>
              {contactOptions
                .filter((contact) => contact && contact.trim() !== "") // Filter out empty strings
                .map((contact) => (
                  <SelectItem key={contact} value={contact}>
                    {contact}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Date Range
          </label>
          <DateRangePicker
            value={filterDateRange}
            onChange={setFilterDateRange}
            placeholder="Select date range"
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
};

export default EventsFilters;
