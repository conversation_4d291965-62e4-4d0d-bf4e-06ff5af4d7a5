import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { CostItemsService } from './cost-items.service';
import { CreateCostItemDto } from './dto/create-cost-item.dto';
import { UpdateCostItemDto } from './dto/update-cost-item.dto';
import { CostItemDto } from './dto/cost-item.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { AdminRoleGuard } from '../../auth/guards/admin-role.guard';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@ApiTags('Admin - Cost Items')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AdminRoleGuard)
@Controller('admin/cost-items')
export class CostItemsController {
  private readonly logger = new Logger(CostItemsController.name);

  constructor(private readonly costItemsService: CostItemsService) {}

  // --- Create Cost Item --- //
  @Post()
  @ApiOperation({ summary: 'Create a new cost item' })
  @ApiResponse({
    status: 201,
    description: 'Cost item created successfully.',
    type: CostItemDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., validation error)',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict (e.g., duplicate item code)',
  })
  create(@Body() createCostItemDto: CreateCostItemDto): Promise<CostItemDto> {
    this.logger.log(
      `Received request to create cost item: ${JSON.stringify(createCostItemDto)}`,
    );
    return this.costItemsService.create(createCostItemDto);
  }

  // --- Find All Cost Items --- //
  @Get()
  @ApiOperation({ summary: 'Get all cost items' })
  // @ApiQuery({ name: '...', type: String, required: false, description: '...' }) // Add query params if needed
  @ApiResponse({
    status: 200,
    description: 'List of cost items.',
    type: [CostItemDto],
  })
  findAll(/* @Query() queryParams: SomeQueryDto */): Promise<CostItemDto[]> {
    this.logger.log('Received request to find all cost items');
    return this.costItemsService.findAll(/* queryParams */);
  }

  // --- Find One Cost Item --- //
  @Get(':id')
  @ApiOperation({ summary: 'Get a specific cost item by ID' })
  @ApiParam({
    name: 'id',
    description: 'The ID of the cost item',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Cost item details.',
    type: CostItemDto,
  })
  @ApiResponse({ status: 404, description: 'Cost item not found' })
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<CostItemDto> {
    this.logger.log(`Received request to find cost item with ID: ${id}`);
    return this.costItemsService.findOne(id);
  }

  // --- Update Cost Item --- //
  @Patch(':id')
  @ApiOperation({ summary: 'Update a specific cost item' })
  @ApiParam({
    name: 'id',
    description: 'The ID of the cost item to update',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Cost item updated successfully.',
    type: CostItemDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., validation error)',
  })
  @ApiResponse({ status: 404, description: 'Cost item not found' })
  @ApiResponse({
    status: 409,
    description: 'Conflict (e.g., duplicate item code on update)',
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCostItemDto: UpdateCostItemDto,
  ): Promise<CostItemDto> {
    this.logger.log(
      `Received request to update cost item ${id}: ${JSON.stringify(updateCostItemDto)}`,
    );
    return this.costItemsService.update(id, updateCostItemDto);
  }

  // --- Remove Cost Item (Soft Delete) --- //
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Soft-delete a specific cost item' })
  @ApiParam({
    name: 'id',
    description: 'The ID of the cost item to delete',
    format: 'uuid',
  })
  @ApiResponse({
    status: 204,
    description: 'Cost item soft-deleted successfully.',
  })
  @ApiResponse({ status: 404, description: 'Cost item not found' })
  remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Received request to delete cost item with ID: ${id}`);
    return this.costItemsService.remove(id);
  }
}
