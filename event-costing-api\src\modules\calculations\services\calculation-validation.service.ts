import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';

/**
 * Service responsible for validation operations on calculations
 * Extracted from the main CalculationsService for better separation of concerns
 */
@Injectable()
export class CalculationValidationService {
  private readonly logger = new Logger(CalculationValidationService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Check if a user owns a specific calculation
   */
  async checkCalculationOwnership(
    calculationId: string,
    userId: string,
  ): Promise<void> {
    this.logger.log(
      `[EXPORT] Starting ownership check for calc ${calculationId}, user ${userId}`,
    );

    const supabase = this.supabaseService.getClient();
    const startTime = Date.now();

    const { data, error } = await supabase
      .from('calculation_history')
      .select('created_by', { count: 'exact' })
      .eq('id', calculationId)
      .eq('is_deleted', false)
      .eq('created_by', userId)
      .maybeSingle<{ created_by: string }>();

    const duration = Date.now() - startTime;
    this.logger.log(
      `[EXPORT] Ownership check query completed in ${duration}ms for calc ${calculationId}`,
    );

    if (error) {
      this.logger.error(
        `[EXPORT] Supabase error in ownership check for calc ${calculationId}: ${error.message}`,
        JSON.stringify(error, null, 2),
      );
      throw new NotFoundException(
        `Calculation with ID ${calculationId} not found or check failed.`,
      );
    }
    if (!data) {
      this.logger.warn(
        `[EXPORT] No data returned from ownership check for calc ${calculationId}, user ${userId}`,
      );
      throw new NotFoundException(
        `Calculation with ID ${calculationId} not found or not accessible.`,
      );
    }

    this.logger.log(
      `[EXPORT] Ownership check passed for calc ${calculationId}, user ${userId}`,
    );
  }

  /**
   * Validate if a calculation exists and is not deleted
   */
  async validateCalculationExists(calculationId: string): Promise<boolean> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('calculation_history')
      .select('id')
      .eq('id', calculationId)
      .eq('is_deleted', false)
      .maybeSingle();

    if (error) {
      this.logger.error(
        `Error validating calculation existence for ${calculationId}: ${error.message}`,
      );
      return false;
    }

    return !!data;
  }

  /**
   * Check if a user can access a calculation (ownership + existence)
   */
  async canUserAccessCalculation(
    calculationId: string,
    userId: string,
  ): Promise<boolean> {
    try {
      await this.checkCalculationOwnership(calculationId, userId);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate calculation status for operations
   */
  async validateCalculationStatus(
    calculationId: string,
    allowedStatuses: string[],
  ): Promise<string> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('calculation_history')
      .select('status')
      .eq('id', calculationId)
      .eq('is_deleted', false)
      .single();

    if (error) {
      this.logger.error(
        `Error fetching calculation status for ${calculationId}: ${error.message}`,
      );
      throw new NotFoundException(
        `Calculation with ID ${calculationId} not found.`,
      );
    }

    if (!allowedStatuses.includes(data.status)) {
      throw new Error(
        `Calculation status '${data.status}' is not allowed for this operation. Allowed statuses: ${allowedStatuses.join(', ')}`,
      );
    }

    return data.status;
  }

  /**
   * Get calculation basic info for validation
   */
  async getCalculationBasicInfo(calculationId: string): Promise<{
    id: string;
    created_by: string;
    status: string;
    is_deleted: boolean;
  }> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('calculation_history')
      .select('id, created_by, status, is_deleted')
      .eq('id', calculationId)
      .single();

    if (error) {
      this.logger.error(
        `Error fetching calculation basic info for ${calculationId}: ${error.message}`,
      );
      throw new NotFoundException(
        `Calculation with ID ${calculationId} not found.`,
      );
    }

    return data;
  }

  /**
   * Validate calculation ownership and status in one call
   */
  async validateCalculationAccess(
    calculationId: string,
    userId: string,
    allowedStatuses?: string[],
  ): Promise<{ status: string; created_by: string }> {
    const basicInfo = await this.getCalculationBasicInfo(calculationId);

    // Check if deleted
    if (basicInfo.is_deleted) {
      throw new NotFoundException(
        `Calculation with ID ${calculationId} not found.`,
      );
    }

    // Check ownership
    if (basicInfo.created_by !== userId) {
      this.logger.warn(
        `User ${userId} attempted to access calculation ${calculationId} owned by ${basicInfo.created_by}`,
      );
      throw new NotFoundException(
        `Calculation with ID ${calculationId} not found or not accessible.`,
      );
    }

    // Check status if provided
    if (allowedStatuses && !allowedStatuses.includes(basicInfo.status)) {
      throw new Error(
        `Calculation status '${basicInfo.status}' is not allowed for this operation. Allowed statuses: ${allowedStatuses.join(', ')}`,
      );
    }

    return {
      status: basicInfo.status,
      created_by: basicInfo.created_by,
    };
  }

  /**
   * Check if calculation has line items
   */
  async hasLineItems(calculationId: string): Promise<boolean> {
    const supabase = this.supabaseService.getClient();

    const { count, error } = await supabase
      .from('calculation_line_items')
      .select('*', { count: 'exact', head: true })
      .eq('calculation_id', calculationId);

    if (error) {
      this.logger.error(
        `Error checking line items for calculation ${calculationId}: ${error.message}`,
      );
      return false;
    }

    return (count ?? 0) > 0;
  }

  /**
   * Check if calculation has custom items
   */
  async hasCustomItems(calculationId: string): Promise<boolean> {
    const supabase = this.supabaseService.getClient();

    const { count, error } = await supabase
      .from('calculation_custom_items')
      .select('*', { count: 'exact', head: true })
      .eq('calculation_id', calculationId);

    if (error) {
      this.logger.error(
        `Error checking custom items for calculation ${calculationId}: ${error.message}`,
      );
      return false;
    }

    return (count ?? 0) > 0;
  }

  /**
   * Validate if calculation can be deleted (business rules)
   */
  async canDeleteCalculation(
    calculationId: string,
    userId: string,
  ): Promise<boolean> {
    try {
      // Check ownership first
      await this.checkCalculationOwnership(calculationId, userId);

      // Get calculation status
      const basicInfo = await this.getCalculationBasicInfo(calculationId);

      // Business rule: Can't delete completed calculations (example)
      if (basicInfo.status === 'completed') {
        this.logger.warn(
          `Attempt to delete completed calculation ${calculationId} by user ${userId}`,
        );
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error validating deletion for calculation ${calculationId}: ${error.message}`,
      );
      return false;
    }
  }
}

