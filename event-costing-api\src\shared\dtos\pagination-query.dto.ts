import { IsOptional, IsString, <PERSON>I<PERSON>, <PERSON>, <PERSON>, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger'; // Optional for Swagger

// Default values for pagination
const DEFAULT_LIMIT = 20;
const MAX_LIMIT = 100;
const DEFAULT_OFFSET = 0;

export class PaginationQueryDto {
  @ApiPropertyOptional({
    description: 'Field to sort by',
    default: 'created_at',
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'created_at';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: MAX_LIMIT,
    default: DEFAULT_LIMIT,
  })
  @IsOptional()
  @Type(() => Number) // Transform query param string to number
  @IsInt()
  @Min(1)
  @Max(MAX_LIMIT) // Prevent excessively large limits
  limit?: number = DEFAULT_LIMIT;

  @ApiPropertyOptional({
    description: 'Number of items to skip',
    minimum: 0,
    default: DEFAULT_OFFSET,
  })
  @IsOptional()
  @Type(() => Number) // Transform query param string to number
  @IsInt()
  @Min(0)
  offset?: number = DEFAULT_OFFSET;
}
