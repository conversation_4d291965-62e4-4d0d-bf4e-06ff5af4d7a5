import { ApiProperty } from '@nestjs/swagger';

export class ProfilePictureResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'A message describing the result of the operation',
    example: 'Profile picture uploaded successfully',
  })
  message: string;

  @ApiProperty({
    description: 'The URL of the uploaded profile picture',
    example:
      'https://example.com/storage/v1/object/public/profiles/profile-pictures/user-id-123456789.jpg',
  })
  profilePictureUrl: string;
}
