import { Controller, Get, UseGuards, Logger } from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import { ApiBearerAuth } from '@nestjs/swagger';

@Controller('users')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class UsersController {
  private readonly logger = new Logger(UsersController.name);

  constructor(private readonly usersService: UsersService) {}

  @Get('me')
  @UseGuards(JwtAuthGuard) // Protect this route
  async getMyProfile(@GetCurrentUser() user: User): Promise<any> {
    this.logger.log(`Request received for user profile: ${user?.email}`);
    // The user object (from Supabase Auth) is extracted by the decorator
    // Pass it to the service to fetch the corresponding profile data
    return this.usersService.findMyProfile(user);
  }
}
