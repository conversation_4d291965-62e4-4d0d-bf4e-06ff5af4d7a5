import React, { useState, useRef } from 'react';
import { toast } from 'sonner';
import { Loader2, Camera } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { uploadProfilePicture } from '@/services/shared/users';
import ProfileImageCropper from '../shared/ProfileImageCropper';
import { optimizeBlob } from '@/lib/imageOptimizer';
import { User } from '@supabase/supabase-js';

interface ProfilePictureSectionProps {
  user: User | null;
  profilePictureUrl: string | null;
  fullName: string;
  onPictureUpdated: (url: string) => void;
}

const ProfilePictureSection: React.FC<ProfilePictureSectionProps> = ({
  user,
  profilePictureUrl,
  fullName,
  onPictureUpdated,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [showCropModal, setShowCropModal] = useState(false);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleImageFileSelected = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || !files.length || !user) return;

    // Get the selected file
    const file = files[0];

    // Reset the file input so the same file can be selected again if needed
    event.target.value = '';

    // Set the selected file and show the crop modal
    setSelectedImageFile(file);
    setShowCropModal(true);
  };

  const handleCropComplete = async (croppedImageBlob: Blob) => {
    if (!user) return;

    setIsUploading(true);

    try {
      // Get file information from the selected image
      const fileName = selectedImageFile?.name || 'profile-picture.jpg';

      // Optimize the cropped image
      const optimizedFile = await optimizeBlob(croppedImageBlob, fileName, {
        maxSizeMB: 0.5, // Limit to 500KB
        maxWidthOrHeight: 500, // Limit dimensions to 500px
        useWebWorker: true,
        initialQuality: 0.8,
        fileType: 'image/jpeg', // Convert to JPEG for better compression
      });

      // Upload the optimized image
      const newProfilePictureUrl = await uploadProfilePicture(optimizedFile);

      // Update parent component
      onPictureUpdated(newProfilePictureUrl);

      toast.success('Profile picture updated successfully');
    } catch (error) {
      console.error('Error processing or uploading image:', error);
      toast.error('Failed to update profile picture');
    } finally {
      setIsUploading(false);
      setSelectedImageFile(null);
    }
  };

  return (
    <Card className='mb-8'>
      <CardHeader>
        <CardTitle>Profile Picture</CardTitle>
        <CardDescription>Update your profile picture</CardDescription>
      </CardHeader>
      <CardContent className='flex flex-col items-center'>
        <div className='relative cursor-pointer group' onClick={handleImageClick}>
          <Avatar className='h-32 w-32 border-2 border-gray-200'>
            <AvatarImage
              src={profilePictureUrl ? `${profilePictureUrl}?t=${Date.now()}` : ''}
              alt={`${fullName}'s profile picture`}
            />
            <AvatarFallback className='text-4xl'>
              {fullName ? fullName[0].toUpperCase() : 'U'}
            </AvatarFallback>
          </Avatar>
          <div className='absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-full flex items-center justify-center transition-all'>
            <Camera className='text-white opacity-0 group-hover:opacity-100' />
          </div>
          {isUploading && (
            <div className='absolute inset-0 bg-black bg-opacity-40 rounded-full flex items-center justify-center'>
              <Loader2 className='h-8 w-8 animate-spin text-white' />
            </div>
          )}
        </div>
        <input
          type='file'
          ref={fileInputRef}
          accept='image/*'
          onChange={handleImageFileSelected}
          className='hidden'
        />

        {/* Image Cropper Modal */}
        <ProfileImageCropper
          open={showCropModal}
          onClose={() => setShowCropModal(false)}
          imageFile={selectedImageFile}
          onCropComplete={handleCropComplete}
        />
        <p className='text-sm text-muted-foreground mt-4'>
          Click on the avatar to upload a new profile picture
        </p>
      </CardContent>
    </Card>
  );
};

export default ProfilePictureSection;
