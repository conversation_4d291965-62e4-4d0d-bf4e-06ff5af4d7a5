{"name": "event-costing-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/**/**.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/swagger": "^11.2.0", "@supabase/supabase-js": "^2.49.4", "@types/cache-manager": "^5.0.0", "@types/passport-jwt": "^4.0.1", "@types/pdfkit": "^0.13.9", "bullmq": "^5.50.0", "cache-manager": "^6.4.3", "cache-manager-redis-store": "^2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "exceljs": "^4.4.0", "ioredis": "^5.6.1", "nest-winston": "^1.10.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdfkit": "^0.17.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "winston": "^3.17.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.1.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/exceljs": "^1.3.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.15.17", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "dotenv-cli": "^8.0.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.32.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}