import React from 'react';
import LoadingSpinner from './loading-spinner';

interface FullPageLoaderProps {
  /**
   * The text to display below the spinner
   * @default "Loading your data..."
   */
  text?: string;
  
  /**
   * The size of the spinner in pixels
   * @default 64
   */
  size?: number;
}

/**
 * A full-page loading component
 */
const FullPageLoader: React.FC<FullPageLoaderProps> = ({
  text = "Loading your data...",
  size = 64
}) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50">
      <LoadingSpinner 
        text={text}
        size={size}
        showText={true}
      />
    </div>
  );
};

export default FullPageLoader;
