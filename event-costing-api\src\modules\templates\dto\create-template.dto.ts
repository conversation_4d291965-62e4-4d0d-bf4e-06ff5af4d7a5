import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON>UID,
  IsISO8601,
  IsArray,
  ValidateNested,
  Min,
  Max,
  Length,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PackageSelectionDto {
  @ApiProperty({
    description: 'Package ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  package_id: string;

  @ApiProperty({
    description: 'Selected option IDs for this package',
    example: ['456e7890-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  option_ids: string[];
}

export class CreateTemplateDto {
  @ApiProperty({
    description: 'Template name',
    example: 'Corporate Event Template',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @Length(2, 100)
  name: string;

  @ApiPropertyOptional({
    description: 'Template description',
    example: 'A comprehensive template for corporate events',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string;

  @ApiPropertyOptional({
    description: 'Event type ID (UUID reference to event_types table)',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  event_type_id?: string;

  @ApiPropertyOptional({
    description: 'Number of attendees',
    example: 100,
    minimum: 1,
    maximum: 100000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100000)
  attendees?: number;

  @ApiPropertyOptional({
    description: 'Template start datetime (ISO 8601 format)',
    example: '2024-06-01T00:00:00.000Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsISO8601()
  template_start_date?: string;

  @ApiPropertyOptional({
    description: 'Template end datetime (ISO 8601 format)',
    example: '2024-06-03T23:59:59.999Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsISO8601()
  template_end_date?: string;

  @ApiProperty({
    description: 'Whether the template is public',
    example: false,
  })
  @IsBoolean()
  is_public: boolean;

  @ApiPropertyOptional({
    description: 'City ID where the template is applicable',
    example: '789e0123-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  city_id?: string;

  @ApiPropertyOptional({
    description: 'Template category ID',
    example: '012e3456-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  category_id?: string;

  @ApiPropertyOptional({
    description: 'Currency ID for the template',
    example: '345e6789-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  currency_id?: string;

  @ApiPropertyOptional({
    description: 'Venue IDs associated with this template',
    example: ['567e8901-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  venue_ids?: string[];

  @ApiPropertyOptional({
    description: 'Package selections for the template',
    type: [PackageSelectionDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageSelectionDto)
  package_selections?: PackageSelectionDto[];
}
