/**
 * Utility functions for debugging
 */

/**
 * Debug log function that only logs in development mode
 * @param message - The message to log
 * @param data - Optional data to log
 */
export const debug = (message: string, data?: any) => {
  if (process.env.NODE_ENV === "development") {
    if (data !== undefined) {
      console.log(`[DEBUG] ${message}`, data);
    } else {
      console.log(`[DEBUG] ${message}`);
    }
  }
};

/**
 * Error log function that logs in all environments
 * @param message - The error message to log
 * @param error - Optional error object to log
 */
export const logError = (message: string, error?: any) => {
  if (error !== undefined) {
    console.error(`[ERROR] ${message}`, error);
  } else {
    console.error(`[ERROR] ${message}`);
  }
};

/**
 * Warning log function that logs in all environments
 * @param message - The warning message to log
 * @param data - Optional data to log
 */
export const logWarning = (message: string, data?: any) => {
  if (data !== undefined) {
    console.warn(`[WARNING] ${message}`, data);
  } else {
    console.warn(`[WARNING] ${message}`);
  }
};
