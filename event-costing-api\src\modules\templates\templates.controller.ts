import {
  <PERSON>,
  Get,
  Query,
  Param,
  Parse<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { ListTemplatesDto } from './dto/list-templates.dto';
import { PaginatedTemplatesResponse } from './dto/template-summary.dto';
import { TemplateDetailDto, EnhancedTemplateDetailDto } from './dto/template-summary.dto';
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiParam,
} from '@nestjs/swagger';

@Controller('templates')
@ApiTags('Templates')
export class TemplatesController {
  private readonly logger = new Logger(TemplatesController.name);

  constructor(private readonly templatesService: TemplatesService) {}

  @Get()
  @ApiOperation({ summary: 'List public templates' })
  @ApiOkResponse({
    description: 'List of public templates with pagination.',
    type: PaginatedTemplatesResponse,
  })
  async findPublicTemplates(
    @Query() queryDto: ListTemplatesDto,
  ): Promise<PaginatedTemplatesResponse> {
    this.logger.log(
      `Public request fetching templates with query: ${JSON.stringify(queryDto)}`,
    );
    return this.templatesService.findPublicTemplates(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific public template by ID' })
  @ApiOkResponse({
    description: 'Public template details.',
    type: TemplateDetailDto,
  })
  @ApiNotFoundResponse({ description: 'Public template not found.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async findOnePublic(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<TemplateDetailDto> {
    this.logger.log(`Public request fetching template details for ID: ${id}`);
    const result = await this.templatesService.findOnePublic(id);

    // Debug log to check if venue_ids are included in the response
    this.logger.log(
      `Public template response for ID ${id}: ${JSON.stringify({
        id: result.id,
        name: result.name,
        venue_ids: result.venue_ids,
        has_venue_ids: !!result.venue_ids,
        venue_ids_length: result.venue_ids?.length || 0,
      })}`,
    );

    return result;
  }

  @Get(':id/enhanced')
  @ApiOperation({ summary: 'Get a specific public template by ID with enhanced package and option names' })
  @ApiOkResponse({
    description: 'Public template details with package and option names.',
    type: EnhancedTemplateDetailDto,
  })
  @ApiNotFoundResponse({ description: 'Public template not found.' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async findOnePublicEnhanced(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<EnhancedTemplateDetailDto> {
    this.logger.log(`Public request fetching enhanced template details for ID: ${id}`);
    const result = await this.templatesService.findOnePublicEnhanced(id);

    // Debug log to check the enhanced response
    this.logger.log(
      `Enhanced public template response for ID ${id}: ${JSON.stringify({
        id: result.id,
        name: result.name,
        package_selections_count: result.package_selections?.length || 0,
        has_package_names: result.package_selections?.some(sel => sel.package_name) || false,
      })}`,
    );

    return result;
  }
}
