/**
 * Hook for fetching consolidated calculation data
 * 
 * This hook implements the consolidated endpoint pattern from the API Architecture Migration Plan.
 * It replaces 4 separate useQuery hooks with a single consolidated data fetch.
 */

import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { 
  getCompleteCalculationData, 
  CalculationCompleteData,
  isConsolidatedEndpointAvailable 
} from '@/services/calculations/calculationCompleteDataService';
import { showError } from '@/lib/notifications';

/**
 * Query key factory for consolidated calculation data
 */
export const consolidatedCalculationQueryKeys = {
  all: ['calculations', 'consolidated'] as const,
  detail: (id: string) => [...consolidatedCalculationQueryKeys.all, 'detail', id] as const,
  availability: () => [...consolidatedCalculationQueryKeys.all, 'availability'] as const,
};

/**
 * Hook options for consolidated calculation data
 */
export interface UseConsolidatedCalculationDataOptions {
  /**
   * Whether to enable the query
   * @default true
   */
  enabled?: boolean;
  
  /**
   * Stale time in milliseconds
   * @default 5 minutes (300000ms)
   */
  staleTime?: number;
  
  /**
   * Cache time in milliseconds
   * @default 10 minutes (600000ms)
   */
  cacheTime?: number;
  
  /**
   * Whether to refetch on window focus
   * @default false
   */
  refetchOnWindowFocus?: boolean;
  
  /**
   * Whether to retry on error
   * @default 1
   */
  retry?: number | boolean;
  
  /**
   * Custom error handler
   */
  onError?: (error: Error) => void;
  
  /**
   * Custom success handler
   */
  onSuccess?: (data: CalculationCompleteData) => void;
}

/**
 * Hook for fetching complete calculation data in a single API call
 * 
 * Benefits:
 * - Reduces API calls from 4 to 1
 * - Improves loading performance
 * - Provides consistent loading states
 * - Includes metadata about the request
 * 
 * @param calculationId - The calculation ID
 * @param options - Query options
 * @returns Query result with complete calculation data
 */
export const useConsolidatedCalculationData = (
  calculationId: string | undefined,
  options: UseConsolidatedCalculationDataOptions = {}
): UseQueryResult<CalculationCompleteData, Error> => {
  const {
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    cacheTime = 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus = false,
    retry = 1,
    onError,
    onSuccess,
  } = options;

  return useQuery({
    queryKey: calculationId ? consolidatedCalculationQueryKeys.detail(calculationId) : [],
    queryFn: () => {
      if (!calculationId) {
        throw new Error('Calculation ID is required');
      }
      return getCompleteCalculationData(calculationId);
    },
    enabled: enabled && !!calculationId,
    staleTime,
    cacheTime,
    refetchOnWindowFocus,
    retry,
    onError: (error: Error) => {
      console.error('[Hook] Error in useConsolidatedCalculationData:', error);
      if (onError) {
        onError(error);
      } else {
        showError('Failed to load calculation data', {
          description: 'There was an error loading the calculation. Please try again.',
        });
      }
    },
    onSuccess: (data: CalculationCompleteData) => {
      console.log('[Hook] Successfully loaded consolidated calculation data:', {
        calculationId,
        loadTime: data.metadata.loadTime,
        lineItems: data.lineItems.length,
        packages: data.packages.length,
        categories: data.categories.length,
        errors: data.metadata.errors?.length || 0,
      });
      
      if (onSuccess) {
        onSuccess(data);
      }
    },
  });
};

/**
 * Hook for checking if the consolidated endpoint is available
 * This can be used for feature detection and fallback logic
 * 
 * @returns Query result indicating endpoint availability
 */
export const useConsolidatedEndpointAvailability = (): UseQueryResult<boolean, Error> => {
  return useQuery({
    queryKey: consolidatedCalculationQueryKeys.availability(),
    queryFn: isConsolidatedEndpointAvailable,
    staleTime: 30 * 60 * 1000, // 30 minutes - endpoint availability doesn't change often
    cacheTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false,
    retry: false, // Don't retry availability checks
    onError: (error: Error) => {
      console.warn('[Hook] Error checking consolidated endpoint availability:', error);
    },
  });
};

/**
 * Derived data selectors for consolidated calculation data
 * These provide convenient access to specific parts of the consolidated data
 */
export const useConsolidatedCalculationSelectors = (data: CalculationCompleteData | undefined) => {
  return {
    /**
     * Get calculation basic info
     */
    calculation: data?.calculation,
    
    /**
     * Get line items
     */
    lineItems: data?.lineItems || [],
    
    /**
     * Get packages organized by category
     */
    packagesByCategory: data?.packages || [],
    
    /**
     * Get all categories
     */
    categories: data?.categories || [],
    
    /**
     * Get metadata about the request
     */
    metadata: data?.metadata,
    
    /**
     * Get financial totals
     */
    totals: data?.calculation ? {
      subtotal: data.calculation.subtotal,
      taxes: data.calculation.taxes,
      discount: data.calculation.discount,
      total: data.calculation.total,
      totalCost: data.calculation.total_cost,
      estimatedProfit: data.calculation.estimated_profit,
    } : null,
    
    /**
     * Get currency information
     */
    currency: data?.calculation?.currency,
    
    /**
     * Get location information
     */
    location: {
      city: data?.calculation?.city,
      venues: data?.calculation?.venues || [],
    },
    
    /**
     * Get related entities
     */
    relatedEntities: {
      client: data?.calculation?.client,
      event: data?.calculation?.event,
    },
    
    /**
     * Get performance metrics
     */
    performance: data?.metadata ? {
      loadTime: data.metadata.loadTime,
      cacheVersion: data.metadata.cacheVersion,
      hasErrors: (data.metadata.errors?.length || 0) > 0,
      errorCount: data.metadata.errors?.length || 0,
      timestamp: data.metadata.timestamp,
    } : null,
  };
};

/**
 * Type guard to check if data is loaded
 */
export const isConsolidatedDataLoaded = (
  data: CalculationCompleteData | undefined
): data is CalculationCompleteData => {
  return !!data && !!data.calculation && !!data.metadata;
};
