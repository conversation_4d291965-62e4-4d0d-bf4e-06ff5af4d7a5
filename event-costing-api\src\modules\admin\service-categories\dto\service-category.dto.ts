import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ServiceCategoryDto {
  @ApiProperty({
    description: 'Unique identifier for the category',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({ description: 'Name of the service category' })
  name: string;

  @ApiPropertyOptional({ description: 'Description of the service category' })
  description?: string;

  @ApiPropertyOptional({
    description: 'ID of the parent category, if applicable',
    format: 'uuid',
    nullable: true,
  })
  parent_category_id?: string | null;

  @ApiProperty({ description: 'Timestamp when the category was created' })
  created_at: Date;

  @ApiProperty({ description: 'Timestamp when the category was last updated' })
  updated_at: Date;
}
