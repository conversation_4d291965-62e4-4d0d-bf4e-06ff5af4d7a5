import { useRef, useState, useCallback, useEffect } from "react";

/**
 * Mutation operation interface
 */
interface MutationOperation {
  id: string;
  type: "add" | "update" | "remove";
  priority: number; // Higher number = higher priority
  operation: () => Promise<any>;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  retryCount?: number;
  maxRetries?: number;
}

/**
 * Mutation queue state
 */
interface MutationQueueState {
  isProcessing: boolean;
  currentOperation: MutationOperation | null;
  queueLength: number;
  completedCount: number;
  failedCount: number;
}

/**
 * Custom hook for managing mutation queue to prevent race conditions
 * Ensures mutations are processed sequentially with proper error handling
 */
export const useMutationQueue = () => {
  const queueRef = useRef<MutationOperation[]>([]);
  const processQueueRef = useRef<(() => Promise<void>) | null>(null);
  const [state, setState] = useState<MutationQueueState>({
    isProcessing: false,
    currentOperation: null,
    queueLength: 0,
    completedCount: 0,
    failedCount: 0,
  });

  // Update state helper
  const updateState = useCallback((updates: Partial<MutationQueueState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  /**
   * Add operation to queue with priority sorting
   */
  const enqueue = useCallback(
    (operation: Omit<MutationOperation, "id">) => {
      const mutationOp: MutationOperation = {
        id: `mutation-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 11)}`,
        retryCount: 0,
        maxRetries: 3,
        ...operation,
      };

      // Add to queue and sort by priority (higher priority first)
      queueRef.current.push(mutationOp);
      queueRef.current.sort((a, b) => b.priority - a.priority);

      updateState({ queueLength: queueRef.current.length });

      console.log(
        `🔄 Enqueued ${operation.type} operation (Priority: ${operation.priority})`
      );
      console.log(`📊 Queue length: ${queueRef.current.length}`);

      // Start processing if not already processing
      if (!state.isProcessing) {
        // Use setTimeout to avoid dependency issues
        setTimeout(() => {
          if (processQueueRef.current) {
            processQueueRef.current();
          }
        }, 0);
      }

      return mutationOp.id;
    },
    [state.isProcessing, updateState]
  );

  /**
   * Process the mutation queue sequentially
   */
  const processQueue = useCallback(async () => {
    if (queueRef.current.length === 0) {
      updateState({
        isProcessing: false,
        currentOperation: null,
        queueLength: 0,
      });
      return;
    }

    updateState({ isProcessing: true });

    while (queueRef.current.length > 0) {
      const operation = queueRef.current.shift()!;

      updateState({
        currentOperation: operation,
        queueLength: queueRef.current.length,
      });

      console.log(
        `⚡ Processing ${operation.type} operation (ID: ${operation.id})`
      );

      try {
        const result = await operation.operation();

        // Success callback
        if (operation.onSuccess) {
          operation.onSuccess(result);
        }

        setState((prev) => ({
          ...prev,
          completedCount: prev.completedCount + 1,
        }));

        console.log(`✅ Completed ${operation.type} operation successfully`);
      } catch (error) {
        console.error(`❌ Error in ${operation.type} operation:`, error);

        // Retry logic
        if (operation.retryCount! < operation.maxRetries!) {
          operation.retryCount!++;

          // Re-add to front of queue for immediate retry
          queueRef.current.unshift(operation);

          console.log(
            `🔄 Retrying ${operation.type} operation (Attempt ${operation.retryCount}/${operation.maxRetries})`
          );

          updateState({ queueLength: queueRef.current.length });
          continue;
        }

        // Max retries reached - call error callback
        if (operation.onError) {
          operation.onError(error);
        }

        setState((prev) => ({
          ...prev,
          failedCount: prev.failedCount + 1,
        }));

        console.log(
          `💥 Failed ${operation.type} operation after ${operation.maxRetries} attempts`
        );
      }

      // Small delay between operations to prevent overwhelming the server
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    updateState({
      isProcessing: false,
      currentOperation: null,
      queueLength: 0,
    });

    console.log(
      `🏁 Queue processing complete. Completed: ${state.completedCount}, Failed: ${state.failedCount}`
    );
  }, [updateState, state.completedCount, state.failedCount]);

  // Assign processQueue to ref for access in enqueue
  processQueueRef.current = processQueue;

  /**
   * Clear the entire queue (emergency stop)
   */
  const clearQueue = useCallback(() => {
    queueRef.current = [];
    updateState({
      isProcessing: false,
      currentOperation: null,
      queueLength: 0,
    });
    console.log("🧹 Mutation queue cleared");
  }, [updateState]);

  /**
   * Remove specific operation from queue
   */
  const removeFromQueue = useCallback(
    (operationId: string) => {
      const initialLength = queueRef.current.length;
      queueRef.current = queueRef.current.filter((op) => op.id !== operationId);

      if (queueRef.current.length !== initialLength) {
        updateState({ queueLength: queueRef.current.length });
        console.log(`🗑️ Removed operation ${operationId} from queue`);
      }
    },
    [updateState]
  );

  /**
   * Get queue status for debugging
   */
  const getQueueStatus = useCallback(() => {
    return {
      ...state,
      queueLength: queueRef.current.length,
      pendingOperations: queueRef.current.map((op) => ({
        id: op.id,
        type: op.type,
        priority: op.priority,
        retryCount: op.retryCount,
      })),
    };
  }, [state]);

  return {
    // Core functions
    enqueue,
    clearQueue,
    removeFromQueue,

    // State
    state,

    // Utilities
    getQueueStatus,

    // Computed values
    isIdle: !state.isProcessing && queueRef.current.length === 0,
    hasOperations: queueRef.current.length > 0,
  };
};
