import { useQuery } from "@tanstack/react-query";
import { showError } from "@/lib/notifications";
import { supabase } from "@/integrations/supabase/client";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { LineItem } from "@/types/calculation";
import {
  getCalculationLineItemsSupabase as getCalculationLineItemsWithCustomItems,
  getLineItemOptionsFromSupabase,
  getLineItemById as getLineItemByIdFromSupabase,
} from "../../../../services/calculations";

/**
 * Custom hook for fetching line items for a calculation
 *
 * @param calculationId - The ID of the calculation
 * @returns Query result with line items data
 */
export function useLineItems(calculationId: string) {
  return useQuery<LineItem[]>({
    queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
    queryFn: async () => {
      const result = await getCalculationLineItemsWithCustomItems(
        calculationId
      );

      // Only log in development
      if (process.env.NODE_ENV === "development") {
        console.log(
          `[useLineItems] Fetched ${result.length} line items for calculation ${calculationId}`
        );
        const customItems = result.filter((item) => item.is_custom);
        const packageItems = result.filter((item) => !item.is_custom);
        console.log(
          `[useLineItems] Breakdown - Custom: ${customItems.length}, Package: ${packageItems.length}`
        );
      }

      return result;
    },
    enabled: !!calculationId,
    // Cache line items for 2 minutes - they change more frequently than calculations
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    meta: {
      onError: (error: Error) => {
        console.error("Error fetching line items:", error);
        showError("Failed to load line items");
      },
    },
  });
}

/**
 * Custom hook for fetching options for a specific line item
 *
 * @param calculationId - The ID of the calculation (kept for query key consistency)
 * @param lineItemId - The ID of the line item
 * @returns Query result with line item options
 */
export function useLineItemOptions(calculationId: string, lineItemId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.calculations.lineItemOptions(
      calculationId,
      lineItemId
    ),
    queryFn: () => getLineItemOptionsFromSupabase(lineItemId),
    enabled: !!calculationId && !!lineItemId,
    meta: {
      onError: (error: Error) => {
        console.error(
          `Error fetching options for line item ${lineItemId}:`,
          error
        );
        showError("Failed to load line item options");
      },
    },
  });
}

/**
 * Custom hook for fetching a single line item by ID
 *
 * @param calculationId - The ID of the calculation
 * @param lineItemId - The ID of the line item
 * @returns Query result with line item data
 */
export function useLineItem(calculationId: string, lineItemId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.calculations.lineItem(calculationId, lineItemId),
    queryFn: () => getLineItemByIdFromSupabase(calculationId, lineItemId),
    enabled: !!calculationId && !!lineItemId,
    meta: {
      onError: (error: Error) => {
        console.error(`Error fetching line item ${lineItemId}:`, error);
        showError("Failed to load line item details");
      },
    },
  });
}
