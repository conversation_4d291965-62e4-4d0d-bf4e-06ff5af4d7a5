import { ApiProperty } from '@nestjs/swagger';

export class InitiateExportResponseDto {
  @ApiProperty({
    type: String,
    description: 'A message indicating the result of the export initiation.',
    example: 'CSV export initiated and history recorded.',
  })
  message: string;

  @ApiProperty({
    type: String,
    format: 'uuid',
    description: 'The ID of the created record in the export_history table.',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    required: false,
  })
  exportHistoryId?: string;
}
