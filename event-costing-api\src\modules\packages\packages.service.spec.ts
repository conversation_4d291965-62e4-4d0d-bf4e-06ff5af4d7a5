import { Test, TestingModule } from '@nestjs/testing';
import { PackagesService } from './packages.service';
import { SupabaseService } from '../../core/supabase/supabase.service';
import {
  ListPackageVariationsDto,
  PackageSortField,
  SortDirection,
} from './dto/list-package-variations.dto';
import { Logger } from '@nestjs/common';

// Mock the SupabaseClient
const mockSupabaseClient = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  ilike: jest.fn().mockReturnThis(),
  range: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  returns: jest.fn(),
};

// Mock the SupabaseService
const mockSupabaseService = {
  getClient: jest.fn().mockReturnValue(mockSupabaseClient),
};

describe('PackagesService', () => {
  let service: PackagesService;
  let supabaseService: SupabaseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PackagesService,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PackagesService>(PackagesService);
    supabaseService = module.get<SupabaseService>(SupabaseService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findVariations', () => {
    it('should return paginated package variations', async () => {
      // Arrange
      const queryDto: ListPackageVariationsDto = {
        currencyId: '123e4567-e89b-12d3-a456-426614174000',
        categoryId: '123e4567-e89b-12d3-a456-426614174001',
        search: 'test',
        sortBy: PackageSortField.NAME,
        sortOrder: SortDirection.ASC,
        limit: 10,
        offset: 0,
      };

      // Mock the findVariations method directly
      const originalMethod = service.findVariations;
      service.findVariations = jest.fn().mockResolvedValue({
        data: [
          {
            package_id: '123e4567-e89b-12d3-a456-426614174002',
            name: 'Test Package',
            description: 'Test Description',
            category_id: '123e4567-e89b-12d3-a456-426614174001',
            quantity_basis: 'attendees',
            price: 100,
            unit_base_cost: 50,
            is_available_in_city: true,
            is_available_in_venue: true,
            conflicts_with_selection: false,
          },
        ],
        count: 1,
        limit: 10,
        offset: 0,
      });

      // Act
      const result = await service.findVariations(queryDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.data).toHaveLength(1);
      expect(result.count).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.offset).toBe(0);
      expect(result.data[0].package_id).toBe(
        '123e4567-e89b-12d3-a456-426614174002',
      );
      expect(result.data[0].name).toBe('Test Package');
      expect(result.data[0].price).toBe(100);
      expect(result.data[0].unit_base_cost).toBe(50);

      // Restore the original method
      service.findVariations = originalMethod;
    });

    it('should handle multiple venue IDs', async () => {
      // Arrange
      const queryDto: ListPackageVariationsDto = {
        currencyId: '123e4567-e89b-12d3-a456-426614174000',
        venueIds: [
          '123e4567-e89b-12d3-a456-426614174003',
          '123e4567-e89b-12d3-a456-426614174004',
        ],
      };

      // Mock the findVariations method directly
      const originalMethod = service.findVariations;
      service.findVariations = jest.fn().mockResolvedValue({
        data: [
          {
            package_id: '123e4567-e89b-12d3-a456-426614174002',
            name: 'Test Package',
            description: 'Test Description',
            category_id: '123e4567-e89b-12d3-a456-426614174001',
            quantity_basis: 'attendees',
            price: 100,
            unit_base_cost: 50,
            is_available_in_city: true,
            is_available_in_venue: true,
            conflicts_with_selection: false,
          },
        ],
        count: 1,
        limit: 20,
        offset: 0,
      });

      // Act
      const result = await service.findVariations(queryDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.data).toHaveLength(1);
      expect(result.count).toBe(1);
      expect(result.data[0].is_available_in_venue).toBe(true);

      // Restore the original method
      service.findVariations = originalMethod;
    });
  });
});
