import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/useAuth';
import LoadingSpinner from '@/components/ui/loading-spinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * ProtectedRoute Component
 *
 * A simplified protected route that only checks if the user is authenticated.
 * If not authenticated, redirects to the login page.
 * If authenticated, renders the children.
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading, isRoleLoading, triggerRefreshSession } = useAuth();

  // Add debugging logs to track component rendering and auth state
  console.log(`[${new Date().toISOString()}] ProtectedRoute rendering with:`, {
    has_user: !!user,
    user_id: user?.id,
    user_email: user?.email,
    loading,
    isRoleLoading,
  });

  // Show loading state while checking authentication
  if (loading) {
    console.log(
      `[${new Date().toISOString()}] ProtectedRoute: Showing loading spinner (loading=true)`,
    );
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <LoadingSpinner text='Verifying authentication...' size={48} />
      </div>
    );
  }

  // If we're just checking the role but the user is authenticated, show the children
  // This prevents unnecessary loading screens when only the role is being checked
  if (isRoleLoading && user) {
    console.log(
      `[${new Date().toISOString()}] ProtectedRoute: Rendering children while checking role (isRoleLoading=true, user exists)`,
    );
    // We can render the children while the role is being checked in the background
    return <>{children}</>;
  }

  // Redirect to auth page if user is not authenticated
  if (!user) {
    console.log(
      `[${new Date().toISOString()}] ProtectedRoute: Redirecting to /auth (no user)`,
    );
    return <Navigate to='/auth' replace />;
  }

  // Render children if user is authenticated
  console.log(
    `[${new Date().toISOString()}] ProtectedRoute: Rendering children (user authenticated)`,
  );
  return <>{children}</>;
};

export default ProtectedRoute;
