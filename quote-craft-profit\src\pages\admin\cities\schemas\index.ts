import { z } from 'zod';
import { formFieldPatterns } from '@/schemas/common';

/**
 * City management validation schemas
 * Centralized schemas for all city forms and components
 */

// City form schema (extracted from CityFormDialog.tsx)
export const cityFormSchema = z.object({
  name: formFieldPatterns.name('City name'),
});

// Type exports for TypeScript inference
export type CityFormValues = z.infer<typeof cityFormSchema>;
