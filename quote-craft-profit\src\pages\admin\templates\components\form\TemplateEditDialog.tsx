import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, MapPin } from "lucide-react";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import {
  getTemplateDetailsById,
  updateTemplate,
} from "@/services/admin/templates";
import { getVenueById } from "@/services/shared/entities/venues";
import {
  templateFormSchema,
  transformTemplateFormDataToApiRequest,
  transformTemplateToFormData,
} from "../../types";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DateRange } from "react-day-picker";
import { EventTypeSelector } from "@/components/ui/event-type-selector";

interface TemplateEditDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  templateId: string | null;
}

type FormValues = z.infer<typeof templateFormSchema>;

const TemplateEditDialog: React.FC<TemplateEditDialogProps> = ({
  isOpen,
  onClose,
  templateId,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [venueNames, setVenueNames] = useState<Record<string, string>>({});

  // Fetch template details
  const {
    data: template,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["templateDetails", templateId],
    queryFn: () => getTemplateDetailsById(templateId!),
    enabled: isOpen && !!templateId,
    meta: {
      onError: (error: Error) => {
        toast.error(`Failed to load template details: ${error.message}`);
      },
    },
  });

  // Fetch venue names when template is loaded
  useEffect(() => {
    if (template?.venue_ids && template.venue_ids.length > 0) {
      const fetchVenueNames = async () => {
        const venueNamesMap: Record<string, string> = {};

        for (const venueId of template.venue_ids) {
          try {
            const venue = await getVenueById(venueId);
            venueNamesMap[venueId] = venue.name;
          } catch (error) {
            console.error(`Error fetching venue ${venueId}:`, error);
            venueNamesMap[venueId] = "Unknown Venue";
          }
        }

        setVenueNames(venueNamesMap);
      };

      fetchVenueNames();
    }
  }, [template?.venue_ids]);

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      name: "",
      description: null,
      event_type_id: null, // Updated to use event_type_id
      attendees: undefined,
      dateRange: undefined,
      is_public: false,
    },
  });

  // Update form values when template data is loaded
  useEffect(() => {
    if (template) {
      const formData = transformTemplateToFormData(template);
      form.reset(formData);
    }
  }, [template, form]);

  const onSubmit = async (values: FormValues) => {
    if (!templateId) return;

    // Validate date range
    if (
      values.dateRange?.from &&
      values.dateRange?.to &&
      values.dateRange.from > values.dateRange.to
    ) {
      toast.error("Start date cannot be after end date");
      return;
    }

    setIsSubmitting(true);
    try {
      const templateData = transformTemplateFormDataToApiRequest(values);
      await updateTemplate(templateId, templateData);
      toast.success("Template updated successfully");
      onClose(true); // Close and refresh
    } catch (error) {
      console.error("Error updating template:", error);
      toast.error(`Failed to update template: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Template</DialogTitle>
          <DialogDescription>
            Update the details of this template.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading template details...</span>
          </div>
        ) : isError ? (
          <div className="text-center p-4 text-destructive">
            <p>Error loading template details.</p>
            <p className="text-sm">
              {(error as Error)?.message || "Unknown error"}
            </p>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Template Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter template name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter template description"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="event_type_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Event Type (Optional)</FormLabel>
                    <FormControl>
                      <EventTypeSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select event type"
                        allowEmpty={true}
                        emptyLabel="None"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="attendees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Attendees</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="Enter number of attendees"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value) || undefined)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Event Date Range</FormLabel>
                    <FormControl>
                      <DateRangePicker
                        value={field.value as DateRange}
                        onChange={field.onChange}
                        placeholder="Select start and end dates"
                        numberOfMonths={2}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_public"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Public Template</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Make this template visible to all users
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              {/* Venues (Read-only) */}
              {template?.venue_ids && template.venue_ids.length > 0 && (
                <Card className="border-dashed">
                  <CardHeader className="py-3">
                    <CardTitle className="text-sm font-medium">
                      Associated Venues
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="py-2">
                    <div className="space-y-2">
                      {template.venue_ids.map((venueId) => (
                        <div key={venueId} className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span className="text-sm">
                            {venueNames[venueId] || "Loading venue..."}
                          </span>
                        </div>
                      ))}
                    </div>
                    <p className="text-xs text-muted-foreground mt-3">
                      Venues are associated with this template but cannot be
                      edited directly.
                    </p>
                  </CardContent>
                </Card>
              )}

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onClose()}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting || isLoading}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default TemplateEditDialog;
