/**
 * Image optimization utilities
 * 
 * This file contains utility functions for optimizing images before upload
 */

import imageCompression from 'browser-image-compression';

/**
 * Options for image optimization
 */
export interface ImageOptimizationOptions {
  maxSizeMB?: number;
  maxWidthOrHeight?: number;
  useWebWorker?: boolean;
  maxIteration?: number;
  exifOrientation?: number;
  fileType?: string;
  initialQuality?: number;
}

/**
 * Default options for profile picture optimization
 */
export const defaultProfilePictureOptions: ImageOptimizationOptions = {
  maxSizeMB: 1, // Max file size in MB
  maxWidthOrHeight: 800, // Max width or height in pixels
  useWebWorker: true, // Use web worker for better performance
  initialQuality: 0.8, // Initial quality (0 to 1)
  fileType: 'image/jpeg', // Output file type
};

/**
 * Optimize an image file
 * 
 * @param imageFile - The image file to optimize
 * @param options - Optimization options
 * @returns A promise that resolves to the optimized image file
 */
export async function optimizeImage(
  imageFile: File,
  options: ImageOptimizationOptions = defaultProfilePictureOptions
): Promise<File> {
  try {
    console.log('Original image file:', {
      name: imageFile.name,
      type: imageFile.type,
      size: `${(imageFile.size / 1024).toFixed(2)} KB`,
    });

    // Compress the image
    const compressedFile = await imageCompression(imageFile, options);

    console.log('Compressed image file:', {
      name: compressedFile.name,
      type: compressedFile.type,
      size: `${(compressedFile.size / 1024).toFixed(2)} KB`,
      compressionRatio: `${(imageFile.size / compressedFile.size).toFixed(2)}x`,
    });

    return compressedFile;
  } catch (error) {
    console.error('Error optimizing image:', error);
    // Return the original file if optimization fails
    return imageFile;
  }
}

/**
 * Optimize a blob (e.g., from canvas)
 * 
 * @param blob - The image blob to optimize
 * @param fileName - The name to give the optimized file
 * @param options - Optimization options
 * @returns A promise that resolves to the optimized image file
 */
export async function optimizeBlob(
  blob: Blob,
  fileName: string,
  options: ImageOptimizationOptions = defaultProfilePictureOptions
): Promise<File> {
  try {
    // Convert blob to file
    const imageFile = new File([blob], fileName, { type: blob.type });
    
    // Optimize the file
    return await optimizeImage(imageFile, options);
  } catch (error) {
    console.error('Error optimizing blob:', error);
    // Return a file from the original blob if optimization fails
    return new File([blob], fileName, { type: blob.type });
  }
}
