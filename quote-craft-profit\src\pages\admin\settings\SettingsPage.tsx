import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TableColumnSettings, CategoryOrderSettings } from './components';
import { useAuth } from '@/contexts/useAuth';
import { Navigate } from 'react-router-dom';

const SettingsPage: React.FC = () => {
  const { user, isAdmin } = useAuth();

  // Redirect non-admin users
  if (!isAdmin) {
    return <Navigate to='/' replace />;
  }

  console.log('Rendering admin settings page for user:', user);

  return (
    <AdminLayout title='Application Settings'>
      <Tabs defaultValue='general' className='w-full'>
        <TabsList className='mb-4 grid grid-cols-2 md:grid-cols-3 lg:flex'>
          <TabsTrigger value='general'>General</TabsTrigger>
          <TabsTrigger value='display'>Display</TabsTrigger>
          <TabsTrigger value='categories'>Categories</TabsTrigger>
          <TabsTrigger value='users'>User Settings</TabsTrigger>
          <TabsTrigger value='notifications'>Notifications</TabsTrigger>
          <TabsTrigger value='security'>Security</TabsTrigger>
        </TabsList>

        <TabsContent value='general'>
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                General application settings will be implemented here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='display'>
          <div className='space-y-6'>
            <TableColumnSettings />
          </div>
        </TabsContent>

        <TabsContent value='categories'>
          <Card>
            <CardHeader>
              <CardTitle>Category Management</CardTitle>
              <CardDescription>
                Manage category display order and settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CategoryOrderSettings />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='users'>
          <Card>
            <CardHeader>
              <CardTitle>User Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                User-related application settings will be implemented here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='notifications'>
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                Notification configuration will be implemented here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='security'>
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                Security settings and policies will be implemented here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default SettingsPage;
