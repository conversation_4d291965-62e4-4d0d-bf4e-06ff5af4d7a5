import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString, Min<PERSON>ength, IsN<PERSON>ber } from 'class-validator';

/**
 * DTO for updating a user
 */
export class UpdateUserDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'User password',
    example: 'password123',
    required: false,
  })
  @IsString()
  @MinLength(6)
  @IsOptional()
  password?: string;

  @ApiProperty({
    description: 'Full name',
    example: '<PERSON>',
    required: false,
  })
  @IsString()
  @IsOptional()
  full_name?: string;

  @ApiProperty({
    description: 'Username',
    example: 'johndoe',
    required: false,
  })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({
    description: 'Role ID',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  role_id?: number;
}
