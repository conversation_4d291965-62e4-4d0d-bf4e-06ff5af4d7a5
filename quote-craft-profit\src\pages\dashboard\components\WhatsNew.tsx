import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  X,
  ExternalLink,
  Star,
  Zap,
  Sparkles,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { useOnboarding } from "@/contexts/OnboardingContext";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

const WhatsNew: React.FC = () => {
  const { showWhatIsNew, dismissWhatIsNew } = useOnboarding();
  const [isOpen, setIsOpen] = useState(false);

  if (!showWhatIsNew) {
    return null;
  }

  const newFeatures = [
    {
      title: "Template Management",
      description:
        "Create and manage templates from your calculations for faster setup",
      icon: <Star className="h-5 w-5 text-yellow-500" />,
      date: "May 10, 2025",
    },
    {
      title: "Package Dependencies",
      description:
        "Define dependencies between packages for more accurate calculations",
      icon: <Zap className="h-5 w-5 text-purple-500" />,
      date: "May 5, 2025",
    },
    {
      title: "Streamlined Dashboard",
      description:
        "New dashboard layout with quick actions and improved navigation",
      icon: <Sparkles className="h-5 w-5 text-blue-500" />,
      date: "May 1, 2025",
    },
  ];

  return (
    <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-100 dark:border-purple-800 mb-6 relative overflow-hidden">
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-2 right-2 h-6 w-6 z-10"
        onClick={dismissWhatIsNew}
      >
        <X className="h-4 w-4" />
      </Button>

      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardContent className="p-6 pb-2 cursor-pointer hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-2 rounded-full mr-3">
                  <Sparkles className="h-5 w-5" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-purple-800 dark:text-purple-300">
                    What's New
                  </h2>
                  <p className="text-sm text-purple-600 dark:text-purple-400">
                    Recent updates and new features
                  </p>
                </div>
              </div>

              <div className="ml-2">
                {isOpen ? (
                  <ChevronUp className="h-4 w-4 text-purple-500" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-purple-500" />
                )}
              </div>
            </div>
          </CardContent>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0 p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {newFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm"
                >
                  <div className="flex items-start">
                    <div className="mt-1 mr-3">{feature.icon}</div>
                    <div>
                      <h3 className="font-medium text-gray-800 dark:text-white">
                        {feature.title}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {feature.description}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
                        {feature.date}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 text-center">
              <Button
                variant="link"
                size="sm"
                className="text-purple-700 dark:text-purple-400"
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                View all updates
              </Button>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default WhatsNew;
