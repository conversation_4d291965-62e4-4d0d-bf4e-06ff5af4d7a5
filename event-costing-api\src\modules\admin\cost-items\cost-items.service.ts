import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreateCostItemDto } from './dto/create-cost-item.dto.js';
import { UpdateCostItemDto } from './dto/update-cost-item.dto.js';
import { CostItemDto } from './dto/cost-item.dto.js';

@Injectable()
export class CostItemsService {
  private readonly logger = new Logger(CostItemsService.name);
  private readonly TABLE_NAME = 'cost_items';

  constructor(private readonly supabaseService: SupabaseService) {}

  async create(createDto: CreateCostItemDto): Promise<CostItemDto> {
    this.logger.log(
      `Attempting to create cost item: ${JSON.stringify(createDto)}`,
    );
    // TODO: Implement creation logic
    // 1. Validate related entities if necessary (e.g., currency_id)
    // 2. Insert into Supabase
    // 3. Handle potential errors (unique constraints, foreign keys)
    // 4. Return the created DTO

    // Placeholder implementation
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .insert({
        ...createDto,
        // Set defaults if needed, e.g., is_active: true
      })
      .select('*')
      .single<CostItemDto>();

    if (error) {
      this.logger.error(
        `Error creating cost item: ${error.message}`,
        error.stack,
      );
      // Add specific error handling (e.g., for unique code '23505')
      if (error.code === '23505') {
        throw new ConflictException(
          `Cost item with code '${createDto.item_code}' may already exist.`,
        );
      }
      throw new InternalServerErrorException(
        `Failed to create cost item: ${error.message}`,
      );
    }

    if (!data) {
      throw new InternalServerErrorException(
        'Cost item creation failed unexpectedly (no data returned).',
      );
    }

    this.logger.log(`Cost item created successfully with ID: ${data.id}`);
    return data;
  }

  async findAll(/* Add query params DTO if needed */): Promise<CostItemDto[]> {
    this.logger.log('Fetching all cost items');
    // TODO: Implement find all logic (potentially with pagination/filtering)
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .eq('is_deleted', false) // Assuming soft delete
      .order('name', { ascending: true });

    if (error) {
      this.logger.error(
        `Error fetching cost items: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to fetch cost items: ${error.message}`,
      );
    }

    return (data ?? []) as CostItemDto[];
  }

  async findOne(id: string): Promise<CostItemDto> {
    this.logger.log(`Fetching cost item with ID: ${id}`);
    // TODO: Implement find one logic
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select('*')
      .match({ id: id, is_deleted: false })
      .single<CostItemDto>();

    if (error) {
      this.logger.error(
        `Error fetching cost item ${id}: ${error.message}`,
        error.stack,
      );
      // Handle case where item is not found due to error or it simply doesn't exist
      if (error.code === 'PGRST116') {
        // PostgREST error code for 'Not Found'
        throw new NotFoundException(`Cost item with ID ${id} not found.`);
      }
      throw new InternalServerErrorException(
        `Failed to fetch cost item ${id}: ${error.message}`,
      );
    }

    if (!data) {
      throw new NotFoundException(`Cost item with ID ${id} not found.`);
    }

    return data;
  }

  async update(id: string, updateDto: UpdateCostItemDto): Promise<CostItemDto> {
    this.logger.log(
      `Attempting to update cost item ${id}: ${JSON.stringify(updateDto)}`,
    );
    // TODO: Implement update logic
    // 1. Check if item exists
    // 2. Perform update in Supabase
    // 3. Handle potential errors

    // Placeholder implementation
    const supabase = this.supabaseService.getClient();

    // Check if there's anything to update
    if (Object.keys(updateDto).length === 0) {
      this.logger.warn(
        `Update called for cost item ${id} with no data. Fetching current.`,
      );
      return this.findOne(id);
    }

    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .update(updateDto)
      .match({ id: id, is_deleted: false })
      .select('*')
      .single<CostItemDto>();

    if (error) {
      this.logger.error(
        `Error updating cost item ${id}: ${error.message}`,
        error.stack,
      );
      if (error.code === 'PGRST116') {
        // Not found during update match
        throw new NotFoundException(`Cost item with ID ${id} not found.`);
      }
      if (error.code === '23505') {
        throw new ConflictException(
          `Update failed due to unique constraint (e.g., item code '${updateDto.item_code}' already exists).`,
        );
      }
      throw new InternalServerErrorException(
        `Failed to update cost item ${id}: ${error.message}`,
      );
    }

    if (!data) {
      // This case might indicate the item was deleted between the findOne check and the update attempt,
      // or if the match clause somehow failed unexpectedly despite the findOne success.
      throw new NotFoundException(
        `Cost item with ID ${id} could not be updated (not found or unexpected state).`,
      );
    }

    this.logger.log(`Cost item ${id} updated successfully.`);
    return data;
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Attempting to soft-delete cost item ${id}`);
    // TODO: Implement remove logic (soft delete)
    const supabase = this.supabaseService.getClient();
    const { error, count } = await supabase
      .from(this.TABLE_NAME)
      .update({ is_deleted: true, deleted_at: new Date().toISOString() })
      .match({ id: id, is_deleted: false });

    if (error) {
      this.logger.error(
        `Error soft-deleting cost item ${id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to delete cost item ${id}: ${error.message}`,
      );
    }

    if (count === 0) {
      throw new NotFoundException(
        `Cost item with ID ${id} not found or already deleted.`,
      );
    }

    this.logger.log(`Cost item ${id} soft-deleted successfully.`);
  }

  // --- Helper Methods (if needed) --- //
  // Example: private async checkSomethingExists(relatedId: string) { ... }
}
