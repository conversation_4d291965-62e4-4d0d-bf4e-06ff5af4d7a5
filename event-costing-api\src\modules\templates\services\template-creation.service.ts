import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { User } from '@supabase/supabase-js';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreateTemplateFromCalculationDto } from '../dto/create-template-from-calculation.dto';
import { CreateTemplateDto } from '../dto/create-template.dto';
import { TemplateSummaryDto } from '../dto/template-summary.dto';
import { TemplateVenueService } from './template-venue.service';
import { TemplateConstants } from '../constants/template.constants';

// Type for calculation line items fetched
interface CalculationLineItem {
  id: string;
  package_id: string | null;
  item_quantity: number;
  item_quantity_basis: number;
}

// Type for calculation line item options fetched
interface CalculationLineItemOption {
  line_item_id: string;
  option_id: string;
}

// Define the structure for items in the package_selections JSON
interface PackageSelectionItem {
  package_id: string;
  option_ids: string[];
  item_quantity: number | null;
  item_quantity_basis: number | null;
}

@Injectable()
export class TemplateCreationService {
  private readonly logger = new Logger(TemplateCreationService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly templateVenueService: TemplateVenueService,
  ) {}

  /**
   * Create a template from a calculation
   */
  async createTemplateFromCalculation(
    createDto: CreateTemplateFromCalculationDto,
    user: User,
  ): Promise<TemplateSummaryDto> {
    this.logger.log(
      `Creating template '${createDto.name}' from calculation ${createDto.calculationId} for user ${user.id}`,
    );
    const supabase = this.supabaseService.getClient();

    // 1. First, fetch the calculation data to get default values including taxes and discounts
    const { data: calculationData, error: calculationError } = await supabase
      .from('calculation_history')
      .select(
        'attendees, event_type_id, city_id, currency_id, event_start_date, event_end_date, taxes, discount',
      )
      .eq('id', createDto.calculationId)
      .single();

    if (calculationError) {
      this.logger.error(
        `Error fetching calculation ${createDto.calculationId}: ${calculationError.message}`,
        calculationError.stack,
      );
      throw new InternalServerErrorException(
        'Failed to fetch calculation details.',
      );
    }

    if (!calculationData) {
      throw new NotFoundException(
        `Calculation with ID ${createDto.calculationId} not found.`,
      );
    }

    this.logger.log(
      `Fetched calculation data: attendees=${calculationData.attendees}, event_type_id=${calculationData.event_type_id}, city_id=${calculationData.city_id}, currency_id=${calculationData.currency_id}`,
    );

    // 2. Fetch calculation line items
    const { data: lineItemsData, error: lineItemsError } = await supabase
      .from('calculation_line_items')
      .select('id, package_id, item_quantity, item_quantity_basis')
      .eq('calculation_id', createDto.calculationId);

    if (lineItemsError) {
      throw new InternalServerErrorException('Failed to fetch line items.');
    }
    const lineItems: CalculationLineItem[] = lineItemsData || [];

    // 2.5. Fetch custom items
    const { data: customItemsData, error: customItemsError } = await supabase
      .from('calculation_custom_items')
      .select(
        'item_name, description, item_quantity, unit_price, unit_cost, currency_id, category_id, city_id, item_quantity_basis, quantity_basis',
      )
      .eq('calculation_id', createDto.calculationId);

    if (customItemsError) {
      throw new InternalServerErrorException('Failed to fetch custom items.');
    }
    const customItems = customItemsData || [];

    if (lineItems.length === 0 && customItems.length === 0) {
      throw new NotFoundException(
        'Calculation not found or has no items (standard or custom).',
      );
    }

    // 3. Fetch options
    const lineItemIds = lineItems.map(item => item.id);
    const { data: lineItemOptionsData, error: optionsError } = await supabase
      .from('calculation_line_item_options')
      .select('line_item_id, option_id')
      .in('line_item_id', lineItemIds);

    if (optionsError) {
      throw new InternalServerErrorException('Failed to fetch item options.');
    }
    const lineItemOptions: CalculationLineItemOption[] =
      lineItemOptionsData || [];

    // 4. Get venue IDs - either from the DTO or fetch from calculation
    let venueIds: string[] = [];

    if (createDto.venueIds && createDto.venueIds.length > 0) {
      // Use venue IDs from the DTO if provided
      venueIds = createDto.venueIds;
      this.logger.log(
        `Using ${venueIds.length} venue IDs provided in the request`,
      );
    } else {
      // Otherwise fetch from calculation
      const { data: venueData, error: venueError } = await supabase
        .from('calculation_venues')
        .select('venue_id')
        .eq('calculation_id', createDto.calculationId);

      if (venueError) {
        this.logger.error(
          `Error fetching venues for calculation ${createDto.calculationId}: ${venueError.message}`,
          venueError.stack,
        );
        // Continue without venues if there's an error
      }

      // Extract venue IDs
      venueIds = venueData?.map(v => v.venue_id) || [];
      this.logger.log(
        `Fetched ${venueIds.length} venue IDs from calculation ${createDto.calculationId}`,
      );
    }

    // Map options
    const optionsMap = new Map<string, string[]>();
    lineItemOptions.forEach(opt => {
      const existing = optionsMap.get(opt.line_item_id) || [];
      existing.push(opt.option_id);
      optionsMap.set(opt.line_item_id, existing);
    });

    // 4. Build blueprint
    const packageSelections: PackageSelectionItem[] = lineItems
      .filter(item => item.package_id)
      .map(item => ({
        package_id: item.package_id as string,
        option_ids: optionsMap.get(item.id) || [],
        item_quantity: item.item_quantity,
        item_quantity_basis: item.item_quantity_basis,
      }));

    // 5. Prepare template data using calculation defaults with DTO overrides
    const templateData = {
      name: createDto.name,
      description: createDto.description,
      package_selections: packageSelections as unknown,
      custom_items: customItems as unknown, // Include custom items
      created_by: user.id,
      is_public: false,
      // Use DTO values if provided, otherwise fall back to calculation defaults
      event_type_id:
        createDto.eventTypeId || calculationData.event_type_id || null, // Updated to use event_type_id
      city_id: createDto.cityId || calculationData.city_id,
      currency_id: createDto.currencyId || calculationData.currency_id,
      attendees: createDto.attendees ?? calculationData.attendees, // Use ?? to allow 0 values
      template_start_date:
        createDto.templateStartDate || calculationData.event_start_date || null,
      template_end_date:
        createDto.templateEndDate || calculationData.event_end_date || null,
      // Include taxes and discounts from the source calculation
      taxes: calculationData.taxes || null,
      discount: calculationData.discount || null,
      // category_id remains omitted - requires separate logic
    };

    // Log the final template data for debugging
    this.logger.log(
      `Template data with calculation defaults: ${JSON.stringify({
        ...templateData,
        package_selections: `[${packageSelections.length} items]`,
        custom_items: `[${customItems.length} items]`,
        attendees_source:
          createDto.attendees !== undefined ? 'DTO' : 'calculation',
        city_source: createDto.cityId ? 'DTO' : 'calculation',
        currency_source: createDto.currencyId ? 'DTO' : 'calculation',
      })}`,
    );

    // 6. Insert template
    const { data: insertedData, error: insertError } = await supabase
      .from(TemplateConstants.TABLE_NAME)
      .insert(templateData)
      .select(TemplateConstants.SUMMARY_SELECT_FIELDS)
      .single<
        Omit<TemplateSummaryDto, 'total_count'> & {
          created_at: string;
          updated_at: string;
          template_start_date?: string | null;
          template_end_date?: string | null;
        }
      >(); // More precise type

    if (insertError) {
      this.logger.error(
        `Error inserting template: ${insertError.message}`,
        insertError.stack,
      );
      throw new InternalServerErrorException('Could not create template.');
    }
    if (!insertedData) {
      throw new InternalServerErrorException(
        'Failed to retrieve created template.',
      );
    }

    // 7. Insert venue associations if any venues were found
    if (venueIds.length > 0) {
      await this.templateVenueService.createTemplateVenueAssociations(
        insertedData.id,
        venueIds,
      );
    }

    this.logger.log(`Successfully created template ${insertedData.id}`);

    // Map inserted data (which has string dates) to DTO
    const resultDto: TemplateSummaryDto = {
      id: insertedData.id,
      name: insertedData.name,
      description: insertedData.description ?? undefined,
      event_type_id: insertedData.event_type_id ?? undefined,
      city_id: insertedData.city_id ?? undefined,
      currency_id: insertedData.currency_id ?? undefined,
      attendees: insertedData.attendees ?? undefined,
      template_start_date: insertedData.template_start_date
        ? new Date(insertedData.template_start_date)
        : undefined,
      template_end_date: insertedData.template_end_date
        ? new Date(insertedData.template_end_date)
        : undefined,
      category_id: insertedData.category_id ?? undefined,
      created_at: new Date(insertedData.created_at),
      updated_at: new Date(insertedData.updated_at),
      created_by: insertedData.created_by,
      is_public: insertedData.is_public,
      is_deleted: insertedData.is_deleted || false, // Ensure is_deleted is always included
      taxes: (insertedData as any).taxes || undefined,
      discount: (insertedData as any).discount || undefined,
    };
    return resultDto;
  }

  /**
   * Create a basic template
   */
  async createTemplate(
    createDto: CreateTemplateDto,
    user: User,
  ): Promise<TemplateSummaryDto> {
    this.logger.log(
      `Creating basic template '${createDto.name}' for user ${user.id}`,
    );
    const supabase = this.supabaseService.getClient();

    // Prepare template data
    const templateData = {
      name: createDto.name,
      description: createDto.description || null,
      event_type_id: createDto.event_type_id || null, // Updated to use event_type_id
      attendees: createDto.attendees || null,
      template_start_date: createDto.template_start_date || null,
      template_end_date: createDto.template_end_date || null,
      is_public: createDto.is_public,
      city_id: createDto.city_id || null,
      category_id: createDto.category_id || null,
      currency_id: createDto.currency_id || null,
      created_by: user.id,
      package_selections: createDto.package_selections || [],
    };

    this.logger.log(
      `Creating template with data: ${JSON.stringify({
        ...templateData,
        package_selections: `[${(createDto.package_selections || []).length} items]`,
      })}`,
    );

    // Insert template
    const { data: insertedData, error: insertError } = await supabase
      .from(TemplateConstants.TABLE_NAME)
      .insert(templateData)
      .select(TemplateConstants.SUMMARY_SELECT_FIELDS)
      .single<
        Omit<TemplateSummaryDto, 'total_count'> & {
          created_at: string;
          updated_at: string;
          template_start_date?: string | null;
          template_end_date?: string | null;
        }
      >();

    if (insertError) {
      this.logger.error(
        `Error inserting template: ${insertError.message}`,
        insertError.stack,
      );
      throw new InternalServerErrorException('Could not create template.');
    }
    if (!insertedData) {
      throw new InternalServerErrorException(
        'Failed to retrieve created template.',
      );
    }

    // Insert venue associations if provided
    if (createDto.venue_ids && createDto.venue_ids.length > 0) {
      await this.templateVenueService.createTemplateVenueAssociations(
        insertedData.id,
        createDto.venue_ids,
      );
    }

    this.logger.log(`Successfully created template ${insertedData.id}`);

    // Map inserted data to DTO
    const resultDto: TemplateSummaryDto = {
      id: insertedData.id,
      name: insertedData.name,
      description: insertedData.description ?? undefined,
      event_type_id: insertedData.event_type_id ?? undefined,
      city_id: insertedData.city_id ?? undefined,
      currency_id: insertedData.currency_id ?? undefined,
      attendees: insertedData.attendees ?? undefined,
      template_start_date: insertedData.template_start_date
        ? new Date(insertedData.template_start_date)
        : undefined,
      template_end_date: insertedData.template_end_date
        ? new Date(insertedData.template_end_date)
        : undefined,
      category_id: insertedData.category_id ?? undefined,
      created_at: new Date(insertedData.created_at),
      updated_at: new Date(insertedData.updated_at),
      created_by: insertedData.created_by,
      is_public: insertedData.is_public,
      is_deleted: insertedData.is_deleted || false,
    };

    return resultDto;
  }
}
