## Admin - Catalogue Management (`/admin/catalogue`)

**Note:** All endpoints in this section require an `Authorization: Bearer <ADMIN_USER_JWT>` header.

### Packages (`/admin/catalogue/packages`)

#### 1. Create Package

- **Method:** `POST`
- **URL:** `/admin/catalogue/packages`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Example - matches `CreatePackageDto`)
  ```json
  {
    "name": "Deluxe Photography Package",
    "description": "Full day coverage, drone shots, album.",
    "category_id": "your-category-uuid",
    "division_id": "your-division-uuid",
    "quantity_basis": "PER_EVENT"
  }
  ```
- **Description:** Creates a new package in the catalogue.
- **Success Response (201 Created):** Returns `PackageDto` representing the created package.
- **Error Response (400 Bad Request):** Validation failed.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (500 Internal Server Error):** DB error.

#### 2. List Packages

- **Method:** `GET`
- **URL:** `/admin/catalogue/packages`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Query Parameters (from `PackageListQueryDto`):**
  - `limit`, `offset`, `sortBy`, `sortOrder` (as in `/calculations`)
  - `name`: String (filter by name, case-insensitive)
  - `categoryId`: UUID (String) (filter by category)
  - `isDeleted`: Boolean (String: "true" or "false") (filter by deletion status, default false)
- **Example URL:** `/admin/catalogue/packages?limit=5&sortBy=name&isDeleted=false`
- **Description:** Lists packages with filtering, sorting, and pagination.
- **Success Response (200 OK):** Returns `PaginatedResponseDto<PackageDto>`.
- **Error Response (400 Bad Request):** Invalid query params.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (500 Internal Server Error):** DB error.

#### 3. Get Package by ID

- **Method:** `GET`
- **URL:** `/admin/catalogue/packages/{package_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Retrieves details of a specific package by its UUID.
- **Success Response (200 OK):** Returns `PackageDto`.
- **Error Response (400 Bad Request):** Invalid UUID.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found.
- **Error Response (500 Internal Server Error):** DB error.

#### 4. Update Package

- **Method:** `PUT`
- **URL:** `/admin/catalogue/packages/{package_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Include only fields to update, matches `UpdatePackageDto`)
  ```json
  {
    "description": "Updated description: Full day coverage, drone shots, premium album.",
    "category_id": "new-category-uuid"
  }
  ```
- **Description:** Updates specific fields of an existing package.
- **Success Response (200 OK):** Returns updated `PackageDto`.
- **Error Response (400 Bad Request):** Validation failed or invalid UUID.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found.
- **Error Response (500 Internal Server Error):** DB error.

#### 5. Delete Package (Soft)

- **Method:** `DELETE`
- **URL:** `/admin/catalogue/packages/{package_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Soft deletes a package.
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found or already deleted.
- **Error Response (500 Internal Server Error):** DB error.

### Package Prices (`/admin/catalogue/packages/{package_id}/prices`)

#### 1. Create Package Price

- **Method:** `POST`
- **URL:** `/admin/catalogue/packages/{package_id}/prices`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Example - matches `CreatePackagePriceDto`)
  ```json
  {
    "currency_id": "currency-uuid-idr",
    "price": 2500000,
    "unit_base_cost": 1000000,
    "description": "IDR Price Point Q2 2025",
    "effective_from": "202  5-04-01T00:00:00Z" // Optional ISO 8601 string
  }
  ```
- **Description:** Adds a price record for a specific package and currency.
- **Success Response (201 Created):** Returns `PackagePriceDto`.
- **Error Response (400 Bad Request):** Validation failed (invalid UUIDs, missing fields).
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found.
- **Error Response (409 Conflict):** Price for this package/currency/effective_from combination already exists.
- **Error Response (500 Internal Server Error):** DB error.

#### 2. List Package Prices

- **Method:** `GET`
- **URL:** `/admin/catalogue/packages/{package_id}/prices`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Lists all prices associated with a specific package, ordered by effective date descending.
- **Success Response (200 OK):** Returns `PackagePriceDto[]`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found.
- **Error Response (500 Internal Server Error):** DB error.

#### 3. Update Package Price

- **Method:** `PUT`
- **URL:** `/admin/catalogue/packages/{package_id}/prices/{price_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Include only fields to update, matches `UpdatePackagePriceDto`)
  ```json
  {
    "price": 2600000,
    "description": "IDR Price Point Q2 2025 - Revised"
  }
  ```
- **Description:** Updates an existing package price record.
- **Success Response (200 OK):** Returns updated `PackagePriceDto`.
- **Error Response (400 Bad Request):** Validation failed or invalid UUIDs.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package or Price not found, or price does not belong to package.
- **Error Response (500 Internal Server Error):** DB error.

#### 4. Delete Package Price

- **Method:** `DELETE`
- **URL:** `/admin/catalogue/packages/{package_id}/prices/{price_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Deletes a specific price record.
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package or Price not found.
- **Error Response (500 Internal Server Error):** DB error.

### Package Options (`/admin/catalogue/packages/{package_id}/options`)

#### 1. Create Package Option

- **Method:** `POST`
- **URL:** `/admin/catalogue/packages/{package_id}/options`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Example - matches `CreatePackageOptionDto`)
  ```json
  {
    "option_code": "EXTRA_HOUR",
    "option_name": "Additional Hour Coverage",
    "currency_id": "currency-uuid-idr",
    "price_adjustment": 500000,
    "cost_adjustment": 200000,
    "description": "Per additional hour",
    "option_group": "HOURS", // Optional
    "is_default_for_package": false, // Optional
    "is_required": false // Optional
  }
  ```
- **Description:** Adds an option to a specific package.
- **Success Response (201 Created):** Returns `PackageOptionDto`.
- **Error Response (400 Bad Request):** Validation failed (invalid UUIDs, missing fields).
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found.
- **Error Response (409 Conflict):** Option with same code/currency already exists for this package.
- **Error Response (500 Internal Server Error):** DB error.

#### 2. List Package Options

- **Method:** `GET`
- **URL:** `/admin/catalogue/packages/{package_id}/options`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Lists all options associated with a specific package.
- **Success Response (200 OK):** Returns `PackageOptionDto[]`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found.
- **Error Response (500 Internal Server Error):** DB error.

#### 3. Update Package Option

- **Method:** `PUT`
- **URL:** `/admin/catalogue/packages/{package_id}/options/{option_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Include only fields to update, matches `UpdatePackageOptionDto`)
  ```json
  {
    "price_adjustment": 550000,
    "description": "Per additional hour (Updated Price)"
  }
  ```
- **Description:** Updates an existing package option.
- **Success Response (200 OK):** Returns updated `PackageOptionDto`.
- **Error Response (400 Bad Request):** Validation failed or invalid UUIDs.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package or Option not found, or option does not belong to package.
- **Error Response (409 Conflict):** If update causes a unique constraint violation.
- **Error Response (500 Internal Server Error):** DB error.

#### 4. Delete Package Option

- **Method:** `DELETE`
- **URL:** `/admin/catalogue/packages/{package_id}/options/{option_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Deletes a specific package option.
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package or Option not found.
- **Error Response (500 Internal Server Error):** DB error.

### Package Dependencies (`/admin/catalogue/packages/{package_id}/dependencies`)

#### 1. Create Package Dependency

- **Method:** `POST`
- **URL:** `/admin/catalogue/packages/{package_id}/dependencies`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Example - matches `CreatePackageDependencyDto`)
  ```json
  {
    "dependent_package_id": "dependent-package-uuid",
    "dependency_type": "INCOMPATIBLE", // Or "REQUIRES_ALL", "REQUIRES_ONE"
    "description": "Cannot select Deluxe Photo with Basic Video." // Optional
  }
  ```
- **Description:** Adds a dependency relationship between two packages.
- **Success Response (201 Created):** Returns `PackageDependencyDto`.
- **Error Response (400 Bad Request):** Validation failed (invalid UUIDs, self-dependency attempt).
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package or Dependent Package not found.
- **Error Response (409 Conflict):** Dependency relationship already exists.
- **Error Response (500 Internal Server Error):** DB error.

#### 2. List Package Dependencies

- **Method:** `GET`
- **URL:** `/admin/catalogue/packages/{package_id}/dependencies`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Lists all dependencies where the specified package is the source.
- **Success Response (200 OK):** Returns `PackageDependencyDto[]`.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found.
- **Error Response (500 Internal Server Error):** DB error.

#### 3. Delete Package Dependency

- **Method:** `DELETE`
- **URL:** `/admin/catalogue/packages/{package_id}/dependencies/{dependent_package_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Removes a specific dependency relationship.
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Dependency relationship not found.
- **Error Response (500 Internal Server Error):** DB error.

### Package Cities (`/admin/catalogue/packages/{package_id}/cities`)

#### 1. Add City Availability

- **Method:** `POST`
- **URL:** `/admin/catalogue/packages/{package_id}/cities`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Example - matches `AddPackageCityDto`)
  ```json
  {
    "city_id": "city-uuid-to-add"
  }
  ```
- **Description:** Makes a package available in the specified city.
- **Success Response:** `201 Created` (No response body, as service returns void)
- **Error Response (400 Bad Request):** Invalid UUIDs.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package or City not found.
- **Error Response (409 Conflict):** Package already available in this city.
- **Error Response (500 Internal Server Error):** DB error.

#### 2. List Available Cities

- **Method:** `GET`
- **URL:** `/admin/catalogue/packages/{package_id}/cities`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Lists all cities where the specified package is available.
- **Success Response (200 OK):** Returns `PackageCityDto[]`.
  ```json
  [
    {
      "id": "city-uuid-1",
      "name": "Jakarta"
    },
    {
      "id": "city-uuid-2",
      "name": "Surabaya"
    }
    // ...
  ]
  ```
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found.
- **Error Response (500 Internal Server Error):** DB error.

#### 3. Remove City Availability

- **Method:** `DELETE`
- **URL:** `/admin/catalogue/packages/{package_id}/cities/{city_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Removes the availability of a package in a specific city.
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Package not found or not configured for the specified city.
- **Error Response (500 Internal Server Error):** DB error.

### Service Categories (`/admin/service-categories`)

**Note:** All endpoints in this section require an `Authorization: Bearer <ADMIN_USER_JWT>` header.

#### 1. Create Service Category

- **Method:** `POST`
- **URL:** `/admin/service-categories`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Example - matches `CreateServiceCategoryDto`)
  ```json
  {
    "name": "Photography Services",
    "description": "All services related to photography.",
    "parent_category_id": null // or "uuid-of-parent-category"
  }
  ```
- **Description:** Creates a new service category.
- **Success Response (201 Created):** Returns `ServiceCategoryDto` representing the created category.
- **Error Response (400 Bad Request):** Validation failed (e.g., missing name, invalid parent UUID).
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (409 Conflict):** A category with the same name might already exist (depending on constraints).
- **Error Response (500 Internal Server Error):** DB error.

#### 2. List Service Categories

- **Method:** `GET`
- **URL:** `/admin/service-categories`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Query Parameters:** None currently defined (add if pagination/filtering is implemented).
- **Description:** Lists all non-deleted service categories.
- **Success Response (200 OK):** Returns `ServiceCategoryDto[]`.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (500 Internal Server Error):** DB error.

#### 3. Get Service Category by ID

- **Method:** `GET`
- **URL:** `/admin/service-categories/{category_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Retrieves details of a specific service category by its UUID.
- **Success Response (200 OK):** Returns `ServiceCategoryDto`.
- **Error Response (400 Bad Request):** Invalid UUID format.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Category not found or is deleted.
- **Error Response (500 Internal Server Error):** DB error.

#### 4. Update Service Category

- **Method:** `PATCH`
- **URL:** `/admin/service-categories/{category_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
  - `Content-Type`: `application/json`
- **Body:** `raw (JSON)` (Include only fields to update, matches `UpdateServiceCategoryDto`)
  ```json
  {
    "description": "Updated description for photography services.",
    "parent_category_id": "new-parent-uuid"
  }
  ```
- **Description:** Updates specific fields of an existing service category.
- **Success Response (200 OK):** Returns updated `ServiceCategoryDto`.
- **Error Response (400 Bad Request):** Validation failed or invalid UUID.
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Category not found or is deleted.
- **Error Response (409 Conflict):** If update causes a unique constraint violation.
- **Error Response (500 Internal Server Error):** DB error.

#### 5. Delete Service Category (Soft)

- **Method:** `DELETE`
- **URL:** `/admin/service-categories/{category_id}`
- **Headers:**
  - `Authorization`: `Bearer <ADMIN_SUPABASE_JWT>`
- **Description:** Soft deletes a service category (sets `is_deleted` to true).
- **Success Response:** `204 No Content`
- **Error Response (401 Unauthorized):** Invalid/missing token.
- **Error Response (403 Forbidden):** User not admin.
- **Error Response (404 Not Found):** Category not found or already deleted.
- **Error Response (500 Internal Server Error):** DB error.
