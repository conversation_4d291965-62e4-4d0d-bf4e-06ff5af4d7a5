import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { AddCustomLineItemDto } from '../dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from '../dto/update-line-item.dto';
import { CustomItemDto } from '../dto/custom-item.dto';

@Injectable()
export class CustomItemsService {
  private readonly logger = new Logger(CustomItemsService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Get all custom items for a calculation
   */
  async getCustomItems(calculationId: string): Promise<CustomItemDto[]> {
    this.logger.log(`Fetching custom items for calculation ${calculationId}`);
    const supabase = this.supabaseService.getClient();

    const { data: customItems, error } = await supabase
      .from('calculation_custom_items')
      .select(`
        id,
        calculation_id,
        item_name,
        description,
        item_quantity,
        item_quantity_basis,
        quantity_basis,
        unit_price,
        unit_cost,
        category_id,
        currency_id,
        city_id,
        created_at,
        updated_at
      `)
      .eq('calculation_id', calculationId)
      .order('created_at', { ascending: true });

    if (error) {
      this.logger.error(`Error fetching custom items: ${error.message}`);
      throw new InternalServerErrorException('Failed to fetch custom items');
    }

    return customItems.map(item => this.transformToCustomItemDto(item));
  }

  /**
   * Get a specific custom item by ID
   */
  async getCustomItemById(calculationId: string, itemId: string): Promise<CustomItemDto> {
    this.logger.log(`Fetching custom item ${itemId} for calculation ${calculationId}`);
    const supabase = this.supabaseService.getClient();

    const { data: customItem, error } = await supabase
      .from('calculation_custom_items')
      .select(`
        id,
        calculation_id,
        item_name,
        description,
        item_quantity,
        item_quantity_basis,
        quantity_basis,
        unit_price,
        unit_cost,
        category_id,
        currency_id,
        city_id,
        created_at,
        updated_at
      `)
      .eq('id', itemId)
      .eq('calculation_id', calculationId)
      .single();

    if (error) {
      this.logger.error(`Error fetching custom item: ${error.message}`);
      throw new NotFoundException(`Custom item with ID ${itemId} not found`);
    }

    return this.transformToCustomItemDto(customItem);
  }

  /**
   * Add a new custom item to a calculation
   */
  async addCustomItem(
    calcId: string,
    addDto: AddCustomLineItemDto,
    user: User,
  ): Promise<{ id: string }> {
    this.logger.log(
      `User ${user.id} adding custom item '${addDto.itemName}' to calculation ${calcId}`,
    );
    const supabase = this.supabaseService.getClient();

    // Check ownership
    await this.checkCalculationOwnership(supabase, calcId, user.id);

    // Get calculation currency
    const { data: calcData, error: calcError } = await supabase
      .from('calculation_history')
      .select('currency_id')
      .eq('id', calcId)
      .single<{ currency_id: string }>();

    if (calcError || !calcData) {
      throw new InternalServerErrorException(
        'Failed to retrieve calculation currency for custom item.',
      );
    }

    // Prepare insert payload
    const insertPayload = {
      calculation_id: calcId,
      item_name: addDto.itemName,
      description: addDto.description,
      item_quantity: addDto.quantity,
      item_quantity_basis: addDto.itemQuantityBasis || 1,
      quantity_basis: addDto.quantityBasis || 'PER_DAY',
      unit_price: addDto.unitPrice,
      unit_cost: addDto.unitCost || 0,
      currency_id: calcData.currency_id,
      category_id: addDto.categoryId,
      city_id: addDto.cityId,
    };

    // Insert the custom item
    const { data, error: insertError } = await supabase
      .from('calculation_custom_items')
      .insert(insertPayload)
      .select('id')
      .single();

    if (insertError || !data) {
      this.logger.error(
        `Failed to add custom item to calculation ${calcId}: ${insertError?.message}`,
        insertError?.stack,
      );
      throw new InternalServerErrorException('Could not add custom item.');
    }

    // Trigger recalculation
    await this.recalcCalculationViaRpc(calcId);

    this.logger.log(
      `Custom item ${data.id} added to calculation ${calcId} and recalculation triggered`,
    );
    return { id: data.id };
  }

  /**
   * Update a custom item
   */
  async updateCustomItem(
    calcId: string,
    itemId: string,
    updateDto: UpdateLineItemDto,
    user: User,
  ): Promise<CustomItemDto> {
    this.logger.log(`Updating custom item ${itemId} in calculation ${calcId}`);
    const supabase = this.supabaseService.getClient();

    // Check ownership
    await this.checkCalculationOwnership(supabase, calcId, user.id);

    const updatePayload: any = {};
    if (updateDto.itemName !== undefined) updatePayload.item_name = updateDto.itemName;
    if (updateDto.description !== undefined) updatePayload.description = updateDto.description;
    if (updateDto.quantity !== undefined) updatePayload.item_quantity = updateDto.quantity;
    if (updateDto.unitPrice !== undefined) updatePayload.unit_price = updateDto.unitPrice;
    if (updateDto.unitCost !== undefined) updatePayload.unit_cost = updateDto.unitCost;
    if (updateDto.itemQuantityBasis !== undefined) updatePayload.item_quantity_basis = updateDto.itemQuantityBasis;
    if (updateDto.quantityBasis !== undefined) updatePayload.quantity_basis = updateDto.quantityBasis;
    if (updateDto.categoryId !== undefined) updatePayload.category_id = updateDto.categoryId;

    updatePayload.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('calculation_custom_items')
      .update(updatePayload)
      .eq('id', itemId)
      .eq('calculation_id', calcId)
      .select('*')
      .single();

    if (error) {
      this.logger.error(
        `Error updating custom item ${itemId}: ${error.message}`,
        error.details,
      );
      throw new InternalServerErrorException('Failed to update custom item.');
    }

    // Trigger recalculation
    await this.recalcCalculationViaRpc(calcId);

    this.logger.log(`Successfully updated custom item ${itemId} and triggered recalculation`);
    return this.transformToCustomItemDto(data);
  }

  /**
   * Delete a custom item
   */
  async deleteCustomItem(calcId: string, itemId: string, user: User): Promise<void> {
    this.logger.log(`Deleting custom item ${itemId} from calculation ${calcId}`);
    const supabase = this.supabaseService.getClient();

    // Check ownership
    await this.checkCalculationOwnership(supabase, calcId, user.id);

    const { error } = await supabase
      .from('calculation_custom_items')
      .delete()
      .eq('id', itemId)
      .eq('calculation_id', calcId);

    if (error) {
      this.logger.error(
        `Error deleting custom item ${itemId}: ${error.message}`,
        error.details,
      );
      throw new InternalServerErrorException('Failed to delete custom item.');
    }

    // Trigger recalculation
    await this.recalcCalculationViaRpc(calcId);

    this.logger.log(`Successfully deleted custom item ${itemId} and triggered recalculation`);
  }

  /**
   * Transform database row to CustomItemDto
   */
  private transformToCustomItemDto(item: any): CustomItemDto {
    return {
      id: item.id,
      calculation_id: item.calculation_id,
      item_name: item.item_name,
      description: item.description,
      item_quantity: item.item_quantity,
      item_quantity_basis: item.item_quantity_basis,
      quantity_basis: item.quantity_basis,
      unit_price: item.unit_price,
      unit_cost: item.unit_cost,
      calculated_total: item.item_quantity * item.unit_price,
      category_id: item.category_id,
      currency_id: item.currency_id,
      city_id: item.city_id,
      created_at: item.created_at,
      updated_at: item.updated_at,
    };
  }

  /**
   * Check if user owns the calculation
   */
  private async checkCalculationOwnership(
    supabase: any,
    calculationId: string,
    userId: string,
  ): Promise<void> {
    const { data, error } = await supabase
      .from('calculation_history')
      .select('id')
      .eq('id', calculationId)
      .eq('created_by', userId)
      .maybeSingle();

    if (error) {
      this.logger.error(
        `Error checking ownership for calc ${calculationId}: ${error.message}`,
      );
      throw new InternalServerErrorException('Ownership check failed.');
    }
    if (!data) {
      this.logger.warn(
        `User ${userId} attempt to modify calc ${calculationId} without ownership.`,
      );
      throw new ForbiddenException('Access denied to this calculation.');
    }
  }

  /**
   * Calls the database function to recalculate totals for a given calculation.
   */
  private async recalcCalculationViaRpc(calcId: string): Promise<void> {
    this.logger.log(`Triggering recalculation RPC for calculation ${calcId}`);
    const supabase = this.supabaseService.getClient();
    const { error: rpcError } = await supabase.rpc(
      'recalculate_calculation_totals',
      { p_calculation_id: calcId },
    );

    if (rpcError) {
      this.logger.error(
        `RPC recalculate_calculation_totals failed for calc ${calcId}: ${rpcError.message}`,
        rpcError.details,
      );
      throw new InternalServerErrorException(
        `Failed to trigger recalculation for calculation ${calcId}.`,
      );
    }

    this.logger.log(
      `Successfully triggered recalculation via RPC for calc ID: ${calcId}`,
    );
  }
}
