import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PlusCircle, FileText, Users, Calendar, Settings } from "lucide-react";
import { useOnboarding } from "@/contexts/OnboardingContext";
import GuidedTooltip from "./GuidedTooltip";

const QuickStart: React.FC = () => {
  const { isFirstTimeUser } = useOnboarding();

  const quickActions = [
    {
      title: "New Calculation",
      description: "Create a new event cost calculation",
      icon: <PlusCircle className="h-5 w-5" />,
      to: "/calculations/new",
      primary: true,
    },
    {
      title: "Browse Templates",
      description: "Use admin-provided event templates",
      icon: <FileText className="h-5 w-5" />,
      to: "/templates",
      primary: false,
    },
    {
      title: "Manage Clients",
      description: "View and manage your clients",
      icon: <Users className="h-5 w-5" />,
      to: "/clients",
      primary: false,
    },
    {
      title: "Manage Events",
      description: "View and manage your events",
      icon: <Calendar className="h-5 w-5" />,
      to: "/events",
      primary: false,
    },
  ];

  return (
    <GuidedTooltip
      step="quick_start"
      title="Quick Start Actions"
      description="Get started quickly with these common actions. Create a new calculation or use a template to save time."
      position="bottom"
      isFirst={true}
    >
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-100 dark:border-blue-800">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <div>
              <h2 className="text-lg font-semibold text-blue-800 dark:text-blue-300">
                Quick Start
              </h2>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                Get started with these common actions
              </p>
            </div>

            {isFirstTimeUser && (
              <div className="mt-2 md:mt-0">
                <Link to="/help/getting-started">
                  <Button
                    variant="link"
                    size="sm"
                    className="text-blue-700 dark:text-blue-400"
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    View Getting Started Guide
                  </Button>
                </Link>
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Link key={index} to={action.to} className="block">
                <div
                  className={`
                  h-full p-4 rounded-lg border transition-all duration-200 hover-scale
                  ${
                    action.primary
                      ? "bg-blue-600 text-white border-blue-700 hover:bg-blue-700"
                      : "bg-white dark:bg-gray-800 text-gray-800 dark:text-white border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  }
                `}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="flex items-start">
                    <div
                      className={`
                      p-2 rounded-full mr-3
                      ${
                        action.primary
                          ? "bg-blue-500"
                          : "bg-blue-100 dark:bg-blue-900/30"
                      }
                    `}
                    >
                      {action.icon}
                    </div>
                    <div>
                      <h3
                        className={`font-medium ${
                          action.primary
                            ? "text-white"
                            : "text-gray-800 dark:text-white"
                        }`}
                      >
                        {action.title}
                      </h3>
                      <p
                        className={`text-sm mt-1 ${
                          action.primary
                            ? "text-blue-100"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {action.description}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>
    </GuidedTooltip>
  );
};

export default QuickStart;
