import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';

// Renamed to avoid conflicts with admin module
@ApiExtraModels()
export class BatchPackageOptionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  option_name: string;

  @ApiProperty({ required: false })
  description: string;

  @ApiProperty()
  price_adjustment: number;

  @ApiProperty()
  cost_adjustment: number;

  @ApiProperty()
  is_default_for_package: boolean;

  @ApiProperty()
  is_required: boolean;
}

export class BatchPackageOptionsResponseDto {
  @ApiProperty({ type: Object })
  options: Record<string, BatchPackageOptionDto[]>;
}

