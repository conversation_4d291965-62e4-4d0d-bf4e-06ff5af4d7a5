import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, InternalServerErrorException } from '@nestjs/common';
import { Job } from 'bullmq';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { ExportsService } from '../exports.service';
import { ExportStorageService } from '../services/export-storage.service';
import { ExportGenerationService } from '../services/export-generation.service';
import { CalculationsService } from '../../calculations/calculations.service';
import { ExportStatus } from '../enums/export-status.enum';
import { ExportFormat } from '../enums/export-format.enum';

export interface PdfExportJobData {
  exportHistoryId: string;
  calculationId: string;
  userId: string;
}

@Processor('pdf-exports')
export class PdfExportProcessor extends WorkerHost {
  private readonly logger = new Logger(PdfExportProcessor.name);

  constructor(
    private readonly exportsService: ExportsService,
    private readonly storageService: ExportStorageService,
    private readonly generationService: ExportGenerationService,
    private readonly calculationsService: CalculationsService,
  ) {
    super();
  }

  async process(job: Job<PdfExportJobData>): Promise<void> {
    const { exportHistoryId, calculationId, userId } = job.data;

    this.logger.log(
      `[PDF] Processing job ${job.id} for export history ${exportHistoryId}`,
    );
    this.logger.log(`[PDF] Calculation: ${calculationId}, User: ${userId}`);

    let tempFilePath: string | undefined = undefined;
    let generatedFileName: string = '';
    let storagePath: string | undefined = undefined;

    try {
      // 1. Update status to PROCESSING
      this.logger.log(
        `[PDF] Updating status to PROCESSING for export ${exportHistoryId}`,
      );
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.PROCESSING,
      );

      // 2. Fetch calculation data
      this.logger.log(`[PDF] Fetching calculation data for ${calculationId}`);
      const calculationData =
        await this.calculationsService.findCalculationForExport(
          calculationId,
          userId,
        );

      if (!calculationData) {
        throw new InternalServerErrorException(
          `[PDF] Calculation data not found for ID: ${calculationId}`,
        );
      }
      this.logger.log(
        `[PDF] Calculation data fetched successfully for ${calculationId}`,
      );

      // 3. Transform data
      this.logger.log(
        `[PDF] Transforming calculation data for ${calculationId}`,
      );
      const transformedData =
        this.generationService.transformCalculationData(calculationData);
      this.logger.log(
        `[PDF] Data transformation completed for ${calculationId}`,
      );

      // 4. Generate PDF file
      this.logger.log(`[PDF] Generating PDF file for ${calculationId}`);
      const sanitizedCalcName = calculationData.name
        .replace(/[^a-z0-9]/gi, '_')
        .toLowerCase();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      generatedFileName = `${sanitizedCalcName}_${timestamp}.pdf`;

      // Create temporary file path
      tempFilePath = path.join(os.tmpdir(), generatedFileName);
      this.logger.log(`[PDF] Creating temporary file at: ${tempFilePath}`);

      // Generate PDF to temporary file
      await this.generationService.generatePdfToFile(
        transformedData,
        tempFilePath,
      );
      this.logger.log(
        `[PDF] PDF file generated successfully at: ${tempFilePath}`,
      );

      // Read file buffer from temporary file
      this.logger.log(`[PDF] Reading file buffer from temporary file`);
      const fileBuffer = fs.readFileSync(tempFilePath);
      this.logger.log(
        `[PDF] File buffer read successfully, size: ${fileBuffer.length} bytes`,
      );

      // 5. Upload file to storage
      this.logger.log(`[PDF] Uploading file to storage: ${generatedFileName}`);
      storagePath = await this.storageService.uploadExportFile(
        userId,
        generatedFileName,
        fileBuffer,
        'application/pdf',
      );
      this.logger.log(`[PDF] File uploaded successfully to: ${storagePath}`);

      // 6. Update status to COMPLETED
      this.logger.log(
        `[PDF] Updating status to COMPLETED for export ${exportHistoryId}`,
      );
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.COMPLETED,
        {
          storagePath: storagePath,
          fileName: generatedFileName,
          fileSize: fileBuffer.length,
          mimeType: 'application/pdf',
        },
      );

      this.logger.log(
        `[PDF] Job ${job.id} completed successfully for export history ${exportHistoryId}`,
      );
    } catch (error: unknown) {
      this.logger.error(
        `[PDF] Job ${job.id} failed for export history ${exportHistoryId}: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );

      // Update status to FAILED
      await this.exportsService.updateExportHistoryStatus(
        exportHistoryId,
        ExportStatus.FAILED,
        {
          error: error instanceof Error ? error.message : String(error),
          fileName: generatedFileName || undefined,
          storagePath: storagePath || undefined,
        },
      );

      throw error; // Re-throw to let BullMQ handle retries/failure state
    } finally {
      // Clean up temporary file if created
      if (tempFilePath) {
        try {
          fs.unlinkSync(tempFilePath);
          this.logger.log(`[PDF] Temporary file deleted: ${tempFilePath}`);
        } catch (unlinkErr: any) {
          this.logger.warn(
            `[PDF] Could not delete temporary file ${tempFilePath}: ${unlinkErr instanceof Error ? unlinkErr.message : String(unlinkErr)}`,
          );
        }
      }
    }
  }
}
