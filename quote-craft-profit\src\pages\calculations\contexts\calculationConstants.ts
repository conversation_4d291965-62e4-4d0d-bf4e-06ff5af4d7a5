/**
 * Constants for calculation context
 * Separated from CalculationContext.tsx to satisfy React Fast Refresh requirements
 */

/**
 * Error message for context usage outside provider
 */
export const CALCULATION_CONTEXT_ERROR_MESSAGE =
  "useCalculationContext must be used within a CalculationProvider. " +
  "Make sure to wrap your component with <CalculationProvider>.";

/**
 * Context name constant
 */
export const CALCULATION_CONTEXT_NAME = "CalculationContext" as const;
