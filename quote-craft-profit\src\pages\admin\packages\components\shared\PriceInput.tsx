
import { UseFormReturn, FieldPath } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { CurrencyInput } from '@/components/ui/currency-input';

interface PriceInputProps<T extends Record<string, any>> {
  form: UseFormReturn<T>;
  name: FieldPath<T>;
  label: string;
  description?: string;
  placeholder?: string;
  currencySymbol?: string;
  required?: boolean;
}

/**
 * Reusable price input component with currency formatting
 * Handles numeric input validation and currency display
 */
export function PriceInput<T extends Record<string, any>>({
  form,
  name,
  label,
  description,
  placeholder = '0',
  currencySymbol = 'Rp',
  required = false,
}: PriceInputProps<T>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel className='font-medium'>
            {label} {required && '*'}
          </FormLabel>
          <FormControl>
            <CurrencyInput
              value={field.value || ''}
              onChange={(numericValue) => {
                field.onChange(numericValue);
              }}
              placeholder={placeholder}
              currencySymbol={currencySymbol}
              showSymbol={true}
              aria-invalid={!!form.formState.errors[name]}
              aria-describedby={description ? `${name}-description` : undefined}
            />
          </FormControl>
          {description && (
            <FormDescription id={`${name}-description`}>
              {description}
            </FormDescription>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
