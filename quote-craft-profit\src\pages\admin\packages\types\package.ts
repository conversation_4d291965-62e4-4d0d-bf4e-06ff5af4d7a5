/**
 * Package types for the admin packages feature
 */

import { Package as GlobalPackage, QuantityBasisEnum } from '@/types/types';

// Re-export the global types for convenience
export { QuantityBasisEnum as QuantityBasis };

// Extend the global Package interface with admin-specific fields
export interface Package extends GlobalPackage {
  createdAt?: string;
  updatedAt?: string;
}

export interface PackageSaveData {
  id?: string;
  name: string;
  description?: string;
  categoryId?: string;
  divisionId?: string;
  cityIds?: string[];
  venueIds?: string[];
  enableVenues?: boolean;
  quantityBasis: QuantityBasisEnum;
  isDeleted?: boolean;
  price?: number;
  unitBaseCost?: number;
  currencyId?: string;
}

export interface PackageFormValues {
  name: string;
  description: string;
  categoryId: string;
  divisionId: string;
  cityIds: string[];
  venueIds: string[]; // Added for venue selection
  enableVenues: boolean; // Flag to enable/disable venue selection
  quantityBasis: QuantityBasisEnum;
  isActive: boolean;
  price: string; // Using string for input handling
  unitBaseCost: string; // Using string for input handling
  currencyId: string; // Added for currency selection
}

export interface PackageFormProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  packageId: string | null;
}

export interface AddPackageDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
}

export interface EditPackageDialogProps {
  isOpen: boolean;
  onClose: (shouldRefresh?: boolean) => void;
  packageId: string;
}
