import { ApiProperty } from '@nestjs/swagger';

export class CalculationTotalsDto {
  @ApiProperty()
  subtotal: number;

  @ApiProperty()
  lineItemsTotal: number;

  @ApiProperty()
  customItemsTotal: number;

  @ApiProperty()
  taxTotal: number;

  @ApiProperty()
  discountAmount: number;

  @ApiProperty()
  total: number;

  @ApiProperty()
  totalCost: number;

  @ApiProperty()
  estimatedProfit: number;
}
