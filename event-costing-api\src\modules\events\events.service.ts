import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { EventDto } from './dto/event.dto';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { PostgrestError } from '@supabase/supabase-js';
import { EventStatus } from './dto/event-status.enum';

@Injectable()
export class EventsService {
  private readonly logger = new Logger(EventsService.name);
  private readonly TABLE_NAME = 'events';

  constructor(private readonly supabaseService: SupabaseService) {}

  private handleSupabaseError(error: PostgrestError, context: string): never {
    this.logger.error(`${context}: ${error.message}`, error.stack);
    if (error.code === '23503') {
      throw new NotFoundException(
        'Referenced client or primary contact not found.',
      );
    }
    throw new InternalServerErrorException(`Could not ${context}.`);
  }

  async create(createEventDto: CreateEventDto): Promise<EventDto> {
    this.logger.log(`Creating a new event: ${createEventDto.event_name}`);
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .insert({
        event_name: createEventDto.event_name,
        client_id: createEventDto.client_id,
        event_start_datetime: createEventDto.event_start_datetime,
        event_end_datetime: createEventDto.event_end_datetime,
        status: createEventDto.status,
        venue_details: createEventDto.venue_details,
        primary_contact_id: createEventDto.primary_contact_id,
        notes: createEventDto.notes,
      })
      .select()
      .single<EventDto>();

    if (error) {
      this.handleSupabaseError(error, 'create event');
    }

    if (!data) {
      this.logger.error('Insert operation did not return event data.');
      throw new InternalServerErrorException('Failed to create event.');
    }

    this.logger.log(`Event created successfully with ID: ${data.id}`);
    return data; // Returning data directly after null check
  }

  async findAll(
    search?: string,
    status?: EventStatus,
    clientId?: string,
    contactId?: string,
  ): Promise<EventDto[]> {
    this.logger.log(
      `Fetching all events ${search ? `matching '${search}'` : ''} ${status ? `with status ${status}` : ''} ${clientId ? `for client ${clientId}` : ''} ${contactId ? `for contact ${contactId}` : ''}`,
    );
    const supabase = this.supabaseService.getClient();

    const selectFields =
      'id, event_name, client_id, event_start_datetime, event_end_datetime, status, venue_details, primary_contact_id, notes, created_at, updated_at';

    let query = supabase
      .from(this.TABLE_NAME)
      .select(selectFields)
      .eq('is_deleted', false)
      .order('event_start_datetime', { ascending: true, nullsFirst: false });

    if (search) {
      query = query.or(
        `event_name.ilike.%${search}%,venue_details.ilike.%${search}%`,
      );
    }
    if (status) {
      query = query.eq('status', status);
    }
    if (clientId) {
      query = query.eq('client_id', clientId);
    }
    if (contactId) {
      query = query.eq('primary_contact_id', contactId);
    }

    const { data, error } = await query.returns<EventDto[]>();

    if (error) {
      this.handleSupabaseError(error, 'find all events');
    }

    return data || [];
  }

  async findOne(id: string): Promise<EventDto> {
    this.logger.log(`Fetching event with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    const selectString = `
      *,
      clients ( id, client_name, contact_person ),
      profiles ( id, full_name, username )
    `;

    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select(selectString) // Use the detailed select string
      .eq('id', id)
      .eq('is_deleted', false)
      .single<EventDto>(); // Make sure EventDto type matches the selected structure

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(`Event with ID ${id} not found.`);
      }
      this.handleSupabaseError(error, `find event by ID ${id}`);
    }

    if (!data) {
      throw new NotFoundException(`Event with ID ${id} not found.`);
    }

    return data;
  }

  async update(id: string, updateEventDto: UpdateEventDto): Promise<EventDto> {
    this.logger.log(`Updating event with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    await this.findOne(id);

    // Update the event with the provided data
    const { error: updateError } = await supabase
      .from(this.TABLE_NAME)
      .update({
        event_name: updateEventDto.event_name,
        client_id: updateEventDto.client_id,
        event_start_datetime: updateEventDto.event_start_datetime,
        event_end_datetime: updateEventDto.event_end_datetime,
        status: updateEventDto.status,
        venue_details: updateEventDto.venue_details,
        primary_contact_id: updateEventDto.primary_contact_id,
        notes: updateEventDto.notes,
      })
      .eq('id', id)
      .eq('is_deleted', false);

    if (updateError) {
      this.handleSupabaseError(updateError, `update event ${id}`);
    }

    const selectString = `
      *,
      clients ( id, client_name, contact_person ),
      profiles ( id, full_name, username )
    `;

    const { data, error } = await supabase
      .from(this.TABLE_NAME)
      .select(selectString) // Use the detailed select string
      .eq('id', id)
      .eq('is_deleted', false)
      .single<EventDto>(); // Make sure EventDto type matches the selected structure
    if (error) {
      this.handleSupabaseError(error, `update event ${id}`);
    }

    if (!data) {
      this.logger.error(`Update operation did not return data for event ${id}`);
      throw new InternalServerErrorException('Failed to update event.');
    }

    this.logger.log(`Event ${id} updated successfully.`);
    return data;
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Soft deleting event with ID: ${id}`);
    const supabase = this.supabaseService.getClient();

    await this.findOne(id);

    const { error, count } = await supabase
      .from(this.TABLE_NAME)
      .update({ is_deleted: true, deleted_at: new Date().toISOString() })
      .eq('id', id)
      .eq('is_deleted', false);

    if (error) {
      this.handleSupabaseError(error, `soft delete event ${id}`);
    }

    if (count === 0) {
      this.logger.warn(
        `Event ${id} not found or already deleted during soft delete attempt.`,
      );
      throw new NotFoundException(
        `Event with ID ${id} not found or already deleted.`,
      );
    }

    this.logger.log(`Event ${id} soft deleted successfully.`);
  }
}
