import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreateServiceCategoryDto } from './dto/create-service-category.dto';
import { UpdateServiceCategoryDto } from './dto/update-service-category.dto';
import { ServiceCategoryDto } from './dto/service-category.dto';
import { PostgrestError } from '@supabase/supabase-js';

@Injectable()
export class ServiceCategoriesService {
  private readonly logger = new Logger(ServiceCategoriesService.name);
  private readonly tableName = 'service_categories';

  constructor(private readonly supabaseService: SupabaseService) {}

  // --- Create Service Category --- //
  async create(
    createDto: CreateServiceCategoryDto,
  ): Promise<ServiceCategoryDto> {
    this.logger.log(
      `Attempting to create service category: ${JSON.stringify(createDto)}`,
    );

    // Explicitly type the expected result
    const { data, error } = await this.supabaseService
      .getClient()
      .from(this.tableName)
      .insert([
        {
          name: createDto.name,
          description: createDto.description,
          parent_category_id: createDto.parent_category_id,
        },
      ])
      .select('*')
      .single<ServiceCategoryDto>(); // Use Supabase generic

    if (error) {
      this.handleSupabaseError(error, 'create', createDto);
    }

    // Check for null data explicitly, even though single() implies one row or error
    if (!data) {
      this.logger.error('Service category creation failed: No data returned.');
      throw new InternalServerErrorException(
        'Service category creation failed unexpectedly.',
      );
    }

    this.logger.log(
      `Service category created successfully with ID: ${data.id}`, // Safe access
    );
    return data; // Return typed data
  }

  // --- Find All Service Categories --- //
  async findAll(): Promise<ServiceCategoryDto[]> {
    this.logger.log('Attempting to fetch all service categories');

    const { data, error } = await this.supabaseService
      .getClient()
      .from(this.tableName)
      .select('*')
      .is('deleted_at', null) // Only fetch non-deleted items
      .order('name', { ascending: true });

    if (error) {
      this.handleSupabaseError(error, 'findAll');
    }

    this.logger.log(`Fetched ${data?.length || 0} service categories.`);
    // Explicitly cast to the expected DTO array type or empty array
    return (data as ServiceCategoryDto[]) || [];
  }

  // --- Find One Service Category by ID --- //
  async findOne(id: string): Promise<ServiceCategoryDto> {
    this.logger.log(`Attempting to fetch service category with ID: ${id}`);

    // Explicitly type the expected result
    const { data, error } = await this.supabaseService
      .getClient()
      .from(this.tableName)
      .select('*')
      .eq('id', id)
      .is('deleted_at', null)
      .maybeSingle<ServiceCategoryDto>(); // Use Supabase generic

    if (error) {
      this.handleSupabaseError(error, 'findOne', { id });
    }

    if (!data) {
      this.logger.warn(`Service category with ID ${id} not found.`);
      throw new NotFoundException(`Service category with ID ${id} not found.`);
    }

    this.logger.log(`Fetched service category with ID: ${id}`);
    return data; // Return typed data
  }

  // --- Update Service Category --- //
  async update(
    id: string,
    updateDto: UpdateServiceCategoryDto,
  ): Promise<ServiceCategoryDto> {
    this.logger.log(
      `Attempting to update service category ${id}: ${JSON.stringify(updateDto)}`,
    );

    // Fetch existing first to ensure it exists
    await this.findOne(id);

    if (Object.keys(updateDto).length === 0) {
      this.logger.warn(
        `Update called for service category ${id} with no data.`,
      );
      // Return current data if no changes are provided
      return this.findOne(id);
    }

    // Explicitly type the expected result
    const { data, error } = await this.supabaseService
      .getClient()
      .from(this.tableName)
      .update({
        ...updateDto,
        updated_at: new Date().toISOString(), // Manually set updated_at
      })
      .eq('id', id)
      .select('*')
      .single<ServiceCategoryDto>(); // Use Supabase generic

    if (error) {
      this.handleSupabaseError(error, 'update', { id, ...updateDto });
    }

    // Check for null data explicitly
    if (!data) {
      this.logger.error(
        `Service category with ID ${id} not found after update attempt.`, // Should theoretically not happen if findOne() passed
      );
      throw new NotFoundException(
        `Service category with ID ${id} not found after update attempt.`, // Or InternalServerErrorException?
      );
    }

    this.logger.log(`Service category ${id} updated successfully.`);
    return data; // Return typed data
  }

  // --- Soft Delete Service Category --- //
  async remove(id: string): Promise<void> {
    this.logger.log(
      `Attempting to soft-delete service category with ID: ${id}`,
    );

    // Fetch existing first to ensure it exists
    await this.findOne(id);

    const { error } = await this.supabaseService
      .getClient()
      .from(this.tableName)
      .update({ deleted_at: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      this.handleSupabaseError(error, 'remove', { id });
    }

    this.logger.log(`Service category ${id} soft-deleted successfully.`);
  }

  // --- Error Handler --- //
  private handleSupabaseError(
    error: PostgrestError,
    operation: string,
    context?: Record<string, any>, // Changed 'unknown' to 'any' for flexibility with DTOs
  ): never {
    this.logger.error(`Supabase error during ${operation}: ${error.message}`, {
      details: error.details,
      code: error.code,
      context,
    });

    // Handle specific error codes
    if (error.code === '23505') {
      // unique_violation
      throw new ConflictException(
        `Operation '${operation}' failed due to a unique constraint violation.`,
      );
    }
    if (error.code === '23503') {
      // foreign_key_violation
      throw new ConflictException(
        `Operation '${operation}' failed due to a foreign key constraint.`,
      );
    }
    if (error.code === 'PGRST116') {
      // Not found during update/delete
      throw new NotFoundException(
        `Operation '${operation}' failed: The record was not found.`,
      );
    }

    // Generic fallback
    throw new InternalServerErrorException(
      `An unexpected error occurred during ${operation}: ${error.message}`,
    );
  }
}
