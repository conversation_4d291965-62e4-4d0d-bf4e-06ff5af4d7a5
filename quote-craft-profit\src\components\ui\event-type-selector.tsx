import React from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, AlertCircle } from "lucide-react";
import { getAllEventTypes } from "@/services/shared/entities/event-types";
import { toast } from "sonner";

interface EventTypeSelectorProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  allowEmpty?: boolean;
  emptyLabel?: string;
  className?: string;
}

export const EventTypeSelector = React.forwardRef<
  React.ElementRef<typeof Select>,
  EventTypeSelectorProps
>(
  (
    {
      value,
      onValueChange,
      placeholder = "Select event type",
      disabled = false,
      allowEmpty = true,
      emptyLabel = "None",
      className,
    },
    ref
  ) => {
    // Fetch event types from API
    const {
      data: eventTypes,
      isLoading,
      isError,
      error,
    } = useQuery({
      queryKey: ["event-types"],
      queryFn: getAllEventTypes,
      meta: {
        onError: (error: Error) => {
          toast.error(`Failed to load event types: ${error.message}`);
        },
      },
    });

    // Handle loading state
    if (isLoading) {
      return (
        <Select disabled>
          <SelectTrigger className={className}>
            <div className="flex items-center">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span>Loading event types...</span>
            </div>
          </SelectTrigger>
        </Select>
      );
    }

    // Handle error state
    if (isError) {
      return (
        <Select disabled>
          <SelectTrigger className={className}>
            <div className="flex items-center text-destructive">
              <AlertCircle className="h-4 w-4 mr-2" />
              <span>Failed to load event types</span>
            </div>
          </SelectTrigger>
        </Select>
      );
    }

    // Handle value conversion for empty state
    const handleValueChange = (newValue: string) => {
      if (newValue === "__empty__") {
        onValueChange?.(undefined);
      } else {
        onValueChange?.(newValue);
      }
    };

    // Convert undefined/null value to special empty value for display
    const displayValue = value || (allowEmpty ? "__empty__" : undefined);

    return (
      <Select
        value={displayValue}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger className={className}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {allowEmpty && (
            <SelectItem value="__empty__">{emptyLabel}</SelectItem>
          )}
          {eventTypes?.map((eventType) => (
            <SelectItem key={eventType.id} value={eventType.id}>
              <div className="flex items-center space-x-2">
                {eventType.icon && (
                  <span className="text-sm">{eventType.icon}</span>
                )}
                <span>{eventType.name}</span>
                {eventType.code && (
                  <span className="text-xs text-muted-foreground">
                    ({eventType.code})
                  </span>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }
);

EventTypeSelector.displayName = "EventTypeSelector";

// Hook to get event type by ID for display purposes
export const useEventType = (eventTypeId?: string) => {
  const { data: eventTypes } = useQuery({
    queryKey: ["event-types"],
    queryFn: getAllEventTypes,
  });

  const eventType = eventTypes?.find((et) => et.id === eventTypeId);

  return {
    eventType,
    eventTypeName: eventType?.name,
    eventTypeCode: eventType?.code,
    eventTypeIcon: eventType?.icon,
    eventTypeColor: eventType?.color,
  };
};

export default EventTypeSelector;
