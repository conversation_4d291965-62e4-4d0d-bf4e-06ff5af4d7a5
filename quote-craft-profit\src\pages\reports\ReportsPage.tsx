import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';

const ReportsPage: React.FC = () => {
  return (
    <MainLayout>
      <div className='mb-6 flex justify-between items-center'>
        <div>
          <h1 className='text-2xl font-bold text-gray-800'>Reports</h1>
          <p className='text-gray-600'>
            Generate and view reports on your events and profitability
          </p>
        </div>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'>
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle>Revenue by Event Type</CardTitle>
          </CardHeader>
          <CardContent className='text-center py-10'>
            <div className='flex justify-center items-center h-40 bg-gray-100 rounded-lg'>
              <p className='text-gray-500'>Chart placeholder</p>
            </div>
          </CardContent>
          <CardFooter>
            <Button className='w-full'>Generate Report</Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle>Profit Margins</CardTitle>
          </CardHeader>
          <CardContent className='text-center py-10'>
            <div className='flex justify-center items-center h-40 bg-gray-100 rounded-lg'>
              <p className='text-gray-500'>Chart placeholder</p>
            </div>
          </CardContent>
          <CardFooter>
            <Button className='w-full'>Generate Report</Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle>Monthly Revenue</CardTitle>
          </CardHeader>
          <CardContent className='text-center py-10'>
            <div className='flex justify-center items-center h-40 bg-gray-100 rounded-lg'>
              <p className='text-gray-500'>Chart placeholder</p>
            </div>
          </CardContent>
          <CardFooter>
            <Button className='w-full'>Generate Report</Button>
          </CardFooter>
        </Card>
      </div>

      <div className='mt-8 bg-white rounded-lg shadow p-6'>
        <h2 className='text-xl font-semibold mb-4'>Custom Report</h2>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>
          <div>
            <label className='block text-sm font-medium mb-2'>Report Type</label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder='Select type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='revenue'>Revenue Report</SelectItem>
                <SelectItem value='profit'>Profit Analysis</SelectItem>
                <SelectItem value='events'>Event Summary</SelectItem>
                <SelectItem value='services'>Services Breakdown</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Date Range</label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder='Select range' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='thisMonth'>This Month</SelectItem>
                <SelectItem value='lastMonth'>Last Month</SelectItem>
                <SelectItem value='thisQuarter'>This Quarter</SelectItem>
                <SelectItem value='lastQuarter'>Last Quarter</SelectItem>
                <SelectItem value='thisYear'>This Year</SelectItem>
                <SelectItem value='custom'>Custom Range</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Format</label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder='Select format' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='pdf'>PDF</SelectItem>
                <SelectItem value='excel'>Excel</SelectItem>
                <SelectItem value='csv'>CSV</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className='flex flex-col md:flex-row gap-4'>
          <Button className='md:w-auto'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='h-5 w-5 mr-2'
              viewBox='0 0 20 20'
              fill='currentColor'
            >
              <path
                fillRule='evenodd'
                d='M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3H6a1 1 0 100 2h3v3a1 1 0 102 0v-3h3a1 1 0 100-2h-3V8z'
                clipRule='evenodd'
              />
            </svg>
            Generate Report
          </Button>

          <Button variant='outline'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='h-5 w-5 mr-2'
              viewBox='0 0 20 20'
              fill='currentColor'
            >
              <path
                fillRule='evenodd'
                d='M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z'
                clipRule='evenodd'
              />
            </svg>
            Download Template
          </Button>
        </div>
      </div>

      <div className='mt-8'>
        <h2 className='text-xl font-semibold mb-4'>Recent Reports</h2>

        <div className='bg-white rounded-lg shadow overflow-hidden'>
          <table className='w-full'>
            <thead>
              <tr className='text-left bg-gray-50'>
                <th className='px-6 py-3 font-medium text-gray-600'>Report Name</th>
                <th className='px-6 py-3 font-medium text-gray-600'>Type</th>
                <th className='px-6 py-3 font-medium text-gray-600'>Generated</th>
                <th className='px-6 py-3 font-medium text-gray-600'>Format</th>
                <th className='px-6 py-3 font-medium text-gray-600'></th>
              </tr>
            </thead>
            <tbody>
              <tr className='border-t border-gray-200'>
                <td className='px-6 py-4 font-medium'>Q1 Revenue Report</td>
                <td className='px-6 py-4'>Revenue</td>
                <td className='px-6 py-4'>Apr 1, 2025</td>
                <td className='px-6 py-4'>PDF</td>
                <td className='px-6 py-4 text-right'>
                  <Button variant='ghost' size='sm'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-4 w-4 mr-1'
                      viewBox='0 0 20 20'
                      fill='currentColor'
                    >
                      <path
                        fillRule='evenodd'
                        d='M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z'
                        clipRule='evenodd'
                      />
                    </svg>
                    Download
                  </Button>
                </td>
              </tr>
              <tr className='border-t border-gray-200'>
                <td className='px-6 py-4 font-medium'>March Event Analysis</td>
                <td className='px-6 py-4'>Event Summary</td>
                <td className='px-6 py-4'>Mar 31, 2025</td>
                <td className='px-6 py-4'>Excel</td>
                <td className='px-6 py-4 text-right'>
                  <Button variant='ghost' size='sm'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-4 w-4 mr-1'
                      viewBox='0 0 20 20'
                      fill='currentColor'
                    >
                      <path
                        fillRule='evenodd'
                        d='M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z'
                        clipRule='evenodd'
                      />
                    </svg>
                    Download
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </MainLayout>
  );
};

export default ReportsPage;
